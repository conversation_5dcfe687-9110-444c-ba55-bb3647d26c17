- All database changes for a given release will be created in a changelog file. This includes, but is not limited to
  new static data to be inserted, new tables or columns, modified tables or columns, dropped tables or columns,
  foreign keys, indexes, etc.
- For each release with database changes, a new file must be created in src/main/resources/db.changelog
  directory.  
  Ex. `changelog-2023.06.10.xml` for ai-agent-service-2023.06.10 Release, `changelog-2023.07.08.xml` for
  catalog-service-2023.07.08 Release
- Then, changelog-master.xml must be updated to include this newly created file.
- Each changeSet tag is uniquely identified by the combination of id, author, and changelog filename.
- changeset id should be in a following format "ai-agent-registry-[Release version]-[Story]-[sequenceId]"  
  Ex: `ai-agent-registry-2023.07.08-CJ-238-1`
- Every changelog file should have tagDatabase entry. It allows to rollback changes to a specific point.
  Ex: ``
  <changeSet author="sumanps" id="ai-agent-registry-2023.06.10-1t" context="PRE">
  <tagDatabase tag="ai-agent-registry-2023.07.08"/>
  </changeSet>
  ``

**References:**

1) https://boomii.atlassian.net/wiki/spaces/BR/pages/650838117/Managing+Database+Changes+with+Liquibase
2) https://www.liquibase.org/get-started/best-practices

# Local Setup

1. Download [Docker](https://www.docker.com/)

1. Run the Local PostgreSQL Server.
   To start the PostgreSQL server for the AI agent registry, use the following Docker command:

```bash
docker run --name ai-agent-registry -p 5457:5432 -e POSTGRES_USER=admin -e POSTGRES_PASSWORD=pw -d postgres
```

4. Execute ([./DBInit Runner.run.xml](./DBInit Runner.run.xml)) to create the custom database. Select it from the run
   configuration menu in IntelliJ (where you find the App Runner).

1. Execute ([./DBUpdate Runner.run.xml](./DBUpdate Runner.run.xml)) to create the necessary tables. Select it from the
   run configuration menu in IntelliJ (where you find the App Runner).
   Note: The tables will be created in the public schema

# Database Initialization and Update

## Non-Local Configuration

We maintain two distinct Spring properties files: one for database initialization
([application-db-init.yml](src/main/resources/application-db-init.yml)) and another for database updates
([application-db-update.yml](src/main/resources/application-db-update.yml)).

The database initialization profile utilizes the default PostgreSQL schema and relies on the
([changelog-db-init.xml](src/main/resources/liquibase.changelog/changelog-db-init.xml)) Liquibase changelog file to
establish the custom database. In contrast, the database update profile employs the custom database created during the
initialization step alongside the
([changelog-master.xml](src/main/resources/liquibase.changelog/changelog-master.xml)) Liquibase changelog file.

In our [./kubectl/deployment.yml](./kubectl/deployment.yml) file, we have defined two separate initialization
containers—one for database initialization and another for database updates. The database initialization container
specifies the Spring profile "db-init," while the database update container specifies the Spring profile "db-update."

```yaml
        env:
          - name: SPRING_PROFILES_ACTIVE
            value: "db-init"
```

```yaml
        env:
          - name: SPRING_PROFILES_ACTIVE
            value: "db-update"
```

## Local Configuration

For local development, we utilize two separate App Run configurations: one for database initialization
([./DBInit Runner.run.xml](./DBInit Runner.run.xml)) and another for database updates
([./DBUpdate Runner.run.xml](./DBUpdate Runner.run.xml)).

## Local - DB Backup and Restore

### Backup

1. Local machine `docker exec -it ai-agent-registry /bin/bash`
   in the container `pg_dump -U admin ai_agent_registry_service > /tmp/outfile`
1. Local machine `docker cp ai-agent-registry:outfile /tmp`
1. Drop the tables or equivalent
1. Run `DBInit Runner.run.xml` to initialize db (if you also dropped the database)
1. Run `DBUpdate Runner.run.xml`

### Restore

1. Local machine: `docker cp /tmp/outfile ai-agent-registry:/tmp`
1. Local machine: `docker exec -it ai-agent-registry /bin/bash`
1. In the container: `psql ai_agent_registry_service < /tmp/outfile`

## We probably need a section for squash commit here 

## Command to do iquibase rollback
mvn liquibase:rollback -Dliquibase.rollbackTag=ai-agent-registry-2025.01.13-CJ-3698
