inputSet:
  name: <PERSON>uildDeployToQA
  tags: {}
  identifier: BuildDeployToQA
  orgIdentifier: boomi
  projectIdentifier: boomi_cj
  pipeline:
    identifier: aiagentregistryservice
    stages:
      - stage:
          identifier: Build_and_Push
          template:
            templateInputs:
              type: CI
              variables:
                - name: MAVEN_COMMAND
                  type: String
                  default: verify
                  value: deploy
      - stage:
          identifier: sandbox_deploy
          type: Deployment
          spec:
            service:
              serviceInputs:
                serviceDefinition:
                  type: Kubernetes
                  spec:
                    artifacts:
                      primary:
                        primaryArtifactRef: Primary
                        sources: <+input>
          variables:
            - name: artifact_name
              type: String
              default: <+input>
              value: <+pipeline.stages.Build_and_Push.spec.execution.steps.Run_Maven_Verify.output.outputVariables.ARTIFACT_VERSION>
            - name: BRANCH_NAME
              type: String
              default: main
              value: main
            - name: run_stage
              type: String
              default: "true"
              value: "true"
      - stage:
          identifier: update_graphqlschema
          template:
            templateInputs:
              type: CI
              variables:
                - name: aws_bucket
                  type: String
                  value: boomi-graphql-schemas-us-east-1
                - name: bucket_path
                  type: String
                  value: CJ/ai-agent-registry
                - name: s3_source_path
                  type: String
                  value: schema.graphql
                - name: aws_region
                  type: String
                  value: us-east-1
                - name: service_name
                  type: String
                  value: ai-agent-registry-service
                - name: version
                  type: String
                  value: <+input>
                - name: team_name
                  type: String
                  value: aiagentregistry
                - name: run_stage
                  type: String
                  default: "true"
                  value: "true"
      - stage:
          identifier: Restart_Graphql_United
          type: Custom
          variables:
            - name: jenkins_env
              type: String
              value: PipelineJenkins
      - stage:
          identifier: qa_deploy
          type: Deployment
          variables:
            - name: artifact_name
              type: String
              default: <+input>
              value: <+pipeline.stages.Build_and_Push.spec.execution.steps.Run_Maven_Verify.output.outputVariables.ARTIFACT_VERSION>
            - name: BRANCH_NAME
              type: String
              default: main
              value: main
            - name: run_stage
              type: String
              default: "true"
              value: "true"
      - stage:
          identifier: update_graphqlschemaqa
          template:
            templateInputs:
              type: CI
              variables:
                - name: aws_bucket
                  type: String
                  value: boomi-graphql-schemas-us-east-1
                - name: bucket_path
                  type: String
                  value: QA/ai-agent-registry
                - name: s3_source_path
                  type: String
                  value: schema.graphql
                - name: aws_region
                  type: String
                  value: us-east-1
                - name: service_name
                  type: String
                  value: ai-agent-registry-service
                - name: version
                  type: String
                  value: <+input>
                - name: team_name
                  type: String
                  value: aiagentregistry
                - name: run_stage
                  type: String
                  default: "true"
                  value: "true"
      - stage:
          identifier: Restart_Graphql_United_qa
          type: Custom
          variables:
            - name: jenkins_env
              type: String
              value: PipelineJenkins
      - stage:
          identifier: Approve_for_ProdClone4
          type: Approval
          variables:
            - name: run_stage
              type: String
              value: "false"
      - stage:
          identifier: ProdClone4_Deploy
          type: Deployment
          spec:
            service:
              serviceInputs:
                serviceDefinition:
                  type: Kubernetes
                  spec:
                    artifacts:
                      primary:
                        primaryArtifactRef: <+input>
                        sources: <+input>
          variables:
            - name: artifact_name
              type: String
              value: <+input>
            - name: BRANCH_NAME
              type: String
              value: <+input>
            - name: run_stage
              type: String
              value: "false"
      - stage:
          identifier: update_graphqlschemaprodclone4
          template:
            templateInputs:
              type: CI
              variables:
                - name: aws_bucket
                  type: String
                  value: <+input>
                - name: bucket_path
                  type: String
                  value: <+input>
                - name: s3_source_path
                  type: String
                  value: <+input>
                - name: aws_region
                  type: String
                  value: <+input>
                - name: service_name
                  type: String
                  value: <+input>
                - name: version
                  type: String
                  value: <+input>
                - name: team_name
                  type: String
                  value: <+input>
                - name: run_stage
                  type: String
                  default: "true"
                  value: "false"
      - stage:
          identifier: Restart_Graphql_United_Prodclone4
          type: Custom
          variables:
            - name: jenkins_env
              type: String
              value: <+input>
      - stage:
          identifier: Approve_for_Prod
          type: Approval
          variables:
            - name: run_stage
              type: String
              value: "false"
      - stage:
          identifier: Prod_Deploy
          type: Deployment
          spec:
            service:
              serviceInputs:
                serviceDefinition:
                  type: Kubernetes
                  spec:
                    artifacts:
                      primary:
                        primaryArtifactRef: <+input>
                        sources: <+input>
          variables:
            - name: artifact_name
              type: String
              value: <+input>
            - name: BRANCH_NAME
              type: String
              value: <+input>
            - name: run_stage
              type: String
              value: "false"
      - stage:
          identifier: update_graphqlschemaprod
          template:
            templateInputs:
              type: CI
              variables:
                - name: aws_bucket
                  type: String
                  value: <+input>
                - name: bucket_path
                  type: String
                  value: <+input>
                - name: s3_source_path
                  type: String
                  value: <+input>
                - name: aws_region
                  type: String
                  value: <+input>
                - name: service_name
                  type: String
                  value: <+input>
                - name: version
                  type: String
                  value: <+input>
                - name: team_name
                  type: String
                  value: <+input>
                - name: run_stage
                  type: String
                  default: "true"
                  value: "false"
      - stage:
          identifier: Restart_Graphql_United_prod
          type: Custom
          variables:
            - name: jenkins_env
              type: String
              value: <+input>
    properties:
      ci:
        codebase:
          build:
            type: branch
            spec:
              branch: main
    variables:
      - name: gu_environment
        type: String
        value: QA
      - name: graphql_schema_bucket_path
        type: String
        value: <+input>
