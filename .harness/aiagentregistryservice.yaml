pipeline:
  name: ai-agent-registry-service
  identifier: aiagentregistryservice
  projectIdentifier: boomi_cj
  orgIdentifier: boomi
  tags: {}
  stages:
    - stage:
        name: Build Queue
        identifier: Build_Queue
        description: ""
        type: FeatureFlag
        spec:
          execution:
            steps:
              - step:
                  type: Queue
                  name: Build Queue
                  identifier: Build_Queue
                  timeout: 2h
                  spec:
                    key: wait_for_aiagentregistryservice_pipeline_completion
                    scope: Pipeline
    - stage:
        name: Build and Push
        identifier: Build_and_Push
        template:
          templateRef: org.orgengptemplatemavenbuilds
          versionLabel: 4.0.0
          templateInputs:
            type: CI
            variables:
              - name: push_to_ecr
                type: String
                default: "true"
                value: "true"
              - name: aws_account_id
                type: String
                default: "************"
                value: "************"
              - name: aws_region
                type: String
                default: us-east-1
                value: us-east-1
              - name: java_vendor
                type: String
                default: oracle
                value: corretto
              - name: java_version
                type: Number
                default: 8
                value: 17
              - name: ecr_repo_name
                type: String
                default: manage-services-ecr-boomi
                value: boomi/ai-agent-registry-service
              - name: ecr_image_addl_tag
                type: String
                default: latest
                value: latest
              - name: service_name
                type: String
                value: ai-agent-registry-service
              - name: MAVEN_COMMAND
                type: String
                default: verify
                value: <+input>.allowedValues(verify,deploy,release)
              - name: aws_connector
                type: String
                default: account.cross_account_boomipipeline_cipipelinedelegate
                value: account.cross_account_boomipipeline_cipipelinedelegate
              - name: s3_bucket_name
                type: String
                value: na
              - name: build_source
                type: String
                value: na
              - name: build_target
                type: String
                value: na
              - name: push_to_s3
                type: String
                default: "false"
                value: "false"
              - name: PLUGIN_STRIP_PREFIX
                type: String
                default: build/
                value: build
              - name: push_to_jfrog
                type: String
                default: "false"
                value: "false"
              - name: jfrog_repo
                type: String
                value: na
              - name: mutation_test
                type: String
                value: "false"
              - name: SLACK_WEBHOOK
                type: String
                default: *********************************************************************************
                value: *********************************************************************************
              - name: enable_mvn_mtt
                type: String
                value: "false"
              - name: verify_memory_limit
                type: String
                value: 8G
    - stage:
        name: sandbox deploy
        identifier: sandbox_deploy
        description: ""
        type: Deployment
        spec:
          deploymentType: Kubernetes
          service:
            serviceRef: org.customerjourneyaiagentregistryservice
            serviceInputs:
              serviceDefinition:
                type: Kubernetes
                spec:
                  artifacts:
                    primary:
                      primaryArtifactRef: <+input>
                      sources: <+input>
          environment:
            environmentRef: org.Sandbox
            deployToAll: false
            infrastructureDefinitions:
              - identifier: infraaiagentregistryservicesandbox
          execution:
            steps:
              - step:
                  type: K8sApply
                  name: Create Namespace
                  identifier: Create_Namespace
                  spec:
                    filePaths:
                      - namespace.yaml
                    skipDryRun: false
                    skipSteadyStateCheck: false
                    skipRendering: false
                    overrides: []
                  timeout: 10m
              - step:
                  name: Rollout Deployment
                  identifier: rolloutDeployment
                  type: K8sRollingDeploy
                  timeout: 20m
                  spec:
                    skipDryRun: false
                    pruningEnabled: false
            rollbackSteps:
              - step:
                  name: Rollback Rollout Deployment
                  identifier: rollbackRolloutDeployment
                  type: K8sRollingRollback
                  timeout: 10m
                  spec:
                    pruningEnabled: false
        tags: {}
        variables:
          - name: artifact_name
            type: String
            default: ""
            description: Use the Docker Tag you wish to Deploy
            value: <+input>
          - name: BRANCH_NAME
            type: String
            default: main
            description: Repo Branch where deployment manifest to pull from
            value: <+input>
          - name: run_stage
            type: String
            default: "true"
            description: ""
            value: <+input>.default(true).allowedValues(true,false)
        when:
          pipelineStatus: Success
          condition: <+stage.variables.run_stage> == "true"
        failureStrategies:
          - onFailure:
              errors:
                - AllErrors
              action:
                type: StageRollback
        delegateSelectors:
          - ai-agent-registry-service-sandbox
    - stage:
        name: update graphql-schema
        identifier: update_graphqlschema
        template:
          templateRef: org.orgengptemplateschemaci
          versionLabel: 2.1.0
          templateInputs:
            type: CI
            variables:
              - name: aws_bucket
                type: String
                value: <+input>
              - name: bucket_path
                type: String
                value: <+input>
              - name: s3_source_path
                type: String
                value: <+input>
              - name: aws_region
                type: String
                value: <+input>
              - name: service_name
                type: String
                value: <+input>
              - name: version
                type: String
                value: <+input>
              - name: team_name
                type: String
                value: <+input>
              - name: run_stage
                type: String
                default: "true"
                value: <+input>.default(true).allowedValues(true,false)
    - stage:
        name: Restart Graphql United
        identifier: Restart_Graphql_United
        description: ""
        type: Custom
        spec:
          execution:
            steps:
              - step:
                  name: Restart Graphql United
                  identifier: Restart_Graphql_United
                  template:
                    templateRef: GraphQLUnited_Restart
                    versionLabel: 0.0.1
        tags: {}
        when:
          pipelineStatus: Success
          condition: not empty(<+pipeline.variables.gu_environment>) and not empty(<+pipeline.variables.graphql_schema_bucket_path>)
        variables:
          - name: job_name
            type: String
            description: ""
            required: false
            value: <+"Apim-" + <+pipeline.variables.gu_environment> + "/Apim-" + <+pipeline.variables.gu_environment> + "-Restart-GraphQL-United">
          - name: jenkins_env
            type: String
            description: ""
            required: false
            value: <+input>
    - stage:
        name: qa deploy
        identifier: qa_deploy
        description: ""
        type: Deployment
        spec:
          deploymentType: Kubernetes
          service:
            serviceRef: org.customerjourneyaiagentregistryservice
            serviceInputs:
              serviceDefinition:
                type: Kubernetes
                spec:
                  artifacts:
                    primary:
                      primaryArtifactRef: Primary
          environment:
            environmentRef: org.QA
            deployToAll: false
            infrastructureDefinitions:
              - identifier: infraaiagentregistryserviceqa
          execution:
            steps:
              - step:
                  type: K8sApply
                  name: Create Namespace
                  identifier: Create_Namespace
                  spec:
                    filePaths:
                      - namespace.yaml
                    skipDryRun: false
                    skipSteadyStateCheck: false
                    skipRendering: false
                    overrides: []
                  timeout: 10m
              - step:
                  name: Rollout Deployment
                  identifier: rolloutDeployment
                  type: K8sRollingDeploy
                  timeout: 10m
                  spec:
                    skipDryRun: false
                    pruningEnabled: false
            rollbackSteps:
              - step:
                  name: Rollback Rollout Deployment
                  identifier: rollbackRolloutDeployment
                  type: K8sRollingRollback
                  timeout: 10m
                  spec:
                    pruningEnabled: false
        tags: {}
        variables:
          - name: artifact_name
            type: String
            default: ""
            description: Use the Docker Tag you wish to Deploy
            value: <+input>
          - name: BRANCH_NAME
            type: String
            default: main
            description: Repo Branch where deployment manifest to pull from
            value: <+input>
          - name: run_stage
            type: String
            default: "true"
            description: ""
            value: <+input>.default(true).allowedValues(true,false)
        when:
          pipelineStatus: Success
          condition: <+stage.variables.run_stage> == "true"
        failureStrategies:
          - onFailure:
              errors:
                - AllErrors
              action:
                type: StageRollback
        delegateSelectors:
          - ai-agent-registry-service-qa
    - stage:
        name: update graphql-schema-qa
        identifier: update_graphqlschemaqa
        template:
          templateRef: org.orgengptemplateschemaci
          versionLabel: 2.1.0
          templateInputs:
            type: CI
            variables:
              - name: aws_bucket
                type: String
                value: <+input>
              - name: bucket_path
                type: String
                value: <+input>
              - name: s3_source_path
                type: String
                value: <+input>
              - name: aws_region
                type: String
                value: <+input>
              - name: service_name
                type: String
                value: <+input>
              - name: version
                type: String
                value: <+input>
              - name: team_name
                type: String
                value: <+input>
              - name: run_stage
                type: String
                default: "true"
                value: <+input>.default(true).allowedValues(true,false)
    - stage:
        name: Restart Graphql United qa
        identifier: Restart_Graphql_United_qa
        description: ""
        type: Custom
        spec:
          execution:
            steps:
              - step:
                  name: Restart Graphql United
                  identifier: Restart_Graphql_United_qa
                  template:
                    templateRef: GraphQLUnited_Restart
                    versionLabel: 0.0.1
        tags: {}
        when:
          pipelineStatus: Success
          condition: not empty(<+pipeline.variables.gu_environment>) and not empty(<+pipeline.variables.graphql_schema_bucket_path>)
        variables:
          - name: job_name
            type: String
            description: ""
            required: false
            value: <+"Apim-" + <+pipeline.variables.gu_environment> + "/Apim-" + <+pipeline.variables.gu_environment> + "-Restart-GraphQL-United">
          - name: jenkins_env
            type: String
            description: ""
            required: false
            value: <+input>
    - stage:
        name: Approve for ProdClone4
        identifier: Approve_for_ProdClone4
        description: ""
        type: Approval
        spec:
          execution:
            steps:
              - step:
                  name: Approve for ProdClone4 Deployment
                  identifier: Approve_for_ProdClone4_Deployment
                  type: HarnessApproval
                  timeout: 1d
                  spec:
                    approvalMessage: |-
                      Please review the following information
                      and approve the pipeline progression
                    includePipelineExecutionHistory: true
                    approvers:
                      minimumCount: 1
                      disallowPipelineExecutor: false
                      userGroups:
                        - Pipeline_Approvers
                    isAutoRejectEnabled: false
                    approverInputs: []
        tags: {}
        variables:
          - name: run_stage
            type: String
            description: ""
            required: false
            value: <+input>.default(true).allowedValues(true,false)
    - stage:
        name: ProdClone4 Deploy
        identifier: ProdClone4_Deploy
        description: ""
        type: Deployment
        spec:
          deploymentType: Kubernetes
          service:
            serviceRef: org.customerjourneyaiagentregistryservice
            serviceInputs:
              serviceDefinition:
                type: Kubernetes
                spec:
                  artifacts:
                    primary:
                      primaryArtifactRef: <+input>
                      sources: <+input>
          execution:
            steps:
              - step:
                  type: K8sApply
                  name: Create Namespace
                  identifier: Create_Namespace
                  spec:
                    filePaths:
                      - namespace.yaml
                    skipDryRun: false
                    skipSteadyStateCheck: false
                    skipRendering: false
                    overrides: []
                  timeout: 10m
              - step:
                  type: K8sRollingDeploy
                  name: Rollout Deployment
                  identifier: Rollout_Deployment
                  spec:
                    skipDryRun: false
                    pruningEnabled: false
                  timeout: 10m
            rollbackSteps: []
          environment:
            environmentRef: org.ProdClone4
            deployToAll: false
            infrastructureDefinitions:
              - identifier: aiagentregistryserviceinfraprodclone4
        tags: {}
        failureStrategies:
          - onFailure:
              errors:
                - AllErrors
              action:
                type: StageRollback
        variables:
          - name: artifact_name
            type: String
            description: ""
            required: false
            value: <+input>
          - name: BRANCH_NAME
            type: String
            description: ""
            required: false
            value: <+input>
          - name: run_stage
            type: String
            description: ""
            required: false
            value: <+input>.default(true).allowedValues(true,false)
          - name: new_relic_license_key
            type: Secret
            description: ""
            required: false
            value: org.customerjourney-ai-agent-registry-service-newrelic-license-prodclone4
        when:
          pipelineStatus: Success
          condition: <+stage.variables.run_stage> == "true" && <+pipeline.stages.Approve_for_ProdClone4.spec.execution.steps.Approve_for_ProdClone4_Deployment.output.approvalActivities[0].action>== "APPROVE"
        delegateSelectors:
          - ai-agent-registry-service-prodclone4-delegate
    - stage:
        name: update graphql-schema-prodclone4
        identifier: update_graphqlschemaprodclone4
        template:
          templateRef: org.orgengptemplateschemaci
          versionLabel: 2.1.0
          templateInputs:
            type: CI
            variables:
              - name: aws_bucket
                type: String
                value: <+input>
              - name: bucket_path
                type: String
                value: <+input>
              - name: s3_source_path
                type: String
                value: <+input>
              - name: aws_region
                type: String
                value: <+input>
              - name: service_name
                type: String
                value: <+input>
              - name: version
                type: String
                value: <+input>
              - name: team_name
                type: String
                value: <+input>
              - name: run_stage
                type: String
                default: "true"
                value: <+input>.default(true).allowedValues(true,false)
    - stage:
        name: Restart Graphql United Prodclone4
        identifier: Restart_Graphql_United_Prodclone4
        description: ""
        type: Custom
        spec:
          execution:
            steps:
              - step:
                  name: Restart Graphql United
                  identifier: Restart_Graphql_United
                  template:
                    templateRef: GraphQLUnited_Restart
                    versionLabel: 0.0.1
        tags: {}
        variables:
          - name: job_name
            type: String
            description: ""
            required: false
            value: "<+\"Apim-\" + <+pipeline.variables.gu_environment> + \"/Apim-\" + <+pipeline.variables.gu_environment> + \"-Restart-GraphQL-United\"> "
          - name: jenkins_env
            type: String
            description: ""
            required: false
            value: <+input>
        when:
          pipelineStatus: Success
          condition: not empty(<+pipeline.variables.gu_environment>) and not empty(<+pipeline.variables.graphql_schema_bucket_path>)
    - stage:
        name: Approve for Prod
        identifier: Approve_for_Prod
        description: ""
        type: Approval
        spec:
          execution:
            steps:
              - step:
                  name: Approve for Prod Deployment
                  identifier: Approve_for_Prod_Deployment
                  type: HarnessApproval
                  timeout: 1d
                  spec:
                    approvalMessage: |-
                      Please review the following information
                      and approve the pipeline progression
                    includePipelineExecutionHistory: true
                    approvers:
                      minimumCount: 1
                      disallowPipelineExecutor: false
                      userGroups:
                        - Pipeline_Approvers
                    isAutoRejectEnabled: false
                    approverInputs: []
        tags: {}
        variables:
          - name: run_stage
            type: String
            description: ""
            required: false
            value: <+input>.default(true).allowedValues(true,false)
    - stage:
        name: Prod Deploy
        identifier: Prod_Deploy
        description: ""
        type: Deployment
        spec:
          deploymentType: Kubernetes
          service:
            serviceRef: org.customerjourneyaiagentregistryservice
            serviceInputs:
              serviceDefinition:
                type: Kubernetes
                spec:
                  artifacts:
                    primary:
                      primaryArtifactRef: <+input>
                      sources: <+input>
          execution:
            steps:
              - step:
                  type: K8sApply
                  name: Create Namespace
                  identifier: Create_Namespace
                  spec:
                    filePaths:
                      - namespace.yaml
                    skipDryRun: false
                    skipSteadyStateCheck: false
                    skipRendering: false
                    overrides: []
                  timeout: 10m
              - step:
                  type: K8sRollingDeploy
                  name: Rollout Deployment
                  identifier: Rollout_Deployment
                  spec:
                    skipDryRun: false
                    pruningEnabled: false
                  timeout: 10m
            rollbackSteps: []
          environment:
            environmentRef: org.Production
            deployToAll: false
            infrastructureDefinitions:
              - identifier: infraaiagentregistryserviceprod
        tags: {}
        failureStrategies:
          - onFailure:
              errors:
                - AllErrors
              action:
                type: StageRollback
        variables:
          - name: artifact_name
            type: String
            description: ""
            required: false
            value: <+input>
          - name: BRANCH_NAME
            type: String
            description: ""
            required: false
            value: <+input>
          - name: run_stage
            type: String
            description: ""
            required: false
            value: <+input>.default(true).allowedValues(true,false)
          - name: new_relic_license_key
            type: Secret
            description: ""
            required: false
            value: account.jenkinsprodapitoken
        delegateSelectors:
          - ai-agent-registry-service-prod
        when:
          pipelineStatus: Success
          condition: <+stage.variables.run_stage> == "true" && <+pipeline.stages.Approve_for_Prod.spec.execution.steps.Approve_for_Prod_Deployment.output.approvalActivities[0].action>== "APPROVE"
    - stage:
        name: update graphql-schema-prod
        identifier: update_graphqlschemaprod
        template:
          templateRef: org.orgengptemplateschemaci
          versionLabel: 2.1.0
          templateInputs:
            type: CI
            variables:
              - name: aws_bucket
                type: String
                value: <+input>
              - name: bucket_path
                type: String
                value: <+input>
              - name: s3_source_path
                type: String
                value: <+input>
              - name: aws_region
                type: String
                value: <+input>
              - name: service_name
                type: String
                value: <+input>
              - name: version
                type: String
                value: <+input>
              - name: team_name
                type: String
                value: <+input>
              - name: run_stage
                type: String
                default: "true"
                value: <+input>.default(true).allowedValues(true,false)
    - stage:
        name: Restart Graphql United prod
        identifier: Restart_Graphql_United_prod
        description: ""
        type: Custom
        spec:
          execution:
            steps:
              - step:
                  name: Restart Graphql United
                  identifier: Restart_Graphql_United
                  template:
                    templateRef: GraphQLUnited_Restart
                    versionLabel: 0.0.1
        tags: {}
        variables:
          - name: job_name
            type: String
            description: ""
            required: false
            value: "<+\"Apim-\" + <+pipeline.variables.gu_environment> + \"/Apim-\" + <+pipeline.variables.gu_environment> + \"-Restart-GraphQL-United\"> "
          - name: jenkins_env
            type: String
            description: ""
            required: false
            value: <+input>
        when:
          pipelineStatus: Success
          condition: not empty(<+pipeline.variables.gu_environment>) and not empty(<+pipeline.variables.graphql_schema_bucket_path>)
  allowStageExecutions: true
  notificationRules:
    - name: Send Slack notifications
      identifier: send_slack_message
      pipelineEvents:
        - type: PipelineSuccess
        - type: PipelineFailed
      notificationMethod:
        type: Slack
        spec:
          userGroups: []
          webhookUrl: *********************************************************************************
      enabled: true
  properties:
    ci:
      codebase:
        connectorRef: account.bitbucketcloudhttp
        repoName: ai-agent-registry-service
        build: <+input>
        sparseCheckout: []
  variables:
    - name: gu_environment
      type: String
      description: The Graphql United Environment key (such as ES)
      required: false
      value: <+input>
    - name: graphql_schema_bucket_path
      type: String
      description: ""
      required: false
      value: <+input>
