apiVersion: v1
kind: Service
metadata:
  name: eks-ai-agent-registry
  namespace: ai-agent-registry
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-attributes: load_balancing.cross_zone.enabled=true
    service.beta.kubernetes.io/aws-load-balancer-type: external
    service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
    service.beta.kubernetes.io/aws-load-balancer-scheme: internal
    service.beta.kubernetes.io/aws-load-balancer-additional-resource-tags: app=eks-ai-agent-registry
    service.beta.kubernetes.io/aws-load-balancer-private-ipv4-addresses: {{.Values.config.ai_agent_registry_private_ipv4_addresses}}
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: {{.Values.config.ai_agent_registry_acm}}
    service.beta.kubernetes.io/aws-load-balancer-ssl-negotiation-policy: ELBSecurityPolicy-TLS13-1-2-2021-06 #ssl policy
  labels:
    app: eks-ai-agent-registry
spec:
  type: LoadBalancer
  selector:
    app: eks-ai-agent-registry
  ports:
    - protocol: TCP
      port: 443
      targetPort: 3037

