{{- if (default false .Values.config.ai_agent_registry_service_enable_hpa) }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: eks-ai-agent-registry-hpa
  namespace: ai-agent-registry
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: eks-ai-agent-registry-deployment
  minReplicas: {{.Values.ai_agent_registry_service_min_replicas}}
  maxReplicas: {{.Values.ai_agent_registry_service_max_replicas}}
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 0
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 900
      policies:
      - type: Percent
        value: 25 
        periodSeconds: 60
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 50 
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70
{{- end }}