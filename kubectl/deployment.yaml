apiVersion: apps/v1
kind: Deployment
metadata:
  name: eks-ai-agent-registry-deployment
  namespace: ai-agent-registry
  labels:
    app: eks-ai-agent-registry
spec:
  replicas: "{{.Values.ai_agent_registry_service_min_replicas}}"
  selector:
    matchLabels:
      app: eks-ai-agent-registry
  template:
    metadata:
      labels:
        app: eks-ai-agent-registry
        environment: "{{.Values.config.ai_agent_registry_environment}}"
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: kubernetes.io/arch
                    operator: In
                    values:
                      - amd64
                      - arm64
      containers:
        - name: ai-agent-registry
          image: "{{.Values.image}}"
          resources:
            requests:
              memory: "4G"
              cpu: "2"
            limits:
              memory: "4G"
              cpu: "2"
          ports:
            - name: http
              containerPort: 3037
          imagePullPolicy: "Always"
          volumeMounts:
            - name: config-volume
              mountPath: /config
          envFrom:
            - configMapRef:
                name: environment-vars-ai-agent-registry
          securityContext:
            allowPrivilegeEscalation: false
            runAsNonRoot: true
            runAsUser: 1000
            capabilities:
              drop:
                - ALL
      initContainers:
        - name: db-init
          image: "{{.Values.image}}"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: environment-vars-ai-agent-registry
          env:
            - name: LOADER_MAIN
              value: com.boomi.services.liquibase.LiquibaseRunner
            - name: spring.profiles.active
              value: db-init,dev
          securityContext:
            allowPrivilegeEscalation: false
            runAsNonRoot: true
            runAsUser: 1000
            capabilities:
              drop:
                - ALL
        - name: db-update
          image: "{{.Values.image}}"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: environment-vars-ai-agent-registry
          env:
            - name: LOADER_MAIN
              value: com.boomi.services.liquibase.LiquibaseRunner
            - name: spring.profiles.active
              value: db-update
          securityContext:
            allowPrivilegeEscalation: false
            runAsNonRoot: true
            runAsUser: 1000
            capabilities:
              drop:
                - ALL
      serviceAccountName: ai-agent-registry-sa
      volumes:
        - name: config-volume
          configMap:
            name: environment-vars-ai-agent-registry
      nodeSelector:
        kubernetes.io/os: linux
