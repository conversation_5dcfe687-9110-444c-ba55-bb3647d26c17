apiVersion: v1
kind: ConfigMap
metadata:
  name: environment-vars-ai-agent-registry
  namespace: ai-agent-registry
  labels:
    app: eks-ai-agent-registry
data:
  com.boomi.aiagentregistry.environment: {{.Values.config.ai_agent_registry_environment}}
  com.boomi.aiagentregistry.stackName: {{.Values.config.ai_agent_registry_stackName}}
  com.boomi.aiagentregistry.stackType: "ai-agent-registry"
  com.boomi.aiagentregistry.boomiContact: {{.Values.config.ai_agent_registry_boomi_contact}}
  com.boomi.aiagentregistry.customer: "Boomi"
  com.boomi.aiagentregistry.serviceName: "ai-agent-registry"
  com.boomi.aiagentregistry.secretManagerRole: "SecretStore"
  com.boomi.aiagentregistry.aws.region: {{.Values.config.ai_agent_registry_aws_region}}
  com.boomi.aiagentregistry.aws.secondary.region: {{.Values.config.ai_agent_registry_aws_secondary_region}}
  boomi.services.aiagentregistry.db.url: {{.Values.config.ai_agent_registry_spring_datasource_url}}
  boomi.services.aiagentregistry.db.password: "#{@parameterStore.getJsonParameter('/aws/reference/secretsmanager/{{.Values.config.ai_agent_registry_stackName}}-{{ lower .Values.config.ai_agent_registry_environment}}-database-credientials', 'password')}"
  boomi.services.aiagentregistry.db.username: {{.Values.config.ai_agent_registry_spring_datasource_username}}
  boomi.services.aiagentregistry.db.default.url: {{.Values.config.ai_agent_registry_spring_datasource_default_url}}
  boomi.services.jwks.url: {{.Values.config.ai_agent_registry_jwks_url}}
  boomi.services.jwks.issuer: {{.Values.config.ai_agent_registry_jwks_issuer}}
  boomi.services.aiagentregistry.platformBaseUrl: {{.Values.config.ai_agent_registry_platformBaseUrl}}
  boomi.services.monitor.user: {{.Values.config.ai_agent_registry_monitor_user}}
  boomi.services.monitor.pass: "#{@parameterStore.getJsonParameter('/aws/reference/secretsmanager/{{.Values.config.ai_agent_registry_stackName}}-{{ lower .Values.config.ai_agent_registry_environment}}-app-credentials', 'monitor_password')}"
  boomi.services.aiagentregistry.write.non.summary.logs: "#{@parameterStore.getJsonParameter('/aws/reference/secretsmanager/{{.Values.config.ai_agent_registry_stackName}}-{{ lower .Values.config.ai_agent_registry_environment}}-app-toggles', 'writeNonSummarySyncLogs')}"
  api.boomi.platform.url: {{.Values.config.ai_agent_registry_apiPlatformUrl}}
  aws.secretsmanager.local.enabled: false
  aws.s3.local.enabled: false
  boomi.services.aiagentregistry.s3.bucket: {{.Values.config.s3_bucket}}-{{.Values.config.ai_agent_registry_environment | lower }}-{{.Values.config.ai_agent_registry_aws_region }}
  management.metrics.export.newrelic.api-key: {{.Values.config.ai_agent_registry_newrelic_api_key}}
  management.metrics.export.newrelic.app: "ai-agent-registry-{{.Values.config.ai_agent_registry_environment}}"
  management.metrics.export.newrelic.stack: {{.Values.config.ai_agent_registry_environment}}
  management.metrics.export.newrelic.frequency: "5"
  NEW_RELIC_DISTRIBUTED_TRACING_ENABLED: true
  NEW_RELIC_APP_NAME: "ai-agent-registry-{{.Values.config.ai_agent_registry_environment}}"
  NEW_RELIC_LICENSE_KEY: {{.Values.config.ai_agent_registry_newrelic_api_key}}
  JVM_ARGS: "-XX:+UseContainerSupport -XX:InitialRAMPercentage=40 -XX:MinRAMPercentage=25 -XX:MaxRAMPercentage=80 -XX:+UseG1GC -XX:+UseStringDeduplication -XX:+ExitOnOutOfMemoryError -javaagent:newrelic-agent.jar"
  boomi.services.aiagentregistry.pattern.externalLink.AGENT.AWS_BEDROCK: {{.Values.config.ai_agent_registry_externalLink_agent_aws_bedrock}}
  boomi.services.aiagentregistry.pattern.externalLink.AGENT.BOOMI: {{.Values.config.ai_agent_registry_externalLink_agent_boomi}}
  boomi.services.aiagentregistry.pattern.externalLink.VERSION.AWS_BEDROCK: {{.Values.config.ai_agent_registry_externalLink_version_aws_bedrock}}
  boomi.services.aiagentregistry.pattern.externalLink.VERSION.BOOMI: {{.Values.config.ai_agent_registry_externalLink_version_boomi}}
  boomi.services.aiagentregistry.pattern.externalLink.ALIAS.AWS_BEDROCK: {{.Values.config.ai_agent_registry_externalLink_alias_aws_bedrock}}
  boomi.services.aiagentregistry.garden.jwtUrl: {{.Values.config.ai_agent_registry_garden_jwtUrl}}
  boomi.services.aiagentregistry.garden.apiUrl: {{.Values.config.ai_agent_registry_garden_apiUrl}}
  boomi.services.aiagentregistry.use.bedrock.assume.role: {{.Values.config.ai_agent_registry_use_BedrockAssumeRole}}
  boomi.services.aiagentregistry.allow.one.external.account.with.one.idp.and.one.auth.type: {{.Values.config.ai_agent_registry_allow_one_external_account_with_one_idp_one_auth}}
  # The IAM role name created in the customer's AWS account to be assumed by the Boomi Agent Control Tower
  boomi.services.aiagentregistry.customer.aws.role.prefix: Boomi-ACT-bedrock-customer-role
  boomi.services.aiagentregistry.customer.aws.role.policy.prefix: Boomi-ACT-bedrock-access-policy
  boomi.services.aiagentregistry.customer.aws.bedrock.role.prefix: Boomi-ACT-bedrock-customer-role
  boomi.services.aiagentregistry.customer.aws.bedrock.role.policy.prefix: Boomi-ACT-bedrock-access-policy
  boomi.services.aiagentregistry.customer.aws.oam.role.prefix: Boomi-ACT-oam-customer-role
  boomi.services.aiagentregistry.customer.aws.oam.role.policy.prefix: Boomi-ACT-oam-access-policy
  # 1 hour (max allowed when using role chaining to assume another role)
  boomi.services.aiagentregistry.customer.aws.role.assume.session_duration_seconds: 3600
  boomi.services.aiagentregistry.monitoring.account.id: {{.Values.config.ai_agent_registry_monitoring_account_id}}
  boomi.services.timestream.roleArn: {{.Values.config.boomi_services_timestream_roleArn}}
  boomi.services.timestream.database_name: {{.Values.config.boomi_services_timestream_database_name}}
  boomi.services.timestream.region: "us-east-1"
  # Enable this temporarily for testing in QA
  boomi.services.aiagentregistry.service.account.role: {{.Values.config.ai_agent_service_account_role}}
  # ai_agent_registry_aws_metrics_stream_in_all_regions is relevant only if ai_agent_registry_use_real_oam_client is true
  boomi.services.aiagentregistry.aws.metrics.stream.in.all.regions: {{.Values.config.ai_agent_registry_aws_metrics_stream_in_all_regions}}
  boomi.services.aiagentregistry.use.real.oam.client: {{.Values.config.ai_agent_registry_use_real_oam_client}}
  boomi.services.aiagentregistry.service.assume.role.session.name: "#{@parameterStore.getJsonParameter('/aws/reference/secretsmanager/{{.Values.config.ai_agent_registry_stackName}}-{{ lower .Values.config.ai_agent_registry_environment}}-app-credentials', 'boomi_session_id')}"
  boomi.services.aiagentregistry.sync.queue.url: {{.Values.config.boomi_services_aiagentregistry_sync_queue_url}}
  boomi.services.aiagentregistry.sync.queue.region: {{.Values.config.boomi_services_aiagentregistry_sync_queue_region}}
  #ai_agent_registry email configuration for notification.
  boomi.services.aiagentregistry.mail.enabled: {{.Values.config.ai_agent_registry_mail_enabled}}
  boomi.services.aiagentregistry.mail.dl: {{.Values.config.ai_agent_registry_mail_dl}}
  boomi.services.aiagentregistry.scheduler.cron.expression: {{.Values.config.ai_agent_registry_scheduler_cron_expression}}
  boomi.services.aiagentregistry.mail.host: {{.Values.config.ai_agent_registry_mail_host}}
  boomi.services.aiagentregistry.mail.username: "#{@parameterStore.getJsonParameter('/aws/reference/secretsmanager/{{.Values.config.ai_agent_registry_stackName}}-{{ lower .Values.config.ai_agent_registry_environment}}-app-credentials', 'smtp_username')}"
  boomi.services.aiagentregistry.mail.password: "#{@parameterStore.getJsonParameter('/aws/reference/secretsmanager/{{.Values.config.ai_agent_registry_stackName}}-{{ lower .Values.config.ai_agent_registry_environment}}-app-credentials', 'smtp_password')}"
  boomi.services.aiagentregistry.mail.port: {{.Values.config.ai_agent_registry_mail_port}}
  boomi.services.aiagentsync.service.auto.account.role: {{.Values.config.ai_agent_sync_auto_service_account_role}}
  boomi.services.aiagentsync.service.manual.account.role: {{.Values.config.ai_agent_sync_manual_service_account_role}}

