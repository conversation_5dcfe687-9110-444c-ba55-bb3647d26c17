// Copyright (c) 2025 Boomi, Inc.
package com.boomi.aiagentregistry.util;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public class TestLogAppender extends AppenderBase<ILoggingEvent> {
    private final List<ILoggingEvent> logs = new ArrayList<>();

    @Override
    protected void append(ILoggingEvent eventObject) {
        logs.add(eventObject);
    }

    public void clear() {
        logs.clear();
    }
}
