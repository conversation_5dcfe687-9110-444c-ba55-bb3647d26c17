// Copyright (c) 2025 <PERSON><PERSON>, LP

// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.util;

import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.model.AssumeRoleRequest;
import software.amazon.awssdk.services.sts.model.AssumeRoleResponse;
import software.amazon.awssdk.services.sts.model.Credentials;

import org.mockito.ArgumentCaptor;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

import static org.mockito.Mockito.when;

public class StsClientTestUtil {

    public static Credentials setup(StsClient mockStsClient,
            ArgumentCaptor<AssumeRoleRequest> assumeRoleRequestArgumentCaptor) {
        // Setup mock responses
        Credentials mockCredentials = Credentials.builder()
                .accessKeyId("MOCK_ACCESS_KEY")
                .secretAccessKey("MOCK_SECRET_KEY")
                .sessionToken("MOCK_SESSION_TOKEN")
                .expiration(Instant.now().plus(1, ChronoUnit.HOURS))
                .build();

        AssumeRoleResponse mockResponse = AssumeRoleResponse.builder()
                .credentials(mockCredentials)
                .build();

        when(mockStsClient.assumeRole(assumeRoleRequestArgumentCaptor.capture()))
                .thenReturn(mockResponse);
        return mockCredentials;
    }
}


