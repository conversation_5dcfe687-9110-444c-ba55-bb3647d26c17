// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.util;

import lombok.extern.slf4j.Slf4j;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;

@Slf4j
public class SQLFileReaderUtil {

    private static final String SQL_FILE_BASE_PATH = "sql";

    public static String readSqlQuery(String fileName) {
        try {
            Resource resource = new ClassPathResource(SQL_FILE_BASE_PATH + "/" + fileName);
            return new String(FileCopyUtils.copyToByteArray(resource.getInputStream()));
        }catch (IOException e){
            log.error("Error reading sql file {}", e.getMessage());
        }
        return null;
    }

}
