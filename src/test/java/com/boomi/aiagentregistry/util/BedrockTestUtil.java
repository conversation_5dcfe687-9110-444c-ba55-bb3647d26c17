package com.boomi.aiagentregistry.util;

import com.boomi.aiagentregistry.service.auth.AwsCredentials;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class BedrockTestUtil {
    @Autowired
    private ObjectMapper _objectMapper;

    public static String getEmptyLlmResponse() {
        return """
                    {
                    "modelDetails": {}
                }
                """;
    }

    public AwsCredentials getAwsCredentials() {
        AwsCredentials credentials = new AwsCredentials();
        credentials.setAwsAccessKeyId("AKIAIOSFODNN7EXAMPLE");
        credentials.setAwsSecretAccessKey("example-key");
        credentials.setAwsRegion("us-west-2");
        credentials.setSessionToken("example-token");
        credentials.setExternalId("test-external-id");
        credentials.setExpirationEpochMilli(System.currentTimeMillis() + 3600000);
        return credentials;
    }

    public String getAwsCredentialsWithJustExternalId() throws JsonProcessingException {
        AwsCredentials credentials = new AwsCredentials();
        credentials.setExternalId(getAwsCredentials().getExternalId());
        return _objectMapper.writeValueAsString(credentials);
    }
}
