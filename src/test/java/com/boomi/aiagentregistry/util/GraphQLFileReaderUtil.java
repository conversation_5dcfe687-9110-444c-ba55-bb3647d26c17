// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.util;

import lombok.extern.slf4j.Slf4j;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;

@Slf4j
public class GraphQLFileReaderUtil {

    private static final String GRAPHQL_FILE_BASE_PATH = "graphql/";

    public static String readGraphQLQuery(String graphqlType, String fileName) {
        try {
            Resource resource = new ClassPathResource(GRAPHQL_FILE_BASE_PATH + graphqlType + "/" + fileName);
            return new String(FileCopyUtils.copyToByteArray(resource.getInputStream()));
        }catch (IOException e){
            log.error("Error reading graphql file {}", e.getMessage());
        }
        return null;
    }


}
