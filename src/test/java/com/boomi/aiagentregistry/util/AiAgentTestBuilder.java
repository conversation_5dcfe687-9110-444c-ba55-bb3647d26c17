package com.boomi.aiagentregistry.util;

import com.boomi.aiagentregistry.constants.TestConstants;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentAlias;
import com.boomi.aiagentregistry.entity.AiAgentGuardrail;
import com.boomi.aiagentregistry.entity.AiAgentGuardrailAssociation;
import com.boomi.aiagentregistry.entity.AiAgentLlm;
import com.boomi.aiagentregistry.entity.AiAgentLlmAssociation;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTask;
import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.entity.AiAgentToolResource;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.servlet.response.boomigarden.AgentSummaryList;
import com.boomi.graphql.server.schema.types.AiAgentOriginType;
import com.boomi.graphql.server.schema.types.AiAgentToolResourceType;
import com.boomi.graphql.server.schema.types.AiAgentToolType;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncData;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import lombok.Builder;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;
import java.util.Set;

import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_ID;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_LAST_UPDATED_ON;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_NAME;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_STATUS;

@Component
public class AiAgentTestBuilder {
    private final TestUtil _testUtil;

    @Builder
    @Data
    public static class AgentSetupParams {
        private String guid;
        private String externalId;
        private List<VersionSetupParams> versions;
        private boolean isDeleted;
        private AiAgentProviderAccount providerAccount;
        private String idpAccountId;
    }

    @Builder
    @Data
    public static class VersionSetupParams {
        private Integer uid;
        private String guid;
        private String name;
        private String description;
        private String versionString;
        private String agentStatus;
        private String instructions;
        private Instant updatedAt;
        private boolean createdInRegistry;
        private AiAgentOriginType updatedByOrigin;
        private boolean isDeleted;
        private AiAgentOriginType createdByOrigin;
        private String externalId;
        private AiAgentProviderAccount providerAccount;
        private String idpAccountId;
        private List<AliasSetupParams> aliases;
        private List<ToolSetupParams> tools;
        private LlmSetupParams llm;
        private GuardrailSetupParams guardrail;

    }

    @Builder
    @Data
    public static class AliasSetupParams {
        private String guid;
        private String externalId;
        private String name;
        private String description;
        private Instant updatedAt;
        private AiAgentProviderAccount providerAccount;
        private String idpAccountId;
        private boolean isDeleted;
        private AiRegistryEntitySyncData syncData;
        private String agentVersion;
    }

    @Builder
    @Data
    public static class ToolSetupParams {
        private String guid;
        private String name;
        private String description;
        private String externalId;
        private AiAgentProviderAccount providerAccount;
        private String idpAccountId;
        private Instant updatedAt;
        private String status;
        private Set<ToolResourceParams> resources;
        private AiAgentToolType type;
    }

    @Builder
    @Data
    public static class ToolResourceParams {
        private String guid;
        private String name;
        private String description;
        private String externalId;
        private String status;
        private Instant updatedAt;
        private AiAgentToolResourceType type;
        private String toolResourceJson;
    }

    @Builder
    @Data
    public static class TagParams {
        private String guid;
        private AiAgentProviderAccount providerAccount;
        private String idpAccountId;
        private String key;
        private String value;
    }

    @Builder
    @Data
    public static class TagAssociationParams {
        private String guid;
        private Integer tagUid;
        private Integer relatedEntityUid;
        private AiRegistryEntityType relatedEntityType;
    }

    @Builder
    @Data
    public static class GuardrailSetupParams {
        private Integer uid;
        private String guid;
        private String name;
        private String description;
        private String versionString;
        private Instant updatedAt;
        private AiAgentOriginType updatedByOrigin;
        private String externalId;
        private AiAgentProviderAccount providerAccount;
        private String idpAccountId;
    }

    @Builder
    @Data
    public static class LlmSetupParams {
        private String guid;
        private String name;
        private String description;
        private String externalId;
        private String versionString;
        private AiAgentProviderAccount providerAccount;
        private String idpAccountId;
    }


    public AiAgentTestBuilder(TestUtil testUtil) {
        _testUtil = testUtil;
    }

    public AiAgentGuardrail setupGuardrail(GuardrailSetupParams guardrailParams) {
        return _testUtil.createGuardrail(guardrailParams);
    }

    public AiAgent setupAgent(AgentSetupParams params) {
        AiAgent agent = _testUtil.createAiAgent(params);

        if (params.getVersions() != null) {
            params.getVersions().forEach(v -> setupVersion(agent, v));
        }


        return agent;
    }

    public AiAgentVersion setupVersion(AiAgent agent, VersionSetupParams params) {

        AiAgentVersion version = _testUtil.createAiAgentVersion(agent, params);

        if (params.getAliases() != null) {
            params.getAliases().forEach(a -> setupAlias(version, a));
        }
        if (params.getTools() != null) {
            params.getTools().forEach(t -> setupTool(version, t));
        }

        if (params.getLlm() != null) {
            setupLlmAssociation(version, params.getLlm());
        }
        if (params.getGuardrail() != null) {
            setupGuardrail(params.getGuardrail());
        }

        return version;
    }

    public AiAgentVersion setupGardenVersion(AiAgent agent, VersionSetupParams params) {

        AiAgentVersion version = _testUtil.createAiAgentVersion(agent, params);

        if (params.getGuardrail() != null) {
            setupGuardrail(params.getGuardrail());
        }

        return version;
    }

    public AiAgentAlias setupAlias(AiAgentVersion version, AliasSetupParams params) {

        return _testUtil.createVersionAlias(version, params);
    }

    public AiAgentTool setupTool(AiAgentVersion version, ToolSetupParams params) {
        AiAgentTool tool = _testUtil.saveAiAgentTool(params);

        if (params.getResources() != null) {
            params.getResources().forEach(r -> _testUtil.createAiAgentToolResource(tool, r));
        }
        //create a relationship between tool and version
        _testUtil.createAiAgentToolAssociation(tool, version);

        return tool;
    }

    public AiAgentToolResource setupToolResource(AiAgentTool tool, ToolResourceParams params) {
        return _testUtil.createAiAgentToolResource(tool, params);
    }

    public AiAgentLlmAssociation setupLlmAssociation(AiAgentVersion version, LlmSetupParams params) {
        AiAgentLlm llm = _testUtil.saveAiAgentLlm(params);
        //create a relationship between llm and version
        return _testUtil.saveAiAgentLlmAssociation(llm, version.getUid(), AiRegistryEntityType.VERSION);
    }

    public AiAgentLlmAssociation setupLlmAssociation(AiAgentVersion version, AiAgentLlm llm) {
        return _testUtil.saveAiAgentLlmAssociation(llm, version.getUid(), AiRegistryEntityType.VERSION);
    }

    public AiAgentGuardrailAssociation setupGrAssociation(AiAgentVersion version, AiAgentGuardrail gr) {
        return _testUtil.saveAiAgentGuardrailAssociation(gr, version.getUid());
    }

    public AiAgentLlm setupLlm(LlmSetupParams llmParams) {
        return _testUtil.saveAiAgentLlm(llmParams);
    }

    public void setupToolAssociation(AiAgentVersion version, AiAgentTool tool) {
        _testUtil.createAiAgentToolAssociation(tool, version);
    }


    public static AgentSetupParams createAgent1Params(AiAgentProviderAccount providerAccount) {
        return AgentSetupParams.builder()
                .guid(TestConstants.Agent1.GUID)
                .externalId(TestConstants.Agent1.EXTERNAL_ID)
                .providerAccount(providerAccount)
                .isDeleted(false)
                .build();
    }

    public static VersionSetupParams createAgent1VersionDraftParams() {

        return VersionSetupParams.builder()
                .guid(TestConstants.Agent1.VersionDraft.GUID)
                .externalId(TestConstants.Agent1.EXTERNAL_ID)
                .updatedAt(TestConstants.Agent1.VersionDraft.UPDATED_AT)
                .name(TestConstants.Agent1.VersionDraft.NAME)
                .description(TestConstants.Agent1.VersionDraft.DESCRIPTION)
                .versionString(TestConstants.Agent1.VersionDraft.VERSION)
                .agentStatus(TestConstants.Agent1.VersionDraft.STATUS)
                .createdByOrigin(AiAgentOriginType.PROVIDER)
                .createdInRegistry(false)
                .instructions(TestConstants.Agent1.VersionDraft.INSTRUCTIONS)
                .isDeleted(false)
                .build();
    }

    public static VersionSetupParams createAgent1Version1Params() {

        return VersionSetupParams.builder()
                .guid(TestConstants.Agent1.Version1.GUID)
                .externalId(TestConstants.Agent1.EXTERNAL_ID)
                .updatedAt(TestConstants.Agent1.Version1.UPDATED_AT)
                .name(TestConstants.Agent1.Version1.NAME)
                .description(TestConstants.Agent1.Version1.DESCRIPTION)
                .versionString(TestConstants.Agent1.Version1.VERSION)
                .agentStatus(TestConstants.Agent1.Version1.STATUS)
                .createdByOrigin(AiAgentOriginType.PROVIDER)
                .createdInRegistry(false)
                .instructions(TestConstants.Agent1.Version1.INSTRUCTIONS)
                .isDeleted(false)
                .build();
    }

    public static AgentSetupParams createAgent3Params(AiAgentProviderAccount providerAccount) {
        return AgentSetupParams.builder()
                .guid(TestConstants.Agent3.GUID)
                .externalId(TestConstants.Agent3.EXTERNAL_ID)
                .providerAccount(providerAccount)
                .isDeleted(false)
                .build();
    }

    public static VersionSetupParams createAgent3VersionDraftParams() {

        return VersionSetupParams.builder()
                .guid(TestConstants.Agent3.VersionDraft.GUID)
                .externalId(TestConstants.Agent3.EXTERNAL_ID)
                .updatedAt(TestConstants.Agent3.VersionDraft.UPDATED_AT)
                .name(TestConstants.Agent3.VersionDraft.NAME)
                .description(TestConstants.Agent3.VersionDraft.DESCRIPTION)
                .versionString(TestConstants.Agent3.VersionDraft.VERSION)
                .agentStatus(TestConstants.Agent3.VersionDraft.STATUS)
                .createdByOrigin(AiAgentOriginType.PROVIDER)
                .createdInRegistry(false)
                .instructions(TestConstants.Agent3.VersionDraft.INSTRUCTIONS)
                .isDeleted(false)
                .build();
    }

    public static VersionSetupParams createAgent3Version1Params() {
        return VersionSetupParams.builder()
                .guid(TestConstants.Agent3.Version1.GUID)
                .externalId(TestConstants.Agent3.EXTERNAL_ID)
                .updatedAt(TestConstants.Agent3.Version1.UPDATED_AT)
                .name(TestConstants.Agent3.Version1.NAME)
                .description(TestConstants.Agent3.Version1.DESCRIPTION)
                .versionString(TestConstants.Agent3.Version1.VERSION)
                .agentStatus(TestConstants.Agent3.Version1.STATUS)
                .createdByOrigin(AiAgentOriginType.PROVIDER)
                .createdInRegistry(false)
                .instructions(TestConstants.Agent3.Version1.INSTRUCTIONS)
                .isDeleted(false)
                .build();
    }

    public static LlmSetupParams createLlm1Params(AiAgentProviderAccount providerAccount) {
        return AiAgentTestBuilder.LlmSetupParams.builder()
                .guid(TestConstants.Llm1.GUID)
                .externalId(TestConstants.Llm1.EXTERNAL_ID)
                .name(TestConstants.Llm1.NAME)
                .versionString(TestConstants.Llm1.VERSION)
                .providerAccount(providerAccount)
                .build();
    }


    public static AliasSetupParams createAgent1Alias1Params() {
        return AliasSetupParams.builder()
                .guid(TestConstants.Agent1.Alias1.GUID)
                .externalId(TestConstants.Agent1.Alias1.EXTERNAL_ID)
                .updatedAt(TestConstants.Agent1.Alias1.UPDATED_AT)
                .name(TestConstants.Agent1.Alias1.NAME)
                .description(TestConstants.Agent1.Alias1.DESCRIPTION)
                .isDeleted(false)
                .build();

    }

    public static GuardrailSetupParams createGuardrail1Params(AiAgentProviderAccount providerAccount) {
        return GuardrailSetupParams.builder()
                .guid(TestConstants.Guardrail1.GUID)
                .externalId(TestConstants.Guardrail1.EXTERNAL_ID)
                .name(TestConstants.Guardrail1.NAME)
                .description(TestConstants.Guardrail1.DESCRIPTION)
                .versionString(TestConstants.Guardrail1.VERSION_STRING)
                .updatedAt(TestConstants.Guardrail1.UPDATED_AT)
                .providerAccount(providerAccount)
                .build();
    }

    public static ToolSetupParams createKnowledgebase1Params(AiAgentProviderAccount account) {
        ToolSetupParams.ToolSetupParamsBuilder builder = ToolSetupParams.builder()
                .guid(TestConstants.Knowledgebase1.GUID)
                .externalId(TestConstants.Knowledgebase1.EXTERNAL_ID)
                .name(TestConstants.Knowledgebase1.NAME)
                .description(TestConstants.Knowledgebase1.DESCRIPTION)
                .updatedAt(TestConstants.Knowledgebase1.UPDATED_AT)
                .status(TestConstants.Knowledgebase1.STATUS)
                .type(AiAgentToolType.AWS_KNOWLEDGE_BASE)
                .providerAccount(account);
        return builder.build();
    }

    public static ToolSetupParams createActionGroup1Params(AiAgentProviderAccount account) {
        ToolSetupParams.ToolSetupParamsBuilder builder = ToolSetupParams.builder()
                .guid(TestConstants.Agent1.ActionGroup1.GUID)
                .externalId(TestConstants.Agent1.ActionGroup1.EXTERNAL_ID)
                .name(TestConstants.Agent1.ActionGroup1.NAME)
                .description(TestConstants.Agent1.ActionGroup1.DESCRIPTION)
                .updatedAt(TestConstants.Agent1.ActionGroup1.UPDATED_AT)
                .status(TestConstants.Agent1.ActionGroup1.STATUS)
                .type(AiAgentToolType.AWS_ACTION_GROUP)
                .providerAccount(account);
        return builder.build();
    }

    public static ToolResourceParams createDataSource1Params() {
        return ToolResourceParams.builder()
                .guid(TestConstants.Knowledgebase1.DataSource1.GUID)
                .externalId(TestConstants.Knowledgebase1.DataSource1.EXTERNAL_ID)
                .name(TestConstants.Knowledgebase1.DataSource1.NAME)
                .updatedAt(TestConstants.Knowledgebase1.DataSource1.UPDATED_AT)
                .status(TestConstants.Knowledgebase1.DataSource1.STATUS)
                .build();
    }
    @Builder
    @Data
    public static class TaskSetupParams {
        private String guid;
        private String name;
        private String description;
        private String externalId;
        private String versionString;
        private AiAgentProviderAccount providerAccount;
        private String idpAccountId;
    }
    public AiAgentTask setupTask(TaskSetupParams taskParams) {
        return _testUtil.saveAiAgentTask(taskParams);
    }

    public static TaskSetupParams createTask1Params(AiAgentProviderAccount providerAccount) {
        return AiAgentTestBuilder.TaskSetupParams.builder()
                .guid(TestConstants.Task1.GUID)
                .externalId(TestConstants.Task1.EXTERNAL_ID)
                .name(TestConstants.Task1.NAME)
                .versionString(TestConstants.Task1.VERSION)
                .providerAccount(providerAccount)
                .build();
    }

    public static ToolResourceParams createFunction1Params(){

        return ToolResourceParams.builder()
                .guid(TestConstants.Agent1.ActionGroup1.Function1.GUID)
                .externalId(TestConstants.Agent1.ActionGroup1.Function1.GUID)
                .name(TestConstants.Agent1.ActionGroup1.Function1.NAME_FROM_BEDROCK)
                .updatedAt(TestConstants.Agent1.ActionGroup1.Function1.UPDATED_AT)
                .build();
    }

    public static AgentSetupParams createGardenAgent1Params(AiAgentProviderAccount providerAccount) {

        return AgentSetupParams.builder()
                .guid((String) AgentSummaryList.AGENT1_SUMMARY.get("guid"))
                .externalId((String) AgentSummaryList.AGENT1_SUMMARY.get(AGENT_SUMMARY_ID))
                .providerAccount(providerAccount)
                .isDeleted(false)
                .build();
    }

    public static VersionSetupParams createGardenAgent1VersionParams() {

        return VersionSetupParams.builder()
                .guid((String) AgentSummaryList.AGENT1_SUMMARY.get("version_guid"))
                .externalId((String) AgentSummaryList.AGENT1_SUMMARY.get(AGENT_SUMMARY_ID))
                .updatedAt(GeneralUtil.getInstantFromString((String) AgentSummaryList.AGENT1_SUMMARY.get(AGENT_SUMMARY_LAST_UPDATED_ON)))
                .name((String) AgentSummaryList.AGENT1_SUMMARY.get(AGENT_SUMMARY_NAME))
                .agentStatus((String) AgentSummaryList.AGENT1_SUMMARY.get(AGENT_SUMMARY_STATUS))
                .createdByOrigin(AiAgentOriginType.PROVIDER)
                .createdInRegistry(false)
                .isDeleted(false)
                .build();
    }

}
