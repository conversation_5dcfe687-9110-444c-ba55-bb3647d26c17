package com.boomi.aiagentregistry.constants;

import com.boomi.aiagentregistry.util.GuidUtil;

import java.time.Instant;

public class TestConstants {
    private TestConstants() {
    }

    public static final String VERSIONS_URI = "/agentversions/";
    public static final String ALIAS_URI = "/agentaliases/";
    public static final String ACT_GRP_URI = "/actiongroups/";
    public static final String KNOWLEDGE_BASES = "/knowledgebases/";
    public static final String DATA_SOURCES = "/datasources/";
    public static final String AGENTS_URI = "/agents/";
    public static final String SLASH = "/";
    public static final String LLM_URI = "/foundation-models/";
    public static final String GUARDRAILS_URI = "/guardrails/";


    /***
     * Agent to updated VersionDraft and Version To add should share at least one action group to test concurrency
     * Agent to update and agent to add versions should use the same knowledgebase as well
     */

    public static final class Agent1 {
        public static final String GUID = GuidUtil.createAIAgentGuid();
        public static final String EXTERNAL_ID = "AGENT2UONE";

        public static final class VersionDraft {
            public static final String GUID = GuidUtil.createAIAgentVersionGuid();
            public static final String VERSION = "DRAFT";
            public static final String NAME = "Agent 1 Version DRAFT Name Original";
            public static final String NAME_FROM_BEDROCK = "Agent draft version  updated name";
            public static final String DESCRIPTION = "Agent 1 Version DRAFT Description Original";
            public static final String DESCRIPTION_FROM_BEDROCK = "agent 1 draft version description updated";
            public static final String INSTRUCTIONS = "Agent 1 Version DRAFT Instructions Original";
            public static final String INSTRUCTIONS_FROM_BEDROCK = "agent 1 draft version instructions updated";
            public static final String STATUS = "AGENT_1_VERSION_DRAFT_STATUS_ORIGINAL";
            public static final String STATUS_FROM_BEDROCK = "PREPARED";
            public static final Instant UPDATED_AT = Instant.parse("2024-11-28T04:42:51.102880+00:00");
            public static final Instant UPDATED_AT_FROM_BEDROCK = Instant.parse("2024-11-29T04:42:51.102880+00:00");
            public static final String LLM = "anthropic.claude-3-5-sonnet-20240620-v1:0";
            public static final String GUARDRAIL = "0vwlgjhd99dv";
            public static final String GR_VERSION = "DRAFT";

        }

        public static final class Version1 {
            public static final String GUID = GuidUtil.createAIAgentVersionGuid();
            public static final String VERSION = "1";
            public static final String NAME = "Agent 1 Version 1 Name Original";
            public static final String NAME_FROM_BEDROCK = "Agent 1 Version 1 name to be reflected";
            public static final String DESCRIPTION = "Agent 1 Version 1 Description Original";
            public static final String DESCRIPTION_FROM_BEDROCK = "this is agent 1 version 1 description to be reflected";
            public static final String INSTRUCTIONS = "Agent 1 version 1 original instructions";
            public static final String INSTRUCTIONS_FROM_BEDROCK = "this is the instruction for agent 1 version 1 to be reflected";
            public static final String STATUS = "AGENT_1_VERSION_1_STATUS_ORIGINAL";
            public static final String STATUS_FROM_BEDROCK = "PREPARED";
            public static final Instant UPDATED_AT = Instant.parse("2024-11-27T05:42:51.102880+00:00");
            public static final Instant UPDATED_AT_FROM_BEDROCK = Instant.parse("2024-11-28T05:42:51.102880+00:00");
            public static final String LLM = "anthropic.claude-3-5-sonnet-20240620-v1:0";
            public static final String GUARDRAIL = "0vwlgjhd99dv";
            public static final String GR_VERSION = "DRAFT";

        }


        public static final class Version2 {
            public static final String VERSION = "2";
        }


        public static final class Alias1 {
            public static final String GUID = GuidUtil.createAIAgentAliasGuid();
            public static final String EXTERNAL_ID = "ANTJDMWTRW";
            public static final String NAME = "Agent 1 Alias 1 Name Original";
            public static final String DESCRIPTION = "Agent 1 Alias 1 Description Original";
            public static final String NAME_FROM_BEDROCK = "omar-agent-1-alias-1 from bedrock";
            public static final String DESCRIPTION_FROM_BEDROCK = "this is the description for agent 1 alias 1 pointing to version 1 from bedrock";
            public static final Instant UPDATED_AT = Instant.parse("2024-11-28T04:42:49.554826+00:00");
            public static final Instant UPDATED_AT_FROM_BEDROCK = Instant.parse("2024-11-29T04:42:49.554826+00:00");

        }


        public static final class ActionGroup1 {
            public static final String GUID = GuidUtil.createAIAgentToolGuid();
            public static final String EXTERNAL_ID = "ACGPFUNONE";
            public static final String NAME = "Agent 1 Action Group 1 Name Original";
            public static final String DESCRIPTION = "Agent 1 Action Group 1 Description Original";
            public static final String NAME_FROM_BEDROCK = "agent 1 action group 1 name from bedrock";
            public static final String DESCRIPTION_FROM_BEDROCK = "agent 1 action group 1 description from bedrock";
            public static final Instant UPDATED_AT = Instant.parse("2024-11-02T01:01:38.587446+00:00");
            public static final Instant UPDATED_AT_FROM_BEDROCK = Instant.parse("2024-12-02T01:01:38.587446+00:00");
            public static final String STATUS = "AGENT_1_ACTION_GROUP_1_STATUS_ORIGINAL";
            public static final String STATUS_FROM_BEDROCK = "ENABLED";

            public static class Function1{
                public static final String GUID = GuidUtil.createToolResourceGuid();
                public static final String NAME = "Agent 1 Action Group 1 Function 1 Name Original";
                public static final Instant UPDATED_AT = Instant.parse("2024-11-02T01:01:38.587446+00:00");
                public static final String NAME_FROM_BEDROCK = "grp_1_fun_1";
                public static final Instant UPDATED_AT_FROM_BEDROCK = Instant.parse("2024-12-02T01:01:38.587446+00:00");
            }

            public static class Function2{
                public static final String GUID = GuidUtil.createToolResourceGuid();
                public static final String NAME_FROM_BEDROCK = "act_1_fun_2";
                public static final Instant UPDATED_AT_FROM_BEDROCK = Instant.parse("2024-12-02T01:01:38.587446+00:00");
            }
        }

        public static final class ActionGroup2 {
            public static final String EXTERNAL_ID = "V08FUNSTWO";
        }

        public static final class ActionGroup3 {
            public static final String EXTERNAL_ID = "NAPIDTSONE";
        }

        public static final class ActionGroup4 {
            public static final String EXTERNAL_ID = "NAPIDTSTWO";
        }

    }

    public static final class Agent3 {
        public static final String GUID = GuidUtil.createAIAgentGuid();
        public static final String EXTERNAL_ID = "D35OMTHREE";


        public static final class Version1 {
            public static final String GUID = GuidUtil.createAIAgentVersionGuid();
            public static final String VERSION = "1";
            public static final String NAME = "Agent 3 Version 1 Name Original";
            public static final String INSTRUCTIONS = "Agent 3 Version 1 Instructions Original";
            public static final String DESCRIPTION = "Agent 3 Version 1 Description Original";
            public static final String STATUS = "AGENT_3_VERSION_1_STATUS_ORIGINAL";
            public static final Instant UPDATED_AT = Instant.parse("2024-11-28T20:17:25.078683+00:00");
            public static final String LLM = "anthropic.claude-3-5-sonnet-20240620-v1:0";
            public static final String GUARDRAIL = "0vwlgjhd99dv";
            public static final String GR_VERSION = "DRAFT";

        }


        public static final class VersionDraft {
            public static final String GUID = GuidUtil.createAIAgentVersionGuid();
            public static final String VERSION = "DRAFT";
            public static final String NAME = "Agent 3 Version Draft Name Original";
            public static final String DESCRIPTION = "Agent 3 Version Draft Description Original";
            public static final String STATUS = "AGENT_3_VERSION_DRAFT_STATUS_ORIGINAL";
            public static final String INSTRUCTIONS = "Agent 3 Version Draft Instructions Original";
            public static final Instant UPDATED_AT = Instant.parse("2024-11-28T20:17:25.078683+00:00");
            public static final Instant UPDATED_AT_FROM_BEDROCK = Instant.parse("2024-11-28T20:17:25.078683+00:00");

            public static final String LLM = "should-not-reflect.claude-3-5-sonnet-20240620-v1:0";
            public static final String GUARDRAIL = "0vwlgjhd99dv";
            public static final String GR_VERSION = "DRAFT";

        }

        public static final class ActionGroup {
            public static final String GUID = GuidUtil.createAIAgentToolGuid();
            public static final Instant UPDATED_AT = Instant.parse("2024-11-28T20:17:25.071535+00:00");

            public static final class Function {
                public static final String GUID = GuidUtil.createAIAgentToolGuid();
                public static final String EXTERNAL_ID = "V14FUN1CIE";
            }

            public static final class Api {
                public static final String GUID = GuidUtil.createAIAgentToolGuid();
                public static final String EXTERNAL_ID = "XXXXXXXXXX";
                public static final Instant UPDATED_AT = Instant.parse("2024-11-28T20:17:25.071535+00:00");

            }

        }

    }


    public static final class Agent2 {
        public static final String EXTERNAL_ID = "M2NT3YGTWO";

        public static final class VersionDraft {
            public static final String VERSION = "DRAFT";
            public static final String NAME = "Agent 2 Name Original";
            public static final String DESCRIPTION = "this is agent 2 version draft";
            public static final String INSTRUCTIONS = "this is agent 2 draft version instructions";
            public static final String STATUS = "PREPARED";
            public static final Instant UPDATED_AT = Instant.parse("2024-11-28T20:17:25.071535+00:00");
            public static final String LLM = "anthropic.claude-3-5-sonnet-20240620-v1:0";

        }

    }

    public static final class Knowledgebase1 {

        public static final String GUID = GuidUtil.createAIAgentToolGuid();
        public static final String EXTERNAL_ID = "ZIM1IYS9CP";
        public static final String NAME = "Agent 1 Version 1 Knowledgebase Original";
        public static final String NAME_FROM_BEDROCK = "Knowledgebase 1 name From Bedrock";
        public static final String DESCRIPTION = "Agent 1 Version 1 Knowledgebase Original";
        public static final String DESCRIPTION_FROM_BEDROCK = "knowledge base 1 description from bedrock";
        public static final Instant UPDATED_AT = Instant.parse("2024-11-10T17:19:36.286117+00:00");
        public static final Instant UPDATED_AT_FROM_BEDROCK = Instant.parse("2024-12-10T17:19:36.286117+00:00");
        public static final String STATUS = "ORIGINAL_STATUS";
        public static final String STATUS_FROM_BEDROCK = "ACTIVE";

        public static final class DataSource1 {
            public static final String GUID = GuidUtil.createAIAgentToolGuid();
            public static final String EXTERNAL_ID = "DS11111111";
            public static final String STATUS = "ORIGINAL_STATUS";
            public static final String STATUS_FROM_BEDROCK = "AVAILABLE";
            public static final String NAME = "Agent 1 Version 1 Knowledgebase Original";
            public static final String NAME_FROM_BEDROCK = "KB 1 data source 1 name from bedrock";
            public static final Instant UPDATED_AT = Instant.parse("2024-11-29T04:42:49.554826+00:00");
            public static final Instant UPDATED_AT_FROM_BEDROCK = Instant.parse("2024-12-10T17:04:04.329120+00:00");
        }

        public static final class DataSource2 {
            public static final String EXTERNAL_ID = "DS22222222";
            public static final String GUID = GuidUtil.createToolResourceGuid();
            public static final String NAME = "KB_TO_ADD";
            public static final String NAME_FROM_BEDROCK = "KB 1 data source 2 name from bedrock";
            public static final String STATUS = "AVAILABLE";
            public static final String STATUS_FROM_BEDROCK = "AVAILABLE";
            public static final Instant UPDATED_AT = Instant.parse("2024-12-10T17:04:04.329120+00:00");
            public static final Instant UPDATED_AT_FROM_BEDROCK = Instant.parse("2024-12-11T17:04:04.329120+00:00");
        }

        public static final class DataSource3 {
            public static final String EXTERNAL_ID = "DS33333333";
            public static final String GUID = GuidUtil.createAIAgentToolGuid();
            public static final String NAME = "keep this name";
            public static final String NAME_FROM_BEDROCK = "KB 1 data source 3 name from bedrock";
            public static final String STATUS = "KEEP_THIS_STATUS";
            public static final String STATUS_FROM_BEDROCK = "AVAILABLE";
            public static final Instant UPDATED_AT = Instant.parse("2024-12-10T17:04:04.329120+00:00");
            public static final Instant UPDATED_AT_FROM_BEDROCK = Instant.parse("2024-12-12T17:04:04.329120+00:00");

        }

        public static final class DataSource4 {

            public static final String EXTERNAL_ID = "DS_DELETE";
            public static final String GUID = GuidUtil.createAIAgentToolGuid();
            public static final Instant UPDATED_AT = Instant.parse("2024-11-29T04:42:49.554826+00:00");
        }

    }

    public static final class Knowledgebase2 {

        public static final String GUID = GuidUtil.createAIAgentToolGuid();
        public static final String EXTERNAL_ID = "XLZPLVQ51N";
        public static final String NAME = "Agent 1 Version 1 Original";
        public static final String NAME_FROM_BEDROCK = "KNOWLEDGE BASE 2 NAME FROM BEDROCK";
        public static final String DESCRIPTION = "Agent 1 Version 1 Knowledgebase Original";
        public static final String DESCRIPTION_FROM_BEDROCK = "KNOWLEDGE BASE 2 DESCRIPTION FROM BEDROCK";
        public static final Instant UPDATED_AT = Instant.parse("2024-11-29T04:42:49.554826+00:00");
        public static final Instant UPDATED_AT_FROM_BEDROCK = Instant.parse("2024-11-20T17:14:40.501117+00:00");
        public static final String STATUS = "ORIGINAL_STATUS";
        public static final String STATUS_FROM_BEDROCK = "ACTIVE";
    }


    public static final class Llm1 {
        public static final String GUID = GuidUtil.createLlmGuid();
        public static final String EXTERNAL_ID = "anthropic.claude-3-5-sonnet-20240620-v1:0";
        public static final String NAME = "Claude 3 Haiku";
        public static final String VERSION = "0";
    }

    public static final class Guardrail1 {
        public static final String GUID = GuidUtil.createAIAgentGuardrailGuid();
        public static final String EXTERNAL_ID = "0vwlgjhd99dv";
        public static final String NAME = "Guardrail Name Original";
        public static final String NAME_FROM_BEDROCK = "omar-testing-guardrail-update-name from bedrock";
        public static final String DESCRIPTION = "Guardrail Description";
        public static final String DESCRIPTION_FROM_BEDROCK = "this is a guard rail to test reg from bedrock";
        public static final String VERSION_STRING = "DRAFT";
        public static final Instant UPDATED_AT = Instant.parse("2025-01-05T19:25:17.810993+00:00");
        public static final Instant UPDATED_AT_FROM_BEDROCK = Instant.parse("2025-01-06T19:25:17.810993+00:00");

    }
    public static final class Task1 {
        public static final String GUID = GuidUtil.createTaskGuid();
        public static final String EXTERNAL_ID = "test-external-id";
        public static final String NAME = "first task ";
        public static final String VERSION = "0";
    }
}


