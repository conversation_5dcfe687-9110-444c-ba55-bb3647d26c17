// Copyright (c) 2024 Boom<PERSON>, LP

package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.model.AiAgentToolAuditLogChangeSetV1;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.graphql.server.schema.types.AiAgentToolCreateInput;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.temporal.ChronoUnit;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * <AUTHOR> Wang.
 */
@ExtendWith(MockitoExtension.class)
public class AIAgentToolMapperTest {

    private AiAgentToolMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(AiAgentToolMapper.class);
    }

    @Test
    void testEntityToGraphQLMapping_whenVersionIsString() {
        AiAgentTool entity = new AiAgentTool();
        String guid = GuidUtil.createAIAgentToolGuid();
        entity.setGuid(guid);
        entity.setName("Test Tool");
        entity.setDescription("Test Description");
        entity.setVersionString("DRAFT");
        entity.setVersionInt(null);
        entity.setCreatedByUserId("testUser");
        Timestamp timestamp = com.boomi.aiagentregistry.util.AuditUtilImpl.getCurrentTime();
        entity.setCreatedTime(timestamp);

        com.boomi.graphql.server.schema.types.AiAgentTool dto = mapper.toAIAgentToolDto(entity);

        assertNotNull(dto);
        assertEquals(guid, dto.getId());
        assertEquals(entity.getName(), dto.getName());
        assertEquals(entity.getDescription(), dto.getDescription());
        assertEquals(entity.getVersionString(), dto.getVersion());
        assertEquals(entity.getCreatedByUserId(), dto.getAuditData().getCreatedByUserId());
        if (entity.getCreatedTime() != null && dto.getAuditData().getCreatedTime() != null) {
            Instant entityInstant = entity.getCreatedTime().toInstant().truncatedTo(ChronoUnit.MILLIS);
            Instant dtoInstant = dto.getAuditData().getCreatedTime().toInstant().truncatedTo(ChronoUnit.MILLIS);

            assertEquals(entityInstant, dtoInstant);
        }
    }

    @Test
    void testEntityToGraphQLMapping_whenVersionIsInt() {
        AiAgentTool entity = new AiAgentTool();
        String guid = GuidUtil.createAIAgentToolGuid();
        entity.setGuid(guid);
        entity.setName("Test Tool");
        entity.setDescription("Test Description");
        entity.setVersionString(null);
        entity.setVersionInt(1);
        entity.setCreatedByUserId("testUser");
        Timestamp timestamp = com.boomi.aiagentregistry.util.AuditUtilImpl.getCurrentTime();
        entity.setCreatedTime(timestamp);

        com.boomi.graphql.server.schema.types.AiAgentTool dto = mapper.toAIAgentToolDto(entity);

        assertNotNull(dto);
        assertEquals(guid.toString(), dto.getId());
        assertEquals(entity.getName(), dto.getName());
        assertEquals(entity.getDescription(), dto.getDescription());
        assertEquals(entity.getVersionInt().toString(), dto.getVersion());
        assertEquals(entity.getCreatedByUserId(), dto.getAuditData().getCreatedByUserId());
        if (entity.getCreatedTime() != null && dto.getAuditData().getCreatedTime() != null) {
            Instant entityInstant = entity.getCreatedTime().toInstant().truncatedTo(ChronoUnit.MILLIS);
            Instant dtoInstant = dto.getAuditData().getCreatedTime().toInstant().truncatedTo(ChronoUnit.MILLIS);

            assertEquals(entityInstant, dtoInstant);
        }
    }

    @Test
    void testCreateInputToEntityMapping_whenVersionIsString() {
        AiAgentToolCreateInput input = setupAiAgentToolDraftCreateInput();
        AiAgentProviderAccount registryAccount = setupAiAgentRegistryAccountEntity();
        AiAgentTool entity = mapper.toAIAgentToolEntityFromToolCreateInput(input, registryAccount);

        assertNotNull(entity);
        assertEquals(input.getName(), entity.getName());
        assertEquals(input.getDescription(), entity.getDescription());
        assertEquals(input.getVersion(), entity.getVersionString());
        assertNull(entity.getVersionInt());
    }

    @Test
    void testCreateInputToEntityMapping_whenVersionIsInt() {
        AiAgentToolCreateInput input = setupAiAgentToolVersionOneCreateInput();
        AiAgentProviderAccount registryAccount = setupAiAgentRegistryAccountEntity();

        AiAgentTool entity = mapper.toAIAgentToolEntityFromToolCreateInput(input, registryAccount);

        assertNotNull(entity);
        assertEquals(input.getName(), entity.getName());
        assertEquals(input.getDescription(), entity.getDescription());
        assertEquals(1, entity.getVersionInt());
        assertNull(entity.getVersionString());
    }

    @Test
    void testToAuditLogChangeSet() {
        AiAgentToolCreateInput input = setupAiAgentToolDraftCreateInput();

        // Act & Assert - ChangeSet V1
        Object changeSetV1 = mapper.toAuditLogChangeSet(input, 1);
        assertNotNull(changeSetV1);
        assertInstanceOf(AiAgentToolAuditLogChangeSetV1.class, changeSetV1);
    }

    @Test
    void testToAuditLogChangeSet_NullInput() {
        Object changeSet = mapper.toAuditLogChangeSet(null, 1);
        assertNull(changeSet);
    }

    @Test
    void testToAuditLogChangeSet_UnsupportedVersion() {
        AiAgentToolCreateInput input = setupAiAgentToolDraftCreateInput();
        Object changeSet = mapper.toAuditLogChangeSet(input, 3);
        assertNull(changeSet);
    }

    private static AiAgentToolCreateInput setupAiAgentToolDraftCreateInput() {
        AiAgentToolCreateInput input = new AiAgentToolCreateInput();
        input.setProviderAccountId("ce042521-acbc-4ef4-826f-00000000ea71");
        input.setName("New Tool");
        input.setDescription("New Description");
        input.setVersion("DRAFT");
        return input;
    }

    private static AiAgentToolCreateInput setupAiAgentToolVersionOneCreateInput() {
        AiAgentToolCreateInput input = new AiAgentToolCreateInput();
        input.setProviderAccountId("ce042521-acbc-4ef4-826f-00000000ea71");
        input.setName("New Tool");
        input.setDescription("New Description");
        input.setVersion("1");
        return input;
    }

    private static AiAgentProviderAccount setupAiAgentRegistryAccountEntity() {
        AiAgentProviderAccount registryAccount = new AiAgentProviderAccount();
        registryAccount.setUid(1);
        registryAccount.setGuid("ce042521-acbc-4ef4-826f-00000000ea71");
        registryAccount.setIdpAccountId("test-idp-account");

        return registryAccount;
    }
}
