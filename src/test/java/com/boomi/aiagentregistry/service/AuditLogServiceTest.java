// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service;

/**
 * <AUTHOR>
 */

import com.boomi.aiagentregistry.constant.ActionEnum;
import com.boomi.aiagentregistry.constant.ActionResultEnum;
import com.boomi.aiagentregistry.model.AuditLogEntry;
import com.boomi.aiagentregistry.repo.AuditLogRepository;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class AuditLogServiceTest {

    @Mock
    private AuditLogRepository auditLogRepository;

    private AuditLogServiceImpl auditLogService;

    @BeforeEach
    void setUp() {
        auditLogService = new AuditLogServiceImpl(auditLogRepository);
    }

    @Test
    void testLogAction_SuccessfulLog() {
        String entityGuid = GuidUtil.createAuditLogGuid();
        String userId = "testUser";
        String accountId = "testAccount";
        String requestId = "req-123";
        String changeSet = "{\"field\":\"value\"}";
        Integer entityVersion = 1;

        auditLogService.logAction(AuditLogEntry.builder().entityName(AiRegistryEntityType.TOOL).entityGuid(entityGuid)
                .action(ActionEnum.CREATE).userId(userId).accountId(accountId).entityVersion(entityVersion).changeSet(
                        changeSet).requestId(requestId).actionResult(ActionResultEnum.SUCCESS).build()
        );

        verify(auditLogRepository).save(argThat(auditLog ->
                auditLog.getEntityName().equals(AiRegistryEntityType.TOOL.name()) &&
                        auditLog.getEntityGuid().equals(entityGuid) &&
                        auditLog.getAction().equals(ActionEnum.CREATE.name()) &&
                        auditLog.getUserId().equals(userId) &&
                        auditLog.getAccountId().equals(accountId) &&
                        auditLog.getEntityVersion().equals(entityVersion) &&
                        auditLog.getChangeSet().equals(changeSet) &&
                        auditLog.getRequestId().equals(requestId) &&
                        auditLog.getActionResult().equals(ActionResultEnum.SUCCESS.name())
        ));
    }

}

