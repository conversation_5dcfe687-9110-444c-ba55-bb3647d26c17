// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;

class TimestreamIntervalCalculatorTest {

    /**
     * Helper method to create a Date object with an offset from a base date
     */
    private Date createDateWithOffset(int offsetSeconds) {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        // Set a fixed base date to ensure consistent testing
        calendar.set(2024, Calendar.JANUARY, 1, 12, 0, 0);
        calendar.add(Calendar.SECOND, offsetSeconds);
        return calendar.getTime();
    }

    @Test
    @DisplayName("Should return 5m when duration is zero")
    void shouldReturnThirtySecondsWhenDurationIsZero() {
        Date sameTime = createDateWithOffset(0);
        assertEquals("5m", TimestreamIntervalCalculator.calculateTimestreamInterval(sameTime, sameTime));
    }

    /**
     * Parameterized test source for different time ranges
     */
    private static Stream<Arguments> timeRangeProvider() {
        return Stream.of(
                // 30 seconds
                Arguments.of(30, "5m"),
                // 30 minutes
                Arguments.of(1800, "5m"),
                // 50 minutes
                Arguments.of(3000, "10m"),
                // 1 hour
                Arguments.of(3600, "10m"),
                // 2 hours
                Arguments.of(7200, "30m"),
                // 3 hours
                Arguments.of(10800, "30m"),
                // 5 hours
                Arguments.of(18000, "1h"),
                // 6 hours
                Arguments.of(21600, "1h"),
                // 9 hours
                Arguments.of(32400, "2h"),
                // 12 hours
                Arguments.of(43200, "2h"),
                // 18 hours
                Arguments.of(64800, "4h"),
                // 24 hours
                Arguments.of(86400, "4h"),
                // 3 days
                Arguments.of(259200, "1d"),
                // 7 days
                Arguments.of(604800, "1d"),
                // 14 days
                Arguments.of(1209600, "7d"),
                // 30 days
                Arguments.of(2592000, "7d"),
                // 60 days
                Arguments.of(5184000, "14d"),
                // 90 days
                Arguments.of(7776000, "14d"),
                // 180 days
                Arguments.of(15552000, "30d"));
    }

    @ParameterizedTest(name = "Duration {0} seconds should return interval {1}")
    @MethodSource("timeRangeProvider")
    @DisplayName("Should return correct interval for different time ranges")
    void shouldReturnCorrectIntervalForTimeRange(int durationInSeconds, String expectedInterval) {
        Date startDate = createDateWithOffset(0);
        Date endDate = createDateWithOffset(durationInSeconds);

        assertEquals(expectedInterval, TimestreamIntervalCalculator.calculateTimestreamInterval(startDate, endDate));
    }

    @Test
    @DisplayName("Should handle negative time range by treating it as positive")
    void shouldHandleNegativeTimeRange() {
        // Later time
        Date startDate = createDateWithOffset(3600);
        // Earlier time
        Date endDate = createDateWithOffset(0);

        assertEquals("10m", TimestreamIntervalCalculator.calculateTimestreamInterval(startDate, endDate));
    }

    @Test
    @DisplayName("Should return 1 month = 30d for very large time ranges")
    void shouldReturnOneDayForVeryLargeTimeRanges() {
        Date startDate = createDateWithOffset(0);
        // 365 days
        Date endDate = createDateWithOffset(31536000);

        assertEquals("30d", TimestreamIntervalCalculator.calculateTimestreamInterval(startDate, endDate));
    }

    @Test
    @DisplayName("Should handle dates across DST changes correctly")
    void shouldHandleDSTChanges() {
        // Create Calendar in EST
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("America/New_York"));

        // Set to March 10, 2024, 1:30 AM EST (just before DST spring forward)
        calendar.set(2024, Calendar.MARCH, 10, 1, 30, 0);
        Date startDate = calendar.getTime();

        // Set to March 10, 2024, 3:30 AM EDT (just after DST spring forward)
        // Note: When we add 1 hours to 1:30 AM during DST change,
        // it becomes 3:30 AM due to the spring forward
        calendar.add(Calendar.HOUR_OF_DAY, 1);

        Date endDate = calendar.getTime();

        // Although it appears to be 2 hours in local time (1:30 AM to 3:30 AM),
        // in UTC it's actually only 1 hour due to DST spring forward
        assertEquals("10m", TimestreamIntervalCalculator.calculateTimestreamInterval(startDate, endDate));
    }
}