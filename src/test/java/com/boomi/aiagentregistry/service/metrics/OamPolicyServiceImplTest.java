// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.PlatformTransactionManager;

import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.oam.OamClient;
import software.amazon.awssdk.services.oam.model.GetSinkPolicyRequest;
import software.amazon.awssdk.services.oam.model.GetSinkPolicyResponse;
import software.amazon.awssdk.services.oam.model.PutSinkPolicyRequest;

import com.boomi.aiagentregistry.exception.OamException;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OamPolicyServiceImplTest {

    @Mock
    private OamClient oamClient;
    @Mock
    private OamSinkArnRetrieverService oamSinkArnRetrieverService;
    @Mock
    private JdbcTemplate jdbcTemplate;
    @Mock
    private PlatformTransactionManager transactionManager;
    @Mock
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    @Mock
    private OamClientHelperService oamClientHelperService;
    @Captor
    ArgumentCaptor<PutSinkPolicyRequest> policyPuCaptor;

    private OamPolicyServiceImpl _oamPolicyServiceImpl;
    private static final String SINK_ARN =
            "arn:aws:oam:us-east-1:************:sink/b1d64f36-6af9-4899-b478-8d5b43f8caec";
    private static final Region REGION = Region.US_EAST_1;
    private static final String MONITORING_ACCOUNT_ID = "************";
    private static final String CUSTOMER_ACCOUNT_ID = "************";

    @BeforeEach
    void setUp() {
        Map<String, String> sinkArnsMap = new HashMap<>();
        sinkArnsMap.put(REGION.toString(), SINK_ARN);
        when(oamSinkArnRetrieverService.getRegionToSinkArnUnmodifiableMap())
                .thenReturn(sinkArnsMap);
        when(oamClientHelperService.createOamClient(REGION))
                .thenReturn(oamClient);
        _oamPolicyServiceImpl = new OamPolicyServiceImpl(oamSinkArnRetrieverService, jdbcTemplate, transactionManager,
                oamClientHelperService, MONITORING_ACCOUNT_ID);
        ReflectionTestUtils.setField(_oamPolicyServiceImpl, "_namedParameterJdbcTemplate", namedParameterJdbcTemplate);
    }

    private String loadPolicyFromFile(String fileName) throws IOException {
        Path path = Paths.get("src/test/resources/oam/" + fileName);
        return Files.readString(path);
    }

    @Test
    void updateOamSinkPolicyAdd_WithArrayFormat_Success() throws IOException {
        // Arrange
        String originalPolicy = loadPolicyFromFile("aws_oam_sink_policy_array.json");
        String expectedPolicy = loadPolicyFromFile("expected_array_format_policy.json");
        Set<String> activeAccounts = new HashSet<>();
        activeAccounts.add("************");
        activeAccounts.add("************");
        // invalid aws account
        activeAccounts.add("33************");
        when(namedParameterJdbcTemplate.queryForList(anyString(), any(MapSqlParameterSource.class), any()))
                .thenReturn(new ArrayList<>(activeAccounts));

        when(oamClient.getSinkPolicy(any(GetSinkPolicyRequest.class))).thenReturn(
                GetSinkPolicyResponse.builder().policy(originalPolicy).sinkArn(SINK_ARN).build());

        // Act
        _oamPolicyServiceImpl.updateOamSinkPolicy(CUSTOMER_ACCOUNT_ID, REGION.toString(), true);

        // Assert
        verify(oamClient).putSinkPolicy(policyPuCaptor.capture());
        PutSinkPolicyRequest capturedRequest = policyPuCaptor.getValue();

        String normalizedExpected = expectedPolicy.replaceAll("\\s+", "");
        String normalizedActual = capturedRequest.policy().replaceAll("\\s+", "");
        assertEquals(normalizedExpected, normalizedActual);

        assertEquals(SINK_ARN, capturedRequest.sinkIdentifier());
    }
    
    @Test
    void updateOamSinkPolicyRemove() throws IOException {
        // Arrange
        String originalPolicy = loadPolicyFromFile("aws_oam_sink_policy_array.json");
        String expectedPolicy = loadPolicyFromFile("expected_array_format_policy_after_delete.json");
        Set<String> activeAccounts = new HashSet<>();
        activeAccounts.add("************");
        activeAccounts.add("************");
        activeAccounts.add(CUSTOMER_ACCOUNT_ID);
        // invalid aws account
        activeAccounts.add("33************");
        when(namedParameterJdbcTemplate.queryForList(anyString(), any(MapSqlParameterSource.class), any()))
                .thenReturn(new ArrayList<>(activeAccounts));

        when(oamClient.getSinkPolicy(any(GetSinkPolicyRequest.class))).thenReturn(
                GetSinkPolicyResponse.builder().policy(originalPolicy).sinkArn(SINK_ARN).build());

        // Act
        _oamPolicyServiceImpl.updateOamSinkPolicy(CUSTOMER_ACCOUNT_ID, REGION.toString(), false);

        // Assert
        verify(oamClient).putSinkPolicy(policyPuCaptor.capture());
        PutSinkPolicyRequest capturedRequest = policyPuCaptor.getValue();

        String normalizedExpected = expectedPolicy.replaceAll("\\s+", "");
        String normalizedActual = capturedRequest.policy().replaceAll("\\s+", "");
        assertEquals(normalizedExpected, normalizedActual);

        assertEquals(SINK_ARN, capturedRequest.sinkIdentifier());
    }

    @Test
    void updateOamSinkPolicy_WithSingleValueFormat_Success() throws IOException {
        // Arrange
        String originalPolicy = loadPolicyFromFile("aws_oam_sink_policy_single.json");
        String expectedPolicy = loadPolicyFromFile("expected_single_to_array_policy.json");
        Set<String> activeAccounts = new HashSet<>();
        activeAccounts.add("************");
        activeAccounts.add("************");

        when(oamClient.getSinkPolicy(any(GetSinkPolicyRequest.class))).thenReturn(
                GetSinkPolicyResponse.builder().policy(originalPolicy).sinkArn(SINK_ARN).build());
        // Act
        _oamPolicyServiceImpl.updateOamSinkPolicy(activeAccounts, "************", REGION, oamClient);

        // Assert
        verify(oamClient).putSinkPolicy(policyPuCaptor.capture());
        PutSinkPolicyRequest capturedRequest = policyPuCaptor.getValue();

        String normalizedExpected = expectedPolicy.replaceAll("\\s+", "");
        String normalizedActual = capturedRequest.policy().replaceAll("\\s+", "");
        assertEquals(normalizedExpected, normalizedActual);

        assertEquals(SINK_ARN, capturedRequest.sinkIdentifier());
    }

    @Test
    void updateOamSinkPolicy_WithEmptyAccounts_UsesMonitoringAccount() throws IOException {
        // Arrange
        String originalPolicy = loadPolicyFromFile("aws_oam_sink_policy_single.json");
        String expectedPolicy = loadPolicyFromFile("aws_oam_sink_policy_single.json");
        expectedPolicy = expectedPolicy.replace("\"arn:aws:iam::************:root\"", "[\"" + MONITORING_ACCOUNT_ID + "\"]");
        Set<String> activeAccounts = Collections.emptySet();

        when(oamClient.getSinkPolicy(any(GetSinkPolicyRequest.class))).thenReturn(
                GetSinkPolicyResponse.builder().policy(originalPolicy).sinkArn(SINK_ARN).build());

        // Act
        _oamPolicyServiceImpl.updateOamSinkPolicy(activeAccounts, "************", REGION, oamClient);

        // Assert

        verify(oamClient).putSinkPolicy(policyPuCaptor.capture());
        PutSinkPolicyRequest capturedRequest = policyPuCaptor.getValue();

        String normalizedExpected = expectedPolicy.replaceAll("\\s+", "");
        String normalizedActual = capturedRequest.policy().replaceAll("\\s+", "");
        assertEquals(normalizedExpected, normalizedActual);

        assertEquals(SINK_ARN, capturedRequest.sinkIdentifier());
    }

    @Test
    void updateOamSinkPolicy_InvalidPolicy_ThrowsException() throws IOException {
        // Arrange
        String invalidPolicy = loadPolicyFromFile("invalid_policy.json");
        Set<String> activeAccounts = new HashSet<>();
        activeAccounts.add("************");

        when(oamClient.getSinkPolicy(any(GetSinkPolicyRequest.class))).thenReturn(
                GetSinkPolicyResponse.builder().policy(invalidPolicy).sinkArn(SINK_ARN).build());

        // Act & Assert
        assertThrows(OamException.class,
                () -> _oamPolicyServiceImpl.updateOamSinkPolicy(activeAccounts, "************", REGION, oamClient));
    }

    @Test
    void updateOamSinkPolicy_NoSinkArn_ThrowsException() {
       // Arrange
        when(oamSinkArnRetrieverService.getRegionToSinkArnUnmodifiableMap())
                        .thenReturn(Collections.emptyMap());
        OamPolicyServiceImpl serviceWithoutSink = new OamPolicyServiceImpl(oamSinkArnRetrieverService, jdbcTemplate, transactionManager,
                        oamClientHelperService, MONITORING_ACCOUNT_ID);
        Set<String> activeAccounts = new HashSet<>();
        activeAccounts.add("************");

        // Act & Assert
        assertThrows(OamException.class,
                () -> serviceWithoutSink.updateOamSinkPolicy(activeAccounts, "************", REGION, oamClient));
    }
}
