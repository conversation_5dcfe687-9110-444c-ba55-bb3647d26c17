package com.boomi.aiagentregistry.service.sync.boomigarden;

import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.constant.ActionEnum;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentGuardrail;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.job.SyncJob;
import com.boomi.aiagentregistry.repo.AiAgentGuardrailAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentGuardrailRepository;
import com.boomi.aiagentregistry.repo.AiAgentLargeTextContentRepository;
import com.boomi.aiagentregistry.repo.AiAgentRepository;
import com.boomi.aiagentregistry.repo.AiAgentVersionRepository;
import com.boomi.aiagentregistry.service.SyncVerificationService;
import com.boomi.aiagentregistry.service.auth.AuthorizationParsingService;
import com.boomi.aiagentregistry.service.auth.BoomiApiTokenCredentials;
import com.boomi.aiagentregistry.service.sync.SyncLimitManagerImpl;
import com.boomi.aiagentregistry.servlet.BedrockMockServerHelper;
import com.boomi.aiagentregistry.servlet.response.boomigarden.AgentSummaryList;
import com.boomi.aiagentregistry.util.AiAgentTestBuilder;
import com.boomi.aiagentregistry.util.GeneralUtil;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.gardenagents.model.AgentDetail;
import com.boomi.gardenagents.model.AgentSummary;
import com.boomi.gardenagents.model.Guardrail;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.createGardenAgent1Params;
import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.createGardenAgent1VersionParams;
import static com.boomi.gardenagents.model.AgentDetail.AGENT_ID;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_CREATED_ON;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_ID;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_LAST_UPDATED_ON;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_NAME;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_OBJECTIVE;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_STATUS;
import static com.boomi.graphql.server.schema.types.AiAgentOriginType.PROVIDER;
import static junit.framework.TestCase.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = TestApplication.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        properties = "spring.main.allow-bean-definition-overriding=true")
public class GardenSyncProviderAccountIntegrationTest extends BaseMockWebServerTest {

    private AiAgentProviderAccount _testAccount;
    @Autowired
    private TestUtil _testUtil;
    @Autowired
    private BedrockMockServerHelper _serverHelper;
    @Autowired
    private AiAgentTestBuilder _testBuilder;
    @Autowired
    private GardenSyncProvider _gardenSyncProvider;
    @Autowired
    private SyncVerificationService _verificationService;
    @Autowired
    private AiAgentRepository _aiAgentRepository;
    @Autowired
    private AiAgentVersionRepository _versionRepository;
    @Autowired
    private AiAgentLargeTextContentRepository _largeTextRepo;
    @MockBean
    private GardenService _gardenService;
    @MockBean
    private AuthorizationParsingService _authorizationParsingService;
    @Autowired
    private AiAgentGuardrailRepository _guardrailRepo;
    @Autowired
    private AiAgentGuardrailAssociationRepository _guardrailAssoRepo;
    @MockBean
    private SyncLimitManagerImpl _limitManager;


    @DynamicPropertySource
    static void registerProperties(DynamicPropertyRegistry registry) {
        registry.add("boomi.services.aiagentregistry.garden.apiUrl",
                () -> "http://localhost:" + _mockWebServer.getPort());
    }

    @BeforeEach
    void setUp() {
        _mockWebServer.setDispatcher(_serverHelper.getDispatcher().createDispatcher());
        _testAccount = _testUtil.createGardenTestAccount();
        _testAccount.setSyncAuditActionType(ActionEnum.SYNC);
        _testAccount.setSyncAuditUserId(SyncJob.SYNC_SYSTEM_SCHEDULED);
        _testAccount.setSyncAuditIdpAccountId(_testAccount.getIdpAccountId());
        BoomiApiTokenCredentials credentials = new BoomiApiTokenCredentials();
        credentials.setApiToken("test-token");
        credentials.setJwt("sampleJWT");
        credentials.setAccountId("test-account");
        credentials.setUserName("test-user");

        // Set up your mocks
        when(_authorizationParsingService.createAuthorization(anyString(), any()))
                .thenReturn(Mono.just(credentials));
        when(_gardenService.getGardenCredentials(_testAccount))
                .thenReturn(Flux.just(credentials));
    }

    @AfterEach
    void tearDown() {
        _serverHelper.reset();
        _testUtil.cleanupDatabases();
    }

    @Nested
    class TestAgent {
        @Test
        void testAgentAdd() {

            AgentSummary agentSummary = createAgentSummary(AgentSummaryList.AGENT2_SUMMARY);

            when(_gardenService.listAgentsRecursively(_testAccount))
                    .thenReturn(Flux.just(agentSummary));
            when(_limitManager.setAccountLimit(any())).thenReturn(Mono.empty());

            invokeSync(_testAccount);

            //assert there is one version
            assertEquals(1, _versionRepository.findAll().size());
            //assert version name, description, status are all updated
            AiAgentVersion version = _versionRepository.findAll().get(0);
            _verificationService.validateAuditFields(version);

            assertEquals(AgentSummaryList.AGENT2_SUMMARY.get(AGENT_SUMMARY_NAME), version.getName());
            assertEquals(AgentSummaryList.AGENT2_SUMMARY.get(AGENT_SUMMARY_ID), version.getExternalId());
            assertEquals(AgentSummaryList.AGENT2_SUMMARY.get(AGENT_SUMMARY_STATUS), version.getAgentStatus());
            assertEquals(convertResponseDataToJson(AgentSummaryList.AGENT2_SUMMARY.get(AGENT_SUMMARY_OBJECTIVE)), version.getPurpose());
            assertEquals(GeneralUtil.tmStmpFromString(
                    (String) AgentSummaryList.AGENT2_SUMMARY.get(AGENT_SUMMARY_LAST_UPDATED_ON)),
                    version.getUpdatedAtProviderTime());
            assertFalse(version.getIsDeleted());
        }

        @Test
        void testAgentUpdate() {
            AiAgentTestBuilder.AgentSetupParams agentParams = createGardenAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);
            agent.setVersions(Set.of(_testBuilder.setupGardenVersion(agent, createGardenAgent1VersionParams())));
            when(_limitManager.setAccountLimit(any())).thenReturn(Mono.empty());

            AgentSummary agentSummary = createAgentSummary(AgentSummaryList.AGENT1_SUMMARY);

            AgentDetail agentDetail = AgentDetail.builder()
                    .id((String) AgentSummaryList
                            .AGENT1_SUMMARY.get(AGENT_ID))
                    .personalityTraits(com.boomi.aiagentregistry.servlet.response.boomigarden.AgentDetail
                                    .PERSONALITY_TRAITS_JSON1)
                    .build();

            when(_gardenService.listAgentsRecursively(_testAccount))
                    .thenReturn(Flux.just(agentSummary));
            when(_gardenService.getGardenAgentById(_testAccount,
                    (String) AgentSummaryList.AGENT1_SUMMARY.get(AGENT_SUMMARY_ID)))
                    .thenReturn(Flux.just(agentDetail));

            invokeSync(_testAccount);

            //assert there is one draftVersion
            assertEquals(1, _versionRepository.findAll().size());
            //assert version name, description, status are all updated
            AiAgentVersion version = getVersion((String) AgentSummaryList.AGENT1_SUMMARY.get(AGENT_SUMMARY_ID));
            assertEquals(AgentSummaryList.AGENT1_SUMMARY.get(AGENT_SUMMARY_NAME), version.getName());
            assertEquals(AgentSummaryList.AGENT1_SUMMARY.get(AGENT_SUMMARY_ID), version.getExternalId());
            assertEquals(AgentSummaryList.AGENT1_SUMMARY.get(AGENT_SUMMARY_STATUS), version.getAgentStatus());
            assertEquals(GeneralUtil.tmStmpFromString(
                            (String) AgentSummaryList.AGENT1_SUMMARY.get(AGENT_SUMMARY_LAST_UPDATED_ON)),
                    version.getUpdatedAtProviderTime());
            assertFalse(version.getIsDeleted());
        }

        @Test
        void testAgentDelete() {
            AiAgentTestBuilder.AgentSetupParams agentParams = createGardenAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);
            agent.setVersions(Set.of(_testBuilder.setupVersion(agent, createGardenAgent1VersionParams())));

            when(_gardenService.listAgentsRecursively(_testAccount))
                    .thenReturn(Flux.empty());
            when(_limitManager.setAccountLimit(any())).thenReturn(Mono.empty());

            invokeSync(_testAccount);

            //assert the versions is delete flag is set to true
            assertEquals(1, _versionRepository.findAll().size());
            AiAgentVersion version = _versionRepository.findAll().get(0);
            assertTrue(version.getIsDeleted());
            //assert the agent is delete flag is set to true
            assertEquals(1, _aiAgentRepository.findAll().size());
            AiAgent aiAgent = _aiAgentRepository.findAll().get(0);
            assertTrue(aiAgent.getIsDeleted());
        }
    }

    @Nested
    class TestGuardrail {

        @Test
        void testGuardrailAdd() {
            AgentSummary agent1Summary = createAgentSummary(AgentSummaryList.AGENT1_SUMMARY);
            AgentSummary agent2Summary = createAgentSummary(AgentSummaryList.AGENT2_SUMMARY);

            AgentDetail agent1Detail = createAgentDetail(
                    (String) AgentSummaryList.AGENT1_SUMMARY.get(AGENT_ID),
                    com.boomi.aiagentregistry.servlet.response.boomigarden.AgentDetail.PERSONALITY_TRAITS_JSON1,
                    createGuardrail(com.boomi.aiagentregistry.servlet.response.boomigarden.AgentDetail.GUARDRAILS1_ID,
                            com.boomi.aiagentregistry.servlet.response.boomigarden.AgentDetail.GUARDRAILS1));

            AgentDetail agent2Detail = createAgentDetail(
                    (String) AgentSummaryList.AGENT2_SUMMARY.get(AGENT_ID),
                    com.boomi.aiagentregistry.servlet.response.boomigarden.AgentDetail.PERSONALITY_TRAITS_JSON2,
                    createGuardrail(com.boomi.aiagentregistry.servlet.response.boomigarden.AgentDetail.GUARDRAILS1_ID,
                            com.boomi.aiagentregistry.servlet.response.boomigarden.AgentDetail.GUARDRAILS1));

            when(_gardenService.listAgentsRecursively(_testAccount))
                    .thenReturn(Flux.fromIterable(List.of(agent1Summary, agent2Summary)));
            when(_gardenService.getGardenAgentById(_testAccount,
                    (String) AgentSummaryList.AGENT1_SUMMARY.get(AGENT_SUMMARY_ID)))
                    .thenReturn(Flux.just(agent1Detail));
            when(_gardenService.getGardenAgentById(_testAccount,
                    (String) AgentSummaryList.AGENT2_SUMMARY.get(AGENT_SUMMARY_ID)))
                    .thenReturn(Flux.just(agent2Detail));
            when(_limitManager.setAccountLimit(any())).thenReturn(Mono.empty());


            invokeSync(_testAccount);
            AiAgentVersion version = getVersion((String) AgentSummaryList.AGENT1_SUMMARY.get(AGENT_SUMMARY_ID));
            assertEquals(convertResponseDataToJson(com.boomi.aiagentregistry.servlet.response.boomigarden.AgentDetail
                            .PERSONALITY_TRAITS_JSON1),
                    version.getPersonalityTraits());
            assertEquals(2, _guardrailRepo.findAll().size());
            //assert the guardrail is created with data from bedrock
            AiAgentGuardrail guardrail = _guardrailRepo.findAll().get(0);
            assertEquals(com.boomi.aiagentregistry.servlet.response.boomigarden.AgentDetail.GUARDRAILS1_ID,
                    guardrail.getExternalId());
            assertEquals(convertResponseDataToJson(
                    com.boomi.aiagentregistry.servlet.response.boomigarden.AgentDetail.GUARDRAILS1),
                    guardrail.getGuardrailJson());
            assertNotNull(guardrail.getAiAgentProviderAccount());
            assertEquals(PROVIDER, guardrail.getUpdatedByOrigin());

            _verificationService.validateAuditFields(guardrail);

            //assert there are 4 guardrail association
            assertEquals(2, _guardrailAssoRepo.findAll().size());
        }
    }

    private void invokeSync(AiAgentProviderAccount account) {
        StepVerifier.create(_gardenSyncProvider.sync(account))
                .thenConsumeWhile(history -> true) // consume all elements
                .expectComplete()
                .verify();
    }

    private AiAgentVersion getVersion(String externalId) {
        return _versionRepository.findAll()
                .stream()
                .filter(version -> version.getExternalId().equals(externalId))
                .findFirst()
                .get();
    }

    public static String convertResponseDataToJson(Object response) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(response);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON conversion failed", e);
        } catch (ClassCastException e) {
            throw new RuntimeException("Invalid data type", e);
        }
    }

    public static AgentSummary createAgentSummary(Map<String, Object> summaryMap) {
        return AgentSummary.builder()
                .id((String) summaryMap.get(AGENT_SUMMARY_ID))
                .name((String) summaryMap.get(AGENT_SUMMARY_NAME))
                .createdOn((String) summaryMap.get(AGENT_SUMMARY_CREATED_ON))
                .lastUpdatedOn((String) summaryMap.get(AGENT_SUMMARY_LAST_UPDATED_ON))
                .objective(convertResponseDataToJson(summaryMap.get(AGENT_SUMMARY_OBJECTIVE)))
                .agentStatus((String) summaryMap.get(AGENT_SUMMARY_STATUS))
                .build();
    }

    public static AgentDetail createAgentDetail(String guardrailId, String personalityTraitsJson1, Guardrail guardrail) {
        return AgentDetail.builder()
                .id(guardrailId)
                .personalityTraits(convertResponseDataToJson(personalityTraitsJson1))
                .guardrails(guardrail)
                .build();
    }

    public static Guardrail createGuardrail(String guardrailId, String guardrail) {
        return Guardrail.builder()
                .id(guardrailId)
                .guardrailJson(convertResponseDataToJson(guardrail))
                .build();
    }


}
