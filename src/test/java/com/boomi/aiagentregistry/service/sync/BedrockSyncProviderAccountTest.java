package com.boomi.aiagentregistry.service.sync;

import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.constants.TestConstants;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentAlias;
import com.boomi.aiagentregistry.entity.AiAgentGuardrail;
import com.boomi.aiagentregistry.entity.AiAgentLlm;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.entity.AiAgentToolResource;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.repo.AiAgentAliasRepository;
import com.boomi.aiagentregistry.repo.AiAgentGuardrailAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentGuardrailRepository;
import com.boomi.aiagentregistry.repo.AiAgentLargeTextContentRepository;
import com.boomi.aiagentregistry.repo.AiAgentLlmAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentLlmRepository;
import com.boomi.aiagentregistry.repo.AiAgentRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolResourceRepository;
import com.boomi.aiagentregistry.repo.AiAgentVersionRepository;
import com.boomi.aiagentregistry.service.SyncVerificationService;
import com.boomi.aiagentregistry.service.sync.bedrock.BedrockSyncProvider;
import com.boomi.aiagentregistry.servlet.BedrockMockServerHelper;
import com.boomi.aiagentregistry.util.AiAgentTestBuilder;
import com.boomi.aiagentregistry.util.AiAgentTestBuilder.AgentSetupParams;
import com.boomi.aiagentregistry.util.AiAgentTestBuilder.AliasSetupParams;
import com.boomi.aiagentregistry.util.AiAgentTestBuilder.GuardrailSetupParams;
import com.boomi.aiagentregistry.util.AiAgentTestBuilder.LlmSetupParams;
import com.boomi.aiagentregistry.util.AiAgentTestBuilder.VersionSetupParams;
import com.boomi.aiagentregistry.util.GeneralUtil;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.graphql.server.schema.types.AiAgentToolType;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Set;

import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.ToolResourceParams;
import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.ToolSetupParams;
import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.createActionGroup1Params;
import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.createAgent1Alias1Params;
import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.createAgent1Params;
import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.createAgent1Version1Params;
import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.createAgent1VersionDraftParams;
import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.createAgent3Params;
import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.createAgent3Version1Params;
import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.createAgent3VersionDraftParams;
import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.createDataSource1Params;
import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.createGuardrail1Params;
import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.createKnowledgebase1Params;
import static com.boomi.aiagentregistry.util.AiAgentTestBuilder.createLlm1Params;
import static com.boomi.graphql.server.schema.types.AiAgentOriginType.PROVIDER;
import static com.boomi.graphql.server.schema.types.AiAgentProviderType.AWS_BEDROCK;
import static com.boomi.graphql.server.schema.types.AiAgentToolResourceType.AWS_FUNCTION;
import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.COMPLETED;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.VERSION;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;


@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Disabled("Need to fix test methods: testDataSourceAdd & testDataSourceUpdate.")
class BedrockSyncProviderAccountTest extends BaseMockWebServerTest {

    private AiAgentProviderAccount _testAccount;
    @Autowired
    private TestUtil _testUtil;
    @Autowired
    private BedrockSyncProvider _bedrockSyncProvider;
    @Autowired
    private AiAgentRepository _aiAgentRepository;
    @Autowired
    private AiAgentVersionRepository _versionRepository;
    @Autowired
    private BedrockMockServerHelper _serverHelper;
    @Autowired
    private AiAgentTestBuilder _testBuilder;

    @Autowired
    private SyncVerificationService _verificationService;

    @Autowired
    private AiAgentAliasRepository _aliasRepository;

    @Autowired
    private AiAgentLargeTextContentRepository _largeTextRepo;

    @Autowired
    private AiAgentLlmRepository _llmRepo;

    @Autowired
    BedrockTestResponseBuilder _responseBuilder;

    @Autowired
    private AiAgentLlmAssociationRepository _llmAssoRepo;

    @Autowired
    private AiAgentGuardrailRepository _guardrailRepo;

    @Autowired
    private AiAgentGuardrailAssociationRepository _guardrailAssoRepo;

    @Autowired
    private AiAgentToolRepository _toolRepo;

    @Autowired
    private AiAgentToolAssociationRepository _toolAssoRepo;

    @Autowired
    private AiAgentToolResourceRepository _toolResourceRepo;


    @BeforeEach
    void setUp() {
        _mockWebServer.setDispatcher(_serverHelper.getDispatcher().createDispatcher());
        _testAccount = _testUtil.createBedrockTestAccount();

    }

    @AfterEach
    void tearDown() {
        _serverHelper.reset();
        _testUtil.cleanupDatabases();
    }

    @Nested
    class TestAgent {
        @Test
        void testBedrockSyncAgentUpdate() {
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);
            agent.setVersions(Set.of(_testBuilder.setupVersion(agent, createAgent1VersionDraftParams())));
            _responseBuilder.mockAgent1WithVersionDraftResponses();

            invokeSync(_testAccount);

            //one version will be updated
            _verificationService.validateSyncHistory(3, 0, 1, 0, COMPLETED);

            //assert there is one draftVersion
            assertEquals(1, _versionRepository.findAll().size());
            //assert draftVersion name, description, status are all updated
            AiAgentVersion draftVersion = getVersion(TestConstants.Agent1.VersionDraft.VERSION);
            _verificationService.validateAuditFields(draftVersion);
            assertEquals(TestConstants.Agent1.VersionDraft.NAME_FROM_BEDROCK, draftVersion.getName());
            assertEquals(TestConstants.Agent1.VersionDraft.DESCRIPTION_FROM_BEDROCK, draftVersion.getDescription());
            assertEquals(TestConstants.Agent1.VersionDraft.STATUS_FROM_BEDROCK, draftVersion.getAgentStatus());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Agent1.VersionDraft.UPDATED_AT_FROM_BEDROCK), draftVersion.getUpdatedAtProviderTime());
            assertFalse(draftVersion.getIsDeleted());
            assertEquals(PROVIDER, draftVersion.getUpdatedByOrigin());
            assertEquals(PROVIDER, draftVersion.getCreatedByOrigin());

            //make sure the draftVersion's instructions are stored
            assertEquals(
                    TestConstants.Agent1.VersionDraft.INSTRUCTIONS_FROM_BEDROCK,
                    _largeTextRepo.findByRelatedEntityUidAndRelatedEntityType(draftVersion.getUid(), VERSION.name()).getContent()
            );
        }

        @Test
        void testBedrockSyncAgentAdd() {
            _responseBuilder.mockAgent2WithVersionsResponses();
            invokeSync(_testAccount);
            //one agent, one version
            _verificationService.validateSyncHistory(3, 2, 0, 0, COMPLETED);

            //assert there is one version
            assertEquals(1, _versionRepository.findAll().size());
            //assert version name, description, status are all updated
            AiAgentVersion version = _versionRepository.findAll().get(0);
            _verificationService.validateAuditFields(version);

            assertEquals(TestConstants.Agent2.VersionDraft.NAME, version.getName());
            assertEquals(TestConstants.Agent2.VersionDraft.DESCRIPTION, version.getDescription());
            assertEquals(TestConstants.Agent2.VersionDraft.STATUS, version.getAgentStatus());
            assertEquals(TestConstants.Agent2.VersionDraft.VERSION, version.getVersionString());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Agent2.VersionDraft.UPDATED_AT), version.getUpdatedAtProviderTime());
            assertFalse(version.getIsDeleted());
            assertEquals(PROVIDER, version.getUpdatedByOrigin());
            assertEquals(PROVIDER, version.getCreatedByOrigin());

            //make sure the version's instructions are stored
            assertEquals(
                    TestConstants.Agent2.VersionDraft.INSTRUCTIONS,
                    _largeTextRepo.findByRelatedEntityUidAndRelatedEntityType(version.getUid(), VERSION.name()).getContent()
            );
        }

        @Test
        void testBedrockSyncAgentDelete() {
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);
            agent.setVersions(Set.of(_testBuilder.setupVersion(agent, createAgent1VersionDraftParams())));

            invokeSync(_testAccount);

            _verificationService.validateSyncHistory(3, 0, 0, 2, COMPLETED);

            //assert the versions is delete flag is set to true
            assertEquals(1, _versionRepository.findAll().size());
            AiAgentVersion version = _versionRepository.findAll().get(0);
            assertTrue(version.getIsDeleted());
            //assert the agent is delete flag is set to true
            assertEquals(1, _aiAgentRepository.findAll().size());
            AiAgent aiAgent = _aiAgentRepository.findAll().get(0);
            assertTrue(aiAgent.getIsDeleted());
        }

        @Test
        void testBedrockSyncAgentNoAction() {
            AgentSetupParams agentParams = createAgent3Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);
            agent.setVersions(Set.of(_testBuilder.setupVersion(agent, createAgent3VersionDraftParams())));
            _responseBuilder.mockAgent3WithVersionDraftResponse();

            invokeSync(_testAccount);
            _verificationService.validateSyncHistory(3, 0, 0, 0, COMPLETED);

            //assert there is one version
            assertEquals(1, _versionRepository.findAll().size());
            //assert version name, description, status are still the same
            AiAgentVersion version = _versionRepository.findAll().get(0);
            assertEquals(TestConstants.Agent3.VersionDraft.NAME, version.getName());
            assertEquals(TestConstants.Agent3.VersionDraft.DESCRIPTION, version.getDescription());
            assertEquals(TestConstants.Agent3.VersionDraft.STATUS, version.getAgentStatus());
            assertEquals(TestConstants.Agent3.VersionDraft.VERSION, version.getVersionString());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Agent3.VersionDraft.UPDATED_AT), version.getUpdatedAtProviderTime());

            //make sure the version's instructions are stored
            assertEquals(
                    TestConstants.Agent3.VersionDraft.INSTRUCTIONS,
                    _largeTextRepo.findByRelatedEntityUidAndRelatedEntityType(version.getUid(), VERSION.name()).getContent()
            );

        }
    }

    @Nested
    class TestVersion {
        @Test
        void testVersionUpdate() {
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);
            agent.setVersions(Set.of(
                    _testBuilder.setupVersion(agent, createAgent1Version1Params()),
                    _testBuilder.setupVersion(agent, createAgent1VersionDraftParams())));

            _responseBuilder.mockAgent1WithVersionsResponse();

            invokeSync(_testAccount);
            //2 versions will e updated
            _verificationService.validateSyncHistory(4, 0, 2, 0, COMPLETED);
            //assert version 1 is added
            assertEquals(2, _versionRepository.findAll().size());
            //assert version 1 is added
            AiAgentVersion version = getVersion(TestConstants.Agent1.Version1.VERSION);
            assertEquals(TestConstants.Agent1.Version1.NAME_FROM_BEDROCK, version.getName());
            assertEquals(TestConstants.Agent1.Version1.DESCRIPTION_FROM_BEDROCK, version.getDescription());
            assertEquals(TestConstants.Agent1.Version1.STATUS_FROM_BEDROCK, version.getAgentStatus());
            assertEquals(TestConstants.Agent1.Version1.VERSION, version.getVersionString());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Agent1.Version1.UPDATED_AT_FROM_BEDROCK), version.getUpdatedAtProviderTime());
            assertFalse(version.getIsDeleted());
            assertEquals(PROVIDER, version.getUpdatedByOrigin());
            assertEquals(PROVIDER, version.getCreatedByOrigin());
            _verificationService.validateAuditFields(version);
            //make sure the version's instructions are stored
            assertEquals(
                    TestConstants.Agent1.Version1.INSTRUCTIONS_FROM_BEDROCK,
                    _largeTextRepo.findByRelatedEntityUidAndRelatedEntityType(version.getUid(), VERSION.name()).getContent()
            );

            //assert the same thing for the draft version
            version = getVersion(TestConstants.Agent1.VersionDraft.VERSION);
            assertEquals(TestConstants.Agent1.VersionDraft.NAME_FROM_BEDROCK, version.getName());
            assertEquals(TestConstants.Agent1.VersionDraft.DESCRIPTION_FROM_BEDROCK, version.getDescription());
            assertEquals(TestConstants.Agent1.VersionDraft.STATUS_FROM_BEDROCK, version.getAgentStatus());
            assertEquals(TestConstants.Agent1.VersionDraft.VERSION, version.getVersionString());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Agent1.VersionDraft.UPDATED_AT_FROM_BEDROCK), version.getUpdatedAtProviderTime());
            assertFalse(version.getIsDeleted());
            assertEquals(PROVIDER, version.getUpdatedByOrigin());
            assertEquals(PROVIDER, version.getCreatedByOrigin());
            _verificationService.validateAuditFields(version);
            //make sure the version's instructions are stored
            assertEquals(
                    TestConstants.Agent1.VersionDraft.INSTRUCTIONS_FROM_BEDROCK,
                    _largeTextRepo.findByRelatedEntityUidAndRelatedEntityType(version.getUid(), VERSION.name()).getContent()
            );
        }

        @Test
        void testVersionDelete() {
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);
            agent.setVersions(Set.of(
                    _testBuilder.setupVersion(agent, createAgent1Version1Params()),
                    _testBuilder.setupVersion(agent, createAgent1VersionDraftParams())));

            invokeSync(_testAccount);
            _verificationService.validateSyncHistory(4, 0, 0, 3, COMPLETED);
            assertEquals(2, _versionRepository.findAll().size());
            //assert version 1 is marked as deleted
            AiAgentVersion version = getVersion(TestConstants.Agent1.Version1.VERSION);
            assertTrue(version.getIsDeleted());
            //assert draft version is deleted
            version = getVersion(TestConstants.Agent1.VersionDraft.VERSION);
            assertTrue(version.getIsDeleted());
        }

        @Test
        void testVersionAdd() {
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            _testBuilder.setupAgent(agentParams);

            _responseBuilder.mockAgent1WithVersionsResponse();

            invokeSync(_testAccount);
            //2 versions will be added
            _verificationService.validateSyncHistory(4, 2, 0, 0, COMPLETED);
            //assert version 1 is added
            assertEquals(2, _versionRepository.findAll().size());
            //assert version 1 is added
            AiAgentVersion version = getVersion(TestConstants.Agent1.Version1.VERSION);
            assertEquals(TestConstants.Agent1.Version1.NAME_FROM_BEDROCK, version.getName());
            assertEquals(TestConstants.Agent1.Version1.DESCRIPTION_FROM_BEDROCK, version.getDescription());
            assertEquals(TestConstants.Agent1.Version1.STATUS_FROM_BEDROCK, version.getAgentStatus());
            assertEquals(TestConstants.Agent1.Version1.VERSION, version.getVersionString());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Agent1.Version1.UPDATED_AT_FROM_BEDROCK), version.getUpdatedAtProviderTime());
            assertFalse(version.getIsDeleted());
            assertEquals(PROVIDER, version.getUpdatedByOrigin());
            assertEquals(PROVIDER, version.getCreatedByOrigin());
            _verificationService.validateAuditFields(version);
            //make sure the version's instructions are stored
            assertEquals(
                    TestConstants.Agent1.Version1.INSTRUCTIONS_FROM_BEDROCK,
                    _largeTextRepo.findByRelatedEntityUidAndRelatedEntityType(version.getUid(), VERSION.name()).getContent()
            );

            //assert the same thing for the draft version
            version = getVersion(TestConstants.Agent1.VersionDraft.VERSION);
            assertEquals(TestConstants.Agent1.VersionDraft.NAME_FROM_BEDROCK, version.getName());
            assertEquals(TestConstants.Agent1.VersionDraft.DESCRIPTION_FROM_BEDROCK, version.getDescription());
            assertEquals(TestConstants.Agent1.VersionDraft.STATUS_FROM_BEDROCK, version.getAgentStatus());
            assertEquals(TestConstants.Agent1.VersionDraft.VERSION, version.getVersionString());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Agent1.VersionDraft.UPDATED_AT_FROM_BEDROCK), version.getUpdatedAtProviderTime());
            assertFalse(version.getIsDeleted());
            assertEquals(PROVIDER, version.getUpdatedByOrigin());
            assertEquals(PROVIDER, version.getCreatedByOrigin());
            _verificationService.validateAuditFields(version);
            //make sure the version's instructions are stored
            assertEquals(
                    TestConstants.Agent1.VersionDraft.INSTRUCTIONS_FROM_BEDROCK,
                    _largeTextRepo.findByRelatedEntityUidAndRelatedEntityType(version.getUid(), VERSION.name()).getContent()
            );
        }

        @Test
        void testVersionNoAction() {
            AgentSetupParams agentParams = createAgent3Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);
            VersionSetupParams versionDraftParams = createAgent3VersionDraftParams();
            VersionSetupParams version1Params = createAgent3Version1Params();

            agent.setVersions(Set.of(
                    _testBuilder.setupVersion(agent, versionDraftParams),
                    _testBuilder.setupVersion(agent, version1Params)));

            _responseBuilder.mockAgent3WithVersionsResponse();

            invokeSync(_testAccount);

            _verificationService.validateSyncHistory(4, 0, 0, 0, COMPLETED);
            //assert draft version and version 1 did not change
            assertEquals(2, _versionRepository.findAll().size());
            //assert version 1 is added
            AiAgentVersion version = getVersion(TestConstants.Agent3.Version1.VERSION);
            assertEquals(TestConstants.Agent3.Version1.NAME, version.getName());
            assertEquals(TestConstants.Agent3.Version1.DESCRIPTION, version.getDescription());
            assertEquals(TestConstants.Agent3.Version1.STATUS, version.getAgentStatus());
            assertEquals(TestConstants.Agent3.Version1.VERSION, version.getVersionString());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Agent3.Version1.UPDATED_AT), version.getUpdatedAtProviderTime());
            //make sure the version's instructions are stored
            assertEquals(
                    TestConstants.Agent3.Version1.INSTRUCTIONS,
                    _largeTextRepo.findByRelatedEntityUidAndRelatedEntityType(version.getUid(), VERSION.name()).getContent()
            );
            //assert draft version is added
            version = getVersion(TestConstants.Agent3.VersionDraft.VERSION);
            assertEquals(TestConstants.Agent3.VersionDraft.NAME, version.getName());
            assertEquals(TestConstants.Agent3.VersionDraft.DESCRIPTION, version.getDescription());
            assertEquals(TestConstants.Agent3.VersionDraft.STATUS, version.getAgentStatus());
            assertEquals(TestConstants.Agent3.VersionDraft.VERSION, version.getVersionString());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Agent3.VersionDraft.UPDATED_AT), version.getUpdatedAtProviderTime());
            //make sure the version's instructions are stored
            assertEquals(
                    TestConstants.Agent3.VersionDraft.INSTRUCTIONS,
                    _largeTextRepo.findByRelatedEntityUidAndRelatedEntityType(version.getUid(), VERSION.name()).getContent()
            );
        }
    }

    @Nested
    class TestAlias {
        @Test
        void testAliasUpdate() {
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);
            VersionSetupParams version1Params = createAgent1Version1Params();
            //to prevent update
            version1Params.setUpdatedAt(TestConstants.Agent1.Version1.UPDATED_AT_FROM_BEDROCK);

            AiAgentVersion version1 = _testBuilder.setupVersion(agent, version1Params);

            AliasSetupParams aliasParams = createAgent1Alias1Params();
            _testBuilder.setupAlias(version1, aliasParams);

            agent.setVersions(Set.of(version1));

            _responseBuilder.mockAgent1WithVersion1Response();

            _responseBuilder.mockAliasForUpdate();
            invokeSync(_testAccount);
            //one alias will be updated
            _verificationService.validateSyncHistory(3, 0, 1, 0, COMPLETED);
            //assert the alias is updated with data from aws
            assertEquals(1, _aliasRepository.findAll().size());
            AiAgentAlias alias = _aliasRepository.findAll().get(0);
            assertEquals(TestConstants.Agent1.Alias1.EXTERNAL_ID, alias.getExternalId());
            assertEquals(TestConstants.Agent1.Alias1.NAME_FROM_BEDROCK, alias.getName());
            assertEquals(TestConstants.Agent1.Alias1.DESCRIPTION_FROM_BEDROCK, alias.getDescription());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Agent1.Alias1.UPDATED_AT_FROM_BEDROCK), alias.getUpdatedAtProviderTime());
            assertEquals(PROVIDER, alias.getUpdatedByOrigin());
            assertEquals(version1.getVersionString(), alias.getAgentVersion().getVersionString());
            _verificationService.validateAuditFields(alias);
        }

        @Test
        void testAliasDelete() {
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);
            VersionSetupParams version1Params = createAgent1Version1Params();
            //to prevent update
            version1Params.setUpdatedAt(TestConstants.Agent1.Version1.UPDATED_AT_FROM_BEDROCK);

            AiAgentVersion version1 = _testBuilder.setupVersion(agent, version1Params);
            AliasSetupParams aliasParams = createAgent1Alias1Params();
            _testBuilder.setupAlias(version1, aliasParams);
            agent.setVersions(Set.of(version1));
            _responseBuilder.mockAgent1WithVersion1Response();
            invokeSync(_testAccount);
            //1 alias will be removed
            _verificationService.validateSyncHistory(3, 0, 0, 1, COMPLETED);
            //assert the alias is deleted
            assertEquals(1, _aliasRepository.findAll().size());
            AiAgentAlias alias = _aliasRepository.findAll().get(0);
            assertTrue(alias.getIsDeleted());
        }

        @Test
        void testAliasAdd() {
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);
            VersionSetupParams version1Params = createAgent1Version1Params();
            //to prevent update
            version1Params.setUpdatedAt(TestConstants.Agent1.Version1.UPDATED_AT_FROM_BEDROCK);

            AiAgentVersion version1 = _testBuilder.setupVersion(agent, version1Params);

            agent.setVersions(Set.of(version1));

            _responseBuilder.mockAgent1WithVersion1Response();

            _responseBuilder.mockAliasForUpdate();

            invokeSync(_testAccount);
            //1 alais will be added
            _verificationService.validateSyncHistory(3, 1, 0, 0, COMPLETED);
            //assert the alias is created
            assertEquals(1, _aliasRepository.findAll().size());
            AiAgentAlias alias = _aliasRepository.findAll().get(0);
            assertEquals(TestConstants.Agent1.Alias1.EXTERNAL_ID, alias.getExternalId());
            assertEquals(TestConstants.Agent1.Alias1.NAME_FROM_BEDROCK, alias.getName());
            assertEquals(TestConstants.Agent1.Alias1.DESCRIPTION_FROM_BEDROCK, alias.getDescription());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Agent1.Alias1.UPDATED_AT_FROM_BEDROCK), alias.getUpdatedAtProviderTime());
            assertEquals(PROVIDER, alias.getUpdatedByOrigin());
            assertEquals(version1.getVersionString(), alias.getAgentVersion().getVersionString());
            _verificationService.validateAuditFields(alias);
        }

        @Test
        void testAliasNoAction() {
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);
            VersionSetupParams version1Params = createAgent1Version1Params();
            //to prevent update
            version1Params.setUpdatedAt(TestConstants.Agent1.Version1.UPDATED_AT_FROM_BEDROCK);

            AiAgentVersion version1 = _testBuilder.setupVersion(agent, version1Params);
            AliasSetupParams aliasParams = createAgent1Alias1Params();
            //set same as bedrock to prevent update
            aliasParams.setUpdatedAt(TestConstants.Agent1.Alias1.UPDATED_AT_FROM_BEDROCK);

            _testBuilder.setupAlias(version1, aliasParams);

            agent.setVersions(Set.of(version1));

            _responseBuilder.mockAgent1WithVersion1Response();

            _responseBuilder.mockAliasForUpdate();

            invokeSync(_testAccount);
            _verificationService.validateSyncHistory(3, 0, 0, 0, COMPLETED);
            //assert the alias is created
            assertEquals(1, _aliasRepository.findAll().size());
            AiAgentAlias alias = _aliasRepository.findAll().get(0);
            assertEquals(TestConstants.Agent1.Alias1.EXTERNAL_ID, alias.getExternalId());
            assertEquals(TestConstants.Agent1.Alias1.NAME, alias.getName());
            assertEquals(TestConstants.Agent1.Alias1.DESCRIPTION, alias.getDescription());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Agent1.Alias1.UPDATED_AT_FROM_BEDROCK), alias.getUpdatedAtProviderTime());
            assertEquals(version1.getVersionString(), alias.getAgentVersion().getVersionString());
            _verificationService.validateAuditFields(alias);
        }
    }

    @Nested
    class TestLlm {
        @Test
        void testLlmAdd() {
            //we will create 2 agents and each will have 2 versions, all versions will have the same llm to test
            // concurrent processing
            _responseBuilder.mockAddLlmResponseForAdd();

            invokeSync(_testAccount);
            //2 agents, 4 versions and 1 llm will be added
            _verificationService.validateSyncHistory(7, 7, 0, 0, COMPLETED);
            //assert there is 1 llm
            assertEquals(1, _llmRepo.findAll().size());
            //assert the llm is created with data from bedrock
            AiAgentLlm llm = _llmRepo.findAll().get(0);
            assertEquals(TestConstants.Llm1.NAME, llm.getName());
            assertEquals(TestConstants.Llm1.VERSION, llm.getVersionString());
            assertEquals(TestConstants.Llm1.EXTERNAL_ID, llm.getExternalId());

            //assert there are 4 llm association
            assertEquals(4, _llmAssoRepo.findAll().size());
        }

        @Test
        void testLlmUpdate() {
            LlmSetupParams llmParams = createLlm1Params(_testAccount);
            _testBuilder.setupLlm(llmParams);
            _responseBuilder.mockAddLlmResponseForUpdate();
            invokeSync(_testAccount);
            //no updates for llm since they are not updatable
            _verificationService.validateSyncHistory(3, 2, 0, 0, COMPLETED);
            //assert there is 1 llm
            assertEquals(1, _llmRepo.findAll().size());
            //assert there are 1 llm association
            assertEquals(1, _llmAssoRepo.findAll().size());

        }


        @Test
        void testLlmDelete() {
            //we will create an agent with one   draft and only since only draft version's llm is updateable
            //then set the llms , then in bedrock response remove the llms which should cause the llm to be deleted
            AgentSetupParams agentParams = createAgent3Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);

            VersionSetupParams versionDraftParams = createAgent3VersionDraftParams();


            LlmSetupParams llmParams = createLlm1Params(_testAccount);

            AiAgentLlm llm = _testBuilder.setupLlm(llmParams);

            AiAgentVersion versionDraft = _testBuilder.setupVersion(agent, versionDraftParams);

            _testBuilder.setupLlmAssociation(versionDraft, llm);

            _responseBuilder.mockAgent3WithVersionDraftResponse();

            invokeSync(_testAccount);
            //the llm will be removed and
            _verificationService.validateSyncHistory(3, 0, 0, 1, COMPLETED);

            //assert llm is deleted
            assertEquals(0, _llmRepo.findAll().size());
            //assert association is deleted
            assertEquals(0, _llmAssoRepo.findAll().size());
        }
    }

    @Nested
    class TestGuardrail {

        @Test
        void testGuardrailAdd() {
            //we will create 2 agents and each will have 2 versions, all versions will have the same guardrail to test
            // concurrent processing
            _responseBuilder.mockAddGrResponseForAdd();

            invokeSync(_testAccount);
            //2 agents, 4 versions and 1 guardrail will be added
            _verificationService.validateSyncHistory(7, 7, 0, 0, COMPLETED);
            //assert there is 1 guardrail
            assertEquals(1, _guardrailRepo.findAll().size());
            //assert the guardrail is created with data from bedrock
            AiAgentGuardrail guardrail = _guardrailRepo.findAll().get(0);
            assertEquals(TestConstants.Agent1.VersionDraft.GUARDRAIL, guardrail.getExternalId());
            assertNotNull(guardrail.getDescription());
            assertNotNull(guardrail.getGuardrailJson());
            assertNotNull(guardrail.getAiAgentProviderAccount());
            assertNotNull(guardrail.getUpdatedAtProviderTime());
            assertEquals(PROVIDER, guardrail.getUpdatedByOrigin());

            _verificationService.validateAuditFields(guardrail);

            //assert there are 4 guardrail association
            assertEquals(4, _guardrailAssoRepo.findAll().size());
        }

        @Test
        void testGuardrailUpdate() {
            GuardrailSetupParams guardrailParams = createGuardrail1Params(_testAccount);
            _testBuilder.setupGuardrail(guardrailParams);
            _responseBuilder.mockAddGrResponseForUpdate();
            invokeSync(_testAccount);
            _verificationService.validateSyncHistory(3, 2, 1, 0, COMPLETED);
            //assert there is 1 guardrail
            assertEquals(1, _guardrailRepo.findAll().size());
            AiAgentGuardrail guardrail = _guardrailRepo.findAll().get(0);
            assertEquals(TestConstants.Guardrail1.NAME_FROM_BEDROCK, guardrail.getName());
            assertEquals(TestConstants.Guardrail1.DESCRIPTION_FROM_BEDROCK, guardrail.getDescription());
            assertEquals(TestConstants.Guardrail1.EXTERNAL_ID, guardrail.getExternalId());
            assertEquals(TestConstants.Guardrail1.VERSION_STRING, guardrail.getVersionString());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Guardrail1.UPDATED_AT_FROM_BEDROCK), guardrail.getUpdatedAtProviderTime());
            assertEquals(PROVIDER, guardrail.getUpdatedByOrigin());
            assertNotNull(guardrail.getGuardrailJson());
            assertNotNull(guardrail.getAiAgentProviderAccount());
            _verificationService.validateAuditFields(guardrail);

            //assert there are 1 guardrail association
            assertEquals(1, _guardrailAssoRepo.findAll().size());

        }

        @Test
        void testGuardrailDelete() {
            //we will create an agent with one draft  version a draft since only versions in draft status can be updated
            //then set the guardrail of the version, then in bedrock response remove the guardrail which should cause the llm to be deleted
            AgentSetupParams agentParams = createAgent3Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);

            VersionSetupParams versionDraftParams = createAgent3VersionDraftParams();

            GuardrailSetupParams grParams = createGuardrail1Params(_testAccount);

            AiAgentGuardrail gr = _testBuilder.setupGuardrail(grParams);

            AiAgentVersion versionDraft = _testBuilder.setupVersion(agent, versionDraftParams);

            _testBuilder.setupGrAssociation(versionDraft, gr);

            _responseBuilder.mockAgent3WithVersionDraftResponse();

            invokeSync(_testAccount);
            _verificationService.validateSyncHistory(3, 0, 0, 1, COMPLETED);

            //assert llm is deleted
            assertEquals(0, _guardrailRepo.findAll().size());
            //assert association is deleted
            assertEquals(0, _guardrailAssoRepo.findAll().size());
        }

        @Test
        void testGuardrailNoAction() {
            //we will create an agent with a version and one guardrail that is up to date
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);

            VersionSetupParams version1Params = createAgent1Version1Params();
            version1Params.setUpdatedAt(TestConstants.Agent1.Version1.UPDATED_AT_FROM_BEDROCK);
            AiAgentVersion version1 = _testBuilder.setupVersion(agent, version1Params);

            GuardrailSetupParams grParams = createGuardrail1Params(_testAccount);
            grParams.setUpdatedAt(TestConstants.Guardrail1.UPDATED_AT_FROM_BEDROCK);
            AiAgentGuardrail gr = _testBuilder.setupGuardrail(grParams);

            _testBuilder.setupGrAssociation(version1, gr);

            agent.setVersions(Set.of(version1));

            _responseBuilder.mockAgent1WithVersion1ResponseForGrNoAction();

            invokeSync(_testAccount);

            _verificationService.validateSyncHistory(3, 0, 0, 0, COMPLETED);
            //assert there is 1 guardrail
            assertEquals(1, _guardrailRepo.findAll().size());
            //assert there are 1 guardrail association
            assertEquals(1, _guardrailAssoRepo.findAll().size());
            //original data of guardrail is preserved
            AiAgentGuardrail guardrail = _guardrailRepo.findAll().get(0);
            assertEquals(TestConstants.Guardrail1.NAME, guardrail.getName());
            assertEquals(TestConstants.Guardrail1.DESCRIPTION, guardrail.getDescription());
            assertEquals(TestConstants.Guardrail1.EXTERNAL_ID, guardrail.getExternalId());
            assertEquals(TestConstants.Guardrail1.VERSION_STRING, guardrail.getVersionString());
            assertNotNull(guardrail.getAiAgentProviderAccount());

        }
    }

    @Nested
    class TestKnowledgebase {
        @Test
        void testKnowledgebaseAdd() {
            //we will create 2 agents and each will have 2 versions, all versions will have the same 2 knowledgebase to test
            // concurrent processing
            _responseBuilder.mockKnowledgebaseAdd();

            invokeSync(_testAccount);
            //2 agents, 4 versions and 2 knowledgebase will be added
            _verificationService.validateSyncHistory(7, 8, 0, 0, COMPLETED);

            //assert there is 2 knowledgebase
            assertEquals(2, _toolRepo.findAll().size());
            //assert the knowledgebase data
            AiAgentTool tool = _toolRepo.findAll()
                    .stream()
                    .filter(t -> t.getExternalId().equals(TestConstants.Knowledgebase1.EXTERNAL_ID))
                    .findFirst()
                    .get();

            assertEquals(TestConstants.Knowledgebase1.NAME_FROM_BEDROCK, tool.getName());
            assertEquals(TestConstants.Knowledgebase1.DESCRIPTION_FROM_BEDROCK, tool.getDescription());
            assertEquals(TestConstants.Knowledgebase1.EXTERNAL_ID, tool.getExternalId());
            assertEquals(TestConstants.Knowledgebase1.STATUS_FROM_BEDROCK, tool.getStatus());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Knowledgebase1.UPDATED_AT_FROM_BEDROCK), tool.getUpdatedAtProviderTime());
            assertEquals(PROVIDER, tool.getUpdatedByOrigin());
            assertNotNull(tool.getAiAgentProviderAccount());
            _verificationService.validateAuditFields(tool);

            tool = _toolRepo.findAll()
                    .stream()
                    .filter(t -> t.getExternalId().equals(TestConstants.Knowledgebase2.EXTERNAL_ID))
                    .findFirst()
                    .get();
            assertEquals(TestConstants.Knowledgebase2.NAME_FROM_BEDROCK, tool.getName());
            assertEquals(TestConstants.Knowledgebase2.DESCRIPTION_FROM_BEDROCK, tool.getDescription());
            assertEquals(TestConstants.Knowledgebase2.EXTERNAL_ID, tool.getExternalId());
            assertEquals(TestConstants.Knowledgebase2.STATUS_FROM_BEDROCK, tool.getStatus());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Knowledgebase2.UPDATED_AT_FROM_BEDROCK), tool.getUpdatedAtProviderTime());
            assertEquals(PROVIDER, tool.getUpdatedByOrigin());
            assertNotNull(tool.getAiAgentProviderAccount());
            _verificationService.validateAuditFields(tool);
            //assert there are 8 association , 2 for each version
            assertEquals(8, _toolAssoRepo.findAll().size());
            //ensure each version has 2 assoications
            List<AiAgentVersion> versions = _versionRepository.findAll();
            versions.forEach(v -> {
                assertEquals(2, _toolAssoRepo.findAll()
                        .stream()
                        .filter(a -> a.getRelatedEntityUid() == v.getUid())
                        .count()
                );
            });
        }

        @Test
        void testKnowledgebaseUpdate() {
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);

            ToolSetupParams toolParams = createKnowledgebase1Params(_testAccount);
            VersionSetupParams version1 = createAgent1Version1Params();
            version1.setUpdatedAt(TestConstants.Agent1.Version1.UPDATED_AT_FROM_BEDROCK);

            version1.setTools(List.of(toolParams));
            agent.setVersions(Set.of(_testBuilder.setupVersion(agent, version1)));

            _responseBuilder.mockKnowledgebaseUpdate();
            invokeSync(_testAccount);

            _verificationService.validateSyncHistory(3, 0, 1, 0, COMPLETED);
            //assert there is 1 knowledgebase
            assertEquals(1, _toolRepo.findAll().size());
            AiAgentTool tool = _toolRepo.findAll().get(0);
            assertEquals(TestConstants.Knowledgebase1.NAME_FROM_BEDROCK, tool.getName());
            assertEquals(TestConstants.Knowledgebase1.DESCRIPTION_FROM_BEDROCK, tool.getDescription());
            assertEquals(TestConstants.Knowledgebase1.EXTERNAL_ID, tool.getExternalId());
            assertEquals(TestConstants.Knowledgebase1.STATUS_FROM_BEDROCK, tool.getStatus());
            assertEquals(GeneralUtil.tmStmpFromInst(TestConstants.Knowledgebase1.UPDATED_AT_FROM_BEDROCK), tool.getUpdatedAtProviderTime());
            assertEquals(PROVIDER, tool.getUpdatedByOrigin());
            assertNotNull(tool.getAiAgentProviderAccount());
            _verificationService.validateAuditFields(tool);

            //assert there are 1 knowledgebase association
            assertEquals(1, _toolAssoRepo.findAll().size());

        }

        @Test
        void testKnowledgebaseDelete() {
            //we will create an agent with two versions a draft and non draft
            //then set the knowledgebase of both, then in bedrock response return no kbs which should cause the llm to be deleted
            AgentSetupParams agentParams = createAgent3Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);

            VersionSetupParams versionDraftParams = createAgent3VersionDraftParams();
            VersionSetupParams version1Params = createAgent3Version1Params();

            AiAgentVersion versionDraft = _testBuilder.setupVersion(agent, versionDraftParams);
            AiAgentVersion version1 = _testBuilder.setupVersion(agent, version1Params);

            ToolSetupParams toolParams = createKnowledgebase1Params(_testAccount);
            //create kb and associate it with draft version
            AiAgentTool kb = _testBuilder.setupTool(versionDraft, toolParams);
            //associate kb with version 1
            _testBuilder.setupToolAssociation(version1, kb);

            agent.setVersions(Set.of(versionDraft, version1));

            _responseBuilder.mockAgent3WithVersionsResponse();

            invokeSync(_testAccount);
            _verificationService.validateSyncHistory(4, 0, 0, 1, COMPLETED);

            //assert the knowledgebase is deleted
            assertEquals(0, _toolRepo.findAll().size());
            //assert association is deleted
            assertEquals(0, _toolAssoRepo.findAll().size());
        }

        @Test
        void testKnowledgebaseNoAction() {
            //we will create an agent with a version and one knowledgebase that is up to date
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);

            ToolSetupParams toolParams = createKnowledgebase1Params(_testAccount);
            toolParams.setUpdatedAt(TestConstants.Knowledgebase1.UPDATED_AT_FROM_BEDROCK);

            VersionSetupParams version1 = createAgent1Version1Params();
            version1.setUpdatedAt(TestConstants.Agent1.Version1.UPDATED_AT_FROM_BEDROCK);

            version1.setTools(List.of(toolParams));
            agent.setVersions(Set.of(_testBuilder.setupVersion(agent, version1)));

            _responseBuilder.mockKnowledgebaseUpdate();

            invokeSync(_testAccount);

            _verificationService.validateSyncHistory(3, 0, 0, 0, COMPLETED);
            //assert there is 1 knowledgebase
            assertEquals(1, _toolRepo.findAll().size());
            //assert there are 1 knowledgebase association
            assertEquals(1, _toolAssoRepo.findAll().size());
            //original data of knowledgebase is preserved
            AiAgentTool kb = _toolRepo.findAll().get(0);
            assertEquals(TestConstants.Knowledgebase1.NAME, kb.getName());
            assertEquals(TestConstants.Knowledgebase1.DESCRIPTION, kb.getDescription());
            assertEquals(TestConstants.Knowledgebase1.EXTERNAL_ID, kb.getExternalId());
            assertEquals(TestConstants.Knowledgebase1.STATUS, kb.getStatus());
            assertNotNull(kb.getAiAgentProviderAccount());

        }
    }

    @Nested
    class TestActionGroup {
        @Test
        void testActionGroupAdd() {
            //we will create 1 agent with 3 versions and they all will share the same action group to test concurrency
            _responseBuilder.mockActionGroupAdd();
            invokeSync(_testAccount);
            _verificationService.validateSyncHistory(5, 5, 0, 0, COMPLETED);
            //assert there is 1 action group
            assertEquals(1, _toolRepo.findAll().size());
            //assert the action group data
            AiAgentTool tool = _toolRepo.findAll()
                    .stream()
                    .filter(t -> t.getExternalId().equals(TestConstants.Agent1.ActionGroup1.EXTERNAL_ID))
                    .findFirst()
                    .get();
            assertEquals(TestConstants.Agent1.ActionGroup1.NAME_FROM_BEDROCK, tool.getName());
            assertEquals(TestConstants.Agent1.ActionGroup1.DESCRIPTION_FROM_BEDROCK, tool.getDescription());
            assertEquals(TestConstants.Agent1.ActionGroup1.EXTERNAL_ID, tool.getExternalId());
            assertEquals(
                    GeneralUtil.tmStmpFromInst(TestConstants.Agent1.ActionGroup1.UPDATED_AT_FROM_BEDROCK),
                    tool.getUpdatedAtProviderTime());
            assertEquals(PROVIDER, tool.getUpdatedByOrigin());
            assertNotNull(tool.getAiAgentProviderAccount());
            assertEquals(AiAgentToolType.AWS_ACTION_GROUP, tool.getToolType());
            assertEquals(TestConstants.Agent1.ActionGroup1.STATUS_FROM_BEDROCK, tool.getStatus());
            assertEquals(AWS_BEDROCK, tool.getToolProvider());
            //assert there are 3 association, 1 for each version
            assertEquals(3, _toolAssoRepo.findAll().size());
            //ensure each version has 1 assoications
            List<AiAgentVersion> versions = _versionRepository.findAll();
            versions.forEach(v -> {
                assertEquals(1, _toolAssoRepo.findAll()
                        .stream()
                        .filter(a -> a.getRelatedEntityUid() == v.getUid())
                        .count()
                );
            });

            _verificationService.validateAuditFields(tool);
        }

        @Test
        void testActionGroupUpdate() {
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);
            VersionSetupParams versionDraft = createAgent1VersionDraftParams();
            versionDraft.setUpdatedAt(TestConstants.Agent1.VersionDraft.UPDATED_AT_FROM_BEDROCK);
            versionDraft.setTools(List.of(createActionGroup1Params(_testAccount)));
            agent.setVersions(Set.of(_testBuilder.setupVersion(agent, versionDraft)));

            _responseBuilder.mockActionGroupUpdate();
            invokeSync(_testAccount);
            _verificationService.validateSyncHistory(3, 0, 1, 0, COMPLETED);
            //assert there is 1 action group
            assertEquals(1, _toolRepo.findAll().size());
            //assert the action group data
            AiAgentTool tool = _toolRepo.findAll().get(0);
            assertEquals(TestConstants.Agent1.ActionGroup1.NAME_FROM_BEDROCK, tool.getName());
            assertEquals(TestConstants.Agent1.ActionGroup1.DESCRIPTION_FROM_BEDROCK, tool.getDescription());
            assertEquals(TestConstants.Agent1.ActionGroup1.EXTERNAL_ID, tool.getExternalId());
            assertEquals(
                    GeneralUtil.tmStmpFromInst(TestConstants.Agent1.ActionGroup1.UPDATED_AT_FROM_BEDROCK),
                    tool.getUpdatedAtProviderTime());
            assertEquals(PROVIDER, tool.getUpdatedByOrigin());
            assertNotNull(tool.getAiAgentProviderAccount());
            assertEquals(AiAgentToolType.AWS_ACTION_GROUP, tool.getToolType());
            assertEquals(TestConstants.Agent1.ActionGroup1.STATUS_FROM_BEDROCK, tool.getStatus());
            assertEquals(AWS_BEDROCK, tool.getToolProvider());

            _verificationService.validateAuditFields(tool);
        }

        @Test
        void testActionGroupDelete() {
            //we will create an agent with two versions a draft and non draft
            //then set the action group of both, then in bedrock response return no action groups which should cause the action group to be deleted
            AgentSetupParams agentParams = createAgent3Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);

            VersionSetupParams versionDraftParams = createAgent3VersionDraftParams();
            VersionSetupParams version1Params = createAgent3Version1Params();


            AiAgentVersion versionDraft = _testBuilder.setupVersion(agent, versionDraftParams);
            AiAgentVersion version1 = _testBuilder.setupVersion(agent, version1Params);

            ToolSetupParams toolParams = createActionGroup1Params(_testAccount);
            //create action group and associate it with draft version
            AiAgentTool actionGroup = _testBuilder.setupTool(versionDraft, toolParams);
            //associate action group with version 1
            _testBuilder.setupToolAssociation(version1, actionGroup);

            agent.setVersions(Set.of(versionDraft, version1));

            _responseBuilder.mockAgent3WithVersionsResponse();

            invokeSync(_testAccount);
            _verificationService.validateSyncHistory(4, 0, 0, 1, COMPLETED);

            //assert llm is deleted
            assertEquals(0, _toolRepo.findAll().size());
            //assert association is deleted
            assertEquals(0, _toolAssoRepo.findAll().size());
        }

        @Test
        void testActionGroupNoAction() {
            //we will create an agent with a version and one action group that is up to date
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);

            ToolSetupParams toolParams = createActionGroup1Params(_testAccount);
            toolParams.setUpdatedAt(TestConstants.Agent1.ActionGroup1.UPDATED_AT_FROM_BEDROCK);

            VersionSetupParams versionDraft = createAgent1VersionDraftParams();
            versionDraft.setUpdatedAt(TestConstants.Agent1.VersionDraft.UPDATED_AT_FROM_BEDROCK);

            versionDraft.setTools(List.of(toolParams));
            agent.setVersions(Set.of(_testBuilder.setupVersion(agent, versionDraft)));

            _responseBuilder.mockActionGroupUpdate();

            invokeSync(_testAccount);

            _verificationService.validateSyncHistory(3, 0, 0, 0, COMPLETED);
            //assert there is 1 action group
            assertEquals(1, _toolRepo.findAll().size());
            //assert there are 1 action group association
            assertEquals(1, _toolAssoRepo.findAll().size());
            //original data of action group is preserved
            AiAgentTool actionGroup = _toolRepo.findAll().get(0);
            assertEquals(TestConstants.Agent1.ActionGroup1.NAME, actionGroup.getName());
            assertEquals(TestConstants.Agent1.ActionGroup1.DESCRIPTION, actionGroup.getDescription());
            assertEquals(TestConstants.Agent1.ActionGroup1.EXTERNAL_ID, actionGroup.getExternalId());
            assertEquals(TestConstants.Agent1.ActionGroup1.STATUS, actionGroup.getStatus());
            assertNotNull(actionGroup.getAiAgentProviderAccount());

        }
    }

    @Nested
    class TestDataSource {

        @Test
        void testDataSourceAdd() {
            //we will create 1 agent with 3 versions and they all will share the same knowledgebase which
            // should have 3 data sources to test concurrency
            _responseBuilder.mockKnowledgebaseAdd();
            _responseBuilder.mockDataSourceAdd();
            invokeSync(_testAccount);
            _verificationService.validateSyncHistory(7, 8, 0, 0, COMPLETED);
            //assert there are 3 data sources
            assertEquals(3, _toolResourceRepo.findAll().size());

            //there should be 1 data source for each data source and the ids should match
            List<AiAgentToolResource> resources = _toolResourceRepo.findAll();
            AiAgentToolResource resource1 = resources
                    .stream()
                    .filter(ds -> ds.getExternalId().equals(TestConstants.Knowledgebase1.DataSource1.EXTERNAL_ID))
                    .findFirst()
                    .orElse(null);
            assertNotNull(resource1);
            assertEquals(TestConstants.Knowledgebase1.DataSource1.NAME_FROM_BEDROCK, resource1.getName());
            assertEquals(TestConstants.Knowledgebase1.DataSource1.STATUS_FROM_BEDROCK, resource1.getStatus());
            assertNotNull(resource1.getToolResourceJson());
            assertEquals(
                    GeneralUtil.tmStmpFromInst(TestConstants.Knowledgebase1.DataSource1.UPDATED_AT_FROM_BEDROCK),
                    resource1.getUpdatedAtProviderTime());
            //assert audit fields
            assertNotNull(resource1.getCreatedTime());
            assertNotNull(resource1.getModifiedTime());

            //assert properties of the second ds
            AiAgentToolResource resource2 = resources
                    .stream()
                    .filter(ds -> ds.getExternalId().equals(TestConstants.Knowledgebase1.DataSource2.EXTERNAL_ID))
                    .findFirst()
                    .orElse(null);
            assertNotNull(resource2);
            assertEquals(TestConstants.Knowledgebase1.DataSource2.NAME_FROM_BEDROCK, resource2.getName());
            assertEquals(TestConstants.Knowledgebase1.DataSource2.STATUS_FROM_BEDROCK, resource2.getStatus());
            assertNotNull(resource2.getToolResourceJson());
            assertEquals(
                    GeneralUtil.tmStmpFromInst(TestConstants.Knowledgebase1.DataSource2.UPDATED_AT_FROM_BEDROCK),
                    resource2.getUpdatedAtProviderTime());
            //assert audit fields
            assertNotNull(resource2.getCreatedTime());
            assertNotNull(resource2.getModifiedTime());

            //assert properties of the third ds
            AiAgentToolResource resource3 = resources
                    .stream()
                    .filter(ds -> ds.getExternalId().equals(TestConstants.Knowledgebase1.DataSource3.EXTERNAL_ID))
                    .findFirst()
                    .orElse(null);
            assertNotNull(resource3);
            assertEquals(TestConstants.Knowledgebase1.DataSource3.NAME_FROM_BEDROCK, resource3.getName());
            assertEquals(TestConstants.Knowledgebase1.DataSource3.STATUS_FROM_BEDROCK, resource3.getStatus());
            assertNotNull(resource2.getToolResourceJson());
            assertEquals(
                    GeneralUtil.tmStmpFromInst(TestConstants.Knowledgebase1.DataSource3.UPDATED_AT_FROM_BEDROCK),
                    resource3.getUpdatedAtProviderTime());
            //assert audit fields
            assertNotNull(resource3.getCreatedTime());
            assertNotNull(resource3.getModifiedTime());

        }

        @Test
        void testDataSourceUpdate() {
            //we will create a data source(data source one), and call to aws will return 2 agents with 3 versions each, and each
            //version will have 3 kbs, only kb will have data sources (data source one), we expect the data source we created
            //to get updated and there should only be one and only one data source with the external id for data source 1
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);

            ToolResourceParams dataSource1Params = createDataSource1Params();
            ToolSetupParams toolParams = createKnowledgebase1Params(_testAccount);
            toolParams.setResources(Set.of(dataSource1Params));
            toolParams.setUpdatedAt(TestConstants.Knowledgebase1.UPDATED_AT_FROM_BEDROCK);

            VersionSetupParams version1 = createAgent1Version1Params();
            version1.setUpdatedAt(TestConstants.Agent1.Version1.UPDATED_AT_FROM_BEDROCK);

            version1.setTools(List.of(toolParams));
            agent.setVersions(Set.of(_testBuilder.setupVersion(agent, version1)));


            _responseBuilder.mockKnowledgebaseAdd();
            _responseBuilder.mockDataSourceUpdate();
            invokeSync(_testAccount);
            assertEquals(1, _toolResourceRepo.findAll().size());
            //assert there is one data source
            assertEquals(2, _toolRepo.findAll().size());
            assertEquals(8, _toolAssoRepo.findAll().size());
            //assert the deleted flag for the agent is false
            assertFalse(_aiAgentRepository.findAll().get(0).getIsDeleted());
            //assert there are 3 versions and the is deleted flag for all of them is false
            assertEquals(4, _versionRepository.findAll().size());
            assertFalse(_versionRepository.findAll().get(0).getIsDeleted());
            assertFalse(_versionRepository.findAll().get(1).getIsDeleted());
            assertFalse(_versionRepository.findAll().get(2).getIsDeleted());
            assertFalse(_versionRepository.findAll().get(3).getIsDeleted());


            _verificationService.validateSyncHistory(7, 5, 0, 0, COMPLETED);
            //assert there is only 1 data source
            assertEquals(1, _toolResourceRepo.findAll().size());
            //assert the data source is updated
            AiAgentToolResource resource = _toolResourceRepo.findAll().get(0);
            assertEquals(TestConstants.Knowledgebase1.DataSource1.NAME_FROM_BEDROCK, resource.getName());
            assertEquals(TestConstants.Knowledgebase1.DataSource1.STATUS_FROM_BEDROCK, resource.getStatus());
            assertNotNull(resource.getToolResourceJson());
            assertEquals(
                    GeneralUtil.tmStmpFromInst(TestConstants.Knowledgebase1.DataSource1.UPDATED_AT_FROM_BEDROCK),
                    resource.getUpdatedAtProviderTime());
            assertEquals(TestConstants.Knowledgebase1.DataSource1.EXTERNAL_ID, resource.getExternalId());
            //assert audit fields
            assertNotNull(resource.getCreatedTime());
            assertNotNull(resource.getModifiedTime());
        }

        @Test
        void testDataSourceDelete() {
            //we will create an agent with 1 version, one kb and one 1 data source, bedrock will then return empty array
            //for data sources which should cause the data source to be deleted
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);
            ToolResourceParams dataSource1Params = createDataSource1Params();
            ToolSetupParams toolParams = createKnowledgebase1Params(_testAccount);
            toolParams.setResources(Set.of(dataSource1Params));
            toolParams.setUpdatedAt(TestConstants.Knowledgebase1.UPDATED_AT_FROM_BEDROCK);
            VersionSetupParams version1 = createAgent1Version1Params();
            version1.setUpdatedAt(TestConstants.Agent1.Version1.UPDATED_AT_FROM_BEDROCK);
            version1.setTools(List.of(toolParams));
            agent.setVersions(Set.of(_testBuilder.setupVersion(agent, version1)));

            _responseBuilder.mockKnowledgebaseUpdate();

            invokeSync(_testAccount);

            _verificationService.validateSyncHistory(3, 0, 0, 0, COMPLETED);
            //assert there are no data sources
            assertEquals(0, _toolResourceRepo.findAll().size());
            //assert knowledgebase is not deleted
            assertEquals(1, _toolRepo.findAll().size());

        }

        @Test
        void testDataSourceNoUpdate() {
            //we will create an agent with 1 version, one kb and one 1 data source with the same updated date returned
            //by bedrock, the data source should not get updated
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);

            ToolResourceParams dataSource1Params = createDataSource1Params();
            dataSource1Params.setUpdatedAt(TestConstants.Knowledgebase1.DataSource1.UPDATED_AT_FROM_BEDROCK);

            ToolSetupParams toolParams = createKnowledgebase1Params(_testAccount);
            toolParams.setUpdatedAt(TestConstants.Knowledgebase1.UPDATED_AT_FROM_BEDROCK);

            toolParams.setResources(Set.of(dataSource1Params));

            VersionSetupParams version1 = createAgent1Version1Params();
            version1.setUpdatedAt(TestConstants.Agent1.Version1.UPDATED_AT_FROM_BEDROCK);

            version1.setTools(List.of(toolParams));

            agent.setVersions(Set.of(_testBuilder.setupVersion(agent, version1)));

            _responseBuilder.mockKnowledgebaseUpdate();
            _responseBuilder.mockDataSourceUpdate();

            invokeSync(_testAccount);

            _verificationService.validateSyncHistory(3, 0, 0, 0, COMPLETED);
            //assert there are no data sources
            assertEquals(1, _toolResourceRepo.findAll().size());
            //assert knowledgebase is not deleted
            assertEquals(1, _toolRepo.findAll().size());

            //assert the data source is not updated
            AiAgentToolResource resource = _toolResourceRepo.findAll().get(0);
            assertEquals(TestConstants.Knowledgebase1.DataSource1.NAME, resource.getName());
            assertEquals(TestConstants.Knowledgebase1.DataSource1.STATUS, resource.getStatus());
            assertEquals(
                    GeneralUtil.tmStmpFromInst(TestConstants.Knowledgebase1.DataSource1.UPDATED_AT_FROM_BEDROCK),
                    resource.getUpdatedAtProviderTime());

        }

    }

    @Nested
    class TestFunction {
        @Test
        void testFunctionAdd() {
            //we will create 1 agent with 3 versions, and they all will share the same action group with the same 2
            //functions to test concurrency
            _responseBuilder.mockActionGroupAdd();
            invokeSync(_testAccount);
            _verificationService.validateSyncHistory(5, 5, 0, 0, COMPLETED);
            //there should be 2 functions (tool resources)
            List<AiAgentToolResource> resources = _toolResourceRepo.findAll();
            assertEquals(2, resources.size());
            //assert function 1 is added, its name is the same in test constant, its external id and guid are the same
            //its audit field and tool resource json is not null, provider type is AWS_BEDROCK
            AiAgentToolResource resource1 = resources
                    .stream()
                    .filter(r -> r.getName().equals(TestConstants.Agent1.ActionGroup1.Function1.NAME_FROM_BEDROCK))
                    .findFirst()
                    .orElse(null);
            assertNotNull(resource1);
            assertEquals(resource1.getGuid(), resource1.getExternalId());
            assertEquals(TestConstants.Agent1.ActionGroup1.Function1.NAME_FROM_BEDROCK, resource1.getName());
            assertNotNull(resource1.getToolResourceJson());
            assertEquals(
                    GeneralUtil.tmStmpFromInst(TestConstants.Agent1.ActionGroup1.Function1.UPDATED_AT_FROM_BEDROCK),
                    resource1.getUpdatedAtProviderTime());
            assertEquals(AWS_FUNCTION, resource1.getResourceType());
            //assert audit fields
            assertNotNull(resource1.getCreatedTime());
            assertNotNull(resource1.getModifiedTime());

            //assert function 2 is added, its name is the same in test constant, its external id and guid are the same
            //its audit field and tool resource json is not null, provider type is AWS_BEDROCK
            AiAgentToolResource resource2 = resources
                    .stream()
                    .filter(r -> r.getName().equals(TestConstants.Agent1.ActionGroup1.Function2.NAME_FROM_BEDROCK))
                    .findFirst()
                    .orElse(null);
            assertNotNull(resource2);
            assertEquals(resource2.getGuid(), resource2.getExternalId());
            assertEquals(TestConstants.Agent1.ActionGroup1.Function2.NAME_FROM_BEDROCK, resource2.getName());
            assertNotNull(resource2.getToolResourceJson());
            assertEquals(
                    GeneralUtil.tmStmpFromInst(TestConstants.Agent1.ActionGroup1.Function2.UPDATED_AT_FROM_BEDROCK),
                    resource2.getUpdatedAtProviderTime());
            assertEquals(AWS_FUNCTION, resource2.getResourceType());
            //assert audit fields
            assertNotNull(resource2.getCreatedTime());
            assertNotNull(resource2.getModifiedTime());
        }

        /*@Test
        void testFunctionUpdate(){
            //we will create a function, and call to aws will return 2 agents with 3 versions each, and each
            //version will have 3 action groups, only action group will have function, we expect the function we created
            //to get updated and there should only be one and only one function with the external id for function 1
            AgentSetupParams agentParams = createAgent1Params(_testAccount);
            AiAgent agent = _testBuilder.setupAgent(agentParams);

            ToolResourceParams function1 = createFunction1Params();
            ToolSetupParams toolParams = createActionGroup1Params(_testAccount);
            toolParams.setResources(Set.of(function1));

            VersionSetupParams version1 = createAgent1Version1Params();
            version1.setUpdatedAt(TestConstants.Agent1.Version1.UPDATED_AT_FROM_BEDROCK);

            version1.setTools(List.of(toolParams));
            agent.setVersions(Set.of(_testBuilder.setupVersion(agent, version1)));

            _responseBuilder.mockActionGroupAdd();
            _responseBuilder.mockFunctionUpdate();
            invokeSync(_testAccount);
            assertEquals(1, _toolResourceRepo.findAll().size());
            //assert there is one data source
            assertEquals(2, _toolRepo.findAll().size());
            assertEquals(8, _toolAssoRepo.findAll().size());
            //assert the deleted flag for the agent is false
            assertFalse(_aiAgentRepository.findAll().get(0).getIsDeleted());
            //assert there are 3 versions and the is deleted flag for all of them is false
            assertEquals(4, _versionRepository.findAll().size());
            assertFalse(_versionRepository.findAll().get(0).getIsDeleted());
            assertFalse(_versionRepository.findAll().get(1).getIsDeleted());
            assertFalse(_versionRepository.findAll().get(2).getIsDeleted());
            assertFalse(_versionRepository.findAll().get(3).getIsDeleted());


            _verificationService.validateSyncHistory(7, 5, 0, 0, COMPLETED);
            //assert there is only 1 data source
            assertEquals(1, _toolResourceRepo.findAll().size());
            //assert the data source is updated
            AiAgentToolResource resource = _toolResourceRepo.findAll().get(0);
            assertEquals(TestConstants.Knowledgebase1.DataSource1.NAME_FROM_BEDROCK, resource.getName());
            assertEquals(TestConstants.Knowledgebase1.DataSource1.STATUS_FROM_BEDROCK, resource.getStatus());
            assertNotNull(resource.getToolResourceJson());
            assertEquals(
                    GeneralUtil.tmStmpFromInst(TestConstants.Knowledgebase1.DataSource1.UPDATED_AT_FROM_BEDROCK),
                    resource.getUpdatedAtProviderTime());
            assertEquals(TestConstants.Knowledgebase1.DataSource1.EXTERNAL_ID, resource.getExternalId());
            //assert audit fields
            assertNotNull(resource.getCreatedTime());
            assertNotNull(resource.getModifiedTime());
        }*/
    }


    private AiAgentVersion getVersion(String version) {
        return _versionRepository.findAll()
                .stream()
                .filter(v -> v.getVersionString().equals(version))
                .findFirst()
                .get();
    }

    private void invokeSync(AiAgentProviderAccount account) {
        StepVerifier.create(_bedrockSyncProvider.sync(account))
                .thenConsumeWhile(history -> true) // consume all elements
                .expectComplete()
                .verify();
    }

}
