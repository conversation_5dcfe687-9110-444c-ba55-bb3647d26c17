package com.boomi.aiagentregistry.service.sync;

import com.boomi.aiagentregistry.constants.TestConstants;
import com.boomi.aiagentregistry.servlet.BedrockMockServerHelper;
import com.boomi.aiagentregistry.servlet.response.Agent1;
import com.boomi.aiagentregistry.servlet.response.Agent1Alias1;
import com.boomi.aiagentregistry.servlet.response.Agent1Version1;
import com.boomi.aiagentregistry.servlet.response.Agent1Version2;
import com.boomi.aiagentregistry.servlet.response.Agent2;
import com.boomi.aiagentregistry.servlet.response.Agent3;
import com.boomi.aiagentregistry.servlet.response.Agent3Version1;
import com.boomi.aiagentregistry.servlet.response.Guardrails;
import com.boomi.aiagentregistry.servlet.response.Kb1DataSources;
import com.boomi.aiagentregistry.servlet.response.Kbs;
import com.boomi.aiagentregistry.servlet.response.Llms;
import com.boomi.aiagentregistry.servlet.response.SummeryLists;
import com.boomi.aiagentregistry.util.AiAgentTestBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.boomi.aiagentregistry.constants.TestConstants.ACT_GRP_URI;
import static com.boomi.aiagentregistry.constants.TestConstants.AGENTS_URI;
import static com.boomi.aiagentregistry.constants.TestConstants.ALIAS_URI;
import static com.boomi.aiagentregistry.constants.TestConstants.DATA_SOURCES;
import static com.boomi.aiagentregistry.constants.TestConstants.GUARDRAILS_URI;
import static com.boomi.aiagentregistry.constants.TestConstants.KNOWLEDGE_BASES;
import static com.boomi.aiagentregistry.constants.TestConstants.LLM_URI;
import static com.boomi.aiagentregistry.constants.TestConstants.SLASH;
import static com.boomi.aiagentregistry.constants.TestConstants.VERSIONS_URI;


@Component
public class BedrockTestResponseBuilder {
    public static final String GUARDRAIL_VERSION_URI = "?guardrailVersion=";
    @Autowired
    BedrockMockServerHelper _serverHelper;
    @Autowired
    AiAgentTestBuilder _testBuilder;
    private static final String AGENT1_BASE = AGENTS_URI + TestConstants.Agent1.EXTERNAL_ID;
    private static final String AGENT2_BASE = AGENTS_URI + TestConstants.Agent2.EXTERNAL_ID;
    private static final String AGENT3_BASE = AGENTS_URI + TestConstants.Agent3.EXTERNAL_ID;

    public String createBedrockListVersionResponse(List<String> summaries) {
        return "{\"agentVersionSummaries\": [" + String.join(",", summaries) + " ]}";
    }

    public String createBedrockListAgentResponse(List<String> summaries) {
        return "{\"agentSummaries\": [" + String.join(",", summaries) + " ]}";
    }

    public String createBedrockListAliasResponse(List<String> summaries) {
        return "{\"agentAliasSummaries\": [" + String.join(", ", summaries) + " ]}";
    }

    public String createBedrockListKnowledgeBaseResponse(List<String> summaries) {
        return "{\"agentKnowledgeBaseSummaries\": [" + String.join(", ", summaries) + " ]}";
    }

    public String createBedrockListActionGroupResponse(List<String> summaries) {
        return "{\"actionGroupSummaries\": [" + String.join(", ", summaries) + " ]}";
    }

    public String createBedrockListDataSourceResponse(List<String> summaries) {
        return "{\"dataSourceSummaries\": [" + String.join(", ", summaries) + " ]}";
    }

    public void mockKnowledgebaseUpdate() {
        _serverHelper.forPath(AGENTS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAgentResponse(List.of(SummeryLists.AGENT1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent1.VERSION1_SUMMARY))
                )
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.DETAILS)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version1.VERSION + SLASH)
                .withResponseCode(200)
                .withBody(Agent1Version1.DETAILS)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version1.VERSION + KNOWLEDGE_BASES)
                .withResponseCode(200)
                .withBody(createBedrockListKnowledgeBaseResponse(List.of(Kbs.KB_1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(KNOWLEDGE_BASES + TestConstants.Knowledgebase1.EXTERNAL_ID)
                .withResponseCode(200)
                .withBody(Kbs.KB_1)
                .enqueue();
    }

    public void mockKnowledgebaseAdd() {
        _serverHelper.forPath(AGENTS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAgentResponse(List.of(SummeryLists.AGENT3_SUMMARY, SummeryLists.AGENT1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent1.VERSION_DRAFT_SUMMARY, Agent1.VERSION1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT3_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent3.VERSION_DRAFT_SUMMARY, Agent3.VERSION1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.DETAILS)
                .enqueue();

        _serverHelper.forPath(AGENT3_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent3.DETAILS)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version1.VERSION + SLASH)
                .withResponseCode(200)
                .withBody(Agent1Version1.DETAILS)
                .enqueue();

        _serverHelper.forPath(AGENT3_BASE + VERSIONS_URI + TestConstants.Agent3.Version1.VERSION + SLASH)
                .withResponseCode(200)
                .withBody(Agent3Version1.DETAILS)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.VersionDraft.VERSION + KNOWLEDGE_BASES)
                .withResponseCode(200)
                .withBody(createBedrockListKnowledgeBaseResponse(List.of(Kbs.KB_1_SUMMARY, Kbs.KB_2_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version1.VERSION + KNOWLEDGE_BASES)
                .withResponseCode(200)
                .withBody(createBedrockListKnowledgeBaseResponse(List.of(Kbs.KB_1_SUMMARY, Kbs.KB_2_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT3_BASE + VERSIONS_URI + TestConstants.Agent3.VersionDraft.VERSION + KNOWLEDGE_BASES)
                .withResponseCode(200)
                .withBody(createBedrockListKnowledgeBaseResponse(List.of(Kbs.KB_1_SUMMARY, Kbs.KB_2_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT3_BASE + VERSIONS_URI + TestConstants.Agent3.Version1.VERSION + KNOWLEDGE_BASES)
                .withResponseCode(200)
                .withBody(createBedrockListKnowledgeBaseResponse(List.of(Kbs.KB_1_SUMMARY, Kbs.KB_2_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(KNOWLEDGE_BASES + TestConstants.Knowledgebase1.EXTERNAL_ID)
                .withResponseCode(200)
                .withBody(Kbs.KB_1)
                .enqueue();

        _serverHelper.forPath(KNOWLEDGE_BASES + TestConstants.Knowledgebase2.EXTERNAL_ID)
                .withResponseCode(200)
                .withBody(Kbs.KB_2)
                .enqueue();
    }

    public void mockAgent3WithVersionsResponse() {
        _serverHelper.forPath(AGENTS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAgentResponse(List.of(SummeryLists.AGENT3_SUMMARY)))
                .enqueue();
        _serverHelper.forPath(AGENT3_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent3.VERSION_DRAFT_SUMMARY, Agent3.VERSION1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT3_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent3.DETAILS)
                .enqueue();

        _serverHelper.forPath(AGENT3_BASE + VERSIONS_URI + TestConstants.Agent3.Version1.VERSION + SLASH)
                .withResponseCode(200)
                .withBody(Agent3Version1.DETAILS)
                .enqueue();

    }

    public void mockAddLlmResponseForAdd() {
        _serverHelper.forPath(AGENTS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAgentResponse(List.of(SummeryLists.AGENT3_SUMMARY, SummeryLists.AGENT1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT3_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent3.VERSION_DRAFT_SUMMARY, Agent3.VERSION1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent1.VERSION_DRAFT_SUMMARY, Agent1.VERSION1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT3_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent3.DETAILS_WITH_LLM)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.DETAILS_WITH_LLM)
                .enqueue();

        _serverHelper.forPath(AGENT3_BASE + VERSIONS_URI + TestConstants.Agent3.Version1.VERSION + SLASH)
                .withResponseCode(200)
                .withBody(Agent3Version1.DETAILS_WITH_LLM)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version1.VERSION + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.VERSION1.DETAILS_WITH_LLM)
                .enqueue();

        _serverHelper.forPath(LLM_URI + TestConstants.Agent3.VersionDraft.LLM)
                .withResponseCode(200)
                .withBody(Llms.LLM_1)
                .enqueue();
        _serverHelper.forPath(LLM_URI + TestConstants.Agent3.Version1.LLM)
                .withResponseCode(200)
                .withBody(Llms.LLM_1)
                .enqueue();

        _serverHelper.forPath(LLM_URI + TestConstants.Agent1.Version1.LLM)
                .withResponseCode(200)
                .withBody(Llms.LLM_1)
                .enqueue();
        _serverHelper.forPath(LLM_URI + TestConstants.Agent1.VersionDraft.LLM)
                .withResponseCode(200)
                .withBody(Llms.LLM_1)
                .enqueue();
    }

    public void mockAddGrResponseForAdd() {
        _serverHelper.forPath(AGENTS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAgentResponse(List.of(SummeryLists.AGENT3_SUMMARY, SummeryLists.AGENT1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT3_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent3.VERSION_DRAFT_SUMMARY, Agent3.VERSION1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent1.VERSION_DRAFT_SUMMARY, Agent1.VERSION1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT3_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent3.DETAILS_WITH_GR)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.DETAILS_WITH_GR)
                .enqueue();

        _serverHelper.forPath(AGENT3_BASE + VERSIONS_URI + TestConstants.Agent3.Version1.VERSION + SLASH)
                .withResponseCode(200)
                .withBody(Agent3Version1.DETAILS_WITH_GR)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version1.VERSION + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.VERSION1.DETAILS_WITH_GR)
                .enqueue();

        _serverHelper.forPath(GUARDRAILS_URI + TestConstants.Agent3.VersionDraft.GUARDRAIL + GUARDRAIL_VERSION_URI + TestConstants.Agent3.VersionDraft.GR_VERSION)
                .withResponseCode(200)
                .withBody(Guardrails.GR_1)
                .enqueue();
        _serverHelper.forPath(GUARDRAILS_URI + TestConstants.Agent3.Version1.GUARDRAIL + GUARDRAIL_VERSION_URI + TestConstants.Agent3.Version1.GR_VERSION)
                .withResponseCode(200)
                .withBody(Guardrails.GR_1)
                .enqueue();

        _serverHelper.forPath(GUARDRAILS_URI + TestConstants.Agent1.Version1.GUARDRAIL + GUARDRAIL_VERSION_URI + TestConstants.Agent1.Version1.GR_VERSION)
                .withResponseCode(200)
                .withBody(Guardrails.GR_1)
                .enqueue();
        _serverHelper.forPath(GUARDRAILS_URI + TestConstants.Agent1.VersionDraft.GUARDRAIL + GUARDRAIL_VERSION_URI + TestConstants.Agent1.VersionDraft.GR_VERSION)
                .withResponseCode(200)
                .withBody(Guardrails.GR_1)
                .enqueue();
    }

    public void mockAgent3WithVersionDraftResponse() {
        _serverHelper.forPath(AGENTS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAgentResponse(List.of(SummeryLists.AGENT3_SUMMARY)))
                .enqueue();
        _serverHelper.forPath(AGENT3_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent3.VERSION_DRAFT_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT3_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent3.DETAILS)
                .enqueue();

    }

    public void mockAgent1WithVersionDraftResponses() {
        _serverHelper.forPath(AGENTS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAgentResponse(List.of(SummeryLists.AGENT1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent1.VERSION_DRAFT_SUMMARY)))
                .enqueue();


        _serverHelper.forPath(AGENT1_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.DETAILS)
                .enqueue();
    }

    public void mockAgent1WithVersionsResponse() {
        _serverHelper.forPath(AGENTS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAgentResponse(List.of(SummeryLists.AGENT1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent1.VERSION1_SUMMARY, Agent1.VERSION_DRAFT_SUMMARY))
                )
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.DETAILS)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version1.VERSION + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.VERSION1.DETAILS)
                .enqueue();
    }

    public void mockAgent1WithVersion1Response() {
        _serverHelper.forPath(AGENTS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAgentResponse(List.of(SummeryLists.AGENT1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent1.VERSION1_SUMMARY))
                )
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.DETAILS)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version1.VERSION + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.VERSION1.DETAILS)
                .enqueue();
    }

    public void mockAgent1WithVersion1ResponseForGrNoAction() {
        _serverHelper.forPath(AGENTS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAgentResponse(List.of(SummeryLists.AGENT1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent1.VERSION1_SUMMARY))
                )
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.DETAILS_WITH_GR)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version1.VERSION + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.VERSION1.DETAILS_WITH_GR)
                .enqueue();
        _serverHelper.forPath(GUARDRAILS_URI + TestConstants.Agent1.Version1.GUARDRAIL + GUARDRAIL_VERSION_URI + TestConstants.Agent1.Version1.GR_VERSION)
                .withResponseCode(200)
                .withBody(Guardrails.GR_1)
                .enqueue();

    }

    public void mockAddLlmResponseForUpdate() {
        _serverHelper.forPath(AGENTS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAgentResponse(List.of(SummeryLists.AGENT1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent1.VERSION1_SUMMARY))
                )
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.DETAILS_WITH_LLM)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version1.VERSION + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.VERSION1.DETAILS_WITH_LLM)
                .enqueue();
    }

    public void mockAgent2WithVersionsResponses() {
        _serverHelper.forPath(AGENTS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAgentResponse(List.of(SummeryLists.AGENT2_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT2_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent2.VERSION_DRAFT_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT2_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent2.DETAILS)
                .enqueue();
    }


    public void mockAddGrResponseForUpdate() {
        _serverHelper.forPath(AGENTS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAgentResponse(List.of(SummeryLists.AGENT1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent1.VERSION1_SUMMARY))
                )
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.DETAILS_WITH_GR)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version1.VERSION + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.VERSION1.DETAILS_WITH_GR)
                .enqueue();

        _serverHelper.forPath(GUARDRAILS_URI + TestConstants.Agent1.VersionDraft.GUARDRAIL + GUARDRAIL_VERSION_URI + TestConstants.Agent1.VersionDraft.GR_VERSION)
                .withResponseCode(200)
                .withBody(Guardrails.GR_1)
                .enqueue();
    }

    public void mockActionGroupAdd() {
        _serverHelper.forPath(AGENTS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAgentResponse(List.of(SummeryLists.AGENT1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent1.VERSION_DRAFT_SUMMARY, Agent1.VERSION1_SUMMARY, Agent1.VERSION2_SUMMARY)))
                .enqueue();


        _serverHelper.forPath(AGENT1_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.DETAILS)
                .enqueue();


        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version1.VERSION + SLASH)
                .withResponseCode(200)
                .withBody(Agent1Version1.DETAILS)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version2.VERSION + SLASH)
                .withResponseCode(200)
                .withBody(Agent1Version2.DETAILS)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.VersionDraft.VERSION + ACT_GRP_URI)
                .withResponseCode(200)
                .withBody(createBedrockListActionGroupResponse(List.of(Agent1.ACTION_GROUP_FUN_1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version1.VERSION + ACT_GRP_URI)
                .withResponseCode(200)
                .withBody(createBedrockListActionGroupResponse(List.of(Agent1.ACTION_GROUP_FUN_1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version2.VERSION + ACT_GRP_URI)
                .withResponseCode(200)
                .withBody(createBedrockListActionGroupResponse(List.of(Agent1.ACTION_GROUP_FUN_1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.VersionDraft.VERSION + ACT_GRP_URI
                              + TestConstants.Agent1.ActionGroup1.EXTERNAL_ID + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.ACTION_GROUP_FUN_1_DETAILS)
                .enqueue();


        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version1.VERSION + ACT_GRP_URI
                              + TestConstants.Agent1.ActionGroup1.EXTERNAL_ID + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.ACTION_GROUP_FUN_1_DETAILS)
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.Version2.VERSION + ACT_GRP_URI
                              + TestConstants.Agent1.ActionGroup1.EXTERNAL_ID + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.ACTION_GROUP_FUN_1_DETAILS)
                .enqueue();

    }

    public void mockActionGroupUpdate() {
        _serverHelper.forPath(AGENTS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAgentResponse(List.of(SummeryLists.AGENT1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListVersionResponse(List.of(Agent1.VERSION_DRAFT_SUMMARY)))
                .enqueue();


        _serverHelper.forPath(AGENT1_BASE + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.DETAILS)
                .enqueue();


        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.VersionDraft.VERSION + ACT_GRP_URI)
                .withResponseCode(200)
                .withBody(createBedrockListActionGroupResponse(List.of(Agent1.ACTION_GROUP_FUN_1_SUMMARY)))
                .enqueue();


        _serverHelper.forPath(AGENT1_BASE + VERSIONS_URI + TestConstants.Agent1.VersionDraft.VERSION + ACT_GRP_URI
                              + TestConstants.Agent1.ActionGroup1.EXTERNAL_ID + SLASH)
                .withResponseCode(200)
                .withBody(Agent1.ACTION_GROUP_FUN_1_DETAILS)
                .enqueue();
    }

    public void mockDataSourceAdd() {
        _serverHelper.forPath(KNOWLEDGE_BASES + TestConstants.Knowledgebase1.EXTERNAL_ID + DATA_SOURCES)
                .withResponseCode(200)
                .withBody(createBedrockListDataSourceResponse(
                        List.of(
                                Kb1DataSources.DATA_SOURCE_1_SUMMARY,
                                Kb1DataSources.DATA_SOURCE_2_SUMMARY,
                                Kb1DataSources.DATA_SOURCE_3_SUMMARY
                        )))
                .enqueue();

        _serverHelper.forPath(KNOWLEDGE_BASES + TestConstants.Knowledgebase1.EXTERNAL_ID +
                              DATA_SOURCES + TestConstants.Knowledgebase1.DataSource1.EXTERNAL_ID)
                .withResponseCode(200)
                .withBody(Kb1DataSources.DS1)
                .enqueue();

        _serverHelper.forPath(KNOWLEDGE_BASES + TestConstants.Knowledgebase1.EXTERNAL_ID +
                              DATA_SOURCES + TestConstants.Knowledgebase1.DataSource2.EXTERNAL_ID)
                .withResponseCode(200)
                .withBody(Kb1DataSources.DS2)
                .enqueue();

        _serverHelper.forPath(KNOWLEDGE_BASES + TestConstants.Knowledgebase1.EXTERNAL_ID +
                              DATA_SOURCES + TestConstants.Knowledgebase1.DataSource3.EXTERNAL_ID)
                .withResponseCode(200)
                .withBody(Kb1DataSources.DS3)
                .enqueue();
    }

    public void mockDataSourceUpdate() {
        _serverHelper.forPath(KNOWLEDGE_BASES + TestConstants.Knowledgebase1.EXTERNAL_ID + DATA_SOURCES)
                .withResponseCode(200)
                .withBody(createBedrockListDataSourceResponse(
                        List.of(
                                Kb1DataSources.DATA_SOURCE_1_SUMMARY
                        )))
                .enqueue();

        _serverHelper.forPath(KNOWLEDGE_BASES + TestConstants.Knowledgebase1.EXTERNAL_ID +
                              DATA_SOURCES + TestConstants.Knowledgebase1.DataSource1.EXTERNAL_ID)
                .withResponseCode(200)
                .withBody(Kb1DataSources.DS1)
                .enqueue();

    }


    public void mockAliasForUpdate() {
        _serverHelper.forPath(AGENT1_BASE + ALIAS_URI)
                .withResponseCode(200)
                .withBody(createBedrockListAliasResponse(List.of(Agent1.ALIAS1_SUMMARY)))
                .enqueue();

        _serverHelper.forPath(AGENT1_BASE + ALIAS_URI + TestConstants.Agent1.Alias1.EXTERNAL_ID + SLASH)
                .withResponseCode(200)
                .withBody(Agent1Alias1.DETAILS)
                .enqueue();
    }

}
