// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.auth;

import reactor.core.publisher.Mono;
import com.boomi.aiagentregistry.servlet.ApiService;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

class BoomiApiTokenAuthorizationParserStrategyTest {

    @InjectMocks
    private BoomiApiTokenAuthorizationParserStrategy _boomiApiTokenAuthorizationParserStrategy;
    @Mock
    private ApiService _mockApiService;
    private final ObjectMapper _objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        _boomiApiTokenAuthorizationParserStrategy.setObjectMapper(_objectMapper);
    }

   @Test
    void testVerifyImmutableFieldsAreNotChanged() throws JsonProcessingException {
        BoomiApiTokenCredentials existingCredentials = new BoomiApiTokenCredentials();
        existingCredentials.setAccountId("existingAccountId");

        BoomiApiTokenCredentials newCredentials = new BoomiApiTokenCredentials();
        newCredentials.setAccountId("newAccountId");

        Optional<List<AiAgentRegistryErrorCode>>
                optionalErrorCodes = _boomiApiTokenAuthorizationParserStrategy.verifyImmutableFieldsAreNotChanged(
                _objectMapper.writeValueAsString(existingCredentials),
                _objectMapper.writeValueAsString(newCredentials));

        assertTrue(optionalErrorCodes.isPresent());
        assertEquals(1, optionalErrorCodes.get().size());
        assertEquals(AiAgentRegistryErrorCode.GARDEN_ACCOUNT_ID_IS_CHANGED, optionalErrorCodes.get().get(0));
    }

    @Test
    void testVerifyCredentialsAreMasked() throws JsonProcessingException {
        String credentials =
                "{\n" + "    \"userName\": \"<EMAIL>\",\n" + "    \"accountId\": \"boomi-internal\",\n" +
                        "    \"apiToken\": \"TestToken\",\n" + "    \"jwt\": \"TestJwt\"\n" + "}";
        Mono<String> maskedCredentials = _boomiApiTokenAuthorizationParserStrategy.getMaskedCredentials(credentials);
        assertNotNull(maskedCredentials);
        JsonNode jsonNode = _objectMapper.readTree(maskedCredentials.block());
        String userName = jsonNode.get("userName").asText();
        assertEquals("<EMAIL>", userName);
        String accountId = jsonNode.get("accountId").asText().trim();
        assertEquals("null", accountId);
        String apiToken = jsonNode.get("apiToken").asText();
        assertEquals("null", apiToken);
        String jwt = jsonNode.get("jwt").asText();
        assertEquals("null", jwt);
    }
}