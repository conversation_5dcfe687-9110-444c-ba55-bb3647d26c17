// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.auth;

import com.boomi.aiagentregistry.servlet.AwsClient;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;


class AwsAuthorizationParserStrategyTest {

    private AwsAuthorizationParserStrategy _authorizationParserStrategy;
    @Mock
    private AwsClient _mockAwsClient;
    private final ObjectMapper _objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        _authorizationParserStrategy = new AwsAuthorizationParserStrategy(_objectMapper, _mockAwsClient);
    }

    @Test
    void testVerifyImmutableFieldsAreNotChanged() throws JsonProcessingException {
        AwsCredentials existingCredentials = new AwsCredentials();
        existingCredentials.setAwsRegion("existingRegion");
        AwsCredentials newCredentials = new AwsCredentials();
        newCredentials.setAwsRegion("newRegion");

        Optional<List<AiAgentRegistryErrorCode>>
                optionalErrorCodes = _authorizationParserStrategy.verifyImmutableFieldsAreNotChanged(
                _objectMapper.writeValueAsString(existingCredentials),
                _objectMapper.writeValueAsString(newCredentials));

        assertTrue(optionalErrorCodes.isPresent());
        assertEquals(1, optionalErrorCodes.get().size());
        assertEquals(AiAgentRegistryErrorCode.BEDROCK_ACCOUNT_REGION_IS_CHANGED, optionalErrorCodes.get().get(0));
    }
}