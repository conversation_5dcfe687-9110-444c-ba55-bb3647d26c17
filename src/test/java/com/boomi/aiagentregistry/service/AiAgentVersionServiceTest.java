// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service;

import graphql.GraphQLError;
import graphql.execution.ExecutionContext;
import graphql.execution.ExecutionStepInfo;
import graphql.execution.ResultPath;
import graphql.schema.DataFetchingEnvironment;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.repo.AiAgentVersionRepository;
import com.boomi.aiagentregistry.util.UserUtil;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiAgentRegistryTrustLevel;
import com.boomi.graphql.server.schema.types.AiAgentTrustLevelQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentTrustLevelQueryResponse;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.text.MessageFormat;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class AiAgentVersionServiceTest extends BaseMockWebServerTest {

    @Mock
    private AiAgentVersionRepository aiAgentVersionRepository;

    @Mock
    private AiAgentProviderAccountRepository aiAgentProviderAccountRepository;


    @InjectMocks
    private AiAgentVersionServiceImpl aiAgentVersionService;

    @Mock
    private UserUtil userUtil;

    private DataFetchingEnvironment dfe;

    private static final String ACCOUNT_ID = "test-account-id";

    @BeforeEach
    void setUp() {
        dfe = mock(DataFetchingEnvironment.class);
    }

    @Test
    void shouldThrowExceptionForNonExistingProviderAccount() {
        testShouldThrowException(UUID.randomUUID().toString(), AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_NOT_FOUND);
    }

    @Test
    void shouldThrowExceptionForInvalidProviderAccountId() {
        testShouldThrowException("Invalid Account Id", AiAgentRegistryErrorCode.INVALID_PROVIDER_ACCOUNT_ID);
    }

    private void testShouldThrowException(String providerAccountId, AiAgentRegistryErrorCode errorCode) {
        // Given
        AiAgentTrustLevelQueryInput trustLevelQueryInput = new AiAgentTrustLevelQueryInput();
        String expectedErrorMessage = MessageFormat.format(errorCode.getDetail(), providerAccountId);
        trustLevelQueryInput.setProviderAccountId(providerAccountId);

        when(userUtil.getAccountId(dfe)).thenReturn(ACCOUNT_ID);
        when(aiAgentProviderAccountRepository.findByGuid(any())).thenReturn(
                Optional.empty());

        ExecutionStepInfo executionStepInfo = mock(ExecutionStepInfo.class);
        ResultPath path = mock(ResultPath.class);
        ExecutionContext executionContext = mock(ExecutionContext.class);

        when(dfe.getExecutionStepInfo()).thenReturn(executionStepInfo);
        when(executionStepInfo.getPath()).thenReturn(path);
        when(dfe.getExecutionContext()).thenReturn(executionContext);
        ArgumentCaptor<GraphQLError> errorCaptor = ArgumentCaptor.forClass(GraphQLError.class);
        doNothing().when(executionContext).addError(errorCaptor.capture());

        // When
        CompletableFuture<AiAgentTrustLevelQueryResponse> futureResponse =
                aiAgentVersionService.getAiAgentTrustLevelMetrics(trustLevelQueryInput, dfe);

        // Then
        AiAgentTrustLevelQueryResponse response = futureResponse.join();
        verify(executionContext).addError(any(GraphQLError.class));

        assertNull(response);
        assertEquals(expectedErrorMessage, errorCaptor.getValue().getMessage());
    }

    @Test
    void testGetAiAgentTrustLevelMetricsSuccess() {
        when(aiAgentProviderAccountRepository.findByGuid(any())).thenReturn(
                Optional.ofNullable(mock(AiAgentProviderAccount.class)));

        testGetAiAgentTrustLevelMetrics(UUID.randomUUID().toString());
    }

    @Test
    void testGetAiAgentTrustLevelMetricsSuccessForOptionalProviderAccountId() {
        testGetAiAgentTrustLevelMetrics(null);
    }

    private void testGetAiAgentTrustLevelMetrics(String providerAccountId) {
        // Given
        AiAgentTrustLevelQueryInput trustLevelQueryInput = new AiAgentTrustLevelQueryInput();
        trustLevelQueryInput.setProviderAccountId(providerAccountId);

        Set<AiAgentVersion> versionSet = createAiAgentVersions();
        when(userUtil.getAccountId(dfe)).thenReturn(ACCOUNT_ID);
        when(aiAgentVersionRepository.getAiAgentVersions(anyString(), any())).thenReturn(Optional.of(versionSet));

        // When
        CompletableFuture<AiAgentTrustLevelQueryResponse> futureResponse = aiAgentVersionService.getAiAgentTrustLevelMetrics(trustLevelQueryInput, dfe);
        AiAgentTrustLevelQueryResponse response = futureResponse.join();

        // Then
        assertNotNull(response);
        assertEquals(4, response.getTotalAgents());
        assertEquals(2, response.getEndorsedAgents());
        assertEquals(1, response.getUnendorsedAgents());
        assertEquals(1, response.getDeprecatedAgents());
    }

    private Set<AiAgentVersion> createAiAgentVersions() {
        return Set.of(
                createAiAgentVersion(AiAgentRegistryTrustLevel.ENDORSED),
                createAiAgentVersion(AiAgentRegistryTrustLevel.UNENDORSED),
                createAiAgentVersion(AiAgentRegistryTrustLevel.DEPRECATED),
                createAiAgentVersion(AiAgentRegistryTrustLevel.ENDORSED)
        );
    }

    private AiAgentVersion createAiAgentVersion(AiAgentRegistryTrustLevel trustLevel) {
        AiAgentVersion version = new AiAgentVersion();
        version.setGuid(UUID.randomUUID().toString());
        version.setTrustLevel(trustLevel);
        return version;
    }
}
