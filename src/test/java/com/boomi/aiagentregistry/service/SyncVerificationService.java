package com.boomi.aiagentregistry.service;

import com.boomi.aiagentregistry.entity.Auditable;
import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.repo.SyncUserAuditRepository;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus;

import org.springframework.stereotype.Service;

import java.util.List;

import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.PROVIDER_ACCOUNT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Service
public class SyncVerificationService {

    private final SyncUserAuditRepository _syncUserAuditRepo;

    public SyncVerificationService(SyncUserAuditRepository syncUserAuditRepo) {
        _syncUserAuditRepo = syncUserAuditRepo;
    }

    public void validateSyncHistory(int size, int added, int updated, int removed, AiRegistryEntitySyncStatus status) {
        List<SyncUserAudit> syncUserAuditList = _syncUserAuditRepo.findAll();
        assertEquals(size * 2, syncUserAuditList.size());
        SyncUserAudit providerAccountSyncAudit = syncUserAuditList
                .stream()
                .filter(audit -> audit.getEntityType()
                                      .equals(PROVIDER_ACCOUNT))
                .findFirst()
                .get();
        assertEquals(updated, providerAccountSyncAudit.getSyncNumberOfEntitiesUpdated());
        assertEquals(added, providerAccountSyncAudit.getSyncNumberOfEntitiesAdded());
        assertEquals(removed, providerAccountSyncAudit.getSyncNumberOfEntitiesRemoved());
        assertEquals(status, providerAccountSyncAudit.getSyncStatus());
        assertNotNull(providerAccountSyncAudit.getEntityType());
        assertNotNull(providerAccountSyncAudit.getEntityUid());
        assertNotNull(providerAccountSyncAudit.getSyncStartDate());
        assertNotNull(providerAccountSyncAudit.getSyncEndDate());
        assertNotNull(providerAccountSyncAudit.getProviderAccountUid());
        assertNotNull(providerAccountSyncAudit.getProviderAccountGuid());
        assertNotNull(providerAccountSyncAudit.getProviderType());
        assertNotNull(providerAccountSyncAudit.getSyncTransactionGuid());
        assertNotNull(providerAccountSyncAudit.getLastUpdatedDate());
        assertNotNull(providerAccountSyncAudit.getEntityGuid());
        assertNotNull(providerAccountSyncAudit.getActionType());
        assertNotNull(providerAccountSyncAudit.getUserId());
        assertNotNull(providerAccountSyncAudit.getIdpAccountId());
        assertNotNull(providerAccountSyncAudit.getChangeOrigin());
    }

    public void validateAuditFields(Auditable entity) {
        assertNotNull(entity.getCreatedTime());
        assertNotNull(entity.getModifiedTime());
    }
}

