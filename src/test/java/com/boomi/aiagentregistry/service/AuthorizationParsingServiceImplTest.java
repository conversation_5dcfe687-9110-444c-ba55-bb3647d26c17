// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service;

import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.service.auth.AuthorizationParsingServiceImpl;
import com.boomi.aiagentregistry.service.auth.BoomiApiTokenCredentials;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.mockwebserver.Dispatcher;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;
import org.junit.Ignore;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * <AUTHOR>

 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestApplication.class)
@Slf4j
@Disabled
class AuthorizationParsingServiceImplTest {
    private static MockWebServer _mockBackEnd = new MockWebServer();
    private static final String JWT = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
    private static final String TOKEN = "a30bf069-1d6a-458e-b54b-4cf726cf1808";
    private static final String USERNAME = "<EMAIL>";
    private static final String BOOMI_ACCT_ID_SUCCESS = "test-parse-boomi-api-success";
    private static final String BOOMI_ACCT_ID_FAILURE = "test-parse-boomi-api-failure";

    @Autowired
    AuthorizationParsingServiceImpl _authorizationParsingService;

    @BeforeAll
    static void setUpAll() throws Exception {
        _mockBackEnd.start();
        Dispatcher approvalSuccessfulDispatcher = new Dispatcher() {
            @Override
            @SneakyThrows
            public MockResponse dispatch(RecordedRequest request) {

                if (request.getPath().contains("/generate/"+BOOMI_ACCT_ID_SUCCESS)) {
                    return new MockResponse().setBody(JWT)
                            .addHeader("Content-Type", "application/json");
                }else if(request.getPath().contains("/generate/"+BOOMI_ACCT_ID_FAILURE)){
                    return new MockResponse().setResponseCode(401).setBody("Unauthorized");
                }
                return new MockResponse().setResponseCode(404);
            }
        };
        _mockBackEnd.setDispatcher(approvalSuccessfulDispatcher);
    }

    @AfterAll
    static void tearDownAll() throws Exception {
        _mockBackEnd.shutdown();
    }


    @AfterEach
    void tearDown() {
    }

    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry r) throws IOException {
        r.add("api.boomi.platform.url", () -> "http://localhost:" + _mockBackEnd.getPort());
    }

    @Ignore("avoid breakage")
    @Test
    void createAwsAuthorization(){
        String json = "{\"awsAccessKeyId\":\"\",\"awsSecretAccessKey\":\"\",\"awsRegion\":\"us-east-1\"}";
        _authorizationParsingService.createAuthorization(json, AiProviderAuthSchema.AWS ).map(credentials->{
            assertNotNull(credentials);
            return credentials;
        });
    }

    @Ignore("avoid breakage")
    @Test
    void createBoomiAuthorization() throws JsonProcessingException, ExecutionException, InterruptedException,
            TimeoutException {
        String json = "{\"apiToken\":\""+TOKEN+"\", \"userName\":\""+USERNAME+"\", \"accountId\":\""+BOOMI_ACCT_ID_SUCCESS+"\"}";
        _authorizationParsingService.createAuthorization(json, AiProviderAuthSchema.BOOMIAPITOKEN)
                .map(credentials -> {
                    BoomiApiTokenCredentials boomiApiTokenCredentials = (BoomiApiTokenCredentials) credentials;
                    assertEquals(JWT,boomiApiTokenCredentials.getJwt());
                    return credentials;
                });


    }

    @Ignore("avoid breakage")
    @Test
    void createBoomiAuthWhenGetJwtFails() {
        String json = "{\"apiToken\":\""+TOKEN+"\", \"userName\":\""+USERNAME+"\", \"accountId\":\""+BOOMI_ACCT_ID_FAILURE+"\"}";
        assertThrows(ExecutionException.class, () -> {

            _authorizationParsingService.createAuthorization(json, AiProviderAuthSchema.BOOMIAPITOKEN).map(credentials1 -> {
                BoomiApiTokenCredentials boomiApiTokenCredentials = (BoomiApiTokenCredentials) credentials1;
                return null;
            });

        });

    }
}
