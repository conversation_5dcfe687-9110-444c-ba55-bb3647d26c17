// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.service;

import graphql.GraphQLError;
import graphql.execution.ExecutionContext;
import graphql.execution.ExecutionStepInfo;
import graphql.execution.ResultPath;
import graphql.schema.DataFetchingEnvironment;
import com.boomi.aiagentregistry.repo.AiAgentRepository;
import com.boomi.aiagentregistry.repo.AiAgentVersionRepository;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.graphql.server.schema.types.AiAgent;
import com.boomi.graphql.server.schema.types.AiAgentsQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentsQueryResponse;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class AIAgentServiceTest {

    @InjectMocks
    private AiAgentServiceImpl aiAgentServiceImpl;

    @Mock
    private AiAgentRepository aiAgentRepository;

    @Mock
    private AiAgentVersionRepository aiAgentVersionRepository;

    private DataFetchingEnvironment dfe;

    private static final String REGISTRY_ACCOUNT_ID = "test_registry_account";

    @BeforeEach
    void setUp() {
        dfe = mock(DataFetchingEnvironment.class);
    }

    @Test
    void getAIAgents_Success() {
        AiAgentsQueryInput input = new AiAgentsQueryInput();
        input.setPageIndex(0);
        input.setPageSize(10);
        List<com.boomi.aiagentregistry.entity.AiAgent> aiAgents = TestUtil.createTestAiAgents();
        Page<com.boomi.aiagentregistry.entity.AiAgent> page =
                new PageImpl<>(aiAgents, PageRequest.of(0, 10), aiAgents.size());

        List<String> registryAccountIds = new ArrayList<>();
        registryAccountIds.add(REGISTRY_ACCOUNT_ID);
        input.setProviderAccountIds(registryAccountIds);
        when(aiAgentVersionRepository.findByAgentUid(anyInt())).thenReturn(Optional.empty());
        when(aiAgentRepository.findAllByAiAgentProviderAccountGuidIn(eq(registryAccountIds), any(Pageable.class)))
                .thenReturn(page);
        CompletableFuture<AiAgentsQueryResponse> futureResponse =
                aiAgentServiceImpl.getAiAgents(input, dfe);
        Assertions.assertNotNull(futureResponse);

        AiAgentsQueryResponse response = futureResponse.join();
        Assertions.assertNotNull(response);
        Assertions.assertEquals(3, response.getNumberOfResults().intValue());
        Assertions.assertEquals(10, response.getCurrentPageSize().intValue());
        List<AiAgent> agentsInResponse = response.getAiAgents();
        Assertions.assertNotNull(agentsInResponse);
        Assertions.assertEquals(3, agentsInResponse.size());

        AiAgent firstAgent = agentsInResponse.get(0);
        Assertions.assertEquals("sampleExternalId", firstAgent.getExternalId());


        AiAgent secondAgent = agentsInResponse.get(1);
        Assertions.assertEquals("sampleExternalId2", secondAgent.getExternalId());
    }

    @Test
    void shouldThrowExceptionForNegativePageIndex() throws ExecutionException, InterruptedException {
        AiAgentsQueryInput input = new AiAgentsQueryInput();
        ExecutionStepInfo executionStepInfo = mock(ExecutionStepInfo.class);
        ResultPath path = mock(ResultPath.class);
        ExecutionContext executionContext = mock(ExecutionContext.class);

        when(dfe.getExecutionStepInfo()).thenReturn(executionStepInfo);
        when(executionStepInfo.getPath()).thenReturn(path);
        when(dfe.getExecutionContext()).thenReturn(executionContext);
        input.setPageIndex(-1); // Setting negative page number to trigger the validation error
        input.setPageSize(10);

        ArgumentCaptor<GraphQLError> errorCaptor = ArgumentCaptor.forClass(GraphQLError.class);
        doNothing().when(executionContext).addError(errorCaptor.capture());

        AiAgentsQueryResponse response = aiAgentServiceImpl.getAiAgents(input, dfe).get();
        verify(executionContext).addError(any(GraphQLError.class));
        assertNull(response);
        assertEquals(
                "The provided page index is invalid. The page number must be 0 or greater. Please ensure the page number is valid.",
                errorCaptor.getValue().getMessage());
    }

    @Test
    void shouldThrowExceptionInvalidPageSize() throws ExecutionException, InterruptedException {
        AiAgentsQueryInput input = new AiAgentsQueryInput();
        ExecutionStepInfo executionStepInfo = mock(ExecutionStepInfo.class);
        ResultPath path = mock(ResultPath.class);
        ExecutionContext executionContext = mock(ExecutionContext.class);

        when(dfe.getExecutionStepInfo()).thenReturn(executionStepInfo);
        when(executionStepInfo.getPath()).thenReturn(path);
        when(dfe.getExecutionContext()).thenReturn(executionContext);
        input.setPageIndex(1);
        input.setPageSize(0);

        ArgumentCaptor<GraphQLError> errorCaptor = ArgumentCaptor.forClass(GraphQLError.class);
        doNothing().when(executionContext).addError(errorCaptor.capture());

        AiAgentsQueryResponse response = aiAgentServiceImpl.getAiAgents(input, dfe).get();
        verify(executionContext).addError(any(GraphQLError.class));
        assertNull(response);
        assertEquals(
                "The provided page size is invalid. The page size must be greater than 0. Please ensure the page size is valid.",
                errorCaptor.getValue().getMessage());
    }
}
