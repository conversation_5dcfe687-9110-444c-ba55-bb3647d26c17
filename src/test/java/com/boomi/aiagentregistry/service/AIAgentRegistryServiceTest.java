// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service;

import graphql.GraphQLError;
import graphql.execution.ExecutionContext;
import graphql.execution.ExecutionStepInfo;
import graphql.execution.ResultPath;
import graphql.schema.DataFetchingEnvironment;
import graphql.schema.DataFetchingFieldSelectionSet;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.repo.AiAgentLatestSyncRepository;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.repo.AiAgentRepository;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.aiagentregistry.util.UserUtil;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountsQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountsQueryResponse;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AIAgentRegistryServiceTest {

    @InjectMocks
    private AiAgentProviderAccountServiceImpl aiAgentProviderService;

    @Mock
    private AiAgentProviderAccountRepository _aiAgentProviderAccountRepository;

    @Mock
    private AiAgentRepository _aiAgentRepository;

    @Mock
    private AiAgentLatestSyncRepository _aiAgentLatestSyncRepository;

    @Mock
    private UserUtil _userUtil;


    private DataFetchingEnvironment dfe;
    private static final String ACCOUNT_ID = "test-account-id";

    @BeforeEach
    void setUp() {
        // Mock DataFetchingEnvironment
        dfe = mock(DataFetchingEnvironment.class);
    }

    @Test
    void getAIAgentProviders_Success() {
        DataFetchingFieldSelectionSet dataFetchingFieldSelectionSet = mock(DataFetchingFieldSelectionSet.class);
        when(dataFetchingFieldSelectionSet.containsAnyOf(anyString(), anyString()))
                .thenReturn(true);
        when(dfe.getSelectionSet())
                .thenReturn(dataFetchingFieldSelectionSet);

        // Arrange
        AiAgentProviderAccountsQueryInput input = new AiAgentProviderAccountsQueryInput();
        input.setPageIndex(0);
        input.setPageSize(10);
        // Setup UserUtil mock behavior
        when(_userUtil.getAccountId(dfe)).thenReturn(ACCOUNT_ID);
        // Create test data for entity
        List<AiAgentProviderAccount> entityProviders = TestUtil.createTestProviders();
        Page<AiAgentProviderAccount> page =
                new PageImpl<>(entityProviders, PageRequest.of(0, 10), entityProviders.size());
        // Mock repository response
        Specification mockIdpAccountSpecification = mock(Specification.class);
        when(_aiAgentProviderAccountRepository.idpAccountId(ACCOUNT_ID))
                .thenReturn(mockIdpAccountSpecification);
        when(_aiAgentProviderAccountRepository.findAll(eq(mockIdpAccountSpecification), any(Pageable.class)))
                .thenReturn(page);

        // Extract UIDs from entityProviders
        entityProviders.stream()
                .map(AiAgentProviderAccount::getUid)
                .collect(Collectors.toList());

        // First create the agent count results
        List<Object[]> agentCountResults = TestUtil.createTestProviders().stream()
                .map(provider -> new Object[]{provider.getUid(),  // The registry account UID
                        2L                  // The count of agents
                }).collect(Collectors.toList());

        // Then use it in the mock
        when(_aiAgentRepository.countAgentsByProviderAccountUids(anyList())).thenReturn(agentCountResults);

        CompletableFuture<AiAgentProviderAccountsQueryResponse> futureResponse =
                aiAgentProviderService.getAiAgentProviders(input, dfe);
        assertNotNull(futureResponse);

        AiAgentProviderAccountsQueryResponse response = futureResponse.join();
        assertNotNull(response);
        assertEquals(3, response.getNumberOfResults().intValue());
        assertEquals(10, response.getCurrentPageSize().intValue());
        List<com.boomi.graphql.server.schema.types.AiAgentProviderAccount> providers = response.getAiAgentProviderAccounts();
        assertNotNull(providers);
        assertEquals(3, providers.size());

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount firstProvider = providers.get(0);
        assertResponseValues("us-east-1", firstProvider, AiAgentProviderType.AWS_BEDROCK,
                AiProviderAuthSchema.BOOMIAPITOKEN, AiAgentProviderAccountStatus.CONNECTED);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount secondProvider = providers.get(1);
        assertResponseValues("us-west-2", secondProvider, AiAgentProviderType.BOOMI,
                AiProviderAuthSchema.AWS, AiAgentProviderAccountStatus.DISABLED);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount thirdProvider = providers.get(2);
        assertResponseValues("eu-west-1", thirdProvider, AiAgentProviderType.BOOMI,
                AiProviderAuthSchema.APITOKEN, AiAgentProviderAccountStatus.SYNCING);

    }

    @Test
    void shouldThrowExceptionForNegativePageIndex() throws ExecutionException, InterruptedException {
        AiAgentProviderAccountsQueryInput input = new AiAgentProviderAccountsQueryInput();
        when(_userUtil.getAccountId(dfe)).thenReturn(ACCOUNT_ID);
        ExecutionStepInfo executionStepInfo = mock(ExecutionStepInfo.class);
        ResultPath path = mock(ResultPath.class);
        ExecutionContext executionContext = mock(ExecutionContext.class);

        when(dfe.getExecutionStepInfo()).thenReturn(executionStepInfo);
        when(executionStepInfo.getPath()).thenReturn(path);
        when(dfe.getExecutionContext()).thenReturn(executionContext);
        input.setPageIndex(-1); // Setting negative page number to trigger the validation error
        input.setPageSize(10);

        ArgumentCaptor<GraphQLError> errorCaptor = ArgumentCaptor.forClass(GraphQLError.class);
        doNothing().when(executionContext).addError(errorCaptor.capture());

        AiAgentProviderAccountsQueryResponse response = aiAgentProviderService.getAiAgentProviders(input, dfe).get();
        verify(executionContext).addError(any(GraphQLError.class));
        assertNull(response);
        assertEquals(
                "The provided page index is invalid. The page number must be 0 or greater. Please ensure the page number is valid.",
                errorCaptor.getValue().getMessage());
    }

    @Test
    void shouldThrowExceptionInvalidPageSize() throws ExecutionException, InterruptedException {
        AiAgentProviderAccountsQueryInput input = new AiAgentProviderAccountsQueryInput();
        when(_userUtil.getAccountId(dfe)).thenReturn(ACCOUNT_ID);
        ExecutionStepInfo executionStepInfo = mock(ExecutionStepInfo.class);
        ResultPath path = mock(ResultPath.class);
        ExecutionContext executionContext = mock(ExecutionContext.class);

        when(dfe.getExecutionStepInfo()).thenReturn(executionStepInfo);
        when(executionStepInfo.getPath()).thenReturn(path);
        when(dfe.getExecutionContext()).thenReturn(executionContext);
        input.setPageIndex(1);
        input.setPageSize(0);

        ArgumentCaptor<GraphQLError> errorCaptor = ArgumentCaptor.forClass(GraphQLError.class);
        doNothing().when(executionContext).addError(errorCaptor.capture());

        AiAgentProviderAccountsQueryResponse response = aiAgentProviderService.getAiAgentProviders(input, dfe).get();
        verify(executionContext).addError(any(GraphQLError.class));
        assertNull(response);
        assertEquals(
                "The provided page size is invalid. The page size must be greater than 0. Please ensure the page size is valid.",
                errorCaptor.getValue().getMessage());
    }

    private void assertResponseValues(String region, com.boomi.graphql.server.schema.types.AiAgentProviderAccount provider,
                                      AiAgentProviderType providerType, AiProviderAuthSchema authSchema, AiAgentProviderAccountStatus status) {
        assertEquals(region, provider.getRegion());
        assertEquals(providerType, provider.getProviderType());
        assertEquals(authSchema, provider.getAuthSchema());
        assertEquals(ACCOUNT_ID, provider.getIdpAccountId());
        assertEquals(status, provider.getProviderAccountStatus());
    }
}
