// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry;

import com.boomi.aiagentregistry.config.TimestreamQuerierConfig;
import com.boomi.graphql.server.servlet.GraphQLConfigurationBuilder;
import com.boomi.services.common.webflux.config.ErrorPageWebfluxConfig;
import com.boomi.services.common.webflux.graphql.GraphQLWebfluxConfig;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 */
@SpringBootConfiguration
@EnableAutoConfiguration
@ComponentScan(basePackages="com.boomi.aiagentregistry",
        excludeFilters = {
                @ComponentScan.Filter(type= FilterType.ASSIGNABLE_TYPE, value= Application.class),
                @ComponentScan.Filter(type= FilterType.ASSIGNABLE_TYPE, value= TimestreamQuerierConfig.class)
        })
@Import({ GraphQLWebfluxConfig.class,  ErrorPageWebfluxConfig.class})
public class TestApplication {

    public static void main(String[] args) {
        System.setProperty("liquibase.duplicateFileMode", "WARN");
        SpringApplication.run(TestApplication.class, args);
    }
    @Bean
    public TestWebFluxGraphQLExecutor executor(GraphQLConfigurationBuilder configBuilder) {
        return new TestWebFluxGraphQLExecutor(configBuilder);
    }

    //create bean to bootstrap jdbc
    @Bean
    public Object bootstrapJdbc() {
        return new Object();
    }
}
