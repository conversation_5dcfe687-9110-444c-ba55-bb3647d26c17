package com.boomi.aiagentregistry.servlet;

import okhttp3.mockwebserver.MockResponse;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class BedrockMockServerHelper {
    private final BedrockMockServerDispatcher _dispatcher;

    public BedrockMockServerHelper() {
        _dispatcher = new BedrockMockServerDispatcher();
    }

    public static class ResponseBuilder {
        private final BedrockMockServerHelper _helper;
        private final String _path;
        private int _responseCode = 200;
        private String _responseBody;
        private Map<String, String> _headers = new HashMap<>();

        ResponseBuilder(BedrockMockServerHelper helper, String path) {
            _helper = helper;
            _path = path;
        }
        public ResponseBuilder withResponseCode(int code) {
            _responseCode = code;
            return this;
        }

        public ResponseBuilder withBody(String body) {
            _responseBody = body;
            return this;
        }

        public ResponseBuilder withHeader(String name, String value) {
            _headers.put(name, value);
            return this;
        }

        public void enqueue() {
            MockResponse response = new MockResponse()
                    .setResponseCode(_responseCode);

            if (_responseBody != null) {
                response.setBody(_responseBody);
            }

            _headers.forEach((name, value) -> response.addHeader(name, value));
            _helper.getDispatcher().enqueueResponse(_path, response);
        }
    }

    public ResponseBuilder forPath(String path) {
        return new ResponseBuilder(this, path);
    }

    public BedrockMockServerDispatcher getDispatcher() {
        return _dispatcher;
    }

    public void reset() {
        _dispatcher.clearAll();
    }
}
