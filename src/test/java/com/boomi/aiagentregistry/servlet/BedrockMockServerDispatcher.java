package com.boomi.aiagentregistry.servlet;


import okhttp3.mockwebserver.Dispatcher;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.RecordedRequest;
import org.jetbrains.annotations.NotNull;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class BedrockMockServerDispatcher {
    private final Map<String, MockResponse> _responses;

    public BedrockMockServerDispatcher() {
        _responses = new ConcurrentHashMap<>();
    }

    public Dispatcher createDispatcher() {
        return new Dispatcher() {
            @Override
            public @NotNull MockResponse dispatch(@NotNull RecordedRequest request) {
                String path = request.getPath();
                return getResponseForPath(path);
            }
        };
    }

    private MockResponse getResponseForPath(String path) {
        MockResponse response = _responses.get(path);
        if (response != null) {
            return response;
        }

        String defaultResp = """
                {
                    "result": []
                }
                """;
        return new MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", "application/json")
                .setBody(defaultResp);

    }


    public void enqueueResponse(String path, MockResponse response) {
        String encodedPath = path.replace(":", "%3A");
        _responses.put(encodedPath, response);
    }

    public void clearAll() {
        _responses.clear();
    }

}

