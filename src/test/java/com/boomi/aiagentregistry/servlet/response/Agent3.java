package com.boomi.aiagentregistry.servlet.response;

public final class Agent3 {
    public static String ALAIS1 = Agent3Alias1.DETAILS;
    public static Agent3Version1 VERSION1 = new Agent3Version1();

    public static String DETAILS = """
            {
                  "agent": {
                      "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/D35OMTHREE",
                      "agentId": "D35OMTHREE",
                      "agentName": "agent 3 name should not be reflected",
                      "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_GQOOQUH87RR",
                      "agentStatus": "PREPARED",
                      "clientToken": "0db5972a-ee21-46cf-a7c7-8906ad217f0b",
                      "createdAt": "2024-11-28T03:43:13.142135+00:00",
                      "description": "this is the 3rd agent",
                      "idleSessionTTLInSeconds": 600,
                      "instruction": "this is the 3rd agent draft version, should not be reflected",
                      "preparedAt": "2024-11-28T03:43:33.723750+00:00",
                      "updatedAt": "2024-11-28T20:17:25.078683+00:00"
                  }
              }
            """;

    public static String DETAILS_WITH_LLM = """
            {
                  "agent": {
                      "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/D35OMTHREE",
                      "agentId": "D35OMTHREE",
                      "agentName": "agent 3 name should not be reflected",
                      "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_GQOOQUH87RR",
                      "agentStatus": "PREPARED",
                      "clientToken": "0db5972a-ee21-46cf-a7c7-8906ad217f0b",
                      "createdAt": "2024-11-28T03:43:13.142135+00:00",
                      "description": "this is the 3rd agent",
                      "foundationModel": "anthropic.claude-3-5-sonnet-20240620-v1:0",
                      "idleSessionTTLInSeconds": 600,
                      "instruction": "this is the 3rd agent draft version, should not be reflected",
                      "preparedAt": "2024-11-28T03:43:33.723750+00:00",
                      "updatedAt": "2024-11-28T20:17:25.078683+00:00"
                  }
              }
            """;

    public static String DETAILS_WITH_GR = """
            {
                  "agent": {
                      "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/D35OMTHREE",
                      "agentId": "D35OMTHREE",
                      "agentName": "agent 3 name should not be reflected",
                      "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_GQOOQUH87RR",
                      "agentStatus": "PREPARED",
                      "clientToken": "0db5972a-ee21-46cf-a7c7-8906ad217f0b",
                      "createdAt": "2024-11-28T03:43:13.142135+00:00",
                      "description": "this is the 3rd agent",
                       "guardrailConfiguration": {
                                  "guardrailIdentifier": "0vwlgjhd99dv",
                                  "guardrailVersion": "DRAFT"
                              },
            
                      "idleSessionTTLInSeconds": 600,
                      "instruction": "this is the 3rd agent draft version, should not be reflected",
                      "preparedAt": "2024-11-28T03:43:33.723750+00:00",
                      "updatedAt": "2024-11-28T20:17:25.078683+00:00"
                  }
              }
            """;

    public static String VERSION_DRAFT_SUMMARY = """
                     {
                          "agentName": "agent 3 draft version summary should not be reflected",
                          "agentStatus": "PREPARED",
                          "agentVersion": "DRAFT",
                          "createdAt": "2024-11-28T03:43:13.142135+00:00",
                          "description": "this is the 3rd agent draft version description which should not be reflected",
                          "updatedAt": "2024-11-28T20:17:25.078683+00:00"
                      }
            """;
    public static String VERSION1_SUMMARY = """
                     {
                          "agentName": "omar-registry-agent-3",
                          "agentStatus": "PREPARED",
                          "agentVersion": "1",
                          "createdAt": "2024-11-28T20:17:24.784381+00:00",
                          "description": "this is the 3rd agent",
                          "updatedAt": "2024-11-28T20:17:25.078683+00:00"
                      }
            """;

    public static final String ALIASES = """
            {
                "agentAliasSummaries": [
                    {
                        "agentAliasId": "DYXBADFLBD",
                        "agentAliasName": "omar-agent-3-alias-1",
                        "agentAliasStatus": "PREPARED",
                        "createdAt": "2024-11-28T20:17:23.453472+00:00",
                        "routingConfiguration": [
                            {
                                "agentVersion": "1"
                            }
                        ],
                        "updatedAt": "2024-11-28T20:17:23.453472+00:00"
                    }
                ]
            }
            """;


}
