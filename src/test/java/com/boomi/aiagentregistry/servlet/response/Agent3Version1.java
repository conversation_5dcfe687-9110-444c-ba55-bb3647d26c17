package com.boomi.aiagentregistry.servlet.response;

public final class Agent3Version1 {
    public static final String DETAILS = """
            {
                "agentVersion": {
                    "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/D35OMTHREE",
                    "agentId": "D35OMTHREE",
                    "agentName": "omar-registry-agent-3-version-1 from bedrock",
                    "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_GQOOQUH87RR",
                    "agentStatus": "PREPARED",
                    "createdAt": "2024-11-28T20:17:24.784381+00:00",
                    "description": "this is the 3rd agent version 1 from bedrock",
                    "idleSessionTTLInSeconds": 600,
                    "instruction": "this is the 3rd agent version 1 form bedrock",
                    "updatedAt": "2024-11-28T20:17:25.078683+00:00",
                    "version": "1"
                }
            }
            """;
    public static final String DETAILS_WITH_LLM = """
            {
                "agentVersion": {
                    "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/D35OMTHREE",
                    "agentId": "D35OMTHREE",
                    "agentName": "omar-registry-agent-3-version-1 from bedrock",
                    "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_GQOOQUH87RR",
                    "agentStatus": "PREPARED",
                    "createdAt": "2024-11-28T20:17:24.784381+00:00",
                    "description": "this is the 3rd agent version 1 from bedrock",
                    "foundationModel": "anthropic.claude-3-5-sonnet-20240620-v1:0",
                    "idleSessionTTLInSeconds": 600,
                    "instruction": "this is the 3rd agent version 1 form bedrock",
                    "updatedAt": "2024-11-28T20:17:25.078683+00:00",
                    "version": "1"
                }
            }
            """;


    public static final String DETAILS_WITH_GR = """
            {
                "agentVersion": {
                    "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/D35OMTHREE",
                    "agentId": "D35OMTHREE",
                    "agentName": "omar-registry-agent-3-version-1 from bedrock",
                    "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_GQOOQUH87RR",
                    "agentStatus": "PREPARED",
                    "createdAt": "2024-11-28T20:17:24.784381+00:00",
                    "description": "this is the 3rd agent version 1 from bedrock", 
                    "guardrailConfiguration": {
                          "guardrailIdentifier": "0vwlgjhd99dv",
                          "guardrailVersion": "DRAFT"
                      },
            
                    "idleSessionTTLInSeconds": 600,
                    "instruction": "this is the 3rd agent version 1 form bedrock",
                    "updatedAt": "2024-11-28T20:17:25.078683+00:00",
                    "version": "1"
                }
            }
            """;
}
