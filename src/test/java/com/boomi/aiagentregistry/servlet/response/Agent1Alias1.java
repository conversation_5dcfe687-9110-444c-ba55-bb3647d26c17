package com.boomi.aiagentregistry.servlet.response;

public final class Agent1Alias1 {
    public static String DETAILS =
            """
                    {
                        "agentAlias": {
                            "agentAliasArn": "arn:aws:bedrock:us-east-1:************:agent-alias/AGENT2UONE/ANTJDMWTRW",
                            "agentAliasHistoryEvents": [
                                {
                                    "endDate": "2024-11-29T04:42:52.671641+00:00",
                                    "routingConfiguration": [
                                        {}
                                    ],
                                    "startDate": "2024-11-29T04:42:49.554826+00:00"
                                },
                                {
                                    "routingConfiguration": [
                                        {
                                            "agentVersion": "1"
                                        }
                                    ],
                                    "startDate": "2024-11-29T04:42:52.671641+00:00"
                                }
                            ],
                            "agentAliasId": "ANTJDMWTRW",
                            "agentAliasName": "omar-agent-1-alias-1 from bedrock",
                            "agentAliasStatus": "PREPARED",
                            "agentId": "AGENT2UONE",
                            "clientToken": "b78dd592-543b-4c34-a603-a85db56402f9",
                            "createdAt": "2024-11-29T04:42:49.554826+00:00",
                            "description": "this is the description for agent 1 alias 1 pointing to version 1 from bedrock",
                            "routingConfiguration": [
                                {
                                    "agentVersion": "1"
                                }
                            ],
                            "updatedAt": "2024-11-29T04:42:49.554826+00:00"
                        }
                    }
                    """;
}
