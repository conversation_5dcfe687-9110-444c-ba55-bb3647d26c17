package com.boomi.aiagentregistry.servlet.response;

public class Agent2Alias1 {
    public static final String DETAILS = """
            {
                 "agentAlias": {
                     "agentAliasArn": "arn:aws:bedrock:us-east-1:************:agent-alias/M2NT3YGTWO/FCVOYDFNMW",
                     "agentAliasHistoryEvents": [
                         {
                             "endDate": "2024-11-28T20:17:26.772612+00:00",
                             "routingConfiguration": [
                                 {}
                             ],
                             "startDate": "2024-11-28T20:17:23.453472+00:00"
                         },
                         {
                             "routingConfiguration": [
                                 {
                                     "agentVersion": "14"
                                 }
                             ],
                             "startDate": "2024-11-28T20:17:26.772612+00:00"
                         }
                     ],
                     "agentAliasId": "FCVOYDFNMW",
                     "agentAliasName": "omar-agent-2-alias-2",
                     "agentAliasStatus": "PREPARED",
                     "agentId": "M2NT3YGTWO",
                     "clientToken": "2619d290-adca-470f-a785-a44f61a6efa0",
                     "createdAt": "2024-11-28T20:17:23.453472+00:00",
                     "routingConfiguration": [
                         {
                             "agentVersion": "14"
                         }
                     ],
                     "updatedAt": "2024-11-28T20:17:23.453472+00:00"
                 }
             }
            """;
}
