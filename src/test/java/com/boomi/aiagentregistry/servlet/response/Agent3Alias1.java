package com.boomi.aiagentregistry.servlet.response;

public final class Agent3Alias1 {
    public static String DETAILS = """
            {
                 "agentAlias": {
                     "agentAliasArn": "arn:aws:bedrock:us-east-1:************:agent-alias/D35OMTHREE/DYXBADFLBD",
                     "agentAliasHistoryEvents": [
                         {
                             "endDate": "2024-11-28T20:17:26.772612+00:00",
                             "routingConfiguration": [
                                 {}
                             ],
                             "startDate": "2024-11-28T20:17:23.453472+00:00"
                         },
                         {
                             "routingConfiguration": [
                                 {
                                     "agentVersion": "14"
                                 }
                             ],
                             "startDate": "2024-11-28T20:17:26.772612+00:00"
                         }
                     ],
                     "agentAliasId": "DYXBADFLBD",
                     "agentAliasName": "omar-agent-3-alias-1",
                     "agentAliasStatus": "PREPARED",
                     "agentId": "D35OMTHREE",
                     "clientToken": "2619d290-adca-470f-a785-a44f61a6efa0",
                     "createdAt": "2024-11-28T20:17:23.453472+00:00",
                     "routingConfiguration": [
                         {
                             "agentVersion": "1"
                         }
                     ],
                     "updatedAt": "2024-11-28T20:17:23.453472+00:00"
                 }
             }
            """;

}
