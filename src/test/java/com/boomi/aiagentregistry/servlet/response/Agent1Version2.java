package com.boomi.aiagentregistry.servlet.response;

public final class Agent1Version2 {
    public final Agent1Version1ActionGroup ACTION_GROUPS = new Agent1Version1ActionGroup();
    public static String DETAILS = """
            {
                 "agentVersion": {
                     "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/AGENT2UONE",
                     "agentId": "AGENT2UONE",
                     "agentName": "agent 1 version 2 name from bedrock",
                     "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_JHHH3O989KS",
                     "agentStatus": "PREPARED",
                     "createdAt": "2024-11-29T04:42:50.826791+00:00",
                     "description": "this is agent 1 version 2 description from bedrock",
                     "idleSessionTTLInSeconds": 600,
                     "instruction": "this is agent 1 version 2 instructions from bedrock",
                     "updatedAt": "2024-11-29T04:42:51.102880+00:00",
                     "version": "2"
                 }
             }
            """;

}
