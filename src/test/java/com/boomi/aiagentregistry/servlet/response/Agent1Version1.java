package com.boomi.aiagentregistry.servlet.response;

public final class Agent1Version1 {

    public static String DETAILS = """
            {
                "agentVersion": {
                    "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/AGENT2UONE",
                    "agentId": "AGENT2UONE",
                    "agentName": "Agent 1 Version 1 name to be reflected",
                    "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_JHHH3O989KS",
                    "agentStatus": "PREPARED",
                    "createdAt": "2024-11-29T04:43:02.855990+00:00",
                    "description": "this is agent 1 version 1 description to be reflected",
                    "idleSessionTTLInSeconds": 600,
                    "instruction": "this is the instruction for agent 1 version 1 to be reflected",
                    "updatedAt": "2024-11-28T05:42:51.102880+00:00",
                    "version": "1"
                }
            }
            """;

    public String DETAILS_WITH_LLM = """
            {
                "agentVersion": {
                    "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/AGENT2UONE",
                    "agentId": "AGENT2UONE",
                    "agentName": "Agent 1 Version 1 name to be reflected",
                    "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_JHHH3O989KS",
                    "agentStatus": "PREPARED",
                    "createdAt": "2024-11-29T04:43:02.855990+00:00",
                    "description": "this is agent 1 version 1 description to be reflected",
                    "foundationModel": "anthropic.claude-3-5-sonnet-20240620-v1:0",
                    "idleSessionTTLInSeconds": 600,
                    "instruction": "this is the instruction for agent 1 version 1 to be reflected",
                    "updatedAt": "2024-11-28T05:42:51.102880+00:00",
                    "version": "1"
                }
            }
            """;
    public String DETAILS_WITH_GR = """
            {
                "agentVersion": {
                    "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/AGENT2UONE",
                    "agentId": "AGENT2UONE",
                    "agentName": "Agent 1 Version 1 name to be reflected",
                    "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_JHHH3O989KS",
                    "agentStatus": "PREPARED",
                    "createdAt": "2024-11-29T04:43:02.855990+00:00",
                    "description": "this is agent 1 version 1 description to be reflected",
                     "guardrailConfiguration": {
                                "guardrailIdentifier": "0vwlgjhd99dv",
                                "guardrailVersion": "DRAFT"
                            },
            
                    "idleSessionTTLInSeconds": 600,
                    "instruction": "this is the instruction for agent 1 version 1 to be reflected",
                    "updatedAt": "2024-11-28T05:42:51.102880+00:00",
                    "version": "1"
                }
            }
            """;

}
