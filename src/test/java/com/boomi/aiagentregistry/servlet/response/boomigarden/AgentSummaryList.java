package com.boomi.aiagentregistry.servlet.response.boomigarden;

import com.boomi.aiagentregistry.util.GuidUtil;

import java.util.Map;

public final class AgentSummaryList {
    public static final Map<String, Object> AGENT1_SUMMARY = Map.of(
            "guid", GuidUtil.createAIAgentGuid(),
                "id", "4928833b-24f9-4984-8f8c-21a5ce057a75",
                "created_on", "2025-02-06T14:34:11Z",
                "last_updated_on", "2025-02-17T20:16:47Z",
                "objective", "Final goal 1 for HRBuddy- updated Feb7 2:16PM",
                "name", "HRBuddy- updated 12:16PM",
                "agent_status", "ACTIVE",
                "provider_name", "Boomi"
            );

    public static Map<String, Object> AGENT2_SUMMARY = Map.of(
                  "id", "514630c7-da7c-4239-a725-0b26213f57bb",
                 "created_on", "2025-01-29T01:57:54.427312Z",
                 "last_updated_on", "2025-02-17T19:53:09.853281Z",
                 "objective", "Testing bot, ok to delete after 2/1/2025",
                 "name", "Temp-1-28-2025 - Updated again Feb5",
                 "agent_status", "DISABLED",
                 "provider_name", "Boomi"
            );

    public static String AGENT3_SUMMARY = """
            {
                "id": "b12de029-bf7e-44a8-b756-4ae4450b7fbf",
                "created_on": "2025-02-01T04:26:54.962478Z",
                "last_updated_on": "2025-02-01T04:33:21.484270Z",
                "objective": "Provides first-line IT support, manages tickets, and escalates complex issues.",
                "name": "ITSupportBot #2 Testing",
                "agent_status": "ACTIVE",
                "provider_name": "Boomi"
            }
            """;

}

