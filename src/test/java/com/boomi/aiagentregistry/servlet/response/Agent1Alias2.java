package com.boomi.aiagentregistry.servlet.response;

public final class Agent1Alias2 {
    public String DETAILS = """
            {
                "agentAlias": {
                    "agentAliasArn": "arn:aws:bedrock:us-east-1:************:agent-alias/AGENT2UONE/JFQEX0LEQT",
                    "agentAliasHistoryEvents": [
                        {
                            "endDate": "2024-11-29T04:43:04.811990+00:00",
                            "routingConfiguration": [
                                {}
                            ],
                            "startDate": "2024-11-29T04:43:01.165772+00:00"
                        },
                        {
                            "routingConfiguration": [
                                {
                                    "agentVersion": "2"
                                }
                            ],
                            "startDate": "2024-11-29T04:43:04.811990+00:00"
                        }
                    ],
                    "agentAliasId": "JFQEX0LEQT",
                    "agentAliasName": "omar-agent-1-alias-2",
                    "agentAliasStatus": "PREPARED",
                    "agentId": "AGENT2UONE",
                    "clientToken": "5041e0e8-6fe1-4f5f-9d10-a46c094ee3e4",
                    "createdAt": "2024-11-29T04:43:01.165772+00:00",
                    "description": "this is the description for agent 1 alias 2 pointing to version 1",
                    "routingConfiguration": [
                        {
                            "agentVersion": "2"
                        }
                    ],
                    "updatedAt": "2024-11-29T04:43:01.165772+00:00"
                }
            }
            """;

}
