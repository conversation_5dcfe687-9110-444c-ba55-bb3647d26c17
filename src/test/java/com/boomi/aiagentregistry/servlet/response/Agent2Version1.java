package com.boomi.aiagentregistry.servlet.response;

public final class Agent2Version1 {
    public static String DETAILS = """
            {
                "agentVersion": {
                    "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/M2NT3YGTWO",
                    "agentId": "M2NT3YGTWO",
                    "agentName": "omar-registry-agent-2",
                    "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_GQOOQUH87RR",
                    "agentStatus": "PREPARED",
                    "createdAt": "2024-11-28T20:17:24.784381+00:00",
                    "description": "this is the agent 2 version 1(14)",
                    "idleSessionTTLInSeconds": 600,
                    "instruction": "this is the 2nd agent version 1(14) instructions ",
                    "updatedAt": "2024-11-28T20:17:25.078683+00:00",
                    "version": "14"
                }
            }
            """;
    public static String DETAILS_WITH_LLM = """
            {
                "agentVersion": {
                    "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/M2NT3YGTWO",
                    "agentId": "M2NT3YGTWO",
                    "agentName": "omar-registry-agent-2",
                    "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_GQOOQUH87RR",
                    "agentStatus": "PREPARED",
                    "createdAt": "2024-11-28T20:17:24.784381+00:00",
                    "description": "this is the agent 2 version 1(14)",
                    "foundationModel": "anthropic.claude-3-5-sonnet-20240620-v1:0",
                    "idleSessionTTLInSeconds": 600,
                    "instruction": "this is the 2nd agent version 1(14) instructions ",
                    "updatedAt": "2024-11-28T20:17:25.078683+00:00",
                    "version": "14"
                }
            }
            """;

}
