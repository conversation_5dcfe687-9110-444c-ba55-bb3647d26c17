package com.boomi.aiagentregistry.servlet.response;

public class Agent2 {
    public static String VERSION1 = Agent2Version1.DETAILS;
    public static String ALIAS1 = Agent2Alias1.DETAILS;
    public static String ALIAS2 = Agent2Alias2.DETAILS;

    public static String DETAILS = """
            {
                  "agent": {
                      "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/M2NT3YGTWO",
                      "agentId": "M2NT3YGTWO",
                      "agentName": "Agent 2 Name Original",
                      "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_GQOOQUH87RR",
                      "agentStatus": "PREPARED",
                      "clientToken": "0db5972a-ee21-46cf-a7c7-8906ad217f0b",
                      "createdAt": "2024-11-28T03:43:13.142135+00:00",
                      "description": "this is agent 2 version draft",
                      "idleSessionTTLInSeconds": 600,
                      "instruction": "this is agent 2 draft version instructions",
                      "preparedAt": "2024-11-28T03:43:33.723750+00:00",
                      "updatedAt": "2024-11-28T20:17:25.071535+00:00"
                  }
              }
            """;

    public static String DETAILS_WITH_LLM = """
            {
                  "agent": {
                      "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/M2NT3YGTWO",
                      "agentId": "M2NT3YGTWO",
                      "agentName": "Agent 2 Name Original",
                      "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_GQOOQUH87RR",
                      "agentStatus": "PREPARED",
                      "clientToken": "0db5972a-ee21-46cf-a7c7-8906ad217f0b",
                      "createdAt": "2024-11-28T03:43:13.142135+00:00",
                      "description": "this is agent 2 version draft",
                      "foundationModel": "anthropic.claude-3-5-sonnet-20240620-v1:0",
                      "idleSessionTTLInSeconds": 600,
                      "instruction": "this is agent 2 draft version instructions",
                      "preparedAt": "2024-11-28T03:43:33.723750+00:00",
                      "updatedAt": "2024-11-28T20:17:25.071535+00:00"
                  }
              }
            """;

    public static String VERSION_DRAFT_SUMMARY = """
             {
                "agentName": "Agent 2 Name Original",
                "agentStatus": "PREPARED",
                "agentVersion": "DRAFT",
                "createdAt": "2024-11-28T03:43:13.142135+00:00",
                "description": "this is agent 2 version draft",
                "updatedAt": "2024-11-28T20:17:25.071535+00:00"
            }
            """;
    public static String VERSION1_SUMMARY = """
              {
                "agentName": "omar-registry-agent-2-version-1",
                "agentStatus": "PREPARED",
                "agentVersion": "14",
                "createdAt": "2024-11-28T20:17:24.784381+00:00",
                "description": "this is agent 2 version 1",
                "updatedAt": "2024-11-28T20:17:25.078683+00:00"
            }
            """;

    public static final String ALIASES = """
            {
                "agentAliasSummaries": [
                    {
                        "agentAliasId": "FCVOYDFNMW",
                        "agentAliasName": "omar-agent-2-alias-1",
                        "agentAliasStatus": "PREPARED",
                        "createdAt": "2024-11-28T20:17:23.453472+00:00",
                        "routingConfiguration": [
                            {
                                "agentVersion": "14"
                            }
                        ],
                        "updatedAt": "2024-11-28T20:17:23.453472+00:00"
                    },
                    {
                        "agentAliasId": "TSTALIASID",
                        "agentAliasName": "agent-2-alias-1",
                        "agentAliasStatus": "PREPARED",
                        "createdAt": "2024-11-28T03:43:13.741447+00:00",
                        "description": "Test Alias for Agent 2 alias 1(draft)",
                        "routingConfiguration": [
                            {
                                "agentVersion": "DRAFT"
                            }
                        ],
                        "updatedAt": "2024-11-28T03:43:13.741447+00:00"
                    }
                ]
            }
            """;
}



