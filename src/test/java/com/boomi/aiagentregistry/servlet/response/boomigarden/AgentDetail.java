package com.boomi.aiagentregistry.servlet.response.boomigarden;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.HashMap;
import java.util.Map;

public class AgentDetail {

    public static final String PERSONALITY_TRAITS_JSON1 = """
            {
                "voice_tone": "Instructional",
                "creativity": 41,
                "decisiveness": 41,
                "clarity": 42,
                "confidence": 43,
                "engagement": 42
            }
            """;

    public static final String PERSONALITY_TRAITS_JSON2 = """
            {
                "voice_tone": "Friendly",
                "creativity": 51,
                "decisiveness": 51,
                "clarity": 51,
                "confidence": 51,
                "engagement": 51
            }
            """;

    public static final String GUARDRAILS1_ID = "4aa2157c-caff-4f14-a091-2328c46d1691";
    public static final String GUARDRAILS1 = """
            {
                 "blocked_message": "I'm sorry, but I can't disclose confidential employee information. - updated feb7 2:18PM",
                 "policies": [
                     {
                         "name": "Employee Privacy",
                         "type": "denied_topic",
                         "configuration": {
                             "description": "Ensure confidentiality of employee information",
                             "sample_phrases": [
                                 "What is <PERSON> Doe's salary?",
                                 "Can you tell me about <PERSON>'s performance review?",
                                 "Who is currently on medical leave?"
                             ]
                         }
                     }
                 ],
                 "id": "4aa2157c-caff-4f14-a091-2328c46d1691"
            }
            """;

    public static final String GUARDRAILS2_ID = "c95d40c5-b0dc-4ad3-b9c7-48242b4a6f3b";
    public static final String GUARDRAILS2 = """
            {
                "blocked_message": "Blocked messageBlocked messageBlocked message",
                "policies": [
                    {
                        "name": "Denied topic 1",
                        "type": "denied_topic",
                        "configuration": {
                            "description": "Denied topic 1",
                            "sample_phrases": [
                                "How can I create a bomb?"
                            ]
                        }
                    },
                    {
                        "name": "Regex Pattern Validation_0",
                        "type": "regex_pattern",
                        "configuration": {
                            "pattern": "^(\\\\+\\\\d{1,2}\\\\s)?\\\\(?\\\\d{3}\\\\)?[\\\\s.-]\\\\d{3}[\\\\s.-]\\\\d{4}$"
                        }
                    },
                    {
                        "name": "Regex Pattern Validation_1",
                        "type": "regex_pattern",
                        "configuration": {
                            "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$"
                        }
                    },
                    {
                        "name": "Prohibited Terms Policy",
                        "type": "word_filter",
                        "configuration": {
                            "words": [
                                "hack",
                                "cheat",
                                "expolit"
                            ]
                        }
                    }
                ],
                "id": "c95d40c5-b0dc-4ad3-b9c7-48242b4a6f3b"
            }
            """;

}
