package com.boomi.aiagentregistry.servlet.response;

public final class Agent1Version1ActionGroup {
    public final Fun2 FUN2 = new Fun2();

    public final Api3 API3 = new Api3();
    public String LIST = """
            {
                 "actionGroupSummaries": [
                     {
                         "actionGroupId": "KEEPFUNCIR",
                         "actionGroupName": "action_grp_fun_to_keep",
                         "actionGroupState": "ENABLED",
                         "updatedAt": "2024-12-02T01:01:38.587446+00:00"
                     },
                     {
                         "actionGroupId": "LAPI2TSQDZ",
                         "actionGroupName": "actn_grp_2",
                         "actionGroupState": "ENABLED",
                         "description": "desc",
                         "updatedAt": "2024-12-02T01:02:57.154725+00:00"
                     }
                     
                 ]
             }
            """;



    public final class Fun2 {
        public String DETAILS = """
                {
                      "agentActionGroup": {
                          "actionGroupExecutor": {
                              "lambda": "arn:aws:lambda:us-east-1:918104040484:function:action_group_1-nabqd"
                          },
                          "actionGroupId": "DFUN2V4CIR",
                          "actionGroupName": "action_group_1",
                          "actionGroupState": "ENABLED",
                          "agentId": "AGENT2UONE",
                          "agentVersion": "DRAFT",
                          "clientToken": "e5ff1536-7bb4-49de-9648-f89b33183fe1",
                          "createdAt": "2024-12-02T01:01:38.587446+00:00",
                          "functionSchema": {
                              "functions": [
                                  {
                                      "name": "grp_1_fun_1",
                                      "parameters": {
                                          "grp_1_fun_1_param_1": {
                                              "description": "this is the description ",
                                              "required": false,
                                              "type": "string"
                                          }
                                      },
                                      "requireConfirmation": "DISABLED"
                                  },
                                  {
                                      "name": "act_1_fun_2",
                                      "parameters": {
                                          "actn_1_fun_2_param_1": {
                                              "description": "des",
                                              "required": false,
                                              "type": "string"
                                          }
                                      },
                                      "requireConfirmation": "DISABLED"
                                  }
                              ]
                          },
                          "updatedAt": "2024-12-02T01:01:38.587446+00:00"
                      }
                  }
                """;

    }



    public final class Api3 {
        public String DETAILS = """
                {
                    "agentActionGroup": {
                        "actionGroupExecutor": {
                            "customControl": "RETURN_CONTROL"
                        },
                        "actionGroupId": "KEEPAPIQDZ",
                        "actionGroupName": "action_grp_api_2_keep",
                        "actionGroupState": "ENABLED",
                        "agentId": "LAPI2TSQDZ",
                        "agentVersion": "DRAFT",
                        "apiSchema": {
                            "payload": "openapi: 3.0.0\\ninfo:\\n  title: Insurance Claims Automation API\\n  version: 1.0.0\\n  description: APIs for managing insurance claims by pulling a list of open claims, identifying outstanding paperwork for each claim, and sending reminders to policy holders.\\npaths:\\n  /claims:\\n    get:\\n      summary: Get a list of all open claims\\n      description: Get the list of all open insurance claims. Return all the open claimIds.\\n      operationId: getAllOpenClaims\\n      responses:\\n        \\"200\\":\\n          description: Gets the list of all open insurance claims for policy holders\\n          content:\\n            application/json:\\n              schema:\\n                type: array\\n                items:\\n                  type: object\\n                  properties:\\n                    claimId:\\n                      type: string\\n                      description: Unique ID of the claim.\\n                    policyHolderId:\\n                      type: string\\n                      description: Unique ID of the policy holder who has filed the claim.\\n                    claimStatus:\\n                      type: string\\n                      description: The status of the claim. Claim can be in Open or Closed state\\n  /claims/{claimId}/identify-missing-documents:\\n    get:\\n      summary: Identify missing documents for a specific claim\\n      description: Get the list of pending documents that need to be uploaded by policy holder before the claim can be processed. The API takes in only one claim id and returns the list of documents that are pending to be uploaded by policy holder for that claim. This API should be called for each claim id\\n      operationId: identifyMissingDocuments\\n      parameters:\\n        - name: claimId\\n          in: path\\n          description: Unique ID of the open insurance claim\\n          required: true\\n          schema:\\n            type: string\\n      responses:\\n        \\"200\\":\\n          description: List of documents that are pending to be uploaded by policy holder for insurance claim\\n          content:\\n            application/json:\\n              schema:\\n                type: object\\n                properties:\\n                  pendingDocuments:\\n                    type: string\\n                    description: The list of pending documents for the claim.\\n  /send-reminders:\\n    post:\\n      summary: API to send reminder to the customer about pending documents for open claim\\n      description: Send reminder to the customer about pending documents for open claim. The API takes in only one claim id and its pending documents at a time, sends the reminder and returns the tracking details for the reminder. This API should be called for each claim id you want to send reminders for.\\n      operationId: sendReminders\\n      requestBody:\\n        required: true\\n        content:\\n          application/json:\\n            schema:\\n              type: object\\n              properties:\\n                claimId:\\n                  type: string\\n                  description: Unique ID of open claims to send reminders for.\\n                pendingDocuments:\\n                  type: string\\n                  description: The list of pending documents for the claim.\\n              required:\\n                - claimId\\n                - pendingDocuments\\n      responses:\\n        \\"200\\":\\n          description: Reminders sent successfullyTE\\n          content:\\n            application/json:\\n              schema:\\n                type: object\\n                properties:\\n                  sendReminderTrackingId:\\n                    type: string\\n                    description: Unique Id to track the status of the send reminder Call\\n                  sendReminderStatus:\\n                    type: string\\n                    description: Status of send reminder notifications\\n        \\"400\\":\\n          description: Bad request. One or more required fields are missing or invalid."
                        },
                        "clientToken": "6b08cce7-d255-4821-8ee4-f21f4c239c00",
                        "createdAt": "2024-12-04T00:38:45.721895+00:00",
                        "updatedAt": "2024-12-02T01:02:57.154725+00:00"
                    }
                }
                """;
    }
}
