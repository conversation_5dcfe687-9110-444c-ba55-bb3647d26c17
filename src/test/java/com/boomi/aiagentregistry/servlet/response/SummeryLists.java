package com.boomi.aiagentregistry.servlet.response;

public final class SummeryLists {
    public static final String AGENT1_SUMMARY = """
            
            {
                "agentId": "AGENT2UONE",
                "agentName": "Agent 1 Version 1 name from summary",
                "agentStatus": "PREPARED",
                "description": "this is agent 1 from summary ",
                "latestAgentVersion": "2",
                "updatedAt": "2024-11-29T04:43:03.114351+00:00"
            }
            """;
    public static String AGENT2_SUMMARY = """
             {
                "agentId": "M2NT3YGTWO",
                "agentName": "omar-registry-agent-2 from summary",
                "agentStatus": "PREPARED",
                "description": "this is agent 2 version draft from summary",
                "latestAgentVersion": "14",
                "updatedAt": "2024-11-28T20:17:25.071535+00:00"
            }
            """;
    public static String AGENT3_SUMMARY = """
                      {
                          "agentId": "D35OMTHREE",
                          "agentName": "omar-registry-agent-3 from summary ",
                          "agentStatus": "PREPARED",
                          "description": "this is agent 3 from summary ",
                          "latestAgentVersion": "1",
                          "updatedAt": "2024-11-28T20:17:25.078683+00:00"
                      }
            """;

}

