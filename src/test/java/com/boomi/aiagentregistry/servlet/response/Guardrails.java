package com.boomi.aiagentregistry.servlet.response;

public final class Guardrails {
    public static final String GR_1 = """
            {
                 "name": "omar-testing-guardrail-update-name from bedrock",
                 "description": "this is a guard rail to test reg from bedrock",
                 "guardrailId": "0vwlgjhd99dv",
                 "guardrailArn": "arn:aws:bedrock:us-east-1:918104040484:guardrail/0vwlgjhd99dv",
                 "version": "DRAFT",
                 "status": "READY",
                 "topicPolicy": {
                     "topics": [
                         {
                             "name": "politics",
                             "definition": "questioning who is a better candidate or which party should I support",
                             "examples": [],
                             "type": "DENY"
                         }
                     ]
                 },
                 "contentPolicy": {
                     "filters": [
                         {
                             "type": "VIOLENCE",
                             "inputStrength": "HIGH",
                             "outputStrength": "HIGH"
                         },
                         {
                             "type": "PROMPT_ATTACK",
                             "inputStrength": "NONE",
                             "outputStrength": "NONE"
                         },
                         {
                             "type": "MISCONDUCT",
                             "inputStrength": "HIGH",
                             "outputStrength": "HIGH"
                         },
                         {
                             "type": "HATE",
                             "inputStrength": "HIGH",
                             "outputStrength": "HIGH"
                         },
                         {
                             "type": "SEXUAL",
                             "inputStrength": "HIGH",
                             "outputStrength": "HIGH"
                         },
                         {
                             "type": "INSULTS",
                             "inputStrength": "HIGH",
                             "outputStrength": "HIGH"
                         }
                     ]
                 },
                 "wordPolicy": {
                     "words": [
                         {
                             "text": "democratic"
                         }
                     ],
                     "managedWordLists": []
                 },
                 "sensitiveInformationPolicy": {
                     "piiEntities": [
                         {
                             "type": "NAME",
                             "action": "ANONYMIZE"
                         }
                     ],
                     "regexes": []
                 },
                 "createdAt": "2024-12-18T20:59:12+00:00",
                 "updatedAt": "2025-01-06T19:25:17.810993+00:00",
                 "statusReasons": [],
                 "failureRecommendations": [],
                 "blockedInputMessaging": "Sorry, the model cannot answer this question.",
                 "blockedOutputsMessaging": "Sorry, the model cannot answer this question."
             }
            
            """;
}
