package com.boomi.aiagentregistry.servlet.response;

public final class Kbs {
    public static final String KB_1 = """
            {
                 "knowledgeBase": {
                     "createdAt": "2024-12-10T17:04:03.683794+00:00",
                     "description": "knowledge base 1 description from bedrock",
                     "knowledgeBaseArn": "arn:aws:bedrock:us-east-1:918104040484:knowledge-base/ZIM1IYS9CP",
                     "knowledgeBaseConfiguration": {
                         "type": "VECTOR",
                         "vectorKnowledgeBaseConfiguration": {
                             "embeddingModelArn": "arn:aws:bedrock:us-east-1::foundation-model/amazon.titan-embed-text-v2:0",
                             "embeddingModelConfiguration": {
                                 "bedrockEmbeddingModelConfiguration": {
                                     "dimensions": 1024
                                 }
                             }
                         }
                     },
                     "knowledgeBaseId": "ZIM1IYS9CP",
                     "name": "Knowledgebase 1 name From Bedrock",
                     "roleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForKnowledgeBase_zo0fn",
                     "status": "ACTIVE",
                     "storageConfiguration": {
                         "opensearchServerlessConfiguration": {
                             "collectionArn": "arn:aws:aoss:us-east-1:918104040484:collection/x3ehc4hta5t97zn63wc5",
                             "fieldMapping": {
                                 "metadataField": "AMAZON_BEDROCK_METADATA",
                                 "textField": "AMAZON_BEDROCK_TEXT",
                                 "vectorField": "bedrock-knowledge-base-default-vector"
                             },
                             "vectorIndexName": "bedrock-knowledge-base-default-index"
                         },
                         "type": "OPENSEARCH_SERVERLESS"
                     },
                     "updatedAt": "2024-12-10T17:19:36.286117+00:00"
                 }
             }
            """;

    public static final String KB_2 = """
            {
                  "knowledgeBase": {
                      "createdAt": "2024-11-01T20:16:10.569057+00:00",
                      "knowledgeBaseArn": "arn:aws:bedrock:us-east-1:918104040484:knowledge-base/XLZPLVQ51N",
                      "knowledgeBaseConfiguration": {
                          "type": "VECTOR",
                          "vectorKnowledgeBaseConfiguration": {
                              "embeddingModelArn": "arn:aws:bedrock:us-east-1::foundation-model/amazon.titan-embed-text-v1"
                          }
                      },
                      "knowledgeBaseId": "XLZPLVQ51N",
                      "name": "KNOWLEDGE BASE 2 NAME FROM BEDROCK",
                      "description": "KNOWLEDGE BASE 2 DESCRIPTION FROM BEDROCK",
                      "roleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForKnowledgeBase_itt6d",
                      "status": "ACTIVE",
                      "storageConfiguration": {
                          "opensearchServerlessConfiguration": {
                              "collectionArn": "arn:aws:aoss:us-east-1:918104040484:collection/omnaes9hgiki87j4jfu2",
                              "fieldMapping": {
                                  "metadataField": "AMAZON_BEDROCK_METADATA",
                                  "textField": "AMAZON_BEDROCK_TEXT_CHUNK",
                                  "vectorField": "bedrock-knowledge-base-default-vector"
                              },
                              "vectorIndexName": "bedrock-knowledge-base-default-index"
                          },
                          "type": "OPENSEARCH_SERVERLESS"
                      },
                      "updatedAt": "2024-11-20T17:14:40.501117+00:00"
                  }
              }
            """;

    public static String KB_1_SUMMARY = """
            {
                "description": "test ",
                "knowledgeBaseId": "ZIM1IYS9CP",
                "knowledgeBaseState": "ENABLED",
                "updatedAt": "2024-11-20T17:14:40.501117+00:00"
            }
            """;

    public static String KB_2_SUMMARY = """
            {
                "description": "second KB",
                "knowledgeBaseId": "XLZPLVQ51N",
                "knowledgeBaseState": "ENABLED",
                "updatedAt": "2024-11-20T17:14:40.501117+00:00"
            }
            """;
}
