package com.boomi.aiagentregistry.servlet.response;

public final class Kb1DataSources {
    public static final String DATA_SOURCE_1_SUMMARY = """
            {
                "dataSourceId": "DS11111111",
                "knowledgeBaseId": "ZIM1IYS9<PERSON>",
                "name": "KB 1 data source 1 name from bedrock",
                "status": "AVAIL<PERSON>LE",
                "updatedAt": "2024-12-10T17:04:04.329120+00:00"
            }
            """;
    public static final String DATA_SOURCE_2_SUMMARY = """
            {
                "dataSourceId": "DS22222222",
                "knowledgeBaseId": "ZIM1IYS9CP",
                "name": "KB 1 data source 2 name from bedrock",
                "status": "AVA<PERSON><PERSON><PERSON>",
                "updatedAt": "2024-12-11T17:04:04.329120+00:00"
            }
            """;
    public static final String DATA_SOURCE_3_SUMMARY = """
            {
                "dataSourceId": "DS33333333",
                "knowledgeBaseId": "ZIM1IYS9CP",
                "name": "KB 1 data source 3 name from bedrock",
                "status": "AVAILABLE",
                "updatedAt": "2024-12-12T17:04:04.329120+00:00"
            }
            """;
    public static final String DS1 = """
            {
                 "dataSource": {
                     "createdAt": "2024-12-10T17:04:04.329120+00:00",
                     "dataDeletionPolicy": "DELETE",
                     "dataSourceConfiguration": {
                         "type": "CUSTOM"
                     },
                     "dataSourceId": "DS11111111",
                     "knowledgeBaseId": "ZIM1IYS9CP",
                     "name": "KB 1 data source 1 name from bedrock",
                     "status": "AVAILABLE",
                     "updatedAt": "2024-12-10T17:04:04.329120+00:00",
                     "vectorIngestionConfiguration": {}
                 }
             }
            """;
    public static final String DS2 = """
            {
                 "dataSource": {
                     "createdAt": "2024-12-10T17:04:04.329120+00:00",
                     "dataDeletionPolicy": "DELETE",
                     "dataSourceConfiguration": {
                         "type": "CUSTOM"
                     },
                     "dataSourceId": "DS22222222",
                     "knowledgeBaseId": "ZIM1IYS9CP",
                     "name": "KB 1 data source 2 name from bedrock",
                     "status": "AVAILABLE",
                     "updatedAt": "2024-12-11T17:04:04.329120+00:00",
                     "vectorIngestionConfiguration": {}
                 }
             }
            """;
    public static final String DS3 = """
            {
                 "dataSource": {
                     "createdAt": "2024-12-10T17:04:04.329120+00:00",
                     "dataDeletionPolicy": "DELETE",
                     "dataSourceConfiguration": {
                         "type": "CUSTOM"
                     },
                     "dataSourceId": "DS33333333",
                     "knowledgeBaseId": "ZIM1IYS9CP",
                     "name": "KB 1 data source 3 name from bedrock",
                     "status": "AVAILABLE",
                     "updatedAt": "2024-12-12T17:04:04.329120+00:00",
                     "vectorIngestionConfiguration": {}
                 }
             }
            """;


}
