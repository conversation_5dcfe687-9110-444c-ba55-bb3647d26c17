package com.boomi.aiagentregistry.servlet.response;

public final class Agent1Alias3 {
    public String DETAILS = """
            {
                "agentAlias": {
                    "agentAliasArn": "arn:aws:bedrock:us-east-1:************:agent-alias/AGENT2UONE/TSTALIASID",
                    "agentAliasId": "TSTALIASID",
                    "agentAliasName": "agent-1-alias-draft",
                    "agentAliasStatus": "PREPARED",
                    "agentId": "AGENT2UONE",
                    "createdAt": "2024-11-29T04:42:00.383789+00:00",
                    "description": "Test Alias for Agent 1 alias draft",
                    "routingConfiguration": [
                        {
                            "agentVersion": "DRAFT"
                        }
                    ],
                    "updatedAt": "2024-11-29T04:42:00.383789+00:00"
                }
            }
            """;
}

