package com.boomi.aiagentregistry.servlet.response;

public final class Agent1 {

    public static Agent1Version1 VERSION1 = new Agent1Version1();
    public static String DETAILS = """
            {
                  "agent": {
                      "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/AGENT2UONE",
                      "agentId": "AGENT2UONE",
                      "agentName": "Agent draft version  updated name",
                      "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_JHHH3O989KS",
                      "agentStatus": "PREPARED",
                      "clientToken": "77af9527-8ee3-4d4a-9d0a-ce74afdbd841",
                      "createdAt": "2024-11-29T04:41:59.614883+00:00",
                      "description": "agent 1 draft version description updated",
                      "idleSessionTTLInSeconds": 600,
                      "instruction": "agent 1 draft version instructions updated",
                      "preparedAt": "2024-11-29T04:42:27.339262+00:00",
                      "updatedAt": "2024-11-29T04:42:51.102880+00:00"
                  }
              }
            """;
    public static String DETAILS_WITH_GR = """
            {
                  "agent": {
                      "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/AGENT2UONE",
                      "agentId": "AGENT2UONE",
                      "agentName": "Agent draft version  updated name",
                      "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_JHHH3O989KS",
                      "agentStatus": "PREPARED",
                      "clientToken": "77af9527-8ee3-4d4a-9d0a-ce74afdbd841",
                      "createdAt": "2024-11-29T04:41:59.614883+00:00",
                       "guardrailConfiguration": {
                                  "guardrailIdentifier": "0vwlgjhd99dv",
                                  "guardrailVersion": "DRAFT"
                              },
            
                      "description": "agent 1 draft version description updated",
                      "idleSessionTTLInSeconds": 600,
                      "instruction": "agent 1 draft version instructions updated",
                      "preparedAt": "2024-11-29T04:42:27.339262+00:00",
                      "updatedAt": "2024-11-29T04:42:51.102880+00:00"
                  }
              }
            """;
    public static String DETAILS_WITH_LLM = """
            {
                  "agent": {
                      "agentArn": "arn:aws:bedrock:us-east-1:918104040484:agent/AGENT2UONE",
                      "agentId": "AGENT2UONE",
                      "agentName": "Agent draft version  updated name",
                      "agentResourceRoleArn": "arn:aws:iam::918104040484:role/service-role/AmazonBedrockExecutionRoleForAgents_JHHH3O989KS",
                      "agentStatus": "PREPARED",
                      "clientToken": "77af9527-8ee3-4d4a-9d0a-ce74afdbd841",
                      "createdAt": "2024-11-29T04:41:59.614883+00:00",
                      "foundationModel": "anthropic.claude-3-5-sonnet-20240620-v1:0",
                      "description": "agent 1 draft version description updated",
                      "idleSessionTTLInSeconds": 600,
                      "instruction": "agent 1 draft version instructions updated",
                      "preparedAt": "2024-11-29T04:42:27.339262+00:00",
                      "updatedAt": "2024-11-29T04:42:51.102880+00:00"
                  }
              }
            """;


    public static String VERSION_DRAFT_SUMMARY = """
            {
                          "agentName": "omar-registry-agent-1-version-draft",
                          "agentStatus": "PREPARED",
                          "agentVersion": "DRAFT",
                          "createdAt": "2024-11-29T04:42:50.826791+00:00",
                          "description": "this is agent 1 version DRAFT",
                          "updatedAt": "2024-11-29T04:42:51.102880+00:00"
                      }
            """;
    public static String VERSION1_SUMMARY = """
            {
                          "agentName": "omar-registry-agent-1-version-1",
                          "agentStatus": "PREPARED",
                          "agentVersion": "1",
                          "createdAt": "2024-11-29T04:42:50.826791+00:00",
                          "description": "this is agent 1 version 1",
                          "updatedAt": "2024-11-28T05:42:51.102880+00:00"
                      }
            """;

    public static String VERSION2_SUMMARY = """
            {
                          "agentName": "omar-registry-agent-1-version-2",
                          "agentStatus": "PREPARED",
                          "agentVersion": "2",
                          "createdAt": "2024-11-29T04:43:02.855990+00:00",
                          "description": "Agent 1 version 2",
                          "updatedAt": "2024-11-29T04:43:03.122856+00:00"
                      }
            """;


    public static String ALIAS1_SUMMARY = """
            
            {
                "agentAliasId": "ANTJDMWTRW",
                "agentAliasName": "omar-agent-1-alias-1-from summary",
                "agentAliasStatus": "PREPARED",
                "createdAt": "2024-11-29T04:42:49.554826+00:00",
                "description": "this is the description for agent 1 alias 1 pointing to version 1 from summary",
                "routingConfiguration": [
                    {
                        "agentVersion": "1"
                    }
                ],
                "updatedAt": "2024-11-29T04:42:49.554826+00:00"
            }
            """;

    public static String ACTION_GROUP_FUN_1_SUMMARY = """
            {
                 "actionGroupId": "ACGPFUNONE",
                 "actionGroupName": "agent 1 action group 1 name from bedrock",
                 "actionGroupState": "ENABLED",
                 "updatedAt": "2024-12-02T01:01:38.587446+00:00"
             }
            """;
    public static String ACTION_GROUP_FUN_2_SUMMARY = """
            {
                 "actionGroupId": "V08FUNSTWO",
                 "actionGroupName": "agent 1 action group 1 description from bedrock",
                 "actionGroupState": "ENABLED",
                 "updatedAt": "2024-12-02T01:01:38.587446+00:00"
             }
            """;
    public static String ACTION_GROUP_API_1_SUMMARY = """
                {
                "actionGroupId": "NAPIDTSONE",
                 "actionGroupName": "actn_grp_2",
                 "actionGroupState": "ENABLED",
                 "description": "desc",
                 "updatedAt": "2024-12-02T01:02:57.154725+00:00"
             }
            """;
    public static String ACTION_GROUP_API_2_SUMMARY = """
            {
                 "actionGroupId": "NAPIDTSTWO",
                 "actionGroupName": "action_grp_api_to_keep",
                 "actionGroupState": "ENABLED",
                 "description": "desc",
                 "updatedAt": "2024-12-02T01:02:57.154725+00:00"
             }
            """;
    public static String ACTION_GROUP_FUN_1_DETAILS = """
                      {
                      "agentActionGroup": {
                          "actionGroupExecutor": {
                              "lambda": "arn:aws:lambda:us-east-1:918104040484:function:action_group_2-nabqd"
                          },
                          "actionGroupId": "ACGPFUNONE",
                          "actionGroupName": "agent 1 action group 1 name from bedrock",
                          "description": "agent 1 action group 1 description from bedrock",
                          "actionGroupState": "ENABLED",
                          "agentId": "AGENT2UONE",
                          "agentVersion": "DRAFT",
                          "clientToken": "e5ff1536-7bb4-49de-9648-f89b33183fe1",
                          "createdAt": "2024-12-02T01:01:38.587446+00:00",
                          "functionSchema": {
                              "functions": [
                                  {
                                      "name": "grp_1_fun_1",
                                      "parameters": {
                                          "grp_1_fun_1_param_1": {
                                              "description": "this is the description ",
                                              "required": false,
                                              "type": "string"
                                          }
                                      },
                                      "requireConfirmation": "DISABLED"
                                  },
                                  {
                                      "name": "act_1_fun_2",
                                      "parameters": {
                                          "actn_1_fun_2_param_1": {
                                              "description": "des",
                                              "required": false,
                                              "type": "string"
                                          }
                                      },
                                      "requireConfirmation": "DISABLED"
                                  }
                              ]
                          },
                          "updatedAt": "2024-12-02T01:01:38.587446+00:00"
                      }
                  }
            """;
    public static String ACTION_GROUP_FUN_2_DETAILS = """
            {
                  "agentActionGroup": {
                      "actionGroupExecutor": {
                          "lambda": "arn:aws:lambda:us-east-1:918104040484:function:action_group_1-nabqd"
                      },
                      "actionGroupId": "V08FUNSTWO",
                      "actionGroupName": "agent 1 action group 1 description from bedrock",
                      "description": "agent 1 action group 2 description from bedrock",
                      "actionGroupState": "ENABLED",
                      "agentId": "AGENT2UONE",
                      "agentVersion": "DRAFT",
                      "clientToken": "e5ff1536-7bb4-49de-9648-f89b33183fe1",
                      "createdAt": "2024-12-02T01:01:38.587446+00:00",
                      "functionSchema": {
                          "functions": [
                              {
                                  "name": "grp_1_fun_1",
                                  "parameters": {
                                      "grp_1_fun_1_param_1": {
                                          "description": "this is the description ",
                                          "required": false,
                                          "type": "string"
                                      }
                                  },
                                  "requireConfirmation": "DISABLED"
                              },
                              {
                                  "name": "act_1_fun_2",
                                  "parameters": {
                                      "actn_1_fun_2_param_1": {
                                          "description": "des",
                                          "required": false,
                                          "type": "string"
                                      }
                                  },
                                  "requireConfirmation": "DISABLED"
                              }
                          ]
                      },
                      "updatedAt": "2024-12-02T01:01:38.587446+00:00"
                  }
              }
            """;

    public static String ACTION_GROUP_API_1_DETAILS = """
            {
                "agentActionGroup": {
                    "actionGroupExecutor": {
                        "customControl": "RETURN_CONTROL"
                    },
                    "actionGroupId": "NAPIDTSONE",
                    "actionGroupName": "A2BUV2UAG2BUAPI2BU",
                    "description": "agent 1 action group 3 description from bedrock",
                    "actionGroupState": "ENABLED",
                    "agentId": "AGENT2UONE",
                    "agentVersion": "DRAFT",
                    "apiSchema": {
                        "payload": "openapi: 3.0.0\\ninfo:\\n  title: Insurance Claims Automation API\\n  version: 1.0.0\\n  description: APIs for managing insurance claims by pulling a list of open claims, identifying outstanding paperwork for each claim, and sending reminders to policy holders.\\npaths:\\n  /claims:\\n    get:\\n      summary: Get a list of all open claims\\n      description: Get the list of all open insurance claims. Return all the open claimIds.\\n      operationId: getAllOpenClaims\\n      responses:\\n        \\"200\\":\\n          description: Gets the list of all open insurance claims for policy holders\\n          content:\\n            application/json:\\n              schema:\\n                type: array\\n                items:\\n                  type: object\\n                  properties:\\n                    claimId:\\n                      type: string\\n                      description: Unique ID of the claim.\\n                    policyHolderId:\\n                      type: string\\n                      description: Unique ID of the policy holder who has filed the claim.\\n                    claimStatus:\\n                      type: string\\n                      description: The status of the claim. Claim can be in Open or Closed state\\n  /claims/{claimId}/identify-missing-documents:\\n    get:\\n      summary: Identify missing documents for a specific claim\\n      description: Get the list of pending documents that need to be uploaded by policy holder before the claim can be processed. The API takes in only one claim id and returns the list of documents that are pending to be uploaded by policy holder for that claim. This API should be called for each claim id\\n      operationId: identifyMissingDocuments\\n      parameters:\\n        - name: claimId\\n          in: path\\n          description: Unique ID of the open insurance claim\\n          required: true\\n          schema:\\n            type: string\\n      responses:\\n        \\"200\\":\\n          description: List of documents that are pending to be uploaded by policy holder for insurance claim\\n          content:\\n            application/json:\\n              schema:\\n                type: object\\n                properties:\\n                  pendingDocuments:\\n                    type: string\\n                    description: The list of pending documents for the claim.\\n  /send-reminders:\\n    post:\\n      summary: API to send reminder to the customer about pending documents for open claim\\n      description: Send reminder to the customer about pending documents for open claim. The API takes in only one claim id and its pending documents at a time, sends the reminder and returns the tracking details for the reminder. This API should be called for each claim id you want to send reminders for.\\n      operationId: sendReminders\\n      requestBody:\\n        required: true\\n        content:\\n          application/json:\\n            schema:\\n              type: object\\n              properties:\\n                claimId:\\n                  type: string\\n                  description: Unique ID of open claims to send reminders for.\\n                pendingDocuments:\\n                  type: string\\n                  description: The list of pending documents for the claim.\\n              required:\\n                - claimId\\n                - pendingDocuments\\n      responses:\\n        \\"200\\":\\n          description: Reminders sent successfullyTE\\n          content:\\n            application/json:\\n              schema:\\n                type: object\\n                properties:\\n                  sendReminderTrackingId:\\n                    type: string\\n                    description: Unique Id to track the status of the send reminder Call\\n                  sendReminderStatus:\\n                    type: string\\n                    description: Status of send reminder notifications\\n        \\"400\\":\\n          description: Bad request. One or more required fields are missing or invalid."
                    },
                    "clientToken": "6b08cce7-d255-4821-8ee4-f21f4c239c00",
                    "createdAt": "2024-12-04T00:38:45.721895+00:00",
                    "updatedAt": "2024-12-02T01:01:38.587446+00:00"
                }
            }
            """;
    public static String ACTION_GROUP_API_2_DETAILS = """
            {
                "agentActionGroup": {
                    "actionGroupExecutor": {
                        "customControl": "RETURN_CONTROL"
                    },
                    "actionGroupId": "NAPIDTSTWO",
                    "actionGroupName": "action_group_quick_start_lkpec",
                    "description": "agent 1 action group 4 description from bedrock",
                    "actionGroupState": "ENABLED",
                    "agentId": "LAPI2TSQDZ",
                    "agentVersion": "DRAFT",
                    "apiSchema": {
                        "payload": "openapi: 3.0.0\\ninfo:\\n  title: Insurance Claims Automation API\\n  version: 1.0.0\\n  description: APIs for managing insurance claims by pulling a list of open claims, identifying outstanding paperwork for each claim, and sending reminders to policy holders.\\npaths:\\n  /claims:\\n    get:\\n      summary: Get a list of all open claims\\n      description: Get the list of all open insurance claims. Return all the open claimIds.\\n      operationId: getAllOpenClaims\\n      responses:\\n        \\"200\\":\\n          description: Gets the list of all open insurance claims for policy holders\\n          content:\\n            application/json:\\n              schema:\\n                type: array\\n                items:\\n                  type: object\\n                  properties:\\n                    claimId:\\n                      type: string\\n                      description: Unique ID of the claim.\\n                    policyHolderId:\\n                      type: string\\n                      description: Unique ID of the policy holder who has filed the claim.\\n                    claimStatus:\\n                      type: string\\n                      description: The status of the claim. Claim can be in Open or Closed state\\n  /claims/{claimId}/identify-missing-documents:\\n    get:\\n      summary: Identify missing documents for a specific claim\\n      description: Get the list of pending documents that need to be uploaded by policy holder before the claim can be processed. The API takes in only one claim id and returns the list of documents that are pending to be uploaded by policy holder for that claim. This API should be called for each claim id\\n      operationId: identifyMissingDocuments\\n      parameters:\\n        - name: claimId\\n          in: path\\n          description: Unique ID of the open insurance claim\\n          required: true\\n          schema:\\n            type: string\\n      responses:\\n        \\"200\\":\\n          description: List of documents that are pending to be uploaded by policy holder for insurance claim\\n          content:\\n            application/json:\\n              schema:\\n                type: object\\n                properties:\\n                  pendingDocuments:\\n                    type: string\\n                    description: The list of pending documents for the claim.\\n  /send-reminders:\\n    post:\\n      summary: API to send reminder to the customer about pending documents for open claim\\n      description: Send reminder to the customer about pending documents for open claim. The API takes in only one claim id and its pending documents at a time, sends the reminder and returns the tracking details for the reminder. This API should be called for each claim id you want to send reminders for.\\n      operationId: sendReminders\\n      requestBody:\\n        required: true\\n        content:\\n          application/json:\\n            schema:\\n              type: object\\n              properties:\\n                claimId:\\n                  type: string\\n                  description: Unique ID of open claims to send reminders for.\\n                pendingDocuments:\\n                  type: string\\n                  description: The list of pending documents for the claim.\\n              required:\\n                - claimId\\n                - pendingDocuments\\n      responses:\\n        \\"200\\":\\n          description: Reminders sent successfullyTE\\n          content:\\n            application/json:\\n              schema:\\n                type: object\\n                properties:\\n                  sendReminderTrackingId:\\n                    type: string\\n                    description: Unique Id to track the status of the send reminder Call\\n                  sendReminderStatus:\\n                    type: string\\n                    description: Status of send reminder notifications\\n        \\"400\\":\\n          description: Bad request. One or more required fields are missing or invalid."
                    },
                    "clientToken": "6b08cce7-d255-4821-8ee4-f21f4c239c00",
                    "createdAt": "2024-12-04T00:38:45.721895+00:00",
                    "updatedAt": "2024-12-04T00:38:45.721895+00:00"
                }
            }
            """;

}

