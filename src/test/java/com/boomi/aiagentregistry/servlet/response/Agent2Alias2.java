package com.boomi.aiagentregistry.servlet.response;

public final class Agent2Alias2 {
    public static final String DETAILS = """
            {
                  "agentAlias": {
                      "agentAliasArn": "arn:aws:bedrock:us-east-1:************:agent-alias/M2NT3YGTWO/TSTALIASID",
                      "agentAliasId": "TSTALIASID",
                      "agentAliasName": "agent-2-alias-draaft",
                      "agentAliasStatus": "PREPARED",
                      "agentId": "M2NT3YGTWO",
                      "createdAt": "2024-11-28T03:43:13.741447+00:00",
                      "description": "Test Alias for Agent 2 alias draft ",
                      "routingConfiguration": [
                          {
                              "agentVersion": "DRAFT"
                          }
                      ],
                      "updatedAt": "2024-11-28T03:43:13.741447+00:00"
                  }
              }
            """;

}

