// Copyright (c) 2025 Boomi, Inc.
package com.boomi.aiagentregistry.servlet;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;
import com.boomi.aiagentregistry.exception.CustomNonRetryableException;
import com.boomi.aiagentregistry.util.TestLogAppender;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.slf4j.LoggerFactory;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ApiClientTest {

    @Mock
    WebClient webClient;

    @Mock
    WebClient.RequestHeadersUriSpec<?> requestHeadersUriSpec;

    @Mock
    WebClient.RequestHeadersSpec<?> requestHeadersSpec;

    @Mock
    WebClient.ResponseSpec responseSpec;

    @Mock
    WebClient.Builder webClientBuilder;

    @Mock
    WebClient.RequestBodyUriSpec requestBodyUriSpec;

    private ApiClient apiClient;

    private static TestLogAppender testLogAppender;

    private static RequestBuilder requestBuilder;

    @BeforeAll
    static void setupLogger() {
        Logger logger = (Logger) LoggerFactory.getLogger(ApiClient.class);
        testLogAppender = new TestLogAppender();
        testLogAppender.start();
        logger.addAppender(testLogAppender);
        logger.setLevel(Level.INFO);

        requestBuilder = RequestBuilder.builder()
                .baseUrl("http://mock-url")
                .path("/resource")
                .queryParams(Collections.emptyMap())
                .headers(Map.of("Authorization", "Bearer token"))
                .build();
    }

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);

        lenient().doReturn(webClient).when(webClientBuilder).build();

        // GET mocks
        lenient().doReturn(requestHeadersUriSpec).when(webClient).get();
        lenient().doReturn(requestHeadersSpec).when(requestHeadersUriSpec).uri(anyString());
        lenient().doReturn(requestHeadersSpec).when(requestHeadersSpec).headers(any());
        lenient().doReturn(responseSpec).when(requestHeadersSpec).retrieve();

        // POST mocks
        lenient().doReturn(requestBodyUriSpec).when(webClient).post();
        lenient().doReturn(requestBodyUriSpec).when(requestBodyUriSpec).uri(anyString());
        lenient().doReturn(requestBodyUriSpec).when(requestBodyUriSpec).headers(any());
        lenient().doReturn(requestBodyUriSpec).when(requestBodyUriSpec).bodyValue(any());
        lenient().doReturn(responseSpec).when(requestBodyUriSpec).retrieve();

        apiClient = new ApiClient(webClientBuilder, 3, 10, 8, 1.0);
    }

    @AfterEach
    void clearLogs() {
        testLogAppender.clear();
    }

    @Test
    void testApiClientGetSuccess() {
        String response = "mocked response";

        when(responseSpec.bodyToFlux(String.class)).thenReturn(Flux.just(response));

        StepVerifier.create(apiClient.get(requestBuilder, String.class))
                .expectNext(response)
                .verifyComplete();

        verify(webClient, times(1)).get();
    }

    @Test
    void shouldRetryAndThenFailOnRetryableError() {
        when(responseSpec.bodyToFlux(String.class))
                .thenReturn(Flux.error(WebClientResponseException.create(500, "Internal Server Error", null, null, null)));

        StepVerifier.create(apiClient.get(requestBuilder, String.class))
                .expectError(WebClientResponseException.class)
                .verify();

        // 1 initial + 3 retries = 4 total calls
        verify(webClient, times(4)).get();

        assertThat(testLogAppender.getLogs()).anyMatch(log ->
                log.getFormattedMessage().contains("Retryable error during API call (500 Internal Server Error)") &&
                        log.getLevel().toString().equals("INFO")
        );
    }

    @Test
    void shouldThrowCustomExceptionForNonRetryable4xx() {
        when(responseSpec.bodyToFlux(String.class))
                .thenReturn(Flux.error(WebClientResponseException.create(400, "Bad Request", null, null, null)));

        StepVerifier.create(apiClient.get(requestBuilder, String.class))
                .expectError(CustomNonRetryableException.class)
                .verify();

        verify(webClient, times(1)).get();

        assertThat(testLogAppender.getLogs()).anyMatch(log ->
                log.getFormattedMessage().contains("Non-retryable client error during API call") &&
                        log.getLevel().toString().equals("ERROR")
        );
    }

    @Test
    void shouldRespectBackoffBetweenRetries() {
        AtomicInteger attempt = new AtomicInteger();

        when(responseSpec.bodyToFlux(String.class))
                .thenAnswer(invocation -> {
                    if (attempt.getAndIncrement() < 3) {
                        return Flux.error(WebClientResponseException.create(503, "Service Unavailable", null, null, StandardCharsets.UTF_8));
                    }
                    return Flux.just("expected response");
                });

        long start = System.currentTimeMillis();

        StepVerifier.create(apiClient.get(requestBuilder, String.class))
                .expectNext("expected response")
                .verifyComplete();

        long duration = System.currentTimeMillis() - start;

        // Minimum backoff total expected (3 retries * 10 ms backoff configured)
        assertThat(duration).isGreaterThanOrEqualTo(30);
        assertThat(attempt.get()).isEqualTo(4);
    }

    static Stream<Arguments> retryableStatusCodes() {
        return Stream.of(
                Arguments.of(408, "Request Timeout"),
                Arguments.of(429, "Too Many Requests"),
                Arguments.of(500, "Internal Server Error"),
                Arguments.of(502, "Bad Gateway"),
                Arguments.of(503, "Service Unavailable"),
                Arguments.of(504, "Gateway Timeout")
        );
    }

    @ParameterizedTest
    @MethodSource("retryableStatusCodes")
    void shouldRetryThenFailAndLogExhaustion(int statusCode, String statusMessage) {
        when(responseSpec.bodyToFlux(String.class)).thenReturn(Flux.error(
                WebClientResponseException.create(statusCode, statusMessage, null, null, StandardCharsets.UTF_8)));

        StepVerifier.create(apiClient.get(requestBuilder, String.class))
                .expectError(WebClientResponseException.class)
                .verify();

        assertThat(testLogAppender.getLogs()).anyMatch(log ->
                log.getFormattedMessage().contains(
                        String.format("Retryable error during API call (%d %s):", statusCode, statusMessage)) &&
                        log.getLevel().toString().equals("INFO")
        );
    }

    @ParameterizedTest
    @MethodSource("retryableStatusCodes")
    void shouldRetryAndThenSucceed(int statusCode, String statusMessage) {
        AtomicInteger attempt = new AtomicInteger();

        when(responseSpec.bodyToFlux(String.class))
                .thenAnswer(invocation -> {
                    if (attempt.getAndIncrement() < 3) {
                        return Flux.error(WebClientResponseException.create(statusCode, statusMessage, null, null, StandardCharsets.UTF_8));
                    }
                    return Flux.just("Recovered");
                });

        StepVerifier.create(apiClient.get(requestBuilder, String.class))
                .expectNext("Recovered")
                .verifyComplete();

        assertThat(attempt.get()).isEqualTo(4);
        verify(webClient, times(4)).get();

        assertThat(testLogAppender.getLogs()).noneMatch(log ->
                log.getFormattedMessage().contains("Retryable error") && log.getLevel().toString().equals("INFO")
        );
    }

    @ParameterizedTest
    @MethodSource("retryableStatusCodes")
    void shouldRetryAndThenSucceedPost(int statusCode, String statusMessage) {
        AtomicInteger attempt = new AtomicInteger();

        when(responseSpec.bodyToFlux(String.class))
                .thenAnswer(invocation -> {
                    if (attempt.getAndIncrement() < 3) {
                        return Flux.error(WebClientResponseException.create(statusCode, statusMessage, null, null, StandardCharsets.UTF_8));
                    }
                    return Flux.just("Recovered");
                });

        StepVerifier.create(apiClient.post(requestBuilder, String.class))
                .expectNext("Recovered")
                .verifyComplete();

        assertThat(attempt.get()).isEqualTo(4);
        verify(webClient, times(4)).post();

        assertThat(testLogAppender.getLogs()).noneMatch(log ->
                log.getFormattedMessage().contains("Retryable error") && log.getLevel().toString().equals("INFO")
        );
    }

    @ParameterizedTest
    @MethodSource("retryableStatusCodes")
    void shouldRetryThenFailAndLogExhaustionPost(int statusCode, String statusMessage) {
        when(responseSpec.bodyToFlux(String.class)).thenReturn(
                Flux.error(WebClientResponseException.create(statusCode, statusMessage, null, null, StandardCharsets.UTF_8)));

        StepVerifier.create(apiClient.post(requestBuilder, String.class))
                .expectError(WebClientResponseException.class)
                .verify();

        assertThat(testLogAppender.getLogs()).anyMatch(log ->
                log.getFormattedMessage().contains(
                        String.format("Retryable error during API call (%d %s):", statusCode, statusMessage)) &&
                        log.getLevel().toString().equals("INFO")
        );
    }

    @Test
    void shouldRespectPostBackoffBetweenRetries() {
        AtomicInteger attempt = new AtomicInteger();

        when(responseSpec.bodyToFlux(String.class))
                .thenAnswer(invocation -> {
                    if (attempt.getAndIncrement() < 3) {
                        return Flux.error(WebClientResponseException.create(503, "Service Unavailable", null, null, StandardCharsets.UTF_8));
                    }
                    return Flux.just("expected response");
                });

        long start = System.currentTimeMillis();

        StepVerifier.create(apiClient.post(requestBuilder, String.class))
                .expectNext("expected response")
                .verifyComplete();

        long duration = System.currentTimeMillis() - start;
        assertThat(duration).isGreaterThanOrEqualTo(30);
        assertThat(attempt.get()).isEqualTo(4);
    }

    @Test
    void shouldThrowCustomExceptionForNonRetryablePost4xx() {
        when(responseSpec.bodyToFlux(String.class)).thenReturn(
                Flux.error(WebClientResponseException.create(400, "Bad Request", null, null, null)));

        StepVerifier.create(apiClient.post(requestBuilder, String.class))
                .expectError(CustomNonRetryableException.class)
                .verify();

        verify(webClient, times(1)).post();

        assertThat(testLogAppender.getLogs()).anyMatch(log ->
                log.getFormattedMessage().contains("Non-retryable client error during API call") &&
                        log.getLevel().toString().equals("ERROR")
        );
    }

    @Test
    void shouldRetryAndThenFailOnRetryablePostError() {
        when(responseSpec.bodyToFlux(String.class)).thenReturn(
                Flux.error(WebClientResponseException.create(500, "Internal Server Error", null, null, null)));

        StepVerifier.create(apiClient.post(requestBuilder, String.class))
                .expectError(WebClientResponseException.class)
                .verify();

        verify(webClient, times(4)).post(); // 1 initial + 3 retries

        assertThat(testLogAppender.getLogs()).anyMatch(log ->
                log.getFormattedMessage().contains("Retryable error during API call (500 Internal Server Error)") &&
                        log.getLevel().toString().equals("INFO")
        );
    }

    @Test
    void testApiClientPostSuccess() {
        String response = "mocked response";

        when(responseSpec.bodyToFlux(String.class)).thenReturn(Flux.just(response));

        StepVerifier.create(apiClient.post(requestBuilder, String.class))
                .expectNext(response)
                .verifyComplete();

        verify(webClient, times(1)).post();
    }
}