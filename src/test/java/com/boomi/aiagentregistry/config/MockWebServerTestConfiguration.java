package com.boomi.aiagentregistry.config;

import com.boomi.aiagentregistry.service.auth.AwsCredentials;
import com.boomi.aiagentregistry.service.sync.boomigarden.GardenServiceProperties;
import com.boomi.aiagentregistry.servlet.AwsClient;
import lombok.extern.slf4j.Slf4j;
import okhttp3.mockwebserver.MockWebServer;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.http.nio.netty.NettyNioAsyncHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.bedrock.BedrockAsyncClient;
import software.amazon.awssdk.services.bedrockagent.BedrockAgentAsyncClient;

import java.io.IOException;
import java.net.URI;

@Slf4j
public class MockWebServerTestConfiguration {
    private final MockWebServer _mockWebServer;

    public MockWebServerTestConfiguration() throws IOException {
        _mockWebServer = new MockWebServer();
        _mockWebServer.start();
    }

    public void shutdown() throws IOException {
        if (_mockWebServer != null) {
            _mockWebServer.shutdown();
        }
    }

    public MockWebServer getMockWebServer() {
        return _mockWebServer;
    }

    @TestConfiguration
    public static class TestConfig {
        private final MockWebServer mockWebServer;

        public TestConfig(MockWebServer mockWebServer) {
            this.mockWebServer = mockWebServer;
        }

        @Bean
        @Primary
        public AwsClient awsClient() {
            return new TestAwsClient(mockWebServer);
        }

        @Bean
        @Primary
        public GardenServiceProperties gardenServiceProperties() {
            GardenServiceProperties properties = new GardenServiceProperties();
            properties.setAgentsPath("/api/v1/agent-garden/agents");  // Set the API URL to point to MockWebServer
            return properties;
        }
    }

    public static class TestAwsClient extends AwsClient {
        private final MockWebServer mockWebServer;

        public TestAwsClient(MockWebServer mockWebServer) {
            this.mockWebServer = mockWebServer;
        }

        @Override
        public BedrockAgentAsyncClient createBedrockAgentAsyncClient(AwsCredentials credentials) {
            AwsBasicCredentials basicCredentials = AwsBasicCredentials.create(
                    credentials.getAwsAccessKeyId(),
                    credentials.getAwsSecretAccessKey());
            return BedrockAgentAsyncClient.builder()
                    .credentialsProvider(StaticCredentialsProvider.create(basicCredentials))
                    .region(Region.of(credentials.getAwsRegion()))
                    .endpointOverride(URI.create(mockWebServer.url("/").toString()))
                    .httpClient(NettyNioAsyncHttpClient.builder().build())
                    .build();
        }

        @Override
        public BedrockAsyncClient createBedrockAsyncClient(AwsCredentials credentials) {
            AwsBasicCredentials basicCredentials = AwsBasicCredentials.create(
                    credentials.getAwsAccessKeyId(),
                    credentials.getAwsSecretAccessKey());
            return BedrockAsyncClient.builder()
                    .credentialsProvider(StaticCredentialsProvider.create(basicCredentials))
                    .region(Region.of(credentials.getAwsRegion()))
                    .endpointOverride(URI.create(mockWebServer.url("/").toString()))
                    .httpClient(NettyNioAsyncHttpClient.builder().build())
                    .build();
        }
    }
}

