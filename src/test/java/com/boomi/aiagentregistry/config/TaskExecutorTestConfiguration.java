package com.boomi.aiagentregistry.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.core.task.SyncTaskExecutor;
import org.springframework.core.task.TaskExecutor;

@TestConfiguration
public class TaskExecutorTestConfiguration {
    /**
     * In order to ease testing of async event handlers we overwrite Springs default TaskExecutor
     * with an in-thread version. Each invocation takes place in the calling thread.
     *
     * @return an instance of SyncTaskExecutor
     */
    @Bean
    @Qualifier("testTaskExecutor")
    TaskExecutor taskExecutor() {
        return new SyncTaskExecutor();
    }
}
