// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.config;

import software.amazon.awssdk.auth.credentials.AwsSessionCredentials;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.oam.OamClient;
import software.amazon.awssdk.services.oam.model.CreateLinkRequest;
import software.amazon.awssdk.services.oam.model.CreateLinkResponse;
import software.amazon.awssdk.services.oam.model.GetSinkPolicyRequest;
import software.amazon.awssdk.services.oam.model.GetSinkPolicyResponse;

import com.boomi.aiagentregistry.service.metrics.OamClientHelperService;
import com.boomi.aiagentregistry.util.TestUtil;

import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.io.IOException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@TestConfiguration
public class MockOamClientTestConfig {

    @Bean
    public OamClient mockOamClient() {
        OamClient mockOamClient = Mockito.mock(OamClient.class);
        
        try {
            String sinkPolicy = TestUtil.readResourceAsString("oam/aws_oam_sink_policy_array.json");
            GetSinkPolicyResponse getSinkPolicyResponse = GetSinkPolicyResponse.builder()
                    .policy(sinkPolicy)
                    .build();
            when(mockOamClient.getSinkPolicy(any(GetSinkPolicyRequest.class)))
                    .thenReturn(getSinkPolicyResponse);
        } catch (IOException e) {
            throw new RuntimeException("Failed to read sink policy file", e);
        }
        setupMockOamClient(mockOamClient);
        return mockOamClient;
    }
    
    @Bean("mockOamClientHelperService")
    // This ensures this bean is chosen when there are multiple candidates
    @Primary
    public OamClientHelperService mockOamClientHelperService(OamClient mockOamClient) {
        OamClientHelperService mockHelper = Mockito.mock(OamClientHelperService.class);
        when(mockHelper.createOamClient(any(Region.class)))
                .thenReturn(mockOamClient);
        when(mockHelper.createOamClient(any(Region.class), any(AwsSessionCredentials.class)))
                        .thenReturn(mockOamClient);
        return mockHelper;
    }

    public static void setupMockOamClient(OamClient mockOamClient) {

        CreateLinkResponse createLinkResponseUsEast1 = CreateLinkResponse
                .builder()
                .arn("arn:aws:oam:us-east-1:000104040484:link/ab8a0e33-5f25-4396-b1f8-69a69ccf4d1d")
                .build();
        CreateLinkResponse createLinkResponseUsWest2 = CreateLinkResponse
                .builder()
                .arn("arn:aws:oam:us-west-2:000104040484:link/0227b8ac-2b60-4f31-848d-0ca2202c32b2")
                .build();
        when(mockOamClient.createLink(any(CreateLinkRequest.class)))
                .thenReturn(createLinkResponseUsEast1, createLinkResponseUsWest2);
    }
}
