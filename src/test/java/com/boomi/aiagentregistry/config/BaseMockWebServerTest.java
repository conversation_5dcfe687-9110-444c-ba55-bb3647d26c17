package com.boomi.aiagentregistry.config;

import okhttp3.mockwebserver.MockWebServer;
import software.amazon.awssdk.services.timestreamquery.TimestreamQueryAsyncClient;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;

import java.io.IOException;

public abstract class BaseMockWebServerTest {

    protected static MockWebServerTestConfiguration _mockConfig;
    protected static MockWebServer _mockWebServer;

    @MockBean
    protected TimestreamQueryAsyncClient _mockTimestreamQueryAsyncClient;

    @BeforeAll
    static void setUpMockServer() throws IOException {
        _mockConfig = new MockWebServerTestConfiguration();
        _mockWebServer = _mockConfig.getMockWebServer();
    }

    @TestConfiguration
    static class BaseTestConfig {
        @Bean
        public MockWebServer mockWebServer() {
            return _mockWebServer;
        }
    }

    @AfterAll
    static void tearDownMockServer() throws IOException {
        if (_mockConfig != null) {
            _mockConfig.shutdown();
        }
    }
}
