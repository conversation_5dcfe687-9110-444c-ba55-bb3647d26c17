// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.resolver.it.mutation;

import jakarta.validation.constraints.NotNull;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.test.StepVerifier;
import software.amazon.awssdk.auth.credentials.AwsSessionCredentials;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.oam.OamClient;
import software.amazon.awssdk.services.oam.model.ConflictException;
import software.amazon.awssdk.services.oam.model.CreateLinkRequest;
import software.amazon.awssdk.services.oam.model.DeleteLinkRequest;
import software.amazon.awssdk.services.oam.model.InvalidParameterException;
import software.amazon.awssdk.services.oam.model.PutSinkPolicyRequest;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.model.AssumeRoleRequest;
import software.amazon.awssdk.services.sts.model.Credentials;
import software.amazon.awssdk.services.sts.model.StsException;
import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.config.MockOamClientTestConfig;
import com.boomi.aiagentregistry.config.TaskExecutorTestConfiguration;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentLargeTextContent;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AwsAssumeRoleProviderAccountMetadataJson;
import com.boomi.aiagentregistry.repo.AiAgentLargeTextContentRepository;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.repo.AiAgentRepository;
import com.boomi.aiagentregistry.service.FeatureManager;
import com.boomi.aiagentregistry.service.SecretsManagerService;
import com.boomi.aiagentregistry.service.auth.AuthorizationParsingService;
import com.boomi.aiagentregistry.service.auth.AwsAssumeRoleAuthorizationParserStrategy;
import com.boomi.aiagentregistry.service.auth.AwsCredentials;
import com.boomi.aiagentregistry.service.metrics.OamClientHelperService;
import com.boomi.aiagentregistry.service.metrics.OamLinkService;
import com.boomi.aiagentregistry.service.s3.FileStorageService;
import com.boomi.aiagentregistry.service.sqs.AwsSqsService;
import com.boomi.aiagentregistry.util.AiAgentProviderAccountCommonServiceUtil;
import com.boomi.aiagentregistry.util.BedrockTestUtil;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphQLQueriesEnum;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.StsClientTestUtil;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountOnboardingTemplate;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.CLOUDFORMATION_S3_KEY_PREFIX;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.HOURS_12;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.SECRET_KEY_PATH_AWS;
import static com.boomi.aiagentregistry.util.GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT_DELETE;
import static com.boomi.aiagentregistry.util.GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT_FOR_BEDROCK_ASSUME_ROLE_SUCCESS;
import static com.boomi.aiagentregistry.util.GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS;
import static com.boomi.aiagentregistry.util.GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT_FOR_CUSTOM_SUCCESS;
import static com.boomi.aiagentregistry.util.GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT_STEP2_FOR_BEDROCK;
import static com.boomi.aiagentregistry.util.GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT_SYNC;
import static com.boomi.aiagentregistry.util.GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT_UPDATE;
import static com.boomi.aiagentregistry.util.GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT_UPDATE_FOR_BEDROCK_ASSUME_ROLE;
import static com.boomi.aiagentregistry.util.GuidUtil.createAIAgentProviderGuid;
import static com.boomi.aiagentregistry.util.TestUtil.COMMON_EXTERNAL_ID;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_CREDENTIALS;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_METADATA;
import static com.boomi.aiagentregistry.util.TestUtil.getLanguageTag;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT;
import static org.junit.Assert.assertFalse;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = { TestApplication.class, TaskExecutorTestConfiguration.class, MockOamClientTestConfig.class,
        StsClientTestUtil.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
class AiAgentProviderAccountMutationResolverImplIntegrationTest extends BaseMockWebServerTest {

    private static final String GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE = "aiAgentProviderAccountCreate";
    private static final String GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CONFIRM = "aiAgentCloudProviderAccountConfirm";
    private static final String GRAPHQL_OPERATION_PROVIDER_ACCOUNT_UPDATE = "aiAgentProviderAccountUpdate";
    private static final String GRAPHQL_OPERATION_PROVIDER_ACCOUNT_DELETE = "aiAgentProviderAccountDelete";

    @Value("${boomi.services.aiagentregistry.monitoring.account.id}")
    String monitoringAccountId;

    @Autowired
    private TestWebFluxGraphQLExecutor executor;

    @Autowired
    private TestUtil testUtil;

    @Autowired
    private BedrockTestUtil _bedrockTestUtil;

    @Autowired
    private AiAgentProviderAccountRepository _providerAccountRepository;
    @Autowired
    private AiAgentLargeTextContentRepository _largeTextContentRepository;

    @Autowired
    AiAgentRepository _aiAgentRepository;

    @Qualifier("testTaskExecutor")
    @Autowired
    private TaskExecutor taskExecutor;

    @Autowired
    private ObjectMapper _objectMapper;

    @Autowired
    private OamClient mockOamClient;

    @Autowired
    private String s3Bucket;

    @Captor
    private ArgumentCaptor<String> _argCaptorForSecret;

    @Captor ArgumentCaptor<byte[]> _argCaptorForS3File;

    @Captor
    private ArgumentCaptor<AssumeRoleRequest> _assumeRoleRequestArgCaptor;

    @Captor
    private ArgumentCaptor<CreateLinkRequest> _createLinkRequestArgumentCaptor;
    @Captor
    private ArgumentCaptor<Consumer<DeleteLinkRequest.Builder>> _deleteLinkRequestArgumentCaptor;
    @Autowired
    private OamClientHelperService mockOamClientHelperService;
    @SpyBean
    private OamLinkService spyOamLinkService;

    // This setup helps in getting the testing port that changes dynamically
    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry r) throws IOException {
        r.add("api.boomi.platform.url", () -> "http://localhost:" + _mockWebServer.getPort());
    }

    // used spybean instead of mockbean so that only method(s) of the bean needed for the test are mocked
    @SpyBean
    private AuthorizationParsingService spyAuthorizationParsingService;
    @SpyBean
    private AwsAssumeRoleAuthorizationParserStrategy spyAwsAssumeRoleAuthorizationParserStrategy;
    @SpyBean
    private SecretsManagerService spySecretsManagerService;
    @SpyBean
    private FeatureManager spyFeatureManager;
    @SpyBean
    private FileStorageService spyFileStorageService;
    @SpyBean
    private AiAgentProviderAccountCommonServiceUtil spyAiAgentProviderAccountCommonServiceUtil;
    @MockBean
    private StsClient mockStsClient;
    @MockBean
    private AwsSqsService mockAwsSqsService;

    @BeforeEach
    public void setup() throws MalformedURLException, JsonProcessingException {
        when(spyAuthorizationParsingService.requiresRegionCheck(any()))
                .thenReturn(true);
        doReturn(Optional.of(new URL("https://example.com")))
                .when(spyFileStorageService).putSmallObject(any(), any(), any());
        doReturn("https://example.com/presigned")
                .when(spyFileStorageService).createPresignedGetUrl(any(), any(), anyLong());

        Mockito.clearInvocations(mockOamClient);
        Mockito.clearInvocations(mockStsClient);
        Mockito.clearInvocations(spyFileStorageService);
        Mockito.clearInvocations(spyAwsAssumeRoleAuthorizationParserStrategy);
        Mockito.clearInvocations(spyAuthorizationParsingService);
        Mockito.clearInvocations(mockOamClientHelperService);

        when(spySecretsManagerService.getSecret(anyString()))
                   .thenReturn(Flux.just(_objectMapper.writeValueAsString(_bedrockTestUtil.getAwsCredentials())));
    }

    @AfterEach
    public void tearDown() {
        deleteAllProviderAccounts();
    }

    private void deleteAllProviderAccounts() {
        List<AiAgentProviderAccount> providerAccounts = _providerAccountRepository.findAll();
        providerAccounts.forEach(this::deleteProviderAccount);
        _providerAccountRepository.flush();
    }

    @Test
    @DisplayName("Should successfully execute mutation to create Boomi AI agent provider account")
    void testAiAgentProviderAccountCreateForBoomi() throws Exception {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS.getFileName());
        assertNotNull(queryStr);
        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentRegistryAccount =
                TestUtil.parseGraphqlResponse(response, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentRegistryAccount);
        assertNotNull(aiAgentRegistryAccount.getId());
        assertEquals("test-provider-account-id", aiAgentRegistryAccount.getExternalProviderAccountId());

        Optional<AiAgentProviderAccount> optionalDbAiAgentProviderAccount = _providerAccountRepository
                .findByGuid(aiAgentRegistryAccount.getId());
        assertTrue(optionalDbAiAgentProviderAccount.isPresent());
        AiAgentProviderAccount dbAiAgentProviderAccount = optionalDbAiAgentProviderAccount.get();
        assertEquals("us-east-1", dbAiAgentProviderAccount.getRegion());
        validateAuditFields(dbAiAgentProviderAccount);
        verify(spySecretsManagerService).createSecret(
                TestUtil.COMMON_ACCOUNT_ID + "/" + dbAiAgentProviderAccount.getGuid(),
                "{\n" + "  \"userName\": \"<EMAIL>\",\n" + "  \"apiToken\": \"boomi\",\n"
                        + "  \"accountId\":\"boomi-internal\"\n" + "}");
    }

    @Test
    @DisplayName("Should successfully execute mutation to create Custom AI agent provider account")
    void testAiAgentProviderAccountCreateForCustom() throws Exception {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_CUSTOM_SUCCESS.getFileName());
        assertNotNull(queryStr);
        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentRegistryAccount =
                TestUtil.parseGraphqlResponse(response, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentRegistryAccount);
        assertNotNull(aiAgentRegistryAccount.getId());
        assertEquals("boomi-internal", aiAgentRegistryAccount.getExternalProviderAccountId());

        Optional<AiAgentProviderAccount> optionalDbAiAgentProviderAccount = _providerAccountRepository
                .findByGuid(aiAgentRegistryAccount.getId());
        assertTrue(optionalDbAiAgentProviderAccount.isPresent());
        AiAgentProviderAccount dbAiAgentProviderAccount = optionalDbAiAgentProviderAccount.get();

        validateAuditFields(dbAiAgentProviderAccount);
    }

    @Test
    @DisplayName("Test Invalid Credentials for mutation to create Boomi AI agent provider account")
    void testInvalidCredentialsForAiAgentProviderAccountCreateForBoomi() throws Exception {
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT_FOR_INVALID_CREDENTIALS.getFileName());
        assertNotNull(queryStr);

        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertEquals("{\"errors\":[{\"message\":\"The credentials provided are either empty, invalid, or incorrectly "
                + "formatted.\",\"path\":[\"aiAgentProviderAccountCreate\"],"
                + "\"extensions\":{\"errorCode\":\"INVALID_CREDENTIALS_INPUT\",\"language\":\"" + getLanguageTag()
                + "\"," + "\"classification\":\"DataFetchingException\"}},{\"message\":\"The region field is required"
                + " when the authSchema is set to BOOMIAPITOKEN.\"," + "\"path\":[\"aiAgentProviderAccountCreate\"],"
                + "\"extensions\":{\"errorCode\":\"REGION_REQUIRED_IF_AUTH\","
                + "\"parameters\":[\"BOOMIAPITOKEN\"],\"language\":\"" + getLanguageTag() + "\","
                + "\"classification\":\"DataFetchingException\"}}],"
                + "\"data\":{\"aiAgentProviderAccountCreate\":null}}", response);
    }

    @Test
    @Transactional
    @DisplayName("Should successfully execute mutation to update AI agent provider account")
    void testAiAgentProviderAccountUpdate() throws Exception {
        ProviderAccountUpdateSetupResult result = createProviderAccountInDatabase(AI_AGENT_PROVIDER_ACCOUNT_UPDATE);
        AiAgentProviderAccount providerAccount = result.providerAccount;
        Timestamp providerLastUpdateTime = providerAccount.getModifiedTime();
        assertNotNull(providerLastUpdateTime);
        String queryStr = result.providerAccountUpdateQueryString;
        AiAgent aiAgent = testUtil.createAiAgent(providerAccount,
                GuidUtil.createAIAgentGuid(), COMMON_EXTERNAL_ID, false);

        TimeUnit.MILLISECONDS.sleep(10);
        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount updatedAiAgentRegistryAccount =
                TestUtil.parseGraphqlResponse(response, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_UPDATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(updatedAiAgentRegistryAccount, response);
        assertNotNull(updatedAiAgentRegistryAccount.getId(), response);

        assertEquals("boomi-test-updated", updatedAiAgentRegistryAccount.getProviderAccountName());
        assertEquals(AiAgentProviderAccountStatus.DISABLED.name(),
                updatedAiAgentRegistryAccount.getProviderAccountStatus().name());
        assertEquals("test-provider-account-id", updatedAiAgentRegistryAccount.getExternalProviderAccountId());
        verify(spySecretsManagerService).updateSecret(
                TestUtil.COMMON_ACCOUNT_ID + "/" + updatedAiAgentRegistryAccount.getId(),
                "{\n" + "  \"userName\": \"<EMAIL>\",\n" + "  \"apiToken\": \"boomi\",\n"
                        + "  \"accountId\":\"boomi-internal\"\n" + "}");
        _aiAgentRepository.delete(aiAgent);

        AiAgentProviderAccount updatedProviderAccount = _providerAccountRepository.findByGuid(
                providerAccount.getGuid()).get();
         Timestamp modifiedTime = updatedProviderAccount.getModifiedTime();
         assertTrue(providerLastUpdateTime.before(modifiedTime));
    }

    @Test
    @Transactional
    @DisplayName("Test input validation for ai agent provider account update mutation")
    void testInputValidationForAiAgentProviderAccountUpdate() throws Exception {
        // the region is checked in a separate test
        when(spyAuthorizationParsingService.requiresRegionCheck(any()))
                .thenReturn(false);
        String queryStr = createProviderAccountInDatabase(
                GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT_UPDATE_INPUT_VALIDATION)
                .providerAccountUpdateQueryString;

        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertEquals("{\"errors\":[{\"message\":\"The supplied provider account name is either empty or null.\","
                + "\"path\":[\"aiAgentProviderAccountUpdate\"],"
                + "\"extensions\":{\"errorCode\":\"ACCOUNT_NAME_EMPTY\",\"language\":\"" + getLanguageTag() + "\","
                + "\"classification\":\"DataFetchingException\"}},{\"message\":\"The supplied metadata JSON "
                + "is either empty or invalid.\",\"path\":[\"aiAgentProviderAccountUpdate\"],"
                + "\"extensions\":{\"errorCode\":\"INVALID_METADATA_JSON\",\"language\":\"" + getLanguageTag() + "\","
                + "\"classification\":\"DataFetchingException\"}},{\"message\":\"The provider account status "
                + "provided is invalid.\",\"path\":[\"aiAgentProviderAccountUpdate\"],"
                + "\"extensions\":{\"errorCode\":\"INVALID_PROVIDER_ACCOUNT_STATUS\",\"language\":\"" + getLanguageTag()
                + "\"," + "\"classification\":\"DataFetchingException\"}},{\"message\":\"The credentials provided are"
                + " either empty, invalid, or incorrectly formatted.\","
                + "\"path\":[\"aiAgentProviderAccountUpdate\"],"
                + "\"extensions\":{\"errorCode\":\"INVALID_CREDENTIALS_INPUT\",\"language\":\"" + getLanguageTag()
                + "\"," + "\"classification\":\"DataFetchingException\"}}],"
                + "\"data\":{\"aiAgentProviderAccountUpdate\":null}}", response);
    }

    @Test
    @DisplayName("Create Mutation for AI agent provider account validates region")
    void testAiAgentProviderAccountCreateValidatesRegion() throws Exception {
        String queryString = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS.getFileName());
        assertNotNull(queryString);
        queryString = queryString.replace("providerAccountName: \"boomi-test-b\"",
                "providerAccountName: \"boomi-test-d\"");
        testRegionIsRequired(queryString, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE);
        // To Do - move this assertion inside testRegionIsRequired if Update api does the connection check once all
        // input fields are valid
        verify(spyAuthorizationParsingService, never())
                .fetchAccountId(any(), any());
    }

    private ProviderAccountUpdateSetupResult createProviderAccountInDatabase(
            GraphQLQueriesEnum aiAgentProviderAccountUpdate) {
        final AiAgentProviderAccount aiAgentProviderAccount = testUtil.saveAiAgentRegistryAccount("test-boomi",
                TestUtil.COMMON_ACCOUNT_ID,
                PROVIDER_ACCOUNT_METADATA, PROVIDER_ACCOUNT_CREDENTIALS, AiAgentProviderType.BOOMI,
                AiProviderAuthSchema.APITOKEN, AiAgentProviderAccountStatus.CONNECTED);

        String providerAccountUpdateQueryString = GraphQLFileReaderUtil.readGraphQLQuery(
                GraphqlTypeEnum.MUTATION.getGraphqlType(),
                aiAgentProviderAccountUpdate.getFileName());
        assertNotNull(providerAccountUpdateQueryString);

        String providerAccountGuid = aiAgentProviderAccount.getGuid();
        providerAccountUpdateQueryString = providerAccountUpdateQueryString
                .replace("ai-agent-provider-account-guid-variable", providerAccountGuid);

        return new ProviderAccountUpdateSetupResult(aiAgentProviderAccount, providerAccountUpdateQueryString);
    }

    private void testRegionIsRequired(@NotNull String queryString, String operationName) throws IOException {
        queryString = queryString.replace("region: \"us-east-1\"", "region:\"\"");

        final String response = executor.executeAtomSphereQuery(queryString, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertTrue(response.contains("\"errorCode\":\"REGION_REQUIRED_IF_AUTH\""), response);
        assertTrue(response.contains("\"message\":\"The region field is required when the authSchema "
                + "is set to BOOMIAPITOKEN.\""), response);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentRegistryAccount =
                TestUtil.parseGraphqlResponse(response, operationName,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(aiAgentRegistryAccount);

        if (operationName.equals(GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE)) {
            verify(spySecretsManagerService, never()).createSecret(anyString(), anyString());
        } else {
            verify(spySecretsManagerService, never()).updateSecret(anyString(), anyString());
        }
    }

    private record ProviderAccountUpdateSetupResult(@NotNull AiAgentProviderAccount providerAccount,
                                                    @NotNull String providerAccountUpdateQueryString) {

    }

    private static void validateAuditFields(AiAgentProviderAccount dbAiAgentProviderAccount) {
        assertEquals(TestUtil.COMMON_USER_NAME, dbAiAgentProviderAccount.getCreatedByUserId());
        assertEquals(TestUtil.COMMON_USER_NAME, dbAiAgentProviderAccount.getModifiedByUserId());
        assertNotNull(dbAiAgentProviderAccount.getModifiedTime());
        assertNotNull(dbAiAgentProviderAccount.getCreatedTime());
    }

    @Test
    @DisplayName("Create Mutation for AI agent provider account should check for duplicate account")
    void testAiAgentProviderAccountCreateChecksForDuplicateAccount() throws Exception {
        deleteAllProviderAccounts();
        // create provider account
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS.getFileName());
        assertNotNull(queryStr);
        final String providerAccountInIdpAccountOneResponse = executor.executeAtomSphereQuery(queryStr,
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount providerAccountInIdpAccountOne =
                TestUtil.parseGraphqlResponse(providerAccountInIdpAccountOneResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(providerAccountInIdpAccountOne, providerAccountInIdpAccountOneResponse);
        assertNotNull(providerAccountInIdpAccountOne.getId());

        // use a unique provider account name so as not to fail validation
        queryStr = queryStr.replace(
                "providerAccountName: \"boomi-test-b\"",
                "providerAccountName: \"boomi-test-b2\"");
        // creating an existing provider account in the same Registry account should error out
        final String errorResponse = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentRegistryAccountDuplicate =
                TestUtil.parseGraphqlResponse(errorResponse, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(aiAgentRegistryAccountDuplicate);
        assertTrue(errorResponse.contains("\"errorCode\":\"PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT\""), errorResponse);
        assertTrue(errorResponse.contains(
                        "\"message\":\"An agent provider account with the specified credentials already exists."),
                errorResponse);

        // creating an existing provider account in the same Registry but in different AWS region should work
        queryStr = queryStr.replaceAll("region: \"us-east-1\"", "region: \"us-west-1\"");
        final String providerAccountInDifferentRegionResponse = executor.executeAtomSphereQuery(queryStr,
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount providerAccountInDifferentRegion =
                TestUtil.parseGraphqlResponse(providerAccountInDifferentRegionResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(providerAccountInDifferentRegion, providerAccountInDifferentRegionResponse);
        assertNotNull(providerAccountInDifferentRegion.getId());

        //  creating the same provider account in a different Registry account should work
        queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS.getFileName());
        final String providerAccountInIdpAccountTwoResponse = executor
                .executeAtomSphereQuery(queryStr, "test-different-acId",
                        CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                        CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount providerAccountInIdpAccountTwo =
                TestUtil.parseGraphqlResponse(providerAccountInIdpAccountTwoResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(providerAccountInIdpAccountTwo);
        assertNotNull(providerAccountInIdpAccountTwo.getId());
    }

    @Test
    void testAiAgentProviderAccountCreateReportsHealthcheckError() throws Exception {
        when(spyAuthorizationParsingService.fetchAccountId(any(), any()))
                .thenReturn(Mono.error(new RuntimeException("junit: connection error")));
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS.getFileName());
        assertNotNull(queryStr);
        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentRegistryAccount =
                TestUtil.parseGraphqlResponse(response, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(aiAgentRegistryAccount);

        assertTrue(response.contains("\"errorCode\":\"INVALID_CONNECTION_DETAILS\""), response);
    }

    @Test
    @DisplayName("Create Mutation for AI agent provider account for long provider account name")
    void testAiAgentProviderAccountCreateForLongProviderAccountName() throws Exception {
        // create provider account
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS.getFileName());
        assertNotNull(queryStr);

        String longProviderAccountName =
                "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the "
                        + "industry's standard dummy text ever since the 1500s, when an unknown printer took a galley"
                        + " of type and scrambled it to make a type specimen book. It has survived not only five "
                        + "centuries, but also the leap into electronic typesetting, remaining essentially unchanged";
        queryStr = queryStr
                .replace("boomi-test-b", longProviderAccountName);

        final String response = executor.executeAtomSphereQuery(queryStr,
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertEquals(
                "{\"errors\":[{\"message\":\"The provided provider account name `Lorem Ipsum is simply dummy text of "
                        + "the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy "
                        + "text ever since the 1500s, when an unknown printer took a galley of type and scrambled it "
                        + "to make a type specimen book. It has survived not only five centuries, but also the leap "
                        + "into electronic typesetting, remaining essentially unchanged` exceeds the maximum limit of"
                        + " 200 characters. Please shorten it to comply with this requirement.\","
                        + "\"path\":[\"aiAgentProviderAccountCreate\"],"
                        + "\"extensions\":{\"errorCode\":\"ACCOUNT_NAME_TOO_LONG\",\"parameters\":[\"Lorem Ipsum is "
                        + "simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the "
                        + "industry's standard dummy text ever since the 1500s, when an unknown printer took a galley"
                        + " of type and scrambled it to make a type specimen book. It has survived not only five "
                        + "centuries, but also the leap into electronic typesetting, remaining essentially "
                        + "unchanged\"],\"language\":\"" + getLanguageTag()
                        + "\",\"classification\":\"DataFetchingException\"}}],"
                        + "\"data\":{\"aiAgentProviderAccountCreate\":null}}", response);
    }

    @Test
    @DisplayName("Update Mutation for AI agent provider account for long provider account name")
    void testAiAgentProviderAccountUpdateForLongProviderAccountName() throws Exception {
        // create provider account
        String queryStr = createProviderAccountInDatabase(AI_AGENT_PROVIDER_ACCOUNT_UPDATE)
                .providerAccountUpdateQueryString;
        assertNotNull(queryStr);

        String longProviderAccountName =
                "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the "
                        + "industry's standard dummy text ever since the 1500s, when an unknown printer took a galley"
                        + " of type and scrambled it to make a type specimen book. It has survived not only five "
                        + "centuries, but also the leap into electronic typesetting, remaining essentially unchanged";
        queryStr = queryStr
                .replace("boomi-test-updated", longProviderAccountName);

        final String response = executor.executeAtomSphereQuery(queryStr,
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertEquals(
                "{\"errors\":[{\"message\":\"The provided provider account name `Lorem Ipsum is simply dummy text of "
                        + "the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy "
                        + "text ever since the 1500s, when an unknown printer took a galley of type and scrambled it "
                        + "to make a type specimen book. It has survived not only five centuries, but also the leap "
                        + "into electronic typesetting, remaining essentially unchanged` exceeds the maximum limit of"
                        + " 200 characters. Please shorten it to comply with this requirement.\","
                        + "\"path\":[\"aiAgentProviderAccountUpdate\"],"
                        + "\"extensions\":{\"errorCode\":\"ACCOUNT_NAME_TOO_LONG\",\"parameters\":[\"Lorem Ipsum is "
                        + "simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the "
                        + "industry's standard dummy text ever since the 1500s, when an unknown printer took a galley"
                        + " of type and scrambled it to make a type specimen book. It has survived not only five "
                        + "centuries, but also the leap into electronic typesetting, remaining essentially "
                        + "unchanged\"],\"language\":\"" + getLanguageTag()
                        + "\",\"classification\":\"DataFetchingException\"}}],"
                        + "\"data\":{\"aiAgentProviderAccountUpdate\":null}}", response);
    }

    @Test
    @DisplayName(
            "Create Mutation for AI agent provider account does not allow duplicate account name in the same Idp account")
    void testAiAgentProviderAccountCreateDoesNotAllowDuplicateAccountName() throws Exception {
        deleteAllProviderAccounts();
        // create provider account
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS.getFileName());
        assertNotNull(queryStr);
        String providerAccountInIdpAccountOneResponse = executor.executeAtomSphereQuery(queryStr,
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount providerAccountInIdpAccountOne =
                TestUtil.parseGraphqlResponse(providerAccountInIdpAccountOneResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(providerAccountInIdpAccountOne, providerAccountInIdpAccountOneResponse);
        assertNotNull(providerAccountInIdpAccountOne.getId());

        // creating a new provider account using an existing provider account name in the Registry account should error out
        final String errorResponse = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentRegistryAccountDuplicate =
                TestUtil.parseGraphqlResponse(errorResponse, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(aiAgentRegistryAccountDuplicate);
        assertTrue(errorResponse.contains("\"errorCode\":\"PROVIDER_ACCOUNT_DUPLICATE_NAME_CONSTRAINT\""),
                errorResponse);
        assertTrue(errorResponse.contains("\"message\":\"The account provider name already exists in the registry. "
                + "Please choose a different name."), errorResponse);

        // soft deleting the first provider should allow to create a new provider with the same name in the same Registry
        // Note this also check for duplicate external id in the same Registry
        AiAgentProviderAccount dbProviderAccount = _providerAccountRepository.findByGuid(
                providerAccountInIdpAccountOne.getId()).get();
        dbProviderAccount.setDeleted(true);
        _providerAccountRepository.saveAndFlush(dbProviderAccount);
        providerAccountInIdpAccountOneResponse = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        providerAccountInIdpAccountOne =
                TestUtil.parseGraphqlResponse(providerAccountInIdpAccountOneResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(providerAccountInIdpAccountOne);

        // creating the same provider account in a different Registry account should work
        queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS.getFileName());
        final String providerAccountInIdpAccountTwoResponse = executor.executeAtomSphereQuery(queryStr,
                TestUtil.ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount providerAccountInIdpAccountTwo =
                TestUtil.parseGraphqlResponse(providerAccountInIdpAccountTwoResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(providerAccountInIdpAccountTwo);
        assertNotNull(providerAccountInIdpAccountTwo.getId());
    }

    @Test
    @Transactional
    @DisplayName("Update mutation should not allow duplicate provider account name for an Idp account")
    void testAiAgentProviderAccountUpdateDoesNotAllowDuplicateAccountName() throws Exception {
        // create provider account 1 in the registry
        final String providerAccountOneName = "test-boomi-1";
        final AiAgentProviderAccount aiAgentProviderAccountOne = testUtil.saveAiAgentRegistryAccount(
                providerAccountOneName,
                TestUtil.ACCOUNT_ID,
                PROVIDER_ACCOUNT_METADATA, PROVIDER_ACCOUNT_CREDENTIALS, AiAgentProviderType.BOOMI,
                AiProviderAuthSchema.APITOKEN, AiAgentProviderAccountStatus.CONNECTED);

        // create provider account 2 in the same registry
        final String providerAccountTwoName = "test-boomi-2";
        final AiAgentProviderAccount aiAgentProviderAccountTwo = testUtil.saveAiAgentRegistryAccount(
                providerAccountTwoName,
                TestUtil.ACCOUNT_ID,
                PROVIDER_ACCOUNT_METADATA, PROVIDER_ACCOUNT_CREDENTIALS, AiAgentProviderType.AWS_BEDROCK,
                AiProviderAuthSchema.AWS, AiAgentProviderAccountStatus.CONNECTED);

        // updating provider account one with the same name as provider account two should not be allowed
        String providerAccountOneUpdateQueryString = GraphQLFileReaderUtil.readGraphQLQuery(
                GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_UPDATE.getFileName());
        assertNotNull(providerAccountOneUpdateQueryString);

        String providerAccountOneGuid = aiAgentProviderAccountOne.getGuid();
        providerAccountOneUpdateQueryString = providerAccountOneUpdateQueryString
                .replace("ai-agent-provider-account-guid-variable", providerAccountOneGuid);
        providerAccountOneUpdateQueryString = providerAccountOneUpdateQueryString
                .replace("providerAccountName: \"boomi-test-updated\"",
                        "providerAccountName: \"" + providerAccountTwoName + "\"");

        String errorResponse = executor.executeAtomSphereQuery(providerAccountOneUpdateQueryString,
                TestUtil.ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount failedUpdate =
                TestUtil.parseGraphqlResponse(errorResponse, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_UPDATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(failedUpdate);
        assertTrue(errorResponse.contains("\"errorCode\":\"PROVIDER_ACCOUNT_DUPLICATE_NAME_CONSTRAINT\""),
                errorResponse);
        assertTrue(errorResponse.contains("\"message\":\"The account provider name already exists in the registry. "
                + "Please choose a different name."), errorResponse);
        verify(spySecretsManagerService, never()).updateSecret(anyString(), anyString());

        // provider account one can be updated with its existing name
        providerAccountOneUpdateQueryString = providerAccountOneUpdateQueryString
                .replace("providerAccountName: \"" + providerAccountTwoName + "\"",
                        "providerAccountName: \"" + providerAccountOneName + "\"");
        String successResponse = executor.executeAtomSphereQuery(providerAccountOneUpdateQueryString,
                TestUtil.ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount successUpdate =
                TestUtil.parseGraphqlResponse(successResponse, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_UPDATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(successUpdate);
    }

    @Test
    @Transactional
    void testAiAgentProviderAccountUpdateChecksThatExternalProviderAccountIdHasNotChanged() throws Exception {
        // create a provider account in the registry
        when(spyAuthorizationParsingService.fetchAccountId(any(), any()))
                .thenReturn(Mono.just("externalProviderId1"));
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS.getFileName());
        final String aiAgentProviderAccountOneResponse = executor.executeAtomSphereQuery(queryStr,
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentProviderAccountOne =
                TestUtil.parseGraphqlResponse(aiAgentProviderAccountOneResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentProviderAccountOne);
        assertNotNull(aiAgentProviderAccountOne.getId());
        assertEquals("externalProviderId1", aiAgentProviderAccountOne.getExternalProviderAccountId());

        // updating the provider account with the credentials which results in a different external provider accoutn id should not be allowed
        String providerAccountOneGuid = aiAgentProviderAccountOne.getId();
        String providerAccountOneUpdateQueryString = GraphQLFileReaderUtil.readGraphQLQuery(
                GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_UPDATE.getFileName());
        assertNotNull(providerAccountOneUpdateQueryString);
        providerAccountOneUpdateQueryString = providerAccountOneUpdateQueryString
                .replace("ai-agent-provider-account-guid-variable", providerAccountOneGuid);
        when(spyAuthorizationParsingService.fetchAccountId(any(), any()))
                .thenReturn(Mono.just("externalProviderId2"));

        String errorResponse = executor.executeAtomSphereQuery(providerAccountOneUpdateQueryString,
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount failedUpdate =
                TestUtil.parseGraphqlResponse(errorResponse, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_UPDATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(failedUpdate);
        assertTrue(errorResponse.contains("\"errorCode\":\"DIFFERENT_EXTERNAL_PROVIDER_ACCOUNT_ID\""), errorResponse);
        assertTrue(errorResponse.contains("externalProviderId1"), errorResponse);
        verify(spySecretsManagerService, never()).updateSecret(anyString(), anyString());

        // updating the provider account  with its own credentials should be allowed
        when(spyAuthorizationParsingService.fetchAccountId(any(), any()))
                .thenReturn(Mono.just("externalProviderId1"));

        String successResponse = executor.executeAtomSphereQuery(providerAccountOneUpdateQueryString,
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount successUpdate =
                TestUtil.parseGraphqlResponse(successResponse, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_UPDATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(successUpdate);
    }

    @Test
    @Transactional
    @DisplayName("Should successfully execute mutation to delete AI agent provider account")
    void testAiAgentProviderAccountDeleteAccount() throws Exception {

        final AiAgentProviderAccount aiAgentProviderAccount = testUtil.saveAiAgentRegistryAccount("test-boomi",
                TestUtil.COMMON_ACCOUNT_ID,
                PROVIDER_ACCOUNT_METADATA, PROVIDER_ACCOUNT_CREDENTIALS, AiAgentProviderType.BOOMI,
                AiProviderAuthSchema.APITOKEN, AiAgentProviderAccountStatus.CONNECTED);
        String providerAccountGuid = aiAgentProviderAccount.getGuid();
        assertNotNull(providerAccountGuid);
        asyncDeleteProviderAccountAndVerify(providerAccountGuid);
    }

    private void asyncDeleteProviderAccountAndVerify(String providerAccountGuid) throws InterruptedException {
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_DELETE.getFileName());
        assertNotNull(queryStr);
        String providerAccountDeleteQueryString = queryStr
                .replace("ai-agent-provider-account-guid-variable", providerAccountGuid);

        AtomicReference<String> response = new AtomicReference<>();
        Mono<Object> deletionMono = Mono.fromRunnable(() -> {
            try {
                response.set(executor.executeAtomSphereQuery(providerAccountDeleteQueryString,
                        TestUtil.COMMON_ACCOUNT_ID,
                        CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                        CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }).subscribeOn(Schedulers.fromExecutor(taskExecutor));

        StepVerifier.create(deletionMono)
                .verifyComplete();
        assertNotNull(response);
        assertEquals("{\"data\":{\"aiAgentProviderAccountDelete\":true}}", response.get());
        Thread.sleep(3000L);
        assertTrue(_providerAccountRepository.findByGuid(providerAccountGuid).isEmpty());
    }

    @Test
    @Transactional
    @DisplayName(" delete AIAAgentProviderAccount by noncreater IDP  account should fail")
    void testAiAgentProviderAccountDeleteByOtherAccount() throws Exception {

        // create provider account with boomi-internal
        final AiAgentProviderAccount aiAgentProviderAccount = testUtil.saveAiAgentRegistryAccount("test-boomi",
                TestUtil.COMMON_ACCOUNT_ID,
                PROVIDER_ACCOUNT_METADATA, PROVIDER_ACCOUNT_CREDENTIALS, AiAgentProviderType.BOOMI,
                AiProviderAuthSchema.APITOKEN, AiAgentProviderAccountStatus.CONNECTED);
        String providerAccountGuid = aiAgentProviderAccount.getGuid();
        assertNotNull(providerAccountGuid);
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_DELETE.getFileName());
        assertNotNull(queryStr);
        String providerAccountDeleteQueryString = queryStr
                .replace("ai-agent-provider-account-guid-variable", providerAccountGuid);
        // delete provider account with test-account id
        final String response = executor.executeAtomSphereQuery(providerAccountDeleteQueryString, TestUtil.ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        assertNotNull(response);
        assertFalse(TestUtil.parseGraphqlResponse(response, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_DELETE,
                Boolean.class));
    }

    private boolean deleteProviderAccount(AiAgentProviderAccount providerAccount) {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_DELETE.getFileName());
        assertNotNull(queryStr);
        String providerAccountDeleteQueryString = queryStr
                .replace("ai-agent-provider-account-guid-variable", providerAccount.getGuid());
        try {
            executor.executeAtomSphereQuery(providerAccountDeleteQueryString,
                    TestUtil.COMMON_ACCOUNT_ID,
                    CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                    CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @Test
    @Transactional
    void testUpdateShouldNotAllowToChangeGardenProviderAccountId() throws Exception {
        // create a provider account in the registry
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS.getFileName());
        final String aiAgentProviderAccountOneResponse = executor.executeAtomSphereQuery(queryStr,
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentProviderAccountOne =
                TestUtil.parseGraphqlResponse(aiAgentProviderAccountOneResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentProviderAccountOne);
        assertNotNull(aiAgentProviderAccountOne.getId());

        String providerAccountOneGuid = aiAgentProviderAccountOne.getId();
        String providerAccountOneUpdateQueryString = GraphQLFileReaderUtil.readGraphQLQuery(
                GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_UPDATE.getFileName());
        assertNotNull(providerAccountOneUpdateQueryString);
        providerAccountOneUpdateQueryString = providerAccountOneUpdateQueryString
                .replace("ai-agent-provider-account-guid-variable", providerAccountOneGuid);
        when(spySecretsManagerService.getSecret(anyString()))
                .thenReturn(Flux.just("provider-account-creds"));
        when(spyAuthorizationParsingService.verifyImmutableFieldsAreNotChanged(
                eq("provider-account-creds"), anyString(), eq(aiAgentProviderAccountOne.getAuthSchema())))
                .thenReturn(Optional.of(List.of(AiAgentRegistryErrorCode.GARDEN_ACCOUNT_ID_IS_CHANGED)));

        String errorResponse = executor.executeAtomSphereQuery(providerAccountOneUpdateQueryString,
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount failedUpdate =
                TestUtil.parseGraphqlResponse(errorResponse, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_UPDATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(failedUpdate);
        assertTrue(errorResponse.contains("\"errorCode\":\"GARDEN_ACCOUNT_ID_IS_CHANGED\""), errorResponse);
        verify(spySecretsManagerService, never()).updateSecret(anyString(), anyString());

        when(spyAuthorizationParsingService.verifyImmutableFieldsAreNotChanged(
                eq("provider-account-creds"), anyString(), eq(aiAgentProviderAccountOne.getAuthSchema())))
                .thenThrow(IllegalArgumentException.class);

        errorResponse = executor.executeAtomSphereQuery(providerAccountOneUpdateQueryString,
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        failedUpdate =
                TestUtil.parseGraphqlResponse(errorResponse, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_UPDATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(failedUpdate, errorResponse);
        assertTrue(errorResponse.contains("\"errorCode\":\"INVALID_CONNECTION_DETAILS\""), errorResponse);
        verify(spySecretsManagerService, never()).updateSecret(anyString(), anyString());
    }

    @Test
    @Transactional
    @DisplayName("Update Mutation for AI agent provider account with account id that does not exists")
    void testAiAgentProverUpdateAccountNotFound() throws Exception {
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(
                GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_UPDATE.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("ai-agent-provider-account-guid-variable", createAIAgentProviderGuid());
        String errorResponse = executor.executeAtomSphereQuery(queryStr,
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount failedUpdate =
                TestUtil.parseGraphqlResponse(errorResponse, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_UPDATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(failedUpdate);
        assertTrue(errorResponse.contains("\"errorCode\":\"ACCOUNT_NOT_FOUND\""), errorResponse);
        verify(spySecretsManagerService, never()).updateSecret(anyString(), anyString());
    }

    @Test
    @Transactional
    @DisplayName("Update Mutation for AI agent provider account with null secret should throw SSM update failed")
    void testAiAgentProviderAccountUpdateWithNullSecret() throws Exception {
        when(spySecretsManagerService.updateSecret(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        String queryStr = createProviderAccountInDatabase(AI_AGENT_PROVIDER_ACCOUNT_UPDATE)
                .providerAccountUpdateQueryString;
        final String errorResponse = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        assertTrue(errorResponse.contains("\"errorCode\":\"SSM_UPDATE_FAILED\""), errorResponse);
    }

    @Test
    @Transactional
    @DisplayName("Update Mutation for AI agent provider account with invalid id format should throw System error")
    void testAiAgentProviderAccountUpdateSystemError() throws Exception {
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(
                GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_UPDATE.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("ai-agent-provider-account-guid-variable", "id");
        String errorResponse = executor.executeAtomSphereQuery(queryStr,
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount failedUpdate =
                TestUtil.parseGraphqlResponse(errorResponse, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_UPDATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(failedUpdate);
        assertTrue(errorResponse.contains("\"errorCode\":\"SYSTEM_ERROR\""), errorResponse);
        verify(spySecretsManagerService, never()).updateSecret(anyString(), anyString());
    }

    @Test
    @Transactional
    @DisplayName("Update Mutation for AI agent provider account by a non-owner")
    void testAiAgentProverUpdateByNonOwner() throws Exception {
        // create a provider account in the registry
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS.getFileName());
        final String aiAgentProviderAccountOneResponse = executor.executeAtomSphereQuery(queryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentProviderAccountOne =
                TestUtil.parseGraphqlResponse(aiAgentProviderAccountOneResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentProviderAccountOne);
        assertNotNull(aiAgentProviderAccountOne.getId());

        // update the created provider account by a non-owner
        String providerAccountOneGuid = aiAgentProviderAccountOne.getId();
        String providerAccountOneUpdateQueryString = GraphQLFileReaderUtil.readGraphQLQuery(
                GraphqlTypeEnum.MUTATION.getGraphqlType(), AI_AGENT_PROVIDER_ACCOUNT_UPDATE.getFileName());
        assertNotNull(providerAccountOneUpdateQueryString);
        providerAccountOneUpdateQueryString = providerAccountOneUpdateQueryString.replace(
                "ai-agent-provider-account-guid-variable", providerAccountOneGuid);

        String errorResponse = executor.executeAtomSphereQuery(providerAccountOneUpdateQueryString, TestUtil.ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        assertTrue(errorResponse.contains("\"errorCode\":\"ACCOUNT_NOT_FOUND\""), errorResponse);
    }

    @Test
    @Transactional
    @DisplayName("Should throw system error when secretsManagerService throws exception")
    void testAiAgentProviderAccountErrorScenario() throws Exception {
        ProviderAccountUpdateSetupResult result = createProviderAccountInDatabase(AI_AGENT_PROVIDER_ACCOUNT_UPDATE);
        String queryStr = result.providerAccountUpdateQueryString;
        when(spySecretsManagerService.updateSecret(anyString(), anyString())).thenThrow(IllegalArgumentException.class);
        final String errorResponse = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        assertTrue(errorResponse.contains("\"errorCode\":\"SYSTEM_ERROR\""), errorResponse);
    }

    @Deprecated
    // The new unit test is testAiAgentProviderAccountCreateForBedrockWithAssumeRoleCapabilitiesAndProgrammaticOamLink()
    @Disabled("a new test is added")
    @Test
    @DisplayName("Should successfully execute mutation to create Bedrock agent provider account with assume role capabilities")
    void testAiAgentProviderAccountCreateForBedrockWithAssumeRoleCapabilities() throws Exception {
        deleteAllProviderAccounts();
        String customerAwsAccountId = "************";
        when(spyAuthorizationParsingService.isDoCheckIfExternalAccountLinkedToAnotherProviderAccount(AiProviderAuthSchema.AWS_ASSUME_ROLE))
                .thenReturn(true);
        doReturn(true).when(spyFeatureManager).isDoProvisioningWithAssumeRole();
        doReturn(false).when(spyFeatureManager).isAutoCreateOamLink();

        // step1 mutation for AWS Bedrock - simulate invalid aws account id
        String step1QueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BEDROCK_ASSUME_ROLE_SUCCESS.getFileName());
        assertNotNull(step1QueryStr);
        validateErrorResponseInStep1(step1QueryStr, "externalAccountId: \"" + customerAwsAccountId + "\"", "externalAccountId: \"a066499543631b\"", "INVALID_AWS_ACCOUNT_ID");

        // step1 mutation for AWS Bedrock - simulate error in saving secret
        doReturn(null)
            .when(spySecretsManagerService).createSecret(anyString(), anyString());
        validateErrorResponseInStep1(step1QueryStr, null, null, "SSM_UPDATE_FAILED");

        // step1 mutation for AWS Bedrock - success path
        Mockito.reset(spySecretsManagerService);
        final String response = executor.executeAtomSphereQuery(step1QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                        CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                        CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentProviderAccount =
                TestUtil.parseGraphqlResponse(response, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentProviderAccount, response);
        assertNotNull(aiAgentProviderAccount.getId(), response);
        assertEquals(customerAwsAccountId, aiAgentProviderAccount.getExternalProviderAccountId(), response);
        assertEquals(AiAgentProviderAccountStatus.PROVISIONING_STEP1_COMPLETE, aiAgentProviderAccount.getProviderAccountStatus());

        String secretName = getSecretName(customerAwsAccountId);
        Optional<AiAgentProviderAccount> optionalDbProviderAccountId = _providerAccountRepository.findByGuid(
                aiAgentProviderAccount.getId());
        assertTrue(optionalDbProviderAccountId.isPresent());
        AiAgentProviderAccount dbProviderAccountId = optionalDbProviderAccountId.get();
        assertEquals("us-east-1", dbProviderAccountId.getRegion());
        assertEquals(customerAwsAccountId, dbProviderAccountId.getExternalProviderAccountId());
        assertEquals(secretName, dbProviderAccountId.getCredentialsKey());
        assertEquals(AiAgentProviderAccountStatus.PROVISIONING_STEP1_COMPLETE, dbProviderAccountId.getProviderAccountStatus());
        validateAuditFields(dbProviderAccountId);
        verify(spySecretsManagerService).createSecret(eq(secretName), _argCaptorForSecret.capture());
        Mockito.verifyNoMoreInteractions(spySecretsManagerService);
        AwsCredentials awsCredentials = _objectMapper.readValue(_argCaptorForSecret.getValue(), AwsCredentials.class);
        assertNotNull(awsCredentials.getExternalId(), _argCaptorForSecret.getValue());
        assertEquals("us-east-1", awsCredentials.getAwsRegion(), _argCaptorForSecret.getValue());
        assertEquals(customerAwsAccountId, awsCredentials.getAwsAccountId(), _argCaptorForSecret.getValue());

        List<AiAgentProviderAccount> providerAccounts = _providerAccountRepository.findAll(
                hasExternalId(customerAwsAccountId));
        assertEquals(1, providerAccounts.size());
        Mockito.reset(spySecretsManagerService);

        // redo step1 mutation for AWS Bedrock should return the above draft provider account
        final String secondResponse = executor.executeAtomSphereQuery(step1QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentProviderAccountRedoResponse =
                TestUtil.parseGraphqlResponse(secondResponse, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentProviderAccountRedoResponse, secondResponse);
        assertEquals(aiAgentProviderAccount.getId(), aiAgentProviderAccountRedoResponse.getId(), secondResponse);
        assertEquals(aiAgentProviderAccount.getProviderAccountStatus(), aiAgentProviderAccountRedoResponse.getProviderAccountStatus(), secondResponse);

        // fetch provider accounts list to ensure the list will contain the draft account
        String getQueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNTS_GET.getFileName());
        assertNotNull(getQueryStr, "GraphQL query string should not be null");

        // Execute the GET Query
        String getResponse = executor.executeAtomSphereQuery(getQueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        JsonNode rootNode = _objectMapper.readTree(getResponse);
        JsonNode accountData = rootNode.get("data").get("aiAgentProviderAccounts");
        assertNotNull(accountData, getResponse);
        assertEquals(1, accountData.get("numberOfResults").asInt());

        AwsCredentials awsCredentialsWithTempAccessToken = _bedrockTestUtil.getAwsCredentials();

        // Get the onboarding template success
        when(spySecretsManagerService.getSecret(secretName))
                .thenReturn(Flux.just(_objectMapper.writeValueAsString(awsCredentialsWithTempAccessToken)));
        String getOnboardingTemplateQueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT.getFileName());
        assertNotNull(getOnboardingTemplateQueryStr, "GraphQL query string should not be null");
        getOnboardingTemplateQueryStr = getOnboardingTemplateQueryStr.replace("ai-agent-provider-account-guid-variable",
                aiAgentProviderAccount.getId());
        // Execute the GET Query
        String getOnboardingTemplateResponse = executor.executeAtomSphereQuery(getOnboardingTemplateQueryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES,
                TestUtil.COMMON_USER_NAME, CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount providerAccountWithTemplate = TestUtil.parseGraphqlResponse(getOnboardingTemplateResponse,
                       "aiAgentProviderAccount", com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(providerAccountWithTemplate, getOnboardingTemplateResponse);
        AiAgentProviderAccountOnboardingTemplate onboardingTemplate =
              providerAccountWithTemplate.getOnboardingTemplate();
        assertNotNull(onboardingTemplate, getOnboardingTemplateResponse);
        assertEquals(awsCredentialsWithTempAccessToken.getExternalId(), onboardingTemplate.getExternalId(), getOnboardingTemplateResponse);
        assertEquals("https://example.com/presigned", onboardingTemplate.getTemplateUrl(), getOnboardingTemplateResponse);
        assertEquals(HOURS_12, onboardingTemplate.getExpiresIn(), getOnboardingTemplateResponse);
        verify(spyFileStorageService).putSmallObject(eq(s3Bucket), eq(CLOUDFORMATION_S3_KEY_PREFIX + dbProviderAccountId.getGuid()), _argCaptorForS3File.capture());
        assertEquals("boomi-ai-agent-registry-app-data-test-us-east-1", s3Bucket);
        byte[] expectCloudFormation =  Files.readAllBytes(
            ResourceUtils.getFile("classpath:cloudformation/customer-account-provision-cj-sandbox-act.yaml")
                    .toPath());
        assertEquals(new String(expectCloudFormation, StandardCharsets.UTF_8), new String(_argCaptorForS3File.getValue(), StandardCharsets.UTF_8));

        // Get onboarding template error 1 in Get External ID
        when(spySecretsManagerService.getSecret(secretName))
                .thenReturn(Flux.error(new RuntimeException("junit")));
        // Execute the GET Query
        getOnboardingTemplateResponse = executor.executeAtomSphereQuery(getOnboardingTemplateQueryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES,
                TestUtil.COMMON_USER_NAME, CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        providerAccountWithTemplate = TestUtil.parseGraphqlResponse(getOnboardingTemplateResponse,
                       "aiAgentProviderAccount", com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(providerAccountWithTemplate.getOnboardingTemplate(), getOnboardingTemplateResponse);
        assertTrue(getOnboardingTemplateResponse.contains("\"errorCode\":\"EXTERNAL_ID_NOT_FOUND\""), getOnboardingTemplateResponse);

        // Get onboarding template error 2 in Get External ID
        when(spySecretsManagerService.getSecret(secretName))
                .thenReturn(Flux.empty());
        // Execute the GET Query
        getOnboardingTemplateResponse = executor.executeAtomSphereQuery(getOnboardingTemplateQueryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES,
                TestUtil.COMMON_USER_NAME, CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        providerAccountWithTemplate = TestUtil.parseGraphqlResponse(getOnboardingTemplateResponse,
                       "aiAgentProviderAccount", com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(providerAccountWithTemplate.getOnboardingTemplate(), getOnboardingTemplateResponse);
        assertTrue(getOnboardingTemplateResponse.contains("\"errorCode\":\"EXTERNAL_ID_NOT_FOUND\""), getOnboardingTemplateResponse);

        Mockito.reset(spySecretsManagerService);
        when(spySecretsManagerService.getSecret(secretName))
                .thenReturn(Flux.just(_objectMapper.writeValueAsString(awsCredentialsWithTempAccessToken)));
       // Get onboarding template error in getting presigned url
        doReturn(Optional.empty())
                .when(spyFileStorageService).putSmallObject(any(), any(), any());
        // Execute the GET Query
        getOnboardingTemplateResponse = executor.executeAtomSphereQuery(getOnboardingTemplateQueryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES,
                TestUtil.COMMON_USER_NAME, CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        providerAccountWithTemplate = TestUtil.parseGraphqlResponse(getOnboardingTemplateResponse,
                       "aiAgentProviderAccount", com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(providerAccountWithTemplate.getOnboardingTemplate(), getOnboardingTemplateResponse);
        assertTrue(getOnboardingTemplateResponse.contains("\"errorCode\":\"PRESIGNED_URL_GENERATION_FAILED\""), getOnboardingTemplateResponse);

        // complete step 2 with error
        StsException stsException = (StsException) StsException.builder().build();
        doThrow(stsException)
                .when(spyAwsAssumeRoleAuthorizationParserStrategy).validateConnection(eq(dbProviderAccountId
                        .getExternalProviderAccountId()), eq(dbProviderAccountId.getRegion()), anyString());
        String step2QueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                 AI_AGENT_PROVIDER_ACCOUNT_STEP2_FOR_BEDROCK.getFileName());
        assertNotNull(step1QueryStr);
        step2QueryStr = step2QueryStr
                         .replace("ai-agent-provider-account-guid-variable", aiAgentProviderAccount.getId());

        Flux<String> credsJsonWithExternalId = Flux.just(_bedrockTestUtil.getAwsCredentialsWithJustExternalId());
        doReturn(credsJsonWithExternalId)
                .when(spySecretsManagerService).getSecret(secretName);
        String step2Response = executor.executeAtomSphereQuery(step2QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                      CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                      CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        assertTrue(step2Response.contains("\"errorCode\":\"INVALID_CONNECTION_DETAILS\""), step2Response);

        // complete step 2 with success
        doReturn(Optional.of(awsCredentialsWithTempAccessToken))
                .when(spyAwsAssumeRoleAuthorizationParserStrategy).validateConnection(eq(dbProviderAccountId
                        .getExternalProviderAccountId()), eq(dbProviderAccountId.getRegion()), eq(awsCredentialsWithTempAccessToken.getExternalId()));

        step2Response = executor.executeAtomSphereQuery(step2QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentRegistryAccountAfterStep2 =
                TestUtil.parseGraphqlResponse(step2Response, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CONFIRM,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentRegistryAccountAfterStep2, step2Response);
        assertEquals(aiAgentProviderAccount.getId(), aiAgentRegistryAccountAfterStep2.getId(), step2Response);
        assertEquals(dbProviderAccountId.getExternalProviderAccountId(), aiAgentRegistryAccountAfterStep2.getExternalProviderAccountId(), step2Response);
        assertEquals(AiAgentProviderAccountStatus.CONNECTED, aiAgentRegistryAccountAfterStep2.getProviderAccountStatus());
        verify(spySecretsManagerService).updateSecret(eq(secretName), _argCaptorForSecret.capture());
        verify(spySecretsManagerService, never()).createSecret(anyString(), anyString());
        awsCredentials = _objectMapper.readValue(_argCaptorForSecret.getValue(), AwsCredentials.class);
        assertNotNull(awsCredentials.getExternalId(), _argCaptorForSecret.getValue());
        assertEquals(awsCredentialsWithTempAccessToken.getAwsAccessKeyId(), awsCredentials.getAwsAccessKeyId(), _argCaptorForSecret.getValue());
        assertEquals(awsCredentialsWithTempAccessToken.getAwsSecretAccessKey(), awsCredentials.getAwsSecretAccessKey(), _argCaptorForSecret.getValue());

        // redoing step 1 should result in an error once the provider account is connected
        String step1DuplicateAccountResponse = executor.executeAtomSphereQuery(step1QueryStr, TestUtil.COMMON_ACCOUNT_ID,
            CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
            CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        assertTrue(step1DuplicateAccountResponse.contains(PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT.name()), step1DuplicateAccountResponse);

        getResponse = executor.executeAtomSphereQuery(getQueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        rootNode = _objectMapper.readTree(getResponse);
        accountData = rootNode.get("data").get("aiAgentProviderAccounts");
        assertNotNull(accountData, getResponse);
        assertEquals(1, accountData.get("numberOfResults").asInt());

        // create another provider account in the same Boomi account using the same aws account and different region
        // it should reuse the AWS credentials obtained during onboarding the first provider
        Mockito.clearInvocations(spySecretsManagerService);
        Mockito.clearInvocations(spyAwsAssumeRoleAuthorizationParserStrategy);
        doReturn(Optional.of(awsCredentialsWithTempAccessToken))
             .when(spyAwsAssumeRoleAuthorizationParserStrategy).validateConnection(eq(dbProviderAccountId
                     .getExternalProviderAccountId()), eq(dbProviderAccountId.getRegion()), eq(awsCredentialsWithTempAccessToken.getExternalId()));
        String step1QueryInDiffRegion = step1QueryStr.replace(" region: \"us-east-1\"", " region: \"us-west-2\"");
        String responseForDifferentRegistryAccount = executor.executeAtomSphereQuery(step1QueryInDiffRegion, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount providerAccountInDifferentRegion =
                TestUtil.parseGraphqlResponse(responseForDifferentRegistryAccount, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(providerAccountInDifferentRegion, responseForDifferentRegistryAccount);
        assertNotNull(providerAccountInDifferentRegion.getId(), responseForDifferentRegistryAccount);
        assertEquals(customerAwsAccountId, providerAccountInDifferentRegion.getExternalProviderAccountId(), response);
        assertEquals(AiAgentProviderAccountStatus.PROVISIONING_STEP1_COMPLETE, providerAccountInDifferentRegion.getProviderAccountStatus());
        Mockito.verifyNoMoreInteractions(spySecretsManagerService);

        // Get the onboarding template for the second provider account
        Mockito.clearInvocations(spyFileStorageService);
        when(spySecretsManagerService.getSecret(secretName))
                .thenReturn(Flux.just(_objectMapper.writeValueAsString(awsCredentialsWithTempAccessToken)));
        doReturn(Optional.of(new URL("https://example.com")))
                .when(spyFileStorageService).putSmallObject(any(), any(), any());
        getOnboardingTemplateQueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT.getFileName());
        getOnboardingTemplateQueryStr = getOnboardingTemplateQueryStr.replace("ai-agent-provider-account-guid-variable",
                providerAccountInDifferentRegion.getId());
        // Execute the GET Query
        getOnboardingTemplateResponse = executor.executeAtomSphereQuery(getOnboardingTemplateQueryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES,
                TestUtil.COMMON_USER_NAME, CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount providerAccountWithOamLinkOnlyTemplate = TestUtil.parseGraphqlResponse(getOnboardingTemplateResponse,
                       "aiAgentProviderAccount", com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(providerAccountWithOamLinkOnlyTemplate, getOnboardingTemplateResponse);
        AiAgentProviderAccountOnboardingTemplate oamLinkOnboardingTemplate =
                providerAccountWithOamLinkOnlyTemplate.getOnboardingTemplate();
        assertNotNull(oamLinkOnboardingTemplate, getOnboardingTemplateResponse);
        assertEquals(awsCredentialsWithTempAccessToken.getExternalId(), oamLinkOnboardingTemplate.getExternalId(), getOnboardingTemplateResponse);
        assertEquals("https://example.com/presigned", oamLinkOnboardingTemplate.getTemplateUrl(), getOnboardingTemplateResponse);
        assertEquals(HOURS_12, oamLinkOnboardingTemplate.getExpiresIn(), getOnboardingTemplateResponse);
        verify(spyFileStorageService).putSmallObject(eq(s3Bucket), eq(CLOUDFORMATION_S3_KEY_PREFIX + providerAccountInDifferentRegion.getId()), _argCaptorForS3File.capture());
        expectCloudFormation =  Files.readAllBytes(
            ResourceUtils.getFile("classpath:cloudformation/customer-account-provision-oam-only-cj-sandbox-act.yaml")
                    .toPath());
        assertEquals(new String(expectCloudFormation, StandardCharsets.UTF_8), new String(_argCaptorForS3File.getValue(), StandardCharsets.UTF_8));

       // do step 2 for the second provider account should not validate connection again
       Mockito.reset(spySecretsManagerService);
       Mockito.reset(spyAwsAssumeRoleAuthorizationParserStrategy);
       step2QueryStr = step2QueryStr
                                 .replace("ai-agent-provider-account-guid-variable", providerAccountInDifferentRegion.getId());
        step2Response = executor.executeAtomSphereQuery(step2QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        aiAgentRegistryAccountAfterStep2 =
                TestUtil.parseGraphqlResponse(step2Response, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CONFIRM,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentRegistryAccountAfterStep2, step2Response);
        assertEquals(aiAgentProviderAccount.getId(), aiAgentRegistryAccountAfterStep2.getId(), step2Response);
        // both provider accounts share the same aws account
        assertEquals(aiAgentProviderAccount.getExternalProviderAccountId(), aiAgentRegistryAccountAfterStep2.getExternalProviderAccountId(), step2Response);
        assertEquals(AiAgentProviderAccountStatus.CONNECTED, aiAgentRegistryAccountAfterStep2.getProviderAccountStatus());
        Mockito.verifyNoMoreInteractions(spySecretsManagerService);
        Mockito.verifyNoMoreInteractions(spyAwsAssumeRoleAuthorizationParserStrategy);

        Mockito.reset(spySecretsManagerService);
        // deleting provider account 1 should not delete the secret
        asyncDeleteProviderAccountAndVerify(dbProviderAccountId.getGuid());
        Mockito.verifyNoMoreInteractions(spySecretsManagerService);
        Assertions.assertFalse(
                _providerAccountRepository.findByGuid(providerAccountInDifferentRegion.getId()).isEmpty());
        // deleting provider account 2 should delete the secret
        asyncDeleteProviderAccountAndVerify(providerAccountInDifferentRegion.getId());
        verify(spySecretsManagerService).deleteSecret(secretName);
    }

    @Test
    @DisplayName("Should successfully execute mutation to create Bedrock agent provider account with assume role capabilities and programmatic OAM link creation")
    void testAiAgentProviderAccountCreateForBedrockWithAssumeRoleCapabilitiesAndProgrammaticOamLink() throws Exception {
        deleteAllProviderAccounts();
        Credentials assumeRoleCredentials = StsClientTestUtil.setup(mockStsClient, _assumeRoleRequestArgCaptor);
        String customerAwsAccountId = "************";
        when(spyAuthorizationParsingService.isDoCheckIfExternalAccountLinkedToAnotherProviderAccount(
                AiProviderAuthSchema.AWS_ASSUME_ROLE)).thenReturn(true);
        doReturn(true).when(spyFeatureManager).isDoProvisioningWithAssumeRole();
        doReturn(true).when(spyFeatureManager).isAutoCreateOamLink();
        MockOamClientTestConfig.setupMockOamClient(mockOamClient);

        // step1 mutation for AWS Bedrock - simulate invalid aws account id
        String step1QueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BEDROCK_ASSUME_ROLE_SUCCESS.getFileName());
        assertNotNull(step1QueryStr);
        validateErrorResponseInStep1(step1QueryStr, "externalAccountId: \"" + customerAwsAccountId + "\"",
                "externalAccountId: \"a066499543631b\"", "INVALID_AWS_ACCOUNT_ID");

        // step1 mutation for AWS Bedrock - simulate error in saving secret
        doReturn(null).when(spySecretsManagerService).createSecret(anyString(), anyString());
        validateErrorResponseInStep1(step1QueryStr, null, null, "SSM_UPDATE_FAILED");

        // step1 mutation for AWS Bedrock - success path
        Mockito.reset(spySecretsManagerService);
        final String response = executor.executeAtomSphereQuery(step1QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME, CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentProviderAccount =
                TestUtil.parseGraphqlResponse(response, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentProviderAccount, response);
        assertNotNull(aiAgentProviderAccount.getId(), response);
        assertEquals(customerAwsAccountId, aiAgentProviderAccount.getExternalProviderAccountId(), response);
        assertEquals(AiAgentProviderAccountStatus.PROVISIONING_STEP1_COMPLETE, aiAgentProviderAccount.getProviderAccountStatus());

        String secretName = getSecretName(customerAwsAccountId);
        Optional<AiAgentProviderAccount> optionalDbProviderAccountId = _providerAccountRepository.findByGuid(
                aiAgentProviderAccount.getId());
        assertTrue(optionalDbProviderAccountId.isPresent());
        AiAgentProviderAccount dbProviderAccountId = optionalDbProviderAccountId.get();
        assertEquals("us-east-1", dbProviderAccountId.getRegion());
        assertEquals(customerAwsAccountId, dbProviderAccountId.getExternalProviderAccountId());
        assertEquals(secretName, dbProviderAccountId.getCredentialsKey());
        assertEquals(AiAgentProviderAccountStatus.PROVISIONING_STEP1_COMPLETE, dbProviderAccountId.getProviderAccountStatus());
        validateAuditFields(dbProviderAccountId);
        verify(spySecretsManagerService).createSecret(eq(secretName), _argCaptorForSecret.capture());
        Mockito.verifyNoMoreInteractions(spySecretsManagerService);
        AwsCredentials awsCredentials = _objectMapper.readValue(_argCaptorForSecret.getValue(), AwsCredentials.class);
        assertNotNull(awsCredentials.getExternalId(), _argCaptorForSecret.getValue());
        assertEquals("us-east-1", awsCredentials.getAwsRegion(), _argCaptorForSecret.getValue());
        assertEquals(customerAwsAccountId, awsCredentials.getAwsAccountId(), _argCaptorForSecret.getValue());

        List<AiAgentProviderAccount> providerAccounts = _providerAccountRepository.findAll(
                hasExternalId(customerAwsAccountId));
        assertEquals(1, providerAccounts.size());
        Mockito.reset(spySecretsManagerService);
        verify(mockOamClient, never()).putSinkPolicy(Mockito.any(PutSinkPolicyRequest.class));
        verify(spyAiAgentProviderAccountCommonServiceUtil, never()).handleAfterProviderCreation(any(), any());

        // redo step1 mutation for AWS Bedrock should return the above draft provider account
        Mockito.clearInvocations(spyAiAgentProviderAccountCommonServiceUtil);
        final String secondResponse = executor.executeAtomSphereQuery(step1QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentProviderAccountRedoResponse =
                TestUtil.parseGraphqlResponse(secondResponse, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentProviderAccountRedoResponse, secondResponse);
        assertEquals(aiAgentProviderAccount.getId(), aiAgentProviderAccountRedoResponse.getId(), secondResponse);
        assertEquals(aiAgentProviderAccount.getProviderAccountStatus(), aiAgentProviderAccountRedoResponse.getProviderAccountStatus(), secondResponse);
        verify(spyAiAgentProviderAccountCommonServiceUtil, never()).handleAfterProviderCreation(any(), any());

        // fetch provider accounts list to ensure the list will contain the draft account
        String getQueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNTS_GET.getFileName());
        assertNotNull(getQueryStr, "GraphQL query string should not be null");

        // Execute the GET Query
        String getResponse = executor.executeAtomSphereQuery(getQueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        JsonNode rootNode = _objectMapper.readTree(getResponse);
        JsonNode accountData = rootNode.get("data").get("aiAgentProviderAccounts");
        assertNotNull(accountData, getResponse);
        assertEquals(1, accountData.get("numberOfResults").asInt());

        AwsCredentials awsCredentialsWithTempAccessToken = _bedrockTestUtil.getAwsCredentials();

        // Get the onboarding template success
        when(spySecretsManagerService.getSecret(secretName)).thenReturn(Flux.just(_objectMapper.writeValueAsString(awsCredentialsWithTempAccessToken)));
        String getOnboardingTemplateQueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT.getFileName());
        assertNotNull(getOnboardingTemplateQueryStr, "GraphQL query string should not be null");
        getOnboardingTemplateQueryStr = getOnboardingTemplateQueryStr.replace("ai-agent-provider-account-guid-variable",
                aiAgentProviderAccount.getId());
        // Execute the GET Query
        String getOnboardingTemplateResponse = executor.executeAtomSphereQuery(getOnboardingTemplateQueryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount providerAccountWithTemplate = TestUtil.parseGraphqlResponse(getOnboardingTemplateResponse,
                "aiAgentProviderAccount", com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(providerAccountWithTemplate, getOnboardingTemplateResponse);
        AiAgentProviderAccountOnboardingTemplate onboardingTemplate = providerAccountWithTemplate.getOnboardingTemplate();
        assertNotNull(onboardingTemplate, getOnboardingTemplateResponse);
        assertEquals(awsCredentialsWithTempAccessToken.getExternalId(), onboardingTemplate.getExternalId(),
                getOnboardingTemplateResponse);
        assertEquals("https://example.com/presigned", onboardingTemplate.getTemplateUrl(), getOnboardingTemplateResponse);
        assertEquals(HOURS_12, onboardingTemplate.getExpiresIn(), getOnboardingTemplateResponse);
        verify(spyFileStorageService).putSmallObject(eq(s3Bucket), eq(CLOUDFORMATION_S3_KEY_PREFIX + dbProviderAccountId.getGuid()), _argCaptorForS3File.capture());
        assertEquals("boomi-ai-agent-registry-app-data-test-us-east-1", s3Bucket);
        byte[] expectCloudFormation = Files.readAllBytes(
                ResourceUtils.getFile("classpath:cloudformation/customer-account-provision-cj-sandbox-act.v2.yaml").toPath());
        String expectedCloudFormationTemplate = new String(expectCloudFormation, StandardCharsets.UTF_8);
        expectedCloudFormationTemplate = expectedCloudFormationTemplate.replace("<CUSTOMER_REGION>", "us-east-1");
        assertEquals(expectedCloudFormationTemplate, new String(_argCaptorForS3File.getValue(), StandardCharsets.UTF_8));

        // Get onboarding template error 1 in Get External ID
        when(spySecretsManagerService.getSecret(secretName)).thenReturn(Flux.error(new RuntimeException("junit")));
        // Execute the GET Query
        getOnboardingTemplateResponse = executor.executeAtomSphereQuery(getOnboardingTemplateQueryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        providerAccountWithTemplate = TestUtil.parseGraphqlResponse(getOnboardingTemplateResponse,
                "aiAgentProviderAccount", com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(providerAccountWithTemplate.getOnboardingTemplate(), getOnboardingTemplateResponse);
        assertTrue(getOnboardingTemplateResponse.contains("\"errorCode\":\"EXTERNAL_ID_NOT_FOUND\""),
                getOnboardingTemplateResponse);

        // Get onboarding template error 2 in Get External ID
        when(spySecretsManagerService.getSecret(secretName)).thenReturn(Flux.empty());
        // Execute the GET Query
        getOnboardingTemplateResponse = executor.executeAtomSphereQuery(getOnboardingTemplateQueryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        providerAccountWithTemplate = TestUtil.parseGraphqlResponse(getOnboardingTemplateResponse,
                "aiAgentProviderAccount", com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(providerAccountWithTemplate.getOnboardingTemplate(), getOnboardingTemplateResponse);
        assertTrue(getOnboardingTemplateResponse.contains("\"errorCode\":\"EXTERNAL_ID_NOT_FOUND\""),
                getOnboardingTemplateResponse);

        Mockito.reset(spySecretsManagerService);
        when(spySecretsManagerService.getSecret(secretName)).thenReturn(Flux.just(_objectMapper.writeValueAsString(awsCredentialsWithTempAccessToken)));
        // Get onboarding template error in getting presigned url
        doReturn(Optional.empty()).when(spyFileStorageService).putSmallObject(any(), any(), any());
        // Execute the GET Query
        getOnboardingTemplateResponse = executor.executeAtomSphereQuery(getOnboardingTemplateQueryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        providerAccountWithTemplate = TestUtil.parseGraphqlResponse(getOnboardingTemplateResponse,
                "aiAgentProviderAccount", com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(providerAccountWithTemplate.getOnboardingTemplate(), getOnboardingTemplateResponse);
        assertTrue(getOnboardingTemplateResponse.contains("\"errorCode\":\"PRESIGNED_URL_GENERATION_FAILED\""),
                getOnboardingTemplateResponse);

        // complete step 2 with error
        Mockito.clearInvocations(spyAiAgentProviderAccountCommonServiceUtil);
        StsException stsException = (StsException) StsException.builder().build();
        doThrow(stsException).when(spyAwsAssumeRoleAuthorizationParserStrategy).validateConnection(eq(dbProviderAccountId.getExternalProviderAccountId()), eq(dbProviderAccountId.getRegion()),
                anyString());
        String step2QueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_STEP2_FOR_BEDROCK.getFileName());
        assertNotNull(step1QueryStr);
        step2QueryStr = step2QueryStr.replace("ai-agent-provider-account-guid-variable", aiAgentProviderAccount.getId());

        Flux<String> credsJsonWithExternalId = Flux.just(_bedrockTestUtil.getAwsCredentialsWithJustExternalId());
        doReturn(credsJsonWithExternalId).when(spySecretsManagerService).getSecret(secretName);
        String step2Response = executor.executeAtomSphereQuery(step2QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME, CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        assertTrue(step2Response.contains("\"errorCode\":\"INVALID_CONNECTION_DETAILS\""), step2Response);
        verify(spyAiAgentProviderAccountCommonServiceUtil, never()).handleAfterProviderCreation(any(), any());

        // complete step 2 with ConflictException if the OAM link already exists
        doThrow(ConflictException.builder().message("Oam Link is already present from your AWS account to the provider account.").build()).when(
                spyOamLinkService).createOamLink(any(), any(), any(), any());
        doReturn(true).when(spyFeatureManager).isAutoCreateOamLink();
        doReturn(Optional.of(awsCredentialsWithTempAccessToken)).when(spyAwsAssumeRoleAuthorizationParserStrategy).validateConnection(eq(dbProviderAccountId.getExternalProviderAccountId()),
                eq(dbProviderAccountId.getRegion()), eq(awsCredentialsWithTempAccessToken.getExternalId()));
        step2Response = executor.executeAtomSphereQuery(step2QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        assertTrue(step2Response.contains("\"errorCode\":\"OAM_LINK_ALREADY_PRESENT_ERROR\""), step2Response);
        reset(spyOamLinkService);

        // complete step 2 with ConflictException wrapped inside another RuntimeException
        // Create the nested ConflictException
        ConflictException conflictException = ConflictException.builder().message("Link already exists").statusCode(409)
                .build();

        // Wrap it inside a generic RuntimeException
        RuntimeException wrapperException = new RuntimeException("Wrapper exception", conflictException);
        doThrow(wrapperException).when(spyOamLinkService).createOamLink(any(), any(), any(), any());
        step2Response = executor.executeAtomSphereQuery(step2QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        assertTrue(step2Response.contains("\"errorCode\":\"OAM_LINK_ALREADY_PRESENT_ERROR\""), step2Response);
        reset(spyOamLinkService);

        // complete step 2 with Unknown Exception
        doThrow(InvalidParameterException.builder().message("Oam Link creation failed due to an unknown reason.").build()).when(spyOamLinkService).createOamLink(any(), any(), any(), any());
        step2Response = executor.executeAtomSphereQuery(step2QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        assertTrue(step2Response.contains("\"errorCode\":\"OAM_LINK_CREATION_FAILED_ERROR\""), step2Response);
        reset(spyOamLinkService);

        // complete step 2 with success
        Mockito.clearInvocations(spyAiAgentProviderAccountCommonServiceUtil);
        doReturn(Optional.of(awsCredentialsWithTempAccessToken)).when(spyAwsAssumeRoleAuthorizationParserStrategy).validateConnection(eq(dbProviderAccountId.getExternalProviderAccountId()),
                eq(dbProviderAccountId.getRegion()), eq(awsCredentialsWithTempAccessToken.getExternalId()));

        step2Response = executor.executeAtomSphereQuery(step2QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentRegistryAccountAfterStep2 =
                TestUtil.parseGraphqlResponse(step2Response, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CONFIRM,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentRegistryAccountAfterStep2, step2Response);
        assertEquals(aiAgentProviderAccount.getId(), aiAgentRegistryAccountAfterStep2.getId(), step2Response);
        assertEquals(dbProviderAccountId.getExternalProviderAccountId(), aiAgentRegistryAccountAfterStep2.getExternalProviderAccountId(), step2Response);
        assertEquals(AiAgentProviderAccountStatus.CONNECTED, aiAgentRegistryAccountAfterStep2.getProviderAccountStatus());
        verify(spySecretsManagerService, times(4)).updateSecret(eq(secretName), _argCaptorForSecret.capture());
        verify(spySecretsManagerService, never()).createSecret(anyString(), anyString());
        awsCredentials = _objectMapper.readValue(_argCaptorForSecret.getValue(), AwsCredentials.class);
        assertNotNull(awsCredentials.getExternalId(), _argCaptorForSecret.getValue());
        assertEquals(awsCredentialsWithTempAccessToken.getAwsAccessKeyId(), awsCredentials.getAwsAccessKeyId(),
                _argCaptorForSecret.getValue());
        assertEquals(awsCredentialsWithTempAccessToken.getAwsSecretAccessKey(), awsCredentials.getAwsSecretAccessKey(),
                _argCaptorForSecret.getValue());
        testOamClientInteractions(customerAwsAccountId, dbProviderAccountId.getRegion(),
                "b1d64f36-6af9-4899-b478-8d5b43f8caec");
        verify(spyAiAgentProviderAccountCommonServiceUtil).handleAfterProviderCreation(any(), any());

        // redoing step 1 should result in an error once the provider account is connected
        Mockito.clearInvocations(spyAiAgentProviderAccountCommonServiceUtil);
        String step1DuplicateAccountResponse = executor.executeAtomSphereQuery(step1QueryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        assertTrue(step1DuplicateAccountResponse.contains(PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT.name()),
                step1DuplicateAccountResponse);

        getResponse = executor.executeAtomSphereQuery(getQueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        rootNode = _objectMapper.readTree(getResponse);
        accountData = rootNode.get("data").get("aiAgentProviderAccounts");
        assertNotNull(accountData, getResponse);
        assertEquals(1, accountData.get("numberOfResults").asInt());
        verify(spyAiAgentProviderAccountCommonServiceUtil, never()).handleAfterProviderCreation(any(), any());

        // update the account
        Mockito.clearInvocations(spyAiAgentProviderAccountCommonServiceUtil);
        clearSpyInvocations(awsCredentialsWithTempAccessToken, secretName, "us-west-2");
        optionalDbProviderAccountId = _providerAccountRepository.findByGuid(
                       aiAgentProviderAccount.getId());
        assertTrue(optionalDbProviderAccountId.isPresent());
        dbProviderAccountId = optionalDbProviderAccountId.get();
        dbProviderAccountId.setProviderAccountStatus(AiAgentProviderAccountStatus.DISCONNECTED);
        _providerAccountRepository.saveAndFlush(dbProviderAccountId);
        String updateQueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
            AI_AGENT_PROVIDER_ACCOUNT_UPDATE_FOR_BEDROCK_ASSUME_ROLE.getFileName());
        updateQueryStr = updateQueryStr
            .replace("ai-agent-provider-account-guid-variable", aiAgentProviderAccount.getId());
        String updateAccountResponse = executor.executeAtomSphereQuery(updateQueryStr, TestUtil.COMMON_ACCOUNT_ID,
            CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME, CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount updatedAiAgentRegistryAccount =
                        TestUtil.parseGraphqlResponse(updateAccountResponse, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_UPDATE,
                                com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(updatedAiAgentRegistryAccount, updateAccountResponse);
        assertNotNull(updatedAiAgentRegistryAccount.getId(), updateAccountResponse);
        Mockito.verifyNoMoreInteractions(mockOamClient);
        verify(spySecretsManagerService, times(1)).updateSecret(eq(secretName), _argCaptorForSecret.capture());
        verify(spySecretsManagerService, never()).createSecret(anyString(), anyString());
        assertEquals(AiAgentProviderAccountStatus.CONNECTED, updatedAiAgentRegistryAccount.getProviderAccountStatus());

        // create a second provider account in the same Boomi account using the same aws account and different region
        // we will simulate health check connection error
        clearSpyInvocations(awsCredentialsWithTempAccessToken, secretName, "us-west-2");
        Mockito.clearInvocations(spyAiAgentProviderAccountCommonServiceUtil);
        doThrow(new RuntimeException("bedrock-agent.us-west-1.amazonaws.com")).when(
                spyAwsAssumeRoleAuthorizationParserStrategy).validateConnection(eq(customerAwsAccountId), eq("us-west-2"), anyString());
        String step1QueryInDiffRegion = step1QueryStr.replace(" region: \"us-east-1\"", " region: \"us-west-2\"");
        String step1ErrorResponseForDifferentRegistryAccount = executor.executeAtomSphereQuery(step1QueryInDiffRegion,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount providerAccountWithErrorInDifferentRegion =
                TestUtil.parseGraphqlResponse(step1ErrorResponseForDifferentRegistryAccount,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertTrue(step1ErrorResponseForDifferentRegistryAccount.contains("\"errorCode\":\"INVALID_CONNECTION_DETAILS\""), step1ErrorResponseForDifferentRegistryAccount);
        assertNull(providerAccountWithErrorInDifferentRegion, step1ErrorResponseForDifferentRegistryAccount);

        // create another provider account in the same Boomi account using the same aws account and different region
        // it should reuse the AWS credentials obtained during onboarding the first provider
        clearSpyInvocations(awsCredentialsWithTempAccessToken, secretName, "us-west-2");
        expectedCloudFormationTemplate = new String(expectCloudFormation, StandardCharsets.UTF_8);
        expectedCloudFormationTemplate = expectedCloudFormationTemplate.replace("<CUSTOMER_REGION>", "us-west-2");
        Mockito.clearInvocations(spyAiAgentProviderAccountCommonServiceUtil);
        step1QueryInDiffRegion = step1QueryStr.replace(" region: \"us-east-1\"", " region: \"us-west-2\"");
        String responseForDifferentRegistryAccount = executor.executeAtomSphereQuery(step1QueryInDiffRegion, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount providerAccountInDifferentRegion =
                TestUtil.parseGraphqlResponse(responseForDifferentRegistryAccount,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(providerAccountInDifferentRegion, responseForDifferentRegistryAccount);
        assertNotNull(providerAccountInDifferentRegion.getId(), responseForDifferentRegistryAccount);
        assertEquals(customerAwsAccountId, providerAccountInDifferentRegion.getExternalProviderAccountId(), response);
        assertEquals(AiAgentProviderAccountStatus.CONNECTED, providerAccountInDifferentRegion.getProviderAccountStatus());
        verify(spySecretsManagerService).updateSecret(eq(secretName), _argCaptorForSecret.capture());
        verify(spySecretsManagerService, never()).createSecret(anyString(), anyString());
        awsCredentials = _objectMapper.readValue(_argCaptorForSecret.getValue(), AwsCredentials.class);
        assertNotNull(awsCredentials.getExternalId(), _argCaptorForSecret.getValue());
        assertEquals(awsCredentialsWithTempAccessToken.getAwsAccessKeyId(), awsCredentials.getAwsAccessKeyId(),
                _argCaptorForSecret.getValue());
        assertEquals(awsCredentialsWithTempAccessToken.getAwsSecretAccessKey(), awsCredentials.getAwsSecretAccessKey(),
                _argCaptorForSecret.getValue());
        testOamClientInteractions(customerAwsAccountId, "us-west-2", "c28e10c7-3253-4f99-a4cc-773407397c9b");
        verify(spyAiAgentProviderAccountCommonServiceUtil).handleAfterProviderCreation(any(), any());

        // Get the onboarding template for the second provider account
        Mockito.clearInvocations(spyFileStorageService);
        doReturn(Optional.of(new URL("https://example.com"))).when(spyFileStorageService).putSmallObject(any(), any(),
                any());
        getOnboardingTemplateQueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT.getFileName());
        getOnboardingTemplateQueryStr = getOnboardingTemplateQueryStr.replace("ai-agent-provider-account-guid-variable",
                providerAccountInDifferentRegion.getId());
        // Execute the GET Query
        getOnboardingTemplateResponse = executor.executeAtomSphereQuery(getOnboardingTemplateQueryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount providerAccountInDiffRegionTemplate = TestUtil.parseGraphqlResponse(getOnboardingTemplateResponse,
                "aiAgentProviderAccount", com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(providerAccountInDiffRegionTemplate, getOnboardingTemplateResponse);
        AiAgentProviderAccountOnboardingTemplate oamLinkOnboardingTemplate =
                providerAccountInDiffRegionTemplate.getOnboardingTemplate();
        assertNotNull(oamLinkOnboardingTemplate, getOnboardingTemplateResponse);
        assertEquals(awsCredentialsWithTempAccessToken.getExternalId(), oamLinkOnboardingTemplate.getExternalId(),
                getOnboardingTemplateResponse);
        assertEquals("https://example.com/presigned", oamLinkOnboardingTemplate.getTemplateUrl(),
                getOnboardingTemplateResponse);
        assertEquals(HOURS_12, oamLinkOnboardingTemplate.getExpiresIn(), getOnboardingTemplateResponse);
        verify(spyFileStorageService).putSmallObject(eq(s3Bucket), eq(CLOUDFORMATION_S3_KEY_PREFIX + providerAccountInDifferentRegion.getId()),
                _argCaptorForS3File.capture());
        assertEquals(expectedCloudFormationTemplate, new String(_argCaptorForS3File.getValue(), StandardCharsets.UTF_8));

        // do step 2 for the second provider account should not validate connection again
        Mockito.clearInvocations(spySecretsManagerService);
        Mockito.clearInvocations(spyAwsAssumeRoleAuthorizationParserStrategy);
        step2QueryStr = step2QueryStr.replace("ai-agent-provider-account-guid-variable",
                providerAccountInDifferentRegion.getId());
        step2Response = executor.executeAtomSphereQuery(step2QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        aiAgentRegistryAccountAfterStep2 = TestUtil.parseGraphqlResponse(step2Response,
                GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CONFIRM, com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentRegistryAccountAfterStep2, step2Response);
        assertEquals(aiAgentProviderAccount.getId(), aiAgentRegistryAccountAfterStep2.getId(), step2Response);
        // both provider accounts share the same aws account
        assertEquals(aiAgentProviderAccount.getExternalProviderAccountId(), aiAgentRegistryAccountAfterStep2.getExternalProviderAccountId(), step2Response);
        assertEquals(AiAgentProviderAccountStatus.CONNECTED, aiAgentRegistryAccountAfterStep2.getProviderAccountStatus());
        Mockito.verifyNoMoreInteractions(spySecretsManagerService);
        Mockito.verifyNoMoreInteractions(spyAwsAssumeRoleAuthorizationParserStrategy);

        // verify oam link arns are saved in the DB
        List<String> linkArns = _largeTextContentRepository.findAll().stream().map(AiAgentLargeTextContent::getContent)
                .map(content -> {
                    try {
                        return _objectMapper.readValue(content, AwsAssumeRoleProviderAccountMetadataJson.class);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                }).map(AwsAssumeRoleProviderAccountMetadataJson::getOamLinkArn).toList();
        assertEquals(List.of("arn:aws:oam:us-east-1:************:link/ab8a0e33-5f25-4396-b1f8-69a69ccf4d1d",
                "arn:aws:oam:us-west-2:************:link/0227b8ac-2b60-4f31-848d-0ca2202c32b2"), linkArns);

        // deleting provider account 1 should not delete the secret
        doNothing().when(spyFileStorageService).deleteObject(eq(s3Bucket), anyString());
        Mockito.clearInvocations(spySecretsManagerService);
        Mockito.clearInvocations(mockOamClient);
        AiAgentProviderAccount dbProviderAccount = _providerAccountRepository.findByGuid(dbProviderAccountId.getGuid()).orElse(null);
        assertNotNull(dbProviderAccount);
        asyncDeleteProviderAccountAndVerify(dbProviderAccountId.getGuid());
        assertEquals(1, _largeTextContentRepository.findAll().size());
        verify(spySecretsManagerService).getSecret(secretName);
        Mockito.verifyNoMoreInteractions(spySecretsManagerService);
        verify(spyFileStorageService).deleteObject(s3Bucket, CLOUDFORMATION_S3_KEY_PREFIX + dbProviderAccountId.getGuid());
        testDeleteOamLink("us-east-1", "ab8a0e33-5f25-4396-b1f8-69a69ccf4d1d", customerAwsAccountId);
        Assertions.assertFalse(
                _providerAccountRepository.findByGuid(providerAccountInDifferentRegion.getId()).isEmpty());

        // deleting provider account 2 should delete the secret
        Mockito.clearInvocations(spySecretsManagerService);
        Mockito.clearInvocations(mockOamClient);
        asyncDeleteProviderAccountAndVerify(providerAccountInDifferentRegion.getId());
        assertEquals(0, _largeTextContentRepository.findAll().size());
        verify(spySecretsManagerService).getSecret(secretName);
        verify(spySecretsManagerService).deleteSecret(secretName);
        verify(spyFileStorageService).deleteObject(s3Bucket, CLOUDFORMATION_S3_KEY_PREFIX + providerAccountInDifferentRegion.getId());
        testDeleteOamLink("us-west-2", "0227b8ac-2b60-4f31-848d-0ca2202c32b2", customerAwsAccountId);
    }

    private static @NotNull String getSecretName(String customerAwsAccountId) {
        return SECRET_KEY_PATH_AWS + "test/" + customerAwsAccountId;
    }

    private void testDeleteOamLink(String region, String linkId, String awsAccountId) {
        verify(mockOamClient).deleteLink(_deleteLinkRequestArgumentCaptor.capture());
        DeleteLinkRequest.Builder builder = DeleteLinkRequest.builder();
        _deleteLinkRequestArgumentCaptor.getValue().accept(builder);
        DeleteLinkRequest deleteLinkRequest = builder.build();
        assertEquals("arn:aws:oam:" + region + ":" + awsAccountId + ":link/" + linkId,
                deleteLinkRequest.identifier());
    }

    private void clearSpyInvocations(AwsCredentials awsCredentialsWithTempAccessToken, String secretName, String region)
            throws JsonProcessingException {
        Mockito.clearInvocations(spySecretsManagerService);
        Mockito.clearInvocations(spyAwsAssumeRoleAuthorizationParserStrategy);
        Mockito.clearInvocations(mockOamClient);
        _assumeRoleRequestArgCaptor = ArgumentCaptor.forClass(AssumeRoleRequest.class);
        StsClientTestUtil.setup(mockStsClient, _assumeRoleRequestArgCaptor);
        doReturn(Optional.of(awsCredentialsWithTempAccessToken))
            .when(spyAwsAssumeRoleAuthorizationParserStrategy).validateConnection(anyString(), eq(region), eq(
                        awsCredentialsWithTempAccessToken.getExternalId()));
        when(spySecretsManagerService.getSecret(secretName))
            .thenReturn(Flux.just(_objectMapper.writeValueAsString(awsCredentialsWithTempAccessToken)));
    }

    private void testOamClientInteractions(String customerAwsAccountId, String region, String sinkId) {
        verify(mockOamClient).createLink(_createLinkRequestArgumentCaptor.capture());
        assertEquals("arn:aws:oam:" + region + ":" + monitoringAccountId + ":sink/" + sinkId,
                _createLinkRequestArgumentCaptor.getValue().sinkIdentifier());
        assertNotNull(_assumeRoleRequestArgCaptor.getAllValues());
        assertEquals(1, _assumeRoleRequestArgCaptor.getAllValues().size());
        assertEquals("arn:aws:iam::" + customerAwsAccountId + ":role/Boomi-ACT-oam-customer-role-test",
                _assumeRoleRequestArgCaptor.getValue().roleArn());
        verify(mockOamClientHelperService).createOamClient(eq(Region.of(region)), any(AwsSessionCredentials.class));
    }

    @Test
    @DisplayName("Bedrock customers belonging to the same AWS account can be onboarded with assume role and by"
            + " using any combination of Step1 (Create Draft). and Step 2 (Confirm)")
    void testAiAgentProviderAccountsForBedrockAnyCombinationOfSteps() throws Exception {
        deleteAllProviderAccounts();
        StsClientTestUtil.setup(mockStsClient, _assumeRoleRequestArgCaptor);
        String customerAwsAccountId = "************";
        when(spyAuthorizationParsingService.isDoCheckIfExternalAccountLinkedToAnotherProviderAccount(AiProviderAuthSchema.AWS_ASSUME_ROLE))
                .thenReturn(true);
        doReturn(true).when(spyFeatureManager).isDoProvisioningWithAssumeRole();
        doReturn(true).when(spyFeatureManager).isAutoCreateOamLink();

        // create a Draft provider account in one aws account and us-east-1
        String step1QueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BEDROCK_ASSUME_ROLE_SUCCESS.getFileName());
        assertNotNull(step1QueryStr);
        final String response = executor.executeAtomSphereQuery(step1QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                        CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                        CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentProviderAccount =
                TestUtil.parseGraphqlResponse(response, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentProviderAccount, response);
        assertNotNull(aiAgentProviderAccount.getId(), response);
        assertEquals(customerAwsAccountId, aiAgentProviderAccount.getExternalProviderAccountId(), response);
        assertEquals(AiAgentProviderAccountStatus.PROVISIONING_STEP1_COMPLETE, aiAgentProviderAccount.getProviderAccountStatus());
        String secretName = getSecretName(customerAwsAccountId);
        verify(spySecretsManagerService).createSecret(eq(secretName), _argCaptorForSecret.capture());
        Mockito.verifyNoMoreInteractions(spySecretsManagerService);
        AwsCredentials awsCredentials = _objectMapper.readValue(_argCaptorForSecret.getValue(), AwsCredentials.class);
        assertNotNull(awsCredentials.getExternalId(), _argCaptorForSecret.getValue());
        String externalId = awsCredentials.getExternalId();
        assertEquals("us-east-1", awsCredentials.getAwsRegion(), _argCaptorForSecret.getValue());
        assertEquals(customerAwsAccountId, awsCredentials.getAwsAccountId(), _argCaptorForSecret.getValue());

        secretName = "legacy-secret-path";
        List<AiAgentProviderAccount> dbProviderAccounts = _providerAccountRepository.findAll(
                 hasExternalId(customerAwsAccountId));
        assertEquals(1, dbProviderAccounts.size());
        dbProviderAccounts.get(0).setCredentialsKey(secretName);
        _providerAccountRepository.saveAndFlush(dbProviderAccounts.get(0));

        AwsCredentials awsCredentialsWithTempAccessToken = _bedrockTestUtil.getAwsCredentials();
        awsCredentialsWithTempAccessToken.setExternalId(externalId);
        // create another provider account in the same Boomi account and using the same aws account and us-west-2
        clearSpyInvocations(awsCredentialsWithTempAccessToken, secretName, "us-west-2");
        String step1QueryInDiffRegion = step1QueryStr.replace(" region: \"us-east-1\"", " region: \"us-west-2\"");
        String responseForDifferentRegistryAccount = executor.executeAtomSphereQuery(step1QueryInDiffRegion, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount providerAccountInDifferentRegion =
                TestUtil.parseGraphqlResponse(responseForDifferentRegistryAccount, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(providerAccountInDifferentRegion, responseForDifferentRegistryAccount);
        assertNotNull(providerAccountInDifferentRegion.getId(), responseForDifferentRegistryAccount);
        assertEquals(customerAwsAccountId, providerAccountInDifferentRegion.getExternalProviderAccountId(), response);
        assertEquals(AiAgentProviderAccountStatus.PROVISIONING_STEP1_COMPLETE, providerAccountInDifferentRegion.getProviderAccountStatus());
        Mockito.verifyNoMoreInteractions(spySecretsManagerService);

        // Complete step 2 for the second provider account
        String step2QueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                        AI_AGENT_PROVIDER_ACCOUNT_STEP2_FOR_BEDROCK.getFileName());
        assertNotNull(step1QueryStr);
        step2QueryStr = step2QueryStr
            .replace("ai-agent-provider-account-guid-variable", providerAccountInDifferentRegion.getId());
        Flux<String> credsJsonWithExternalId = Flux.just(_bedrockTestUtil.getAwsCredentialsWithJustExternalId());
        clearSpyInvocations(awsCredentialsWithTempAccessToken, secretName, "us-west-2");
        String step2Response = executor.executeAtomSphereQuery(step2QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentRegistryAccountAfterStep2 =
                TestUtil.parseGraphqlResponse(step2Response, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CONFIRM,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentRegistryAccountAfterStep2, step2Response);
        assertNotNull(aiAgentRegistryAccountAfterStep2.getId(), step2Response);
        assertEquals(aiAgentProviderAccount.getExternalProviderAccountId(), aiAgentRegistryAccountAfterStep2.getExternalProviderAccountId(), step2Response);
        assertEquals(AiAgentProviderAccountStatus.CONNECTED, aiAgentRegistryAccountAfterStep2.getProviderAccountStatus());
        verify(spySecretsManagerService).getSecret(eq(secretName));
        verify(spySecretsManagerService).updateSecret(eq(secretName), _argCaptorForSecret.capture());
        Mockito.verifyNoMoreInteractions(spySecretsManagerService);
        awsCredentials = _objectMapper.readValue(_argCaptorForSecret.getValue(), AwsCredentials.class);
        assertEquals(externalId, awsCredentials.getExternalId());
        assertEquals(awsCredentialsWithTempAccessToken.getAwsAccessKeyId(), awsCredentials.getAwsAccessKeyId(), _argCaptorForSecret.getValue());
        assertEquals(awsCredentialsWithTempAccessToken.getAwsSecretAccessKey(), awsCredentials.getAwsSecretAccessKey(), _argCaptorForSecret.getValue());
        // verify source account was added to OAM sink policy, OAM link was created and the
        // source account was removed from the OAM sink policy
        testOamClientInteractions(customerAwsAccountId, "us-west-2", "c28e10c7-3253-4f99-a4cc-773407397c9b");

        // Complete step 2 for the first provider account - should do the  OAM operation
        // because the OAM is at region level
         clearSpyInvocations(awsCredentialsWithTempAccessToken, secretName, "us-east-1");
         step2QueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                         AI_AGENT_PROVIDER_ACCOUNT_STEP2_FOR_BEDROCK.getFileName());
         step2QueryStr = step2QueryStr
             .replace("ai-agent-provider-account-guid-variable", aiAgentProviderAccount.getId());
         step2Response = executor.executeAtomSphereQuery(step2QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                 CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                 CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
         aiAgentRegistryAccountAfterStep2 =
                 TestUtil.parseGraphqlResponse(step2Response, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CONFIRM,
                         com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
         assertNotNull(aiAgentRegistryAccountAfterStep2, step2Response);
         assertNotNull(aiAgentRegistryAccountAfterStep2.getId(), step2Response);
         assertEquals(aiAgentProviderAccount.getExternalProviderAccountId(), aiAgentRegistryAccountAfterStep2.getExternalProviderAccountId(), step2Response);
         assertEquals(AiAgentProviderAccountStatus.CONNECTED, aiAgentRegistryAccountAfterStep2.getProviderAccountStatus());
         verify(spySecretsManagerService).getSecret(eq(secretName));
         verify(spySecretsManagerService).updateSecret(eq(secretName), _argCaptorForSecret.capture());
         Mockito.verifyNoMoreInteractions(spySecretsManagerService);
         awsCredentials = _objectMapper.readValue(_argCaptorForSecret.getValue(), AwsCredentials.class);
         assertEquals(externalId, awsCredentials.getExternalId());
         assertEquals(awsCredentialsWithTempAccessToken.getAwsAccessKeyId(), awsCredentials.getAwsAccessKeyId(), _argCaptorForSecret.getValue());
         assertEquals(awsCredentialsWithTempAccessToken.getAwsSecretAccessKey(), awsCredentials.getAwsSecretAccessKey(), _argCaptorForSecret.getValue());
         // verify source account was added to OAM sink policy, OAM link was created and the
         // source account was removed from the OAM sink policy
         testOamClientInteractions(customerAwsAccountId, "us-east-1", "b1d64f36-6af9-4899-b478-8d5b43f8caec");
    }

    @Test
    @Transactional
    @DisplayName("Should successfully execute mutation to sync DISABLED AI agent provider account")
    void testAiAgentProviderAccountSync_DISABLED() throws Exception {
        final AiAgentProviderAccount aiAgentProviderAccount = testUtil.saveAiAgentRegistryAccount("test-boomi",
                TestUtil.COMMON_ACCOUNT_ID,
                PROVIDER_ACCOUNT_METADATA, PROVIDER_ACCOUNT_CREDENTIALS, AiAgentProviderType.BOOMI,
                AiProviderAuthSchema.APITOKEN, AiAgentProviderAccountStatus.DISABLED);
        String providerAccountGuid = aiAgentProviderAccount.getGuid();
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_SYNC.getFileName());
        assertNotNull(queryStr);
        String providerAccountSyncQueryString = queryStr
                .replace("ai-agent-provider-account-guid-variable", providerAccountGuid);
        assertNotNull(providerAccountSyncQueryString);
        final String response = executor.executeAtomSphereQuery(providerAccountSyncQueryString,
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        assertNotNull(response);
        assertEquals("{\"errors\":[{"
                + "\"message\":\"Agent sync failed. Syncing has been deactivated for the supplied provider"
                + " account ID "+ providerAccountGuid +" .\",\"path\":[\"aiAgentProviderAccountSync\"],\"extensions\":{"
                + "\"errorCode\":\"PROVIDER_ACCOUNT_SYNC_DISABLED\",\"parameters\":[\"" + providerAccountGuid
                + "\"],\"language\":\"" + getLanguageTag() + "\",\"classification\":\"DataFetchingException\""
                + "}}],\"data\":{\"aiAgentProviderAccountSync\":\"FAILED\"}}",response);
        assertTrue(_providerAccountRepository.findByGuid(providerAccountGuid).isPresent());
    }

    @Test
    @DisplayName("It should not add monitoring aws account Id to OAM sink policy")
    void testItShouldNotAddMonitoringAccountToSinkPolicy() throws Exception {
        deleteAllProviderAccounts();
        when(spyAuthorizationParsingService.isDoCheckIfExternalAccountLinkedToAnotherProviderAccount(AiProviderAuthSchema.AWS_ASSUME_ROLE))
                .thenReturn(true);
        doReturn(true).when(spyFeatureManager).isDoProvisioningWithAssumeRole();
        String customerAwsAccountId = monitoringAccountId;

        String step1QueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BEDROCK_ASSUME_ROLE_SUCCESS.getFileName());
        assertNotNull(step1QueryStr);
        step1QueryStr = step1QueryStr.replace("externalAccountId: \"************\"", "externalAccountId: \"" + monitoringAccountId +  "\"");
        final String response = executor.executeAtomSphereQuery(step1QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                        CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                        CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentProviderAccount =
                TestUtil.parseGraphqlResponse(response, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(aiAgentProviderAccount, response);
        assertNotNull(aiAgentProviderAccount.getId(), response);
        assertEquals(customerAwsAccountId, aiAgentProviderAccount.getExternalProviderAccountId(), response);
        assertEquals(AiAgentProviderAccountStatus.PROVISIONING_STEP1_COMPLETE, aiAgentProviderAccount.getProviderAccountStatus());

        String secretName = getSecretName(customerAwsAccountId);
        Optional<AiAgentProviderAccount> optionalDbProviderAccountId = _providerAccountRepository.findByGuid(
                aiAgentProviderAccount.getId());
        assertTrue(optionalDbProviderAccountId.isPresent());
        AiAgentProviderAccount dbProviderAccountId = optionalDbProviderAccountId.get();
        assertEquals("us-east-1", dbProviderAccountId.getRegion());
        assertEquals(customerAwsAccountId, dbProviderAccountId.getExternalProviderAccountId());
        assertEquals(secretName, dbProviderAccountId.getCredentialsKey());
        assertEquals(AiAgentProviderAccountStatus.PROVISIONING_STEP1_COMPLETE, dbProviderAccountId.getProviderAccountStatus());
        validateAuditFields(dbProviderAccountId);
        verify(spySecretsManagerService).createSecret(eq(secretName), _argCaptorForSecret.capture());
        Mockito.verifyNoInteractions(mockOamClient);
     }

    private void validateErrorResponseInStep1(String step1QueryStr, String searchString, String replaceString, String errorCode) throws IOException {
        if (searchString != null) {
            step1QueryStr = step1QueryStr.replace(searchString, replaceString);
        }
        final String errorResponse = executor.executeAtomSphereQuery(step1QueryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentRegistryAccount =
                TestUtil.parseGraphqlResponse(errorResponse, GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(aiAgentRegistryAccount, errorResponse);
        assertTrue(errorResponse.contains("\"errorCode\":\"" + errorCode + "\""), errorResponse);
    }

    private Specification<AiAgentProviderAccount> hasExternalId(String inputExternalId) {
        return (entityRoot, query, providerType) -> {
            if (inputExternalId == null) {
                return null;
            }
            return providerType.equal(entityRoot.get("externalProviderAccountId"), inputExternalId);
        };
    }

    private Specification<AiAgentLargeTextContent> hasLargeTextRelatedEntityUidAndType(
            int relatedEntityUid) {

        return (root, query, cb) ->
            cb.and(
                cb.equal(root.get("relatedEntityUid"), relatedEntityUid),
                cb.equal(root.get("relatedEntityType"), AiRegistryEntityType.PROVIDER_ACCOUNT.name())
            );
    }

}
