// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.resolver.it.query;

import jakarta.transaction.Transactional;
import software.amazon.awssdk.services.timestreamquery.model.ColumnInfo;
import software.amazon.awssdk.services.timestreamquery.model.Datum;
import software.amazon.awssdk.services.timestreamquery.model.QueryRequest;
import software.amazon.awssdk.services.timestreamquery.model.QueryResponse;
import software.amazon.awssdk.services.timestreamquery.model.Row;
import software.amazon.awssdk.services.timestreamquery.model.ScalarType;
import software.amazon.awssdk.services.timestreamquery.model.Type;
import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentAlias;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.util.AiAgentTestBuilder;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphQLQueriesEnum;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.graphql.server.schema.types.AiAgentListing;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.boomi.aiagentregistry.util.TestUtil.COMMON_EXTERNAL_ID;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_CREDENTIALS;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_METADATA;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
class AiAgentRegistryMetricsQueryResolverIntegrationTest extends BaseMockWebServerTest {

    @Autowired
    private TestWebFluxGraphQLExecutor _executor;

    @Autowired
    private TestUtil _testUtil;

    private String aiAgentAliasOneId;
    private String aiAgentAliasTwoId;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final String BEDROCK_AGENT_ID = GuidUtil.createAIAgentGuid();
    private static final String BOOMI_AGENT_ID_1 = GuidUtil.createAIAgentGuid();
    private static final String BOOMI_AGENT_ID_2 = GuidUtil.createAIAgentGuid();
    private static final String VERSION_GUID_1 = GuidUtil.createAIAgentGuid();
    private static final String VERSION_GUID_2 = GuidUtil.createAIAgentGuid();
    private static final String AI_AGENT_REGISTRY_GRAPH_METRICS = "aiAgentRegistryGraphMetrics";
    private static final String AI_AGENT_REGISTRY_AGGREGATE_METRICS = "aiAgentRegistryAggregateMetrics";
    private static final String AI_AGENT_REGISTRY_INVOCATIONS_METRICS = "aiAgentRegistryInvocationsMetrics";
    private static final String AGENT_EXTERNAL_ID_1 = "ZCXHFK3RYV";
    private static final String AGENT_EXTERNAL_ID_2 = "4DSTT3LXLQ";
    private static final String ALIAS_EXTERNAL_ID_1 = "BIVEZPARWQ";
    private static final String ALIAS_EXTERNAL_ID_2 = "O1TQYITQS15";
    private static final String VERSION_EXTERNAL_ID = "version-external-id";
    private static final String AGENT_EXTERNAL_ID = "test-external-id";
    private static final String AGENT_EXTERNAL_ID_FIELD = "agentExternalId";
    private static final String ALIAS_EXTERNAL_ID_FIELD = "aliasExternalId";
    private static final String PROVIDER_ACCOUNT_GUID_FIELD = "providerAccountGuId";
    private static final String PROVIDER_TYPE_FIELD = "providerType";
    private static final String PROVIDER_NAME = "boomi-test-b";
    private static final String TOTAL_INVOCATIONS_FIELD = "totalInvocations";
    private static final String AVG_TIME_FIELD = "averageTime";
    private static final String AI_AGENT_LISTING_FIELD = "aiAgentListing";

    // This setup helps in getting the testing port that changes dynamically
    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry r) throws IOException {
        r.add("api.boomi.platform.url", () -> "http://localhost:" + _mockWebServer.getPort());
    }

    @Test
    @Transactional
    @DisplayName("Should successfully execute query to fetch  AI agent registry metrics for graph")
    void testAiAgentGraphMetrics() throws Exception {

        when(_mockTimestreamQueryAsyncClient.query(any(QueryRequest.class))).thenReturn(
                generateTimestreamResponseForGraphs());

        // Create test data for entity
        AiAgentProviderAccount providerAccount1 = _testUtil.createAiAgentProvider();

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_GRAPH_METRICS.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("provider_account_id_variable", providerAccount1.getGuid());

        final String response = _executor.executeAtomSphereQuery(queryStr, TestUtil.ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        List<Map<String, Object>> aiAgentRegistryGraphMetricsList = TestUtil.parseGraphqlResponse(response,
                AI_AGENT_REGISTRY_GRAPH_METRICS, List.class);
        assertNotNull(aiAgentRegistryGraphMetricsList, response);
        assertEquals(2, aiAgentRegistryGraphMetricsList.size(), response);
        Map<String, Object> actualMap = aiAgentRegistryGraphMetricsList.get(0);

        assertEquals("2024-01-15T10:00:00.123Z", actualMap.get("bucketTs"));
        assertEquals(200.0, actualMap.get("totalInvocations"));
        assertEquals(1000.0, actualMap.get("totalModelInvocations"));
        assertEquals(Double.valueOf("250.5"), actualMap.get("averageTimePerInvocation"));
        assertEquals(50000.0, actualMap.get("totalTokens"));
        assertEquals(20000.0, actualMap.get("totalInputTokens"));
        assertEquals(30000.0, actualMap.get("totalOutputTokens"));
        assertEquals(Double.valueOf("20.0"), actualMap.get("avgInputTokens"));
        assertEquals(Double.valueOf("30.0"), actualMap.get("avgOutputTokens"));
        assertEquals(5.0, actualMap.get("totalInvocationServerErrors"));
        assertEquals(3.0, actualMap.get("totalInvocationClientErrors"));
        assertEquals(2.0, actualMap.get("totalInvocationThrottles"));
        assertEquals(3.0, actualMap.get("totalInvocationClientErrors"));
    }

    @Test
    @Transactional
    @DisplayName("Should successfully execute query to fetch  AI agent registry metrics for aggregates")
    void testAiAgentAggregateMetrics() throws Exception {

        when(_mockTimestreamQueryAsyncClient.query(any(QueryRequest.class))).thenReturn(
                generateTimestreamResponseForAggregates());

        // Create test data for entity
        _testUtil.createAiAgentProvider();
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_AGGREGATE_METRICS.getFileName());
        assertNotNull(queryStr);

        final String response = _executor.executeAtomSphereQuery(queryStr, TestUtil.ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        Map<String, Object> actualMap = TestUtil.parseGraphqlResponse(response, AI_AGENT_REGISTRY_AGGREGATE_METRICS,
                Map.class);
        assertNotNull(actualMap, response);

        // Active agents validation
        assertEquals(1000, actualMap.get("activeAgents"));

        // Total tokens validation
        assertEquals(75000.0, actualMap.get("totalTokens"));

        // Average total time validation
        assertEquals(245.67d, actualMap.get("avgResponseTime"));

        // Total errors validation
        assertEquals(15.0, actualMap.get("totalErrors"));

        // Total invocations validation
        assertEquals(1000.0, actualMap.get("totalInvocations"));

        // Average model latency validation
        assertEquals(180.25d, actualMap.get("avgModelLatency"));

        // Average invocation throttles validation
        assertEquals(0.05d, actualMap.get("avgInvocationThrottles"));

        // Success rate validation
        assertEquals(98.50d, actualMap.get("successRate"));

        // Error rate validation
        assertEquals(1.50d, actualMap.get("errorRate"));
    }

    @Test
    @Disabled("It will be enabled once AiAgentListingRepository sync integration testing is implemented in TestUtil.")
    @Transactional
    @DisplayName("Should successfully execute query to fetch AI agent registry invocations metrics for provider accounts with only external alias ids")
    void testAiAgentInvocationsMetricsForAccountsWithExternalAliasIds() throws Exception {

        AiAgentProviderAccount bedrockProviderAccount = setupProviderAccountWithExternalAliasIds();
        when(_mockTimestreamQueryAsyncClient.query(any(QueryRequest.class))).thenReturn(
                generateTimestreamResponseForInvocations(bedrockProviderAccount.getGuid(), null));

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_INVOCATIONS_METRICS.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("providerAccountsVariable", bedrockProviderAccount.getGuid());
        queryStr = queryStr.replace("providerTypesVariable", "AWS_BEDROCK");

        final String response = _executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        List<Map<String, Object>> metricsList = TestUtil.parseGraphqlResponse(response,
                AI_AGENT_REGISTRY_INVOCATIONS_METRICS, List.class);
        assertNotNull(metricsList, response);
        assertEquals(2, metricsList.size(), response);

        verifyExtAliasMetric(metricsList.get(0), ALIAS_EXTERNAL_ID_1, 100, 14.33, aiAgentAliasOneId, "agent-alias-1",
                "1", VERSION_GUID_1, "test-ai-agent-version-1");

        verifyExtAliasMetric(metricsList.get(1), ALIAS_EXTERNAL_ID_2, 80, 11.11, aiAgentAliasTwoId, "agent-alias-2", "2", VERSION_GUID_2, "test-ai-agent-version-2");
    }

    @Test
    @Transactional
    @Disabled("It will be enabled once AiAgentListingRepository sync integration testing is implemented in TestUtil.")
    @DisplayName("Should successfully execute query to fetch AI agent registry invocations metrics for provider accounts with external agent ids and no external alias ids")
    void testAiAgentInvocationsMetricsForAccountsWithExternalAgentIds() throws Exception {

        AiAgentProviderAccount boomiAccount = setupProviderAccountWithExternalAgentIds();
        when(_mockTimestreamQueryAsyncClient.query(any(QueryRequest.class))).thenReturn(
                generateTimestreamResponseForInvocations(null, boomiAccount.getGuid()));

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_INVOCATIONS_METRICS.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("providerAccountsVariable", boomiAccount.getGuid());
        queryStr = queryStr.replace("providerTypesVariable", "BOOMI");

        final String response = _executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        List<Map<String, Object>> metricsList = TestUtil.parseGraphqlResponse(response,
                AI_AGENT_REGISTRY_INVOCATIONS_METRICS, List.class);
        assertNotNull(metricsList, response);
        assertEquals(2, metricsList.size(), response);

        verifyExtAgentMetrics(metricsList.get(0), AGENT_EXTERNAL_ID_1, 100.0, 14.33);
        verifyExtAgentMetrics(metricsList.get(1), AGENT_EXTERNAL_ID_2, 80.0, 11.11);
    }

    @Test
    @Transactional
    @Disabled("It will be enabled once AiAgentListingRepository sync integration testing is implemented in TestUtil.")
    @DisplayName("Should successfully execute query to fetch AI agent registry invocations metrics for provider accounts with both external alias & external agent ids")
    void testAiAgentInvocationsMetricsForAccountsWithExtAliasAndExtAgentIds() throws Exception {
        AiAgentProviderAccount accountWithExtAliasIds = setupProviderAccountWithExternalAliasIds();
        AiAgentProviderAccount accountWithOnlyExtAgentIds = setupProviderAccountWithExternalAgentIds();
        when(_mockTimestreamQueryAsyncClient.query(any(QueryRequest.class))).thenReturn(
                generateTimestreamResponseForInvocations(accountWithExtAliasIds.getGuid(), accountWithOnlyExtAgentIds.getGuid()));

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_INVOCATIONS_METRICS.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("providerAccountsVariable", accountWithExtAliasIds.getGuid() + "\", \"" + accountWithOnlyExtAgentIds.getGuid());
        queryStr = queryStr.replace("providerTypesVariable", "AWS_BEDROCK, BOOMI");

        verifyExtAgentAndExtAliasMetrics(queryStr);
    }

    @Test
    @Transactional
    @Disabled("It will be enabled once AiAgentListingRepository sync integration testing is implemented in TestUtil.")
    @DisplayName("Should successfully execute query to fetch AI agent registry invocations metrics for empty provider types & Ids input")
    void testAiAgentInvocationsMetricsForEmptyProviderTypesAndIds() throws Exception {
        AiAgentProviderAccount accountWithExtAliasIds = setupProviderAccountWithExternalAliasIds();
        AiAgentProviderAccount accountWithOnlyExtAgentIds = setupProviderAccountWithExternalAgentIds();
        when(_mockTimestreamQueryAsyncClient.query(any(QueryRequest.class))).thenReturn(
                generateTimestreamResponseForInvocations(accountWithExtAliasIds.getGuid(), accountWithOnlyExtAgentIds.getGuid()));

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_INVOCATIONS_METRICS.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("providerAccountIds: [\"providerAccountsVariable\"]", StringUtils.EMPTY);
        queryStr = queryStr.replace("providerTypes: [providerTypesVariable]", StringUtils.EMPTY);

        verifyExtAgentAndExtAliasMetrics(queryStr);
    }

    public void verifyExtAgentAndExtAliasMetrics(String queryStr) throws Exception {
        final String response = _executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        List<Map<String, Object>> invocationsMetricsList = TestUtil.parseGraphqlResponse(response,
                AI_AGENT_REGISTRY_INVOCATIONS_METRICS, List.class);
        assertNotNull(invocationsMetricsList, response);
        assertEquals(4, invocationsMetricsList.size(), response);

        List<Map<String, Object>> extAliasMetricsList = invocationsMetricsList.subList(0, 2);

        verifyExtAliasMetric(extAliasMetricsList.get(0), ALIAS_EXTERNAL_ID_1, 100, 14.33, aiAgentAliasOneId, "agent-alias-1",
                "1", VERSION_GUID_1, "test-ai-agent-version-1");

        verifyExtAliasMetric(extAliasMetricsList.get(1), ALIAS_EXTERNAL_ID_2, 80, 11.11, aiAgentAliasTwoId, "agent-alias-2",
                "2", VERSION_GUID_2, "test-ai-agent-version-2");

        List<Map<String, Object>> extAgentMetricsList = invocationsMetricsList.subList(2, 4);
        verifyExtAgentMetrics(extAgentMetricsList.get(0), AGENT_EXTERNAL_ID_1, 100, 14.33);
        verifyExtAgentMetrics(extAgentMetricsList.get(1), AGENT_EXTERNAL_ID_2, 80, 11.11);
    }


    private CompletableFuture<QueryResponse> generateTimestreamResponseForGraphs() {
        // Define column information for each metric field
        List<ColumnInfo> columnInfo = Arrays.asList(
                // Timestamp for the metrics bucket
                ColumnInfo.builder().name("bucketTs").type(Type.builder().scalarType(ScalarType.TIMESTAMP).build())
                        .build(),
                // Total number of agent invocations
                ColumnInfo.builder().name("totalInvocations")
                    .type(Type.builder().scalarType(ScalarType.BIGINT).build()).build(),
                // Total number of model invocations
                ColumnInfo.builder().name("totalModelInvocations")
                        .type(Type.builder().scalarType(ScalarType.BIGINT).build()).build(),
                // Average time taken per model invocation
                ColumnInfo.builder().name("averageTimePerInvocation")
                        .type(Type.builder().scalarType(ScalarType.DOUBLE).build()).build(),
                // Total tokens (input + output) used
                ColumnInfo.builder().name("totalTokens").type(Type.builder().scalarType(ScalarType.BIGINT).build())
                        .build(),
                // Total input tokens consumed
                ColumnInfo.builder().name("totalInputTokens").type(Type.builder().scalarType(ScalarType.BIGINT).build())
                        .build(),
                // Total output tokens generated
                ColumnInfo.builder().name("totalOutputTokens")
                        .type(Type.builder().scalarType(ScalarType.BIGINT).build()).build(),
                // Average input tokens per request
                ColumnInfo.builder().name("avgInputTokens").type(Type.builder().scalarType(ScalarType.DOUBLE).build())
                        .build(),
                // Average output tokens per request
                ColumnInfo.builder().name("avgOutputTokens").type(Type.builder().scalarType(ScalarType.DOUBLE).build())
                        .build(),
                // Count of server-side errors
                ColumnInfo.builder().name("totalInvocationServerErrors")
                        .type(Type.builder().scalarType(ScalarType.BIGINT).build()).build(),
                // Count of client-side errors
                ColumnInfo.builder().name("totalInvocationClientErrors")
                        .type(Type.builder().scalarType(ScalarType.BIGINT).build()).build(),
                // Count of throttling events
                ColumnInfo.builder().name("totalInvocationThrottles")
                        .type(Type.builder().scalarType(ScalarType.BIGINT).build()).build());

        // Create sample data rows with metrics
        List<Row> rows = Arrays.asList(
                // First metrics row - 10:00 AM timeframe
                Row.builder().data(Arrays.asList(
                        // Metrics for 10 AM bucket
                        Datum.builder().scalarValue("2024-01-15 10:00:00.123456789").build(),
                        // 200 total agent invocations
                        Datum.builder().scalarValue("200.0").build(),
                        // 1000 total model invocations
                        Datum.builder().scalarValue("1000.0").build(),
                        // 250.5ms average response time
                        Datum.builder().scalarValue("250.5").build(),
                        // 50000 total tokens processed
                        Datum.builder().scalarValue("50000.0").build(),
                        // 20000 input tokens
                        Datum.builder().scalarValue("20000.0").build(),
                        // 30000 output tokens
                        Datum.builder().scalarValue("30000.0").build(),
                        // Average of 20 input tokens per request
                        Datum.builder().scalarValue("20.0").build(),
                        // Average of 30 output tokens per request
                        Datum.builder().scalarValue("30.0").build(),
                        // server errors
                        Datum.builder().scalarValue("5.0").build(),
                        // client errors
                        Datum.builder().scalarValue("3.0").build(),
                        //throttling events
                        Datum.builder().scalarValue("2.0").build())).build(),
                // Second metrics row - 11:00 AM timeframe
                Row.builder().data(Arrays.asList(
                        // Metrics for 11 AM bucket
                        Datum.builder().scalarValue("2024-01-15 11:00:00.123456789").build(),
                        // 300 total agent invocations
                        Datum.builder().scalarValue("300").build(),
                        // 1500 total model invocations
                        Datum.builder().scalarValue("1500").build(),
                        // 275.3ms average response time
                        Datum.builder().scalarValue("275.3").build(),
                        // 75000 total tokens processed
                        Datum.builder().scalarValue("75000").build(),
                        // 35000 input tokens
                        Datum.builder().scalarValue("35000").build(),
                        // 40000 output tokens
                        Datum.builder().scalarValue("40000").build(),
                        // Average of 23.33 input tokens per request
                        Datum.builder().scalarValue("23.33").build(),
                        // Average of 26.67 output tokens per request
                        Datum.builder().scalarValue("26.67").build(),
                        // 8 server errors
                        Datum.builder().scalarValue("8").build(),
                        // 4 client errors
                        Datum.builder().scalarValue("4").build(),
                        // 3 throttling events
                        Datum.builder().scalarValue("3").build())).build());

        // Build the final QueryResponse with column info and data rows
        return CompletableFuture.completedFuture(QueryResponse.builder().columnInfo(columnInfo).rows(rows).build());
    }

    private CompletableFuture<QueryResponse> generateTimestreamResponseForAggregates() {
        // Column definitions
        List<ColumnInfo> columnInfo = Arrays.asList(
                ColumnInfo.builder().name("activeAgents")
                                        .type(Type.builder().scalarType(ScalarType.BIGINT).build()).build(),

                ColumnInfo.builder().name("totalTokens")
                        .type(Type.builder().scalarType(ScalarType.BIGINT).build()).build(),

                ColumnInfo.builder().name("avgResponseTime").type(Type.builder().scalarType(ScalarType.DOUBLE).build())
                        .build(),

                ColumnInfo.builder().name("totalErrors").type(Type.builder().scalarType(ScalarType.BIGINT).build())
                        .build(),

                ColumnInfo.builder().name("totalInvocations").type(Type.builder().scalarType(ScalarType.BIGINT).build())
                        .build(),

                ColumnInfo.builder().name("avgModelLatency").type(Type.builder().scalarType(ScalarType.DOUBLE).build())
                        .build(),

                ColumnInfo.builder().name("avgInvocationThrottles")
                        .type(Type.builder().scalarType(ScalarType.DOUBLE).build()).build(),

                ColumnInfo.builder().name("successRate").type(Type.builder().scalarType(ScalarType.DOUBLE).build())
                        .build(),

                ColumnInfo.builder().name("errorRate").type(Type.builder().scalarType(ScalarType.DOUBLE).build())
                        .build());

        // Mock data rows
        Row row = Row.builder().data(Arrays.asList(

                // activeAgents
                Datum.builder().scalarValue("1000").build(),

                // totalTokens (SUM(InputTokenCount) + SUM(OutputTokenCount))
                Datum.builder().scalarValue("75000").build(),

                // avgResponseTime (ROUND(AVG(TotalTime), 2))
                Datum.builder().scalarValue("245.67").build(),

                // totalErrors (SUM(InvocationServerErrors) + SUM(InvocationClientErrors))
                Datum.builder().scalarValue("15").build(),

                // totalInvocations (COUNT(*))
                Datum.builder().scalarValue("1000").build(),

                // avgModelLatency (ROUND(AVG(ModelLatency), 2))
                Datum.builder().scalarValue("180.25").build(),

                // avgInvocationThrottles (ROUND(AVG(CAST(InvocationThrottles AS DOUBLE)), 2))
                Datum.builder().scalarValue("0.05").build(),

                // successRate (percentage of successful invocations)
                Datum.builder().scalarValue("98.50").build(),

                // errorRate (percentage of failed invocations)
                Datum.builder().scalarValue("1.50").build())).build();

        // Build the final QueryResponse with column info and data rows
        return CompletableFuture.completedFuture(QueryResponse.builder().columnInfo(columnInfo).rows(row).build());
    }

    private CompletableFuture<QueryResponse> generateTimestreamResponseForInvocations(String bedrockAccountId,
            String boomiAccountId) {
        // Define column information for each metric field
        List<ColumnInfo> columnInfo = Arrays.asList(
                // Unique ID by which the timestream AWS_BEDROCK records are grouped by
                ColumnInfo.builder().name(ALIAS_EXTERNAL_ID_FIELD).type(Type.builder().scalarType(ScalarType.VARCHAR).build())
                        .build(),
                // Unique ID by which the timestream BOOMI records are grouped by
                ColumnInfo.builder().name(AGENT_EXTERNAL_ID_FIELD).type(Type.builder().scalarType(ScalarType.VARCHAR).build())
                        .build(),
                // Provider Account UUID of each record
                ColumnInfo.builder().name(PROVIDER_ACCOUNT_GUID_FIELD).type(Type.builder().scalarType(ScalarType.VARCHAR).build())
                        .build(),
                // Provider Account Type of each record
                ColumnInfo.builder().name(PROVIDER_TYPE_FIELD).type(Type.builder().scalarType(ScalarType.VARCHAR).build())
                        .build(),
                // Total number of agent invocations
                ColumnInfo.builder().name(TOTAL_INVOCATIONS_FIELD)
                        .type(Type.builder().scalarType(ScalarType.BIGINT).build()).build(),
                // Average time for agent invocations
                ColumnInfo.builder().name(AVG_TIME_FIELD)
                        .type(Type.builder().scalarType(ScalarType.DOUBLE).build()).build());

        // Create sample data rows with metrics
        List<Row> rows;
        List<Row> bedRockRecords = Collections.emptyList();
        List<Row> boomiRecords = Collections.emptyList();
        if (bedrockAccountId != null) {
            bedRockRecords = getRows(bedrockAccountId, "AWS_BEDROCK", ALIAS_EXTERNAL_ID_1, ALIAS_EXTERNAL_ID_2, COMMON_EXTERNAL_ID, COMMON_EXTERNAL_ID);
        }
        if (boomiAccountId != null) {
            boomiRecords = getRows(boomiAccountId, "BOOMI", null, null, AGENT_EXTERNAL_ID_1, AGENT_EXTERNAL_ID_2);
        }

        rows =  Stream.concat(bedRockRecords.stream(), boomiRecords.stream())
                .collect(Collectors.toList());

        // Build the final QueryResponse with column info and data rows
        return CompletableFuture.completedFuture(QueryResponse.builder().columnInfo(columnInfo).rows(rows).build());
    }

    private List<Row> getRows(String providerAccountId, String providerType, String aliasId1, String aliasId2, String agentId1, String agentId2) {
        return Arrays.asList(
                // First metrics row - 10:00 AM timeframe
                Row.builder().data(Arrays.asList(
                        // AgentAliasExternalId of the record
                        Datum.builder().scalarValue(aliasId1).build(),
                        // AgentExternalId of the record
                        Datum.builder().scalarValue(agentId1).build(),
                        // Provider account UUID
                        Datum.builder().scalarValue(providerAccountId).build(),
                        // Provider account type
                        Datum.builder().scalarValue(providerType).build(),
                        // 200 total agent invocations
                        Datum.builder().scalarValue("100").build(),
                        // 14.33 average invocation time
                        Datum.builder().scalarValue("14.33").build())).build(),
                // Second metrics row - 11:00 AM timeframe
                Row.builder().data(Arrays.asList(
                        // AgentAliasExternalId of the record
                        Datum.builder().scalarValue(aliasId2).build(),
                        // AgentExternalId of the record
                        Datum.builder().scalarValue(agentId2).build(),
                        // Provider account UUID
                        Datum.builder().scalarValue(providerAccountId).build(),
                        // Provider account type
                        Datum.builder().scalarValue(providerType).build(),
                        // 80 total agent invocations
                        Datum.builder().scalarValue("80").build(),
                        // 11.11 average invocation time
                        Datum.builder().scalarValue("11.11").build())).build());
    }

    private void verifyExtAliasMetric(Map<String, Object> metricsMap, String aliasExternalId,
            int invocations, double avgTime, String aliasId, String aliasName, String version,
            String versionId, String versionName) {

        AiAgentListing metadata = OBJECT_MAPPER.convertValue(metricsMap.get(AI_AGENT_LISTING_FIELD), AiAgentListing.class);

        assertEquals(invocations, metricsMap.get(TOTAL_INVOCATIONS_FIELD));
        assertEquals(avgTime, metricsMap.get(AVG_TIME_FIELD));

        assertEquals(AGENT_EXTERNAL_ID, metadata.getAgentExternalId());
        assertEquals(aliasExternalId, metadata.getAliasExternalId());
        assertEquals(AiAgentProviderType.AWS_BEDROCK, metadata.getProviderType());
        assertEquals(BEDROCK_AGENT_ID, metadata.getAgentId());
        assertEquals(aliasId, metadata.getAliasId());
        assertEquals(aliasName, metadata.getAliasName());
        assertEquals(PROVIDER_NAME, metadata.getProviderAccountName());
        assertEquals(version, metadata.getVersion());
        assertEquals(versionId, metadata.getVersionId());
        assertEquals(versionName, metadata.getVersionName());
        assertEquals(VERSION_EXTERNAL_ID, metadata.getVersionExternalId());
        assertFalse(metadata.isAgentIsDeleted());
    }

    private void verifyExtAgentMetrics(Map<String, Object> metricsMap, String externalId, double invocations, double averageTime) {
        AiAgentListing metadata = OBJECT_MAPPER.convertValue(metricsMap.get(AI_AGENT_LISTING_FIELD), AiAgentListing.class);

        assertEquals(invocations, metricsMap.get(TOTAL_INVOCATIONS_FIELD));
        assertEquals(averageTime, metricsMap.get(AVG_TIME_FIELD));

        assertEquals(externalId, metadata.getAgentExternalId());
        assertNull(metadata.getAliasExternalId());
        assertEquals(AiAgentProviderType.BOOMI, metadata.getProviderType());
        assertNull(metadata.getVersion());
        assertNull(metadata.getVersionId());
        assertNull(metadata.getVersionName());
        assertNull(metadata.getVersionExternalId());
        assertNull(metadata.getAliasId());
        assertNull(metadata.getAliasName());
        assertEquals("boomi-test-c", metadata.getProviderAccountName());
        assertFalse(metadata.isAgentIsDeleted());
    }

    public AiAgentProviderAccount setupProviderAccountWithExternalAliasIds() {
        AiAgentProviderAccount bedrockAccount = _testUtil.saveAiAgentRegistryAccount("boomi-test-b", TestUtil.COMMON_ACCOUNT_ID,
                PROVIDER_ACCOUNT_METADATA,
                PROVIDER_ACCOUNT_CREDENTIALS,
                AiAgentProviderType.AWS_BEDROCK, AiProviderAuthSchema.AWS,
                AiAgentProviderAccountStatus.CONNECTED);

        // create Agent
        AiAgent aiAgent = _testUtil.createAiAgent(bedrockAccount, BEDROCK_AGENT_ID, COMMON_EXTERNAL_ID,
                false);

        // create Agent Versions
        AiAgentVersion versionOne = _testUtil.createAiAgentVersion(aiAgent, VERSION_GUID_1,
                "test-ai-agent-version-1", "1", "RUNNING", "Instructions", false, null);

        AiAgentVersion versionTwo = _testUtil.createAiAgentVersion(aiAgent, VERSION_GUID_2,
                "test-ai-agent-version-2", "2", "RUNNING", "Instructions", true, null);

        // create Agent alias
        AiAgentTestBuilder.AliasSetupParams params = AiAgentTestBuilder.AliasSetupParams.builder()
                .name("agent-alias-1")
                .externalId(ALIAS_EXTERNAL_ID_1)
                .agentVersion("1")
                .isDeleted(false)
                .idpAccountId(aiAgent.getIdpAccountId())
                .providerAccount(aiAgent.getAiAgentProviderAccount())
                .updatedAt(Instant.now())
                .build();

        AiAgentAlias aliasOne = _testUtil.createVersionAlias(versionOne, params);

        params = AiAgentTestBuilder.AliasSetupParams.builder()
                .name("agent-alias-2")
                .externalId(ALIAS_EXTERNAL_ID_2)
                .agentVersion("2")
                .isDeleted(false)
                .idpAccountId(aiAgent.getIdpAccountId())
                .providerAccount(aiAgent.getAiAgentProviderAccount())
                .updatedAt(Instant.now())
                .build();

        AiAgentAlias aliasTwo = _testUtil.createVersionAlias(versionTwo, params);

        aiAgentAliasOneId = aliasOne.getGuid();
        aiAgentAliasTwoId = aliasTwo.getGuid();

        return bedrockAccount;
    }

    public AiAgentProviderAccount setupProviderAccountWithExternalAgentIds() {
        // create providerAccount
        AiAgentProviderAccount boomiAccount = _testUtil.saveAiAgentRegistryAccount("boomi-test-c", TestUtil.COMMON_ACCOUNT_ID,
                PROVIDER_ACCOUNT_METADATA,
                PROVIDER_ACCOUNT_CREDENTIALS,
                AiAgentProviderType.BOOMI, AiProviderAuthSchema.AWS,
                AiAgentProviderAccountStatus.CONNECTED);

        // create Agent for providerAccountTwo
        _testUtil.createAiAgent(boomiAccount, BOOMI_AGENT_ID_1, AGENT_EXTERNAL_ID_1,
                false);
        _testUtil.createAiAgent(boomiAccount, BOOMI_AGENT_ID_2, AGENT_EXTERNAL_ID_2,
                false);

        return boomiAccount;
    }
}
