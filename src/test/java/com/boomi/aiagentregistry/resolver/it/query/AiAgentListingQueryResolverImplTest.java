package com.boomi.aiagentregistry.resolver.it.query;

import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentLlm;
import com.boomi.aiagentregistry.entity.AiAgentLlmAssociation;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTag;
import com.boomi.aiagentregistry.entity.AiAgentTagAssociation;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.repo.AgentListingViewRepository;
import com.boomi.aiagentregistry.util.AiAgentTestBuilder;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphQLQueriesEnum;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentRegistryTrustLevel;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.transaction.Transactional;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.jdbc.SqlConfig;

import java.util.List;

import static com.boomi.aiagentregistry.util.TestUtil.*;
import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@Disabled("TODO: Since approach is changed to get the data. We need to change test class. Reach <EMAIL>")
public class AiAgentListingQueryResolverImplTest extends BaseMockWebServerTest {

    @Autowired
    private TestWebFluxGraphQLExecutor _executor;

    @Autowired
    private TestUtil testUtil;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    AgentListingViewRepository _agentListingViewRepository;

    AiAgentProviderAccount aiAgentProviderAccount;

    AiAgentTag aiAgentTag = null;

    String queryStr;



    @Test
    @Sql(scripts = "/schema.sql")
    @Transactional
    @DisplayName("Should successfully execute query to fetch  AI agent listing filter value when provider account id is not present.")
    void testAiAgentListingFilterValuesWithoutProviderAccId() throws Exception {

        testUtil.cleanupDatabases();
        testUtil.cleanupProviderAccount();

        aiAgentProviderAccount = testUtil.saveAiAgentRegistryAccount("boomi-test-b",
                COMMON_ACCOUNT_ID,
                PROVIDER_ACCOUNT_METADATA, PROVIDER_ACCOUNT_CREDENTIALS, AiAgentProviderType.AWS_BEDROCK,
                AiProviderAuthSchema.AWS, AiAgentProviderAccountStatus.CONNECTED);

        AiAgentLlm aiAgentLlm = testUtil.saveAiAgentLlm(aiAgentProviderAccount);

        AiAgentLlmAssociation aiAgentLlmAssociation = null;

        AiAgent aiAgent = testUtil.createAiAgent(aiAgentProviderAccount, GuidUtil.createAIAgentGuid(),
                COMMON_EXTERNAL_ID,
                false);

        AiAgentVersion aiAgentVersion = testUtil.createAiAgentVersion(aiAgent, GuidUtil.createAIAgentVersionGuid(),
                "test-ai-agent-version", "123", "RUNNING", "Instructions",
                false, null);
        aiAgentVersion.setTrustLevel(AiAgentRegistryTrustLevel.ENDORSED);

        // create Agent Tag
        AiAgentTagAssociation aiAgentTagAssociation = null;

        if (aiAgentTag == null && aiAgentVersion != null) {
            AiAgentTestBuilder.TagParams params = AiAgentTestBuilder.TagParams.builder()
                    .guid(GuidUtil.createAIAgentTagGuid())
                    .providerAccount(aiAgentProviderAccount)
                    .idpAccountId(aiAgentProviderAccount.getIdpAccountId())
                    .key("test-key")
                    .value("test-value")
                    .build();
            aiAgentTag = testUtil.createAiAgentTag(params);
        }

        // create Agent Tag Association
        if (aiAgentTagAssociation == null && aiAgentVersion != null && aiAgentTag != null) {
            AiAgentTestBuilder.TagAssociationParams params = AiAgentTestBuilder.TagAssociationParams.builder()
                    .guid(GuidUtil.createAIAgentTagAssociationGuid())
                    .tagUid(aiAgentTag.getUid())
                    .relatedEntityUid(aiAgentVersion.getUid())
                    .relatedEntityType(AiRegistryEntityType.VERSION)
                    .build();
            aiAgentTagAssociation = testUtil.createAiAgentTagAssociation(params);
        }

        // create LLM associations with aiAgentVersion
        if (aiAgentLlm == null && aiAgentVersion != null) {
            aiAgentLlm = testUtil.saveAiAgentLlm(aiAgentProviderAccount);
        }
        if (aiAgentLlmAssociation == null && aiAgentLlm != null) {
            aiAgentLlmAssociation = testUtil.saveAiAgentLlmAssociation(aiAgentLlm,
                    aiAgentVersion.getUid(),
                    AiRegistryEntityType.VERSION);
        }
        queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LISTING_FILTER_VALUES.getFileName());

        String aiAgentListingQueryString2 = queryStr.replace("providerAccountId: \"provider-account-id\"", "");

        final String response = _executor.executeAtomSphereQuery(aiAgentListingQueryString2, testUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertEquals("{\"data\":{\"aiAgentListingFilterValues\":" +
                "{\"provider\":[\"AWS_BEDROCK\"],\"account\":[" +
                "{\"name\":\"boomi-test-b\",\"guid\":\"" +
                aiAgentProviderAccount.getGuid() +
                "\"}],\"tag\":[{\"name\":\"test-key\",\"guid\":\"" +
                aiAgentTag.getGuid() +
                "\"}],\"model\":[\"agent-llm-name\"],\"trustLevel\":[\"ENDORSED\"]}}}", response);

    }

    @Test
    @Sql(scripts = "/schema.sql")
    @Transactional
    @DisplayName("Should successfully execute query to fetch  AI agent listing filter values when provider account id is present")
    void testAiAgentListingFilterValuesWithProviderAccId() throws Exception {

        testUtil.cleanupDatabases();
        testUtil.cleanupProviderAccount();

        aiAgentProviderAccount = testUtil.saveAiAgentRegistryAccount("boomi-test-b",
                COMMON_ACCOUNT_ID,
                PROVIDER_ACCOUNT_METADATA, PROVIDER_ACCOUNT_CREDENTIALS, AiAgentProviderType.AWS_BEDROCK,
                AiProviderAuthSchema.AWS, AiAgentProviderAccountStatus.CONNECTED);

        AiAgentLlm aiAgentLlm = testUtil.saveAiAgentLlm(aiAgentProviderAccount);

        AiAgentLlmAssociation aiAgentLlmAssociation = null;

        AiAgent aiAgent = testUtil.createAiAgent(aiAgentProviderAccount, GuidUtil.createAIAgentGuid(),
                COMMON_EXTERNAL_ID,
                false);

        AiAgentVersion aiAgentVersion = testUtil.createAiAgentVersion(aiAgent, GuidUtil.createAIAgentVersionGuid(),
                "test-ai-agent-version", "123", "RUNNING", "Instructions",
                false, null);
        aiAgentVersion.setTrustLevel(AiAgentRegistryTrustLevel.ENDORSED);

        // create Agent Tag
        AiAgentTagAssociation aiAgentTagAssociation = null;

        if (aiAgentTag == null && aiAgentVersion != null) {
            AiAgentTestBuilder.TagParams params = AiAgentTestBuilder.TagParams.builder()
                    .guid(GuidUtil.createAIAgentTagGuid())
                    .providerAccount(aiAgentProviderAccount)
                    .idpAccountId(aiAgentProviderAccount.getIdpAccountId())
                    .key("test-key")
                    .value("test-value")
                    .build();
            aiAgentTag = testUtil.createAiAgentTag(params);
        }

        // create Agent Tag Association
        if (aiAgentTagAssociation == null && aiAgentVersion != null && aiAgentTag != null) {
            AiAgentTestBuilder.TagAssociationParams params = AiAgentTestBuilder.TagAssociationParams.builder()
                    .guid(GuidUtil.createAIAgentTagAssociationGuid())
                    .tagUid(aiAgentTag.getUid())
                    .relatedEntityUid(aiAgentVersion.getUid())
                    .relatedEntityType(AiRegistryEntityType.VERSION)
                    .build();
            aiAgentTagAssociation = testUtil.createAiAgentTagAssociation(params);
        }

        // create LLM associations with aiAgentVersion
        if (aiAgentLlm == null && aiAgentVersion != null) {
            aiAgentLlm = testUtil.saveAiAgentLlm(aiAgentProviderAccount);
        }
        if (aiAgentLlmAssociation == null && aiAgentLlm != null) {
            aiAgentLlmAssociation = testUtil.saveAiAgentLlmAssociation(aiAgentLlm,
                    aiAgentVersion.getUid(),
                    AiRegistryEntityType.VERSION);
        }
        //sandesh comment
        //testUtil.createAiAgentListing(aiAgentVersion, aiAgentProviderAccount);

        queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LISTING_FILTER_VALUES.getFileName());

        String aiAgentListingQueryString1 = queryStr.replace("provider-account-id", aiAgentProviderAccount.getGuid());

        final String response = _executor.executeAtomSphereQuery(aiAgentListingQueryString1, testUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertEquals("{\"data\":{\"aiAgentListingFilterValues\":{\"provider\":[\"AWS_BEDROCK\"]," +
                "\"account\":null,\"tag\":[{\"name\":\"test-key\",\"guid\":\"" +
                aiAgentTag.getGuid() +
                "\"}]," +
                "\"model\":[\"agent-llm-name\"],\"trustLevel\":[\"ENDORSED\"]}}}", response);
    }
}
