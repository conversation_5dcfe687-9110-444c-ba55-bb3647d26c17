// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.resolver.it.query;

import jakarta.transaction.Transactional;
import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphQLQueriesEnum;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.graphql.server.schema.types.AiAgentProvidersQueryResponse;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;

import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.junit4.SpringRunner;

import static com.boomi.graphql.server.schema.types.AiAgentProviderType.AWS_BEDROCK;
import static com.boomi.graphql.server.schema.types.AiAgentProviderType.BOOMI;
import static graphql.org.antlr.v4.runtime.atn.LexerActionType.CUSTOM;
import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestApplication.class)
@Configuration
public class AIAgentProvidersQueryResolverIntegrationTest {

    public static final String AI_AGENT_PROVIDERS = "aiAgentProviders";
    @Autowired
    private TestWebFluxGraphQLExecutor _executor;

    @Autowired
    TestUtil _testUtil;

    @Test
    @Transactional
    @DisplayName("Should successfully execute query to fetch  AI agent registry providers")
    public void testAiAgentRegistryProviders() throws Exception {
        // Create test data for entity
        _testUtil.createAiAgentProvider();
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_PROVIDER_PROVIDERS.getFileName());
        assertNotNull(queryStr);

        final String response = _executor.executeAtomSphereQuery(queryStr, TestUtil.ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        AiAgentProvidersQueryResponse aiAgentProvidersQueryResponse = TestUtil.parseGraphqlResponse(response,
                AI_AGENT_PROVIDERS, AiAgentProvidersQueryResponse.class);
        assertNotNull(aiAgentProvidersQueryResponse);
        assertResponse(aiAgentProvidersQueryResponse);
    }

    private void assertResponse(AiAgentProvidersQueryResponse response) {

        response.getAiAgentProviders().forEach(provider -> {
            if (provider.getAiAgentProviderType().equals(AWS_BEDROCK)) {
                assertEquals(0, provider.getNumberOfAccounts().intValue());
                assertEquals(0, provider.getNumberOfAgents().intValue());
            } else if (provider.getAiAgentProviderType().equals(BOOMI)) {
                assertEquals(3, provider.getNumberOfAgents().intValue());
                assertEquals(1, provider.getNumberOfAccounts().intValue());
            } else if (provider.getAiAgentProviderType().equals(CUSTOM)) {
                assertEquals(0, provider.getNumberOfAgents().intValue());
                assertEquals(0, provider.getNumberOfAccounts().intValue());
            }
        });
    }
}
