// Copyright (c) 2025 Boomi, Inc.
package com.boomi.aiagentregistry.resolver.it.query;

import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.entity.AiAgentLlm;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphQLQueriesEnum;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.graphql.server.schema.types.AiAgentLlmsQueryResponse;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.List;

import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_CREDENTIALS;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_METADATA;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class AiAgentLlmQueryResolverImplIntegrationTest extends BaseMockWebServerTest {
    @Autowired
    private TestUtil testUtil;

    @Autowired
    private TestWebFluxGraphQLExecutor executor;

    private static final String AI_AGENT_LLM = "aiAgentLlms";

    public AiAgentProviderAccount createAccount() {
        return testUtil.saveAiAgentRegistryAccount("boomi-test-b", TestUtil.COMMON_ACCOUNT_ID,
                PROVIDER_ACCOUNT_METADATA, PROVIDER_ACCOUNT_CREDENTIALS, AiAgentProviderType.AWS_BEDROCK,
                AiProviderAuthSchema.AWS, AiAgentProviderAccountStatus.CONNECTED);
    }

    @Test
    @Transactional
    @DisplayName("Test Successful execution of AiAgentLlmQuery to fetch Agent LLMs")
    public void testAiAgentLlmQuery() throws IOException {
        // Given
        AiAgentLlm aiAgentLlm = createAiAgentLlm();

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LLM.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("startIndexNumber", "0");
        queryStr = queryStr.replace("endIndexNumber", "10");

        // When
        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        // Then
        verifyAiAgentLlmResponse(response, aiAgentLlm);
    }

    @Test
    @Transactional
    @DisplayName("Test Successful execution of AiAgentLlmQuery to fetch Agent LLMs when startIndex and endIndex are not specified")
    public void testAiAgentLlmQueryWithoutInputIndexInfo() throws IOException {
        // Given
        AiAgentLlm aiAgentLlm = createAiAgentLlm();

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LLM.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("startIndex: startIndexNumber", StringUtils.EMPTY);
        queryStr = queryStr.replace("endIndex: endIndexNumber", StringUtils.EMPTY);

        // When
        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        // Then
        verifyAiAgentLlmResponse(response, aiAgentLlm);
    }

    private AiAgentLlm createAiAgentLlm() {
        AiAgentProviderAccount aiAgentProviderAccount = createAccount();
        assertNotNull(aiAgentProviderAccount);
        AiAgentLlm aiAgentLlm = testUtil.saveAiAgentLlm(aiAgentProviderAccount);
        assertNotNull(aiAgentLlm);
        return aiAgentLlm;
    }

    private void verifyAiAgentLlmResponse(String response, AiAgentLlm aiAgentLlm) throws IOException {
        assertNotNull(response);

        AiAgentLlmsQueryResponse aiAgentLlmResponse = TestUtil.parseGraphqlResponse(response,
                AI_AGENT_LLM, AiAgentLlmsQueryResponse.class);
        assertNotNull(aiAgentLlmResponse);
        List<com.boomi.graphql.server.schema.types.AiAgentLlm> agentLlms = aiAgentLlmResponse.getLlms();

        assertNotNull(agentLlms);
        assertEquals(1, agentLlms.size());
        assertEquals(1, aiAgentLlmResponse.getTotalResults());
        assertEquals(1, aiAgentLlmResponse.getCurrentPageSize());

        com.boomi.graphql.server.schema.types.AiAgentLlm aiAgentLlm1 = agentLlms.get(0);
        assertNotNull(aiAgentLlm1);

        assertEquals(aiAgentLlm.getGuid(), aiAgentLlm1.getId());
        assertEquals(aiAgentLlm.getName(), aiAgentLlm1.getName());
        assertEquals(aiAgentLlm.getDescription(), aiAgentLlm1.getDescription());
        assertEquals(aiAgentLlm.getVersionString(), aiAgentLlm1.getVersion());
        assertEquals(aiAgentLlm.getExternalId(), aiAgentLlm1.getExternalId());
    }
}