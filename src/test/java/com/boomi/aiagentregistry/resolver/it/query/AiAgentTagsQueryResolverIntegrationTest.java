// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.resolver.it.query;

import jakarta.persistence.EntityManager;
import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentTag;
import com.boomi.aiagentregistry.entity.AiAgentTagAssociation;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.util.AiAgentTestBuilder;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphQLQueriesEnum;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.SQLFileReaderUtil;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.JsonNode;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;

import static com.boomi.aiagentregistry.util.TestUtil.COMMON_EXTERNAL_ID;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_CREDENTIALS;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_METADATA;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class AiAgentTagsQueryResolverIntegrationTest extends BaseMockWebServerTest {

    @Autowired
    private TestWebFluxGraphQLExecutor executor;

    @Autowired
    private TestUtil testUtil;

    @Autowired
    private EntityManager entityManager;

    // This setup helps in getting the testing port that changes dynamically
    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry r) throws IOException {
        r.add("api.boomi.platform.url", () -> "http://localhost:" + _mockWebServer.getPort());
    }

    public static com.boomi.aiagentregistry.entity.AiAgentProviderAccount aiAgentProviderAccount;
    public static AiAgent aiAgent;
    public static AiAgentVersion aiAgentVersion;
    public static AiAgentTag aiAgentTag;
    public static AiAgentTagAssociation aiAgentTagAssociation;


    @BeforeEach
    public void setUpAccount() {
        if (aiAgentProviderAccount == null) {
            aiAgentProviderAccount = testUtil.saveAiAgentRegistryAccount("boomi-test-b", TestUtil.COMMON_ACCOUNT_ID,
                    PROVIDER_ACCOUNT_METADATA,
                    PROVIDER_ACCOUNT_CREDENTIALS,
                    AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                    AiAgentProviderAccountStatus.CONNECTED);
        }
        // create Agent
        if (aiAgent == null && aiAgentProviderAccount != null) {
            aiAgent = testUtil.createAiAgent(aiAgentProviderAccount, GuidUtil.createAIAgentGuid(), COMMON_EXTERNAL_ID,
                    false);
        }

        // create Agent version
        if (aiAgentVersion == null && aiAgent != null) {
            aiAgentVersion = testUtil.createAiAgentVersion(aiAgent, GuidUtil.createAIAgentVersionGuid(),
                    "test-ai-agent-version", "1.0", "RUNNING", "Instructions", false, null);
        }

        // create Agent Tag
        if(aiAgentTag == null && aiAgentVersion != null){
            AiAgentTestBuilder.TagParams params = AiAgentTestBuilder.TagParams.builder()
                    .guid(GuidUtil.createAIAgentTagGuid())
                    .providerAccount(aiAgentProviderAccount)
                    .idpAccountId(aiAgentProviderAccount.getIdpAccountId())
                    .key("test-key")
                    .value("test-value")
                    .build();
            aiAgentTag = testUtil.createAiAgentTag(params);
        }

        // create Agent Tag Association
        if(aiAgentTagAssociation == null && aiAgentVersion != null && aiAgentTag != null){
            AiAgentTestBuilder.TagAssociationParams params = AiAgentTestBuilder.TagAssociationParams.builder()
                    .guid(GuidUtil.createAIAgentTagAssociationGuid())
                    .tagUid(aiAgentTag.getUid())
                    .relatedEntityUid(aiAgentVersion.getUid())
                    .relatedEntityType(AiRegistryEntityType.AGENT)
                    .build();
            aiAgentTagAssociation = testUtil.createAiAgentTagAssociation(params);
        }
    }

    @Test
    @DisplayName("Query - Test AiAgent Tags")
    @Transactional
    public void testGetAiAgentTags() throws IOException {

        assertNotNull(aiAgentProviderAccount);
        assertNotNull(aiAgent);
        assertNotNull(aiAgentVersion);
        assertNotNull(aiAgentTag);
        assertNotNull(aiAgentTagAssociation);

        createMaterialisedView();
        refreshMaterializedView();

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_TAG_QUERY.getFileName());

        assertNotNull(queryStr);

        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        assertNotNull(response);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        JsonNode aiAgentTagData = rootNode.get("data").get("aiAgentTags").get("tags");

        assertNotNull(aiAgentTagData);
        assertTrue(aiAgentTagData.toString().contains("test-key"));


    }

    private void createMaterialisedView(){

        String createQuery = SQLFileReaderUtil.readSqlQuery("CreateAiAgentTagView.sql");

        entityManager.createNativeQuery(createQuery)
                .executeUpdate();
        entityManager.flush();
    }

    private void refreshMaterializedView() {

        String refreshQuery = SQLFileReaderUtil.readSqlQuery("RefreshAiAgentTagView.sql");
        entityManager.createNativeQuery(refreshQuery)
                .executeUpdate();
        entityManager.flush();
    }

}
