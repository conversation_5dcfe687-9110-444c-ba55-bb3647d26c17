// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.resolver.it.mutation;

import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphQLQueriesEnum;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentTool;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;

import java.text.MessageFormat;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import static com.boomi.aiagentregistry.util.TestUtil.ACCOUNT_ID;
import static com.boomi.aiagentregistry.util.TestUtil.COMMON_ACCOUNT_ID;
import static com.boomi.aiagentregistry.util.TestUtil.getLanguageTag;
import static com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus.CONNECTED;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_PROVIDER_ACCOUNT_ID;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_TOOL_ID;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_NOT_FOUND;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.STALE_UPDATE;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.TOOL_NOT_FOUND;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
class AiAgentToolMutationResolverImplIntegrationTest extends BaseMockWebServerTest {

    @Autowired
    private TestWebFluxGraphQLExecutor executor;

    @Autowired
    private TestUtil testUtil;

    // This setup helps in getting the testing port that changes dynamically
    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry r) {
        r.add("api.boomi.platform.url", () -> "http://localhost:" + _mockWebServer.getPort());
    }

    @Test
    @DisplayName("Should successfully execute mutation to create AI Agent Tool")
    void testAiAgentToolCreate() throws Exception {
        AiAgentProviderAccount providerAccount = testUtil.saveAiAgentRegistryAccount("test-boomi",
                TestUtil.COMMON_ACCOUNT_ID, null, null, AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                CONNECTED);

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.TEST_AI_AGENT_TOOL_CREATE.getFileName());
        assertNotNull(queryStr);
        String providerAccountGuid = providerAccount.getGuid();
        queryStr = queryStr.replace("provider_account_id_variable", providerAccountGuid);

        final String response = executor.executeAtomSphereQuery(queryStr, COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        AiAgentTool aiAgentTool = TestUtil.parseGraphqlResponse(response, "aiAgentToolCreate", AiAgentTool.class);
        assertNotNull(aiAgentTool);
        assertNotNull(aiAgentTool.getId());
    }

    @Test
    @DisplayName("Test Invalid Provider Account ID format to create AI Agent Tool")
    void testInvalidProviderAccountIdFormatForAiAgentToolCreate() throws Exception {
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.TEST_AI_AGENT_TOOL_CREATE.getFileName());
        assertNotNull(queryStr);
        String providerAccountId = "INVALID-FORMAT-UUID";
        queryStr = queryStr.replace("provider_account_id_variable", providerAccountId);

        final String response = executor.executeAtomSphereQuery(queryStr, COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        String errorMessage = INVALID_PROVIDER_ACCOUNT_ID.getDetail();
        assertEquals("{\"errors\":[{\"message\":\"" + errorMessage + "\",\"path\":[\"aiAgentToolCreate\"],"
                + "\"extensions\":{\"errorCode\":\"INVALID_PROVIDER_ACCOUNT_ID\","
                + "\"language\":\"" + getLanguageTag() + "\",\"classification\":\"DataFetchingException\"}}],"
                + "\"data\":{\"aiAgentToolCreate\":null}}", response);
    }

    @Test
    @DisplayName("Test non-existing Provider Account to create AI Agent Tool")
    void testNonExistProviderAccountForAiAgentToolCreate() throws Exception {
        AiAgentProviderAccount providerAccount = testUtil.saveAiAgentRegistryAccount("test-boomi",
                TestUtil.COMMON_ACCOUNT_ID, null, null, AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                CONNECTED);

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.TEST_AI_AGENT_TOOL_CREATE.getFileName());
        assertNotNull(queryStr);
        String nonExistProviderAccountGuid = GuidUtil.createAIAgentProviderGuid();
        assertNotEquals(providerAccount.getGuid(), nonExistProviderAccountGuid);
        queryStr = queryStr.replace("provider_account_id_variable", nonExistProviderAccountGuid);

        final String response = executor.executeAtomSphereQuery(queryStr, COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        String errorMessage = MessageFormat.format(PROVIDER_ACCOUNT_NOT_FOUND.getDetail(), nonExistProviderAccountGuid);
        assertEquals("{\"errors\":[{\"message\":\"" + errorMessage + "\",\"path\":[\"aiAgentToolCreate\"],"
                        + "\"extensions\":{\"errorCode\":\"" + PROVIDER_ACCOUNT_NOT_FOUND + "\"," + "\"parameters\":[\""
                        + nonExistProviderAccountGuid + "\"],\"language\":\"" + getLanguageTag() + "\","
                        + "\"classification\":\"DataFetchingException\"}}],\"data\":{\"aiAgentToolCreate\":null}}",
                response);
    }

    @Test
    @DisplayName("Should successfully execute mutation to update AI Agent Tool")
    void testAiAgentToolUpdate() throws Exception {
        AiAgentProviderAccount providerAccount = testUtil.saveAiAgentRegistryAccount("test-boomi",
                TestUtil.COMMON_ACCOUNT_ID, null, null, AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                CONNECTED);
        com.boomi.aiagentregistry.entity.AiAgentTool aiAgentTool = testUtil.saveAiAgentTool("test-external-id",
                providerAccount, COMMON_ACCOUNT_ID, "Tool One", "Draft", null, "{test tool json}");

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.TEST_AI_AGENT_TOOL_UPDATE.getFileName());
        assertNotNull(queryStr);
        String registryAccountGuid = providerAccount.getGuid();
        queryStr = queryStr.replace("provider_account_id_variable", registryAccountGuid);
        queryStr = queryStr.replace("tool_id_variable", aiAgentTool.getGuid());
        String currentTimestamp = Instant.now().atZone(ZoneOffset.UTC).format(
                DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
        queryStr = queryStr.replace("updated_at_provider_time_variable", currentTimestamp);

        final String response = executor.executeAtomSphereQuery(queryStr, COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        AiAgentTool aiAgentToolUpdate = TestUtil.parseGraphqlResponse(response, "aiAgentToolUpdate", AiAgentTool.class);
        assertNotNull(aiAgentToolUpdate);
        assertNotNull(aiAgentToolUpdate.getId());
        assertEquals("tool update", aiAgentToolUpdate.getName());
        assertNotNull(aiAgentToolUpdate.getResources());
        assertEquals(2, aiAgentToolUpdate.getResources().size());
    }

    @Test
    @DisplayName("Test Invalid Tool ID format to update AI Agent Tool")
    void testInvalidToolIdFormatForAiAgentToolUpdate() throws Exception {
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.TEST_AI_AGENT_TOOL_UPDATE.getFileName());
        assertNotNull(queryStr);
        String toolId = "INVALID-FORMAT-UUID";
        queryStr = queryStr.replace("tool_id_variable", toolId);
        String currentTimestamp = Instant.now().atZone(ZoneOffset.UTC).format(
                DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
        queryStr = queryStr.replace("updated_at_provider_time_variable", currentTimestamp);

        final String response = executor.executeAtomSphereQuery(queryStr, COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        String errorMesasge = INVALID_TOOL_ID.getDetail();
        assertEquals("{\"errors\":[{\"message\":\"" + errorMesasge + "\",\"path\":[\"aiAgentToolUpdate\"],"
                        + "\"extensions\":{\"errorCode\":\"INVALID_TOOL_ID\"," + "\"language\":\"" + getLanguageTag()
                        + "\",\"classification\":\"DataFetchingException\"}}]," + "\"data\":{\"aiAgentToolUpdate" +
                        "\":null}}",
                response);
    }

    @Test
    @DisplayName("Test stale update to reject the AI Agent Tool update")
    void testStaleUpdateForAiAgentToolUpdate() throws Exception {
        AiAgentProviderAccount providerAccount = testUtil.saveAiAgentRegistryAccount("test-boomi",
                TestUtil.COMMON_ACCOUNT_ID, null, null, AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                CONNECTED);
        java.sql.Timestamp updatedAtProviderTimestamp = java.sql.Timestamp.from(ZonedDateTime.now(ZoneOffset.UTC)
                .plusDays(1L).toInstant());
        com.boomi.aiagentregistry.entity.AiAgentTool aiAgentTool = testUtil.saveAiAgentTool("test-external-id",
                providerAccount, COMMON_ACCOUNT_ID, "Tool One", "Draft", null, "{test tool json}",
                updatedAtProviderTimestamp);

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.TEST_AI_AGENT_TOOL_UPDATE.getFileName());
        assertNotNull(queryStr);

        queryStr = queryStr.replace("provider_account_id_variable", providerAccount.getGuid());
        queryStr = queryStr.replace("tool_id_variable", aiAgentTool.getGuid());
        String currentTimestamp = Instant.now().atZone(ZoneOffset.UTC).format(
                DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
        queryStr = queryStr.replace("updated_at_provider_time_variable", currentTimestamp);

        final String response = executor.executeAtomSphereQuery(queryStr, COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        String errorMessage = STALE_UPDATE.getDetail().replace("'", "");
        assertEquals("{\"errors\":[{\"message\":\"" + errorMessage + "\",\"path\":[\"aiAgentToolUpdate\"],"
                        + "\"extensions\":{\"errorCode\":\"STALE_UPDATE\"," + "\"language\":\"" + getLanguageTag()
                        + "\",\"classification\":\"DataFetchingException\"}}]," + "\"data\":{\"aiAgentToolUpdate" +
                        "\":null}}",
                response);
    }

    @Test
    @DisplayName("Test non-exist AI Agent Tool due to tool id when update AI Agent Tool")
    void testNonExistToolDueToToolIdForAiAgentToolUpdate() throws Exception {
        AiAgentProviderAccount providerAccount = testUtil.saveAiAgentRegistryAccount("test-boomi",
                TestUtil.COMMON_ACCOUNT_ID, null, null, AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                CONNECTED);

        testUtil.saveAiAgentTool("test-tool-external-id",
                providerAccount, COMMON_ACCOUNT_ID, "Tool One", "Draft", null, "{test tool json}");

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.TEST_AI_AGENT_TOOL_UPDATE.getFileName());
        assertNotNull(queryStr);
        String differentToolId = GuidUtil.createAIAgentToolGuid();
        queryStr = queryStr.replace("tool_id_variable", differentToolId);
        String currentTimestamp = Instant.now().atZone(ZoneOffset.UTC).format(
                DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
        queryStr = queryStr.replace("updated_at_provider_time_variable", currentTimestamp);

        final String response = executor.executeAtomSphereQuery(queryStr, COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        String errorMessage = MessageFormat.format(TOOL_NOT_FOUND.getDetail(), differentToolId);
        assertEquals("{\"errors\":[{\"message\":\"" + errorMessage + "\",\"path\":[\"aiAgentToolUpdate\"],"
                + "\"extensions\":{\"errorCode\":\"TOOL_NOT_FOUND\"," + "\"parameters\":[\"" + differentToolId
                + "\"],\"language\":\"" + getLanguageTag() + "\"," + "\"classification\":\"DataFetchingException\"}}],"
                + "\"data\":{\"aiAgentToolUpdate\":null" + "}}", response);
    }

    @Test
    @DisplayName("Test non-exist AI Agent Tool due to idp accout id when update AI Agent Tool")
    void testNonExistToolDueToIdpAccountIdForAiAgentToolUpdate() throws Exception {
        testUtil.saveAiAgentRegistryAccount("test-boomi-one",
                COMMON_ACCOUNT_ID, null, null, AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                CONNECTED);
        AiAgentProviderAccount providerAccountTwo = testUtil.saveAiAgentRegistryAccount("test-boomi-two", ACCOUNT_ID,
                null, null, AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                CONNECTED);

        com.boomi.aiagentregistry.entity.AiAgentTool aiAgentTool = testUtil.saveAiAgentTool("test-tool-external-id",
                providerAccountTwo, ACCOUNT_ID, "Tool One", "Draft", null, "{test tool json}");

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.TEST_AI_AGENT_TOOL_UPDATE.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("provider_account_id_variable", providerAccountTwo.getGuid());
        queryStr = queryStr.replace("tool_id_variable", aiAgentTool.getGuid());
        String currentTimestamp = Instant.now().atZone(ZoneOffset.UTC).format(
                DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
        queryStr = queryStr.replace("updated_at_provider_time_variable", currentTimestamp);

        final String response = executor.executeAtomSphereQuery(queryStr, COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        String errorMessage = MessageFormat.format(TOOL_NOT_FOUND.getDetail(), aiAgentTool.getGuid());
        assertEquals("{\"errors\":[{\"message\":\"" + errorMessage + "\",\"path\":[\"aiAgentToolUpdate\"],"
                + "\"extensions\":{\"errorCode\":\"TOOL_NOT_FOUND\"," + "\"parameters\":[\"" + aiAgentTool.getGuid()
                + "\"],\"language\":\"" + getLanguageTag() + "\"," + "\"classification\":\"DataFetchingException\"}}],"
                + "\"data\":{\"aiAgentToolUpdate\":null" + "}}", response);
    }
}


