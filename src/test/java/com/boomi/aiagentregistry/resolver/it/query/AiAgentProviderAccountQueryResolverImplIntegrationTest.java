package com.boomi.aiagentregistry.resolver.it.query;

import reactor.core.publisher.Mono;
import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.entity.AgentEntitySyncLatest;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.service.auth.AuthorizationParsingService;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphQLQueriesEnum;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiRegistryEntityAuditData;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncData;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.JsonNode;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.node.ArrayNode;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.time.Instant;
import java.util.List;
import java.util.stream.IntStream;

import static com.boomi.aiagentregistry.util.TestUtil.ACCOUNT_ID;
import static com.boomi.aiagentregistry.util.TestUtil.TEST_REGISTRY_ACCOUNT;
import static com.boomi.aiagentregistry.util.TestUtil.getLanguageTag;
import static com.boomi.aiagentregistry.util.TestUtil.hasAgents;
import static com.boomi.aiagentregistry.util.TestUtil.isBoomiOrBedrockProvider;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_PROVIDER_ACCOUNT_ID;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_NOT_FOUND;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

/**
 * <AUTHOR> Satpute
 */
@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
class AiAgentProviderAccountQueryResolverImplIntegrationTest extends BaseMockWebServerTest {

    @Autowired
    private TestWebFluxGraphQLExecutor executor;

    @Autowired
    private TestUtil testUtil;

    @SpyBean
    private AuthorizationParsingService authorizationParsingService;

    // This setup helps in getting the testing port that changes dynamically
    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry r) throws IOException {
        r.add("api.boomi.platform.url", () -> "http://localhost:" + _mockWebServer.getPort());
    }

    private static List<AiAgentProviderAccount> aiAgentProviderAccounts;
    private static AgentEntitySyncLatest firstProviderAccountSyncHistory;
    private Timestamp now;
    private Timestamp later;
    private static boolean areProviderAccountsCreated = false;

    @BeforeEach
    public void setUpAccount() {
        now = new Timestamp(System.currentTimeMillis());
        later = new Timestamp(System.currentTimeMillis() + 300000);

        if (!areProviderAccountsCreated) {
            // create four provider accounts
            aiAgentProviderAccounts = testUtil.createAiAgentProviders(4);

            // create sync history for first provider account
            AiAgentProviderAccount firstAiAgentProviderAccount = aiAgentProviderAccounts.get(0);
            firstProviderAccountSyncHistory =
                    testUtil.saveAgentEntitySyncHistory(firstAiAgentProviderAccount.getUid(), AiRegistryEntitySyncStatus.COMPLETED, now, later,
                            AiRegistryEntityType.PROVIDER_ACCOUNT);
            assertNotNull(firstProviderAccountSyncHistory.getUid());

            areProviderAccountsCreated = true;
        }
        String boomiResponse = """
                {
                    "userName": "<EMAIL>",
                    "accountId": "boomi-internal",
                    "apiToken": "TestToken",
                    "jwt": "TestJwt"
                }""";
        doReturn(Mono.just(boomiResponse)).when(authorizationParsingService).getMaskedCredentials(any(), any());
    }

    @Test
    @DisplayName("Should successfully execute query to get Boomi AI agent provider account")
    void testGetAiAgentRegistryAccount() throws Exception {

        // Construct Query
        String getQueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNTS_GET.getFileName());
        assertNotNull(getQueryStr, "GraphQL query string should not be null");

        // create sync history for fourth provider account
        AgentEntitySyncLatest syncHistory =
                testUtil.saveAgentEntitySyncHistory(aiAgentProviderAccounts.get(3).getUid(), AiRegistryEntitySyncStatus.COMPLETED_WITH_ERROR, now, later,
                        AiRegistryEntityType.PROVIDER_ACCOUNT);
        assertNotNull(syncHistory.getUid());

        // Execute the GET Query
        final String getResponse = executor.executeAtomSphereQuery(getQueryStr, ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        // Parse response
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(getResponse);
        JsonNode accountData = rootNode.get("data").get("aiAgentProviderAccounts");

        assertNotNull(accountData);

        // Assert Pagination data
        assertEquals(4, accountData.get("numberOfResults").asInt());

        JsonNode aiAgentProviderAccounts = accountData.get("aiAgentProviderAccounts");
        assertEquals(2, ((ArrayNode) aiAgentProviderAccounts).size());
        IntStream.range(0, 2)
                .forEach(index -> {

                    JsonNode account = aiAgentProviderAccounts.get(index);

                    if (hasAgents(index)) {
                        assertEquals(3, account.get("numberOfAgents").asInt());
                    } else {
                        assertEquals(0, account.get("numberOfAgents").asInt());
                    }
                    AiAgentProviderType expectedProviderType = isBoomiOrBedrockProvider(index);
                    assertEquals(expectedProviderType.name(), account.get("providerType").asText());
                    assertEquals(TEST_REGISTRY_ACCOUNT + index, account.get("providerAccountName").asText());
                    assertEquals("CONNECTED", account.get("providerAccountStatus").asText());
                    assertEquals(ACCOUNT_ID, account.get("idpAccountId").asText());

                    // Parse and verify metadataJson
                    ObjectMapper mapper = new ObjectMapper();
                    JsonNode metadata = null;
                    try {
                        metadata = mapper.readTree(account.get("metadataJson").asText());
                    } catch (IOException e) {
                        fail(e.getMessage());
                    }

                    assertEquals("Test Value", metadata.get("awsAccessKeyId").asText());
                    assertEquals("Test Key", metadata.get("awsSecretAccessKey").asText());
                    assertEquals("ab-east-1", metadata.get("awsRegion").asText());

                    JsonNode latestSyncData = account.get("syncData");
                    if (index == 0 || index == 2) {
                        assertFalse(latestSyncData.isNull());
                        AiRegistryEntitySyncStatus expectedSyncStatus = index == 0
                                ? AiRegistryEntitySyncStatus.COMPLETED
                                : AiRegistryEntitySyncStatus.COMPLETED_WITH_ERROR;
                        assertEquals(expectedSyncStatus.name(), latestSyncData.get("lastSyncStatus").asText());
                        Instant actualStartTime = Instant.parse(latestSyncData.get("lastSyncStartDate").asText());
                        assertEquals(now.toInstant(), actualStartTime);
                        Instant actualEndTime = Instant.parse(latestSyncData.get("lastSyncEndDate").asText());
                        assertEquals(later.toInstant(), actualEndTime);
                    } else {
                        assertTrue(latestSyncData.isNull());
                    }
                });

        testGetFilteredAiAgentProvidersAccounts();
    }

    void testGetFilteredAiAgentProvidersAccounts() throws Exception {

        // Construct Query
        String getQueryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNTS_FILTER_GET.getFileName());
        assertNotNull(getQueryStr, "GraphQL query string should not be null");

        // Execute the GET Query
        final String getResponse = executor.executeAtomSphereQuery(getQueryStr, ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        // Parse response
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(getResponse);
        JsonNode accountData = rootNode.get("data").get("aiAgentProviderAccounts");

        assertNotNull(accountData);

        // Assert Pagination data
        assertEquals(2, accountData.get("numberOfResults").asInt());

        JsonNode aiAgentProviderAccounts = accountData.get("aiAgentProviderAccounts");
        assertEquals(1, aiAgentProviderAccounts.size());
        int index = 3;
        JsonNode account = aiAgentProviderAccounts.get(0);
        assertEquals(0, account.get("numberOfAgents").asInt());
        assertEquals(AiAgentProviderType.AWS_BEDROCK.name(), account.get("providerType").asText());
        assertEquals(TEST_REGISTRY_ACCOUNT + index, account.get("providerAccountName").asText());
        assertEquals("CONNECTED", account.get("providerAccountStatus").asText());
        assertEquals(ACCOUNT_ID, account.get("idpAccountId").asText());

        // Parse and verify metadataJson
        ObjectMapper mapper = new ObjectMapper();
        JsonNode metadata = null;
        try {
            metadata = mapper.readTree(account.get("metadataJson").asText());
        } catch (IOException e) {
            fail(e.getMessage());
        }

        assertEquals("Test Value", metadata.get("awsAccessKeyId").asText());
        assertEquals("Test Key", metadata.get("awsSecretAccessKey").asText());
        assertEquals("ab-east-1", metadata.get("awsRegion").asText());

        JsonNode latestSyncData = account.get("syncData");
        assertFalse(latestSyncData.isNull());
        AiRegistryEntitySyncStatus expectedSyncStatus = AiRegistryEntitySyncStatus.COMPLETED_WITH_ERROR;
        assertEquals(expectedSyncStatus.name(), latestSyncData.get("lastSyncStatus").asText());
        Instant actualStartTime = Instant.parse(latestSyncData.get("lastSyncStartDate").asText());
        assertEquals(now.toInstant(), actualStartTime);
        Instant actualEndTime = Instant.parse(latestSyncData.get("lastSyncEndDate").asText());
        assertEquals(later.toInstant(), actualEndTime);
    }

    @Test
    @DisplayName("Should successfully execute query to get AI agent registry account for guid")
    void testAiAgentRegistryAccountById() throws Exception {
        AiAgentProviderAccount firstAiAgentProviderAccount = aiAgentProviderAccounts.get(0);

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT.getFileName());
        assertNotNull(queryStr);

        String registryAccountGuid = firstAiAgentProviderAccount.getGuid();
        queryStr = queryStr.replace("ai-agent-provider-account-guid-variable", registryAccountGuid);

        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount searchedAiAgentRegistryAccount = TestUtil.parseGraphqlResponse(response,
                "aiAgentProviderAccount", com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(searchedAiAgentRegistryAccount);
        assertEquals("test_registry_account0", searchedAiAgentRegistryAccount.getProviderAccountName());
        assertEquals(searchedAiAgentRegistryAccount.getCredential(),
                "{\n" + "    \"userName\": \"<EMAIL>\",\n" + "    \"accountId\": \"boomi-internal\",\n" +
                        "    \"apiToken\": \"TestToken\",\n" + "    \"jwt\": \"TestJwt\"\n" + "}");

        //verify sync data
        AiRegistryEntitySyncData syncData = searchedAiAgentRegistryAccount.getSyncData();
        assertNotNull(syncData);
        assertEquals(firstProviderAccountSyncHistory.getSyncStartDate().getTime(), syncData.getLastSyncStartDate().getTime());
        assertEquals(firstProviderAccountSyncHistory.getSyncEndDate().getTime(), syncData.getLastSyncEndDate().getTime());
        assertEquals(AiRegistryEntitySyncStatus.COMPLETED, syncData.getLastSyncStatus());

        // verify audit data
        AiRegistryEntityAuditData auditData = searchedAiAgentRegistryAccount.getAuditData();
        assertNotNull(auditData);
        assertEquals(firstAiAgentProviderAccount.getCreatedByUserId(), auditData.getCreatedByUserId());
        assertEquals(firstAiAgentProviderAccount.getModifiedByUserId(), auditData.getModifiedByUserId());
        assertEquals(firstAiAgentProviderAccount.getCreatedTime().getTime(), auditData.getCreatedTime().getTime());
        assertEquals(firstAiAgentProviderAccount.getModifiedTime().getTime(), auditData.getModifiedTime().getTime());
    }

    @Test
    @DisplayName("Should successfully execute query to get AI agent registry account for invalid guid")
    void testAiAgentRegistryAccountForInvalidGuid() throws Exception {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT.getFileName());
        assertNotNull(queryStr);

        String invalidRegistryAccountGuid = GuidUtil.createAIAgentGuid();
        queryStr = queryStr.replace("ai-agent-provider-account-guid-variable", invalidRegistryAccountGuid);

        final String response = executor.executeAtomSphereQuery(queryStr, ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        String errorMessage = MessageFormat.format(PROVIDER_ACCOUNT_NOT_FOUND.getDetail(), invalidRegistryAccountGuid);
        assertEquals(
                "{\"errors\":[{\"message\":\"" + errorMessage + "\",\"path\":[\"aiAgentProviderAccount\"],"
                        + "\"extensions\":{\"errorCode\":\"" + PROVIDER_ACCOUNT_NOT_FOUND
                        + "\"," + "\"parameters\":[\""
                        + invalidRegistryAccountGuid + "\"],\"language\":\"" + getLanguageTag() + "\","
                        + "\"classification\":\"DataFetchingException\"}}],\"data\":{\"aiAgentProviderAccount\":null}}",
                response);
    }
    @Test
    @DisplayName(" Test AiAgentProviderAccount query with Invalid  GUID as input id  ")
    void testAiAgentRegistryAccountForInvalidGuidFormat() throws Exception {
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT.getFileName());
        assertNotNull(queryStr);
        String invalidAccountGuidFormat = "invalidId";
        queryStr = queryStr.replace("ai-agent-provider-account-guid-variable", invalidAccountGuidFormat);

        final String response = executor.executeAtomSphereQuery(queryStr, ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        String errorMessage = MessageFormat.format(INVALID_PROVIDER_ACCOUNT_ID.getDetail(), invalidAccountGuidFormat);
        assertEquals(
                "{\"errors\":[{\"message\":\"" + errorMessage + "\",\"path\":[\"aiAgentProviderAccount\"],"
                        + "\"extensions\":{\"errorCode\":\"" + INVALID_PROVIDER_ACCOUNT_ID
                        + "\"," + "\"parameters\":[\""
                        + invalidAccountGuidFormat + "\"],\"language\":\"" + getLanguageTag() + "\","
                        + "\"classification\":\"DataFetchingException\"}}],\"data\":{\"aiAgentProviderAccount\":null}}",
                response);
    }
}
