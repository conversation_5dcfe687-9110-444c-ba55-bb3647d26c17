package com.boomi.aiagentregistry.resolver.it.query;

import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentAlias;
import com.boomi.aiagentregistry.entity.AiAgentGuardrail;
import com.boomi.aiagentregistry.entity.AiAgentGuardrailAssociation;
import com.boomi.aiagentregistry.entity.AiAgentLargeTextContent;
import com.boomi.aiagentregistry.entity.AiAgentLlm;
import com.boomi.aiagentregistry.entity.AiAgentLlmAssociation;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTask;
import com.boomi.aiagentregistry.entity.AiAgentTaskAssociation;
import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.entity.AiAgentToolAssociation;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.repo.AiAgentAliasRepository;
import com.boomi.aiagentregistry.repo.AiAgentLargeTextContentRepository;
import com.boomi.aiagentregistry.repo.AiAgentRepository;
import com.boomi.aiagentregistry.repo.AiAgentVersionRepository;
import com.boomi.aiagentregistry.util.AiAgentTestBuilder;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphQLQueriesEnum;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.JsonNode;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.sql.Timestamp;
import java.time.Instant;

import static com.boomi.aiagentregistry.util.TestUtil.COMMON_ACCOUNT_ID;
import static com.boomi.aiagentregistry.util.TestUtil.COMMON_EXTERNAL_ID;
import static com.boomi.aiagentregistry.util.TestUtil.CREATED_DATE;
import static com.boomi.aiagentregistry.util.TestUtil.MODIFIED_DATE;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_CREDENTIALS;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_METADATA;
import static com.boomi.aiagentregistry.util.TestUtil.getLanguageTag;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class AiAgentQueryResolverIntegrationTest extends BaseMockWebServerTest {

    @Autowired
    private TestWebFluxGraphQLExecutor executor;

    @Autowired
    private TestUtil testUtil;

    @Autowired
    private AiAgentRepository _aiAgentRepository;

    @Autowired
    private AiAgentVersionRepository _aiAgentVersionRepository;

    @Autowired
    private AiAgentAliasRepository _aiAgentAliasRepository;
    @Autowired
    private AiAgentTestBuilder _testBuilder;

    @Autowired
    AiAgentLargeTextContentRepository _largeTextRepo;
    public static AiAgentTask aiAgentTask;
    public static AiAgentTaskAssociation aiAgentTaskAssociation;

    // This setup helps in getting the testing port that changes dynamically
    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry r) throws IOException {
        r.add("api.boomi.platform.url", () -> "http://localhost:" + _mockWebServer.getPort());
    }

    public static com.boomi.aiagentregistry.entity.AiAgentProviderAccount aiAgentProviderAccount;
    private static AiAgentProviderAccount aiAgentProviderAccountWithAnotherIdp;
    public static AiAgent aiAgent;
    public static AiAgentVersion aiAgentVersionOne;
    public static AiAgentAlias aiAgentAlias;
    public static AiAgentGuardrail aiAgentGuardrail;
    public static AiAgentGuardrailAssociation aiAgentGuardrailAssociation;
    public static AiAgentTool aiAgentTool;
    public static AiAgentToolAssociation aiAgentToolAssociation;
    public static AiAgentLlm aiAgentLlm;
    public static AiAgentLlmAssociation aiAgentLlmAssociation;

    @BeforeEach
    public void setUpAccount() {
        if (aiAgentProviderAccount == null) {
            aiAgentProviderAccount = testUtil.saveAiAgentRegistryAccount("boomi-test-b", TestUtil.COMMON_ACCOUNT_ID,
                    PROVIDER_ACCOUNT_METADATA,
                    PROVIDER_ACCOUNT_CREDENTIALS,
                    AiAgentProviderType.AWS_BEDROCK, AiProviderAuthSchema.AWS,
                    AiAgentProviderAccountStatus.CONNECTED);

            // create another provider account with different idp
            aiAgentProviderAccountWithAnotherIdp =
                    testUtil.saveAiAgentRegistryAccount("boomi-test-c", TestUtil.ACCOUNT_ID, PROVIDER_ACCOUNT_METADATA,
                            PROVIDER_ACCOUNT_CREDENTIALS, AiAgentProviderType.AWS_BEDROCK, AiProviderAuthSchema.AWS,
                            AiAgentProviderAccountStatus.CONNECTED);
        }
        // create Agent
        if (aiAgent == null && aiAgentProviderAccount != null) {
            aiAgent = testUtil.createAiAgent(aiAgentProviderAccount, GuidUtil.createAIAgentGuid(), COMMON_EXTERNAL_ID,
                    false);

            testUtil.createAiAgent(aiAgentProviderAccountWithAnotherIdp, GuidUtil.createAIAgentGuid(), "agent-in-another-idp",
                    false);
        }

        // create Agent version
        if (aiAgentVersionOne == null && aiAgent != null) {
            aiAgentVersionOne = testUtil.createAiAgentVersion(aiAgent, GuidUtil.createAIAgentVersionGuid(),
                    "test-ai-agent-version", "1", "RUNNING", "Instructions", false, null);
        }

        // create Agent alias
        if (aiAgentAlias == null && aiAgentVersionOne != null) {
            AiAgentTestBuilder.AliasSetupParams params = AiAgentTestBuilder.AliasSetupParams.builder()
                    .name("agent-alias")
                    .externalId("agent-alias-external-id")
                    .description("Agent Alias Description")
                    .isDeleted(false)
                    .idpAccountId(aiAgent.getIdpAccountId())
                    .providerAccount(aiAgent.getAiAgentProviderAccount())
                    .updatedAt(Instant.now())
                    .build();

            aiAgentAlias = testUtil.createVersionAlias(aiAgentVersionOne, params);
        }

        // create guardrail
        if (aiAgentGuardrail == null && aiAgentProviderAccount != null) {
            aiAgentGuardrail = testUtil.saveAiAgentGuardrail(aiAgentProviderAccount, COMMON_ACCOUNT_ID);
        }

        // create guardrailAssociation
        if (aiAgentGuardrailAssociation == null && aiAgentGuardrail != null && aiAgentVersionOne != null) {
            aiAgentGuardrailAssociation =
                    testUtil.saveAiAgentGuardrailAssociation(aiAgentGuardrail, aiAgentVersionOne.getUid());
        }

        // create tool and tool resources
        if (aiAgentTool == null && aiAgentProviderAccount != null) {
            aiAgentTool = testUtil.saveAiAgentTool("test-tool-external-id", aiAgentProviderAccount, COMMON_ACCOUNT_ID,
                    "Tool One", "Draft", null, "{test tool json}");
            testUtil.createAiAgentToolResource(aiAgentTool, GuidUtil.createAIAgentToolResourceGuid(),
                    "Lambda Function One", "function-1-description", "{tool resource json 1}");
            testUtil.createAiAgentToolResource(aiAgentTool, GuidUtil.createAIAgentToolResourceGuid(),
                    "Lambda Function Two", "function-2-description", "{tool resource json 2}");
        }

        // create tool association with aiAgentVersion
        if (aiAgentToolAssociation == null && aiAgentTool != null && aiAgent != null) {
            aiAgentToolAssociation = testUtil.saveAiAgentToolAssociation(aiAgentTool, aiAgentVersionOne.getUid());
        }

        // create LLM associations with aiAgentVersion
        if (aiAgentLlm == null && aiAgentVersionOne != null) {
            aiAgentLlm = testUtil.saveAiAgentLlm(aiAgentProviderAccount);
        }
        if (aiAgentLlmAssociation == null && aiAgentLlm != null) {
            aiAgentLlmAssociation = testUtil.saveAiAgentLlmAssociation(aiAgentLlm,
                    aiAgentVersionOne.getUid(),
                    AiRegistryEntityType.VERSION);
        }
        if(aiAgentTask==null && aiAgentProviderAccount!=null) {
            AiAgentTestBuilder.TaskSetupParams taskParams =
                    AiAgentTestBuilder.createTask1Params(aiAgentProviderAccount);

            aiAgentTask = _testBuilder.setupTask(taskParams);

            if (aiAgentTaskAssociation == null && aiAgentTask != null) {
                testUtil.saveAiAgentTaskAssociation(
                        aiAgentVersionOne, aiAgentTask);
            }
            // create tool association with aiAgentTask
            if (aiAgentTask!=null && aiAgentTool != null && aiAgent != null) {

                AiAgentToolAssociation  aiAgentToolAssociation1=  testUtil.saveAiAgentToolTaskAssociation(aiAgentTool,
                        aiAgentTask);
            }

            AiAgentLargeTextContent instruction = AiAgentLargeTextContent.builder()
                    .content("open url enter not null data")
                    .relatedEntityType(AiRegistryEntityType.TASK.name())
                    .relatedEntityUid(aiAgentTask.getUid())
                    .build();
            _largeTextRepo.save(instruction);
        }

    }

    @Test
    @DisplayName("Test Successful execution of AiAgents")
    public void testAiAgents() throws IOException {

        validateDataSetup();

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENTS.getFileName());

        assertNotNull(queryStr);

        String providerAccountGuid = aiAgentProviderAccount.getGuid();
        queryStr = queryStr.replace("ai-agent-provider-account-id-1", providerAccountGuid);

        testGetAiAgentsCommon(queryStr);
    }

    private static void validateAiAgent(JsonNode responseAiAgent) {
        assertNotNull(responseAiAgent);
        assertEquals(String.valueOf(aiAgent.getGuid()), responseAiAgent.get("id").asText());
        assertTrue(StringUtils.isNotBlank(aiAgent.getExternalId()));

        JsonNode responseAiAgentVersions = responseAiAgent.get("agentVersions");
        assertNotNull(responseAiAgentVersions);

        JsonNode responseAiAgentVersion = responseAiAgentVersions.get(0);
        assertNotNull(responseAiAgentVersion);
        assertEquals("1", responseAiAgentVersion.get("version").asText());

        JsonNode versionAuditData = responseAiAgentVersion.get("auditData");
        assertNotNull(versionAuditData);
        validateAuditData(versionAuditData);

        JsonNode agentGuardrails = responseAiAgentVersion.get("agentGuardrails");
        assertNotNull(agentGuardrails);
        JsonNode configJson = agentGuardrails.get(0).get("configurationJson");
        if (configJson != null) {
            String expectedJson =
                    "{\"name\": \"Test Guardrail\", \"description\": \"This is a test guardrail configuration\"}";
            assertEquals(expectedJson, configJson.asText(), "Configuration JSON does not match expected value");
        }
        JsonNode responseAgentGuardrail = agentGuardrails.get(0);
        assertNotNull(responseAgentGuardrail);
        assertEquals(String.valueOf(aiAgentGuardrail.getGuid()), responseAgentGuardrail.get("id").asText());
        // Add assertion for guardrailJson

        JsonNode agentTools = responseAiAgentVersion.get("agentTools");
        assertNotNull(agentTools);

        JsonNode responseAgentTool = agentTools.get(0);
        assertNotNull(responseAgentTool);
        assertEquals(String.valueOf(aiAgentTool.getGuid()), responseAgentTool.get("id").asText());

        JsonNode responseAgentToolResources = responseAgentTool.get("resources");
        assertNotNull(responseAgentToolResources);
        assertEquals(2, responseAgentToolResources.size());
        //assert responseAgentToolResources contains one record with name "Lambda Function One" and one with "Lambda Function Two"
        assertTrue(responseAgentToolResources.get(0).get("name").asText().equals("Lambda Function One") ||
                   responseAgentToolResources.get(0).get("name").asText().equals("Lambda Function Two"));
        assertTrue(responseAgentToolResources.get(1).get("name").asText().equals("Lambda Function One") ||
                   responseAgentToolResources.get(1).get("name").asText().equals("Lambda Function Two"));

        JsonNode instructions = responseAiAgentVersion.get("instructions");
        assertNotNull(instructions);
        assertTrue(instructions.isArray());

        JsonNode agentLlms = responseAiAgentVersion.get("llms");
        assertNotNull(agentLlms);
        assertEquals(1, agentLlms.size());
        assertEquals("agent-llm-name", agentLlms.get(0).get("name").asText());
        assertEquals("agent-llm-description", agentLlms.get(0).get("description").asText());

        JsonNode agentAliases = responseAiAgentVersions.get(0).get("agentAliases");
        assertNotNull(agentAliases);
        assertEquals(1, agentAliases.size());
        assertEquals("agent-alias", agentAliases.get(0).get("name").asText());
        assertEquals("Agent Alias Description", agentAliases.get(0).get("description").asText());

        JsonNode agentAliasesAuditData = agentAliases.get(0).get("auditData");
        assertNotNull(agentAliasesAuditData);
        validateAuditData(agentAliasesAuditData);

        JsonNode providerAccountSummary = responseAiAgent.get("providerAccountSummary");
        assertNotNull(providerAccountSummary);
        assertEquals("AWS_BEDROCK", providerAccountSummary.get("providerType").asText());
        assertEquals("boomi-test-b", providerAccountSummary.get("providerAccountName").asText());
        assertEquals("test-provider-account-id", providerAccountSummary.get("externalProviderAccountId").asText());
        assertEquals("us-east-1", providerAccountSummary.get("region").asText());
    }

    private static void validateAiAgentExternalLink(JsonNode responseAiAgent) {
        String expectedExternalId = "https://us-east-1.console.aws.amazon.com/bedrock/home?region=us-east-1#"
                                    + "/agents/"
                                    + aiAgent.getExternalId();
        assertEquals(expectedExternalId, responseAiAgent.get("externalLink").asText());
    }

    @Test
    @Transactional
    @DisplayName("Test Successful execution of AiAgent using version ID")
    public void testAiAgentByVersionId() throws IOException {

        validateDataSetup();

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_BY_VERSION_ID.getFileName());

        assertNotNull(queryStr);

        String agentVersionGuid = aiAgentVersionOne.getGuid();
        queryStr = queryStr.replace("%versionId%", agentVersionGuid);

        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        // Parse response
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        assertFalse(rootNode.has("errors"));
        JsonNode responseAiAgent = rootNode.get("data").get("aiAgentByVersionId");
        validateAiAgent(responseAiAgent);
        JsonNode responseAiAgentVersions = responseAiAgent.get("agentVersions");
        assertNotNull(responseAiAgentVersions);

        JsonNode responseAiAgentTasks = responseAiAgentVersions.get(0).get("agentTasks");
        assertNotNull(responseAiAgentTasks);
        validateTask(responseAiAgentTasks);
        JsonNode instructions = responseAiAgentTasks.get(0).get("instructions");
        assertNotNull(instructions);

        JsonNode tools = responseAiAgentTasks.get(0).get("tools");
        assertNotNull(tools);
        validateInstruction(instructions);
        JsonNode responseAiAgentVersion = responseAiAgentVersions.get(0);
        assertNotNull(responseAiAgentVersion);
        validateAiAgentVersionExternalLink(responseAiAgentVersions);
        validateAiAgentExternalLink(responseAiAgent);
    }
    private void validateInstruction(JsonNode instructions) {
        assertNotNull(instructions);
        assertEquals("open url enter not null data",instructions.get(0).asText());
    }

    private void validateTask(JsonNode responseAiAgentTasks) {
        assertEquals(1, responseAiAgentTasks.size());
        assertEquals("first task ", responseAiAgentTasks.get(0).get("name").asText());
    }

    private static void validateAiAgentVersionExternalLink(JsonNode responseAiAgentVersions) {
        String expectedVersionExternalId = "https://us-east-1.console.aws.amazon.com/bedrock/home?region=us-east-1#"
                                           + "/agents/"
                                           + aiAgent.getExternalId()
                                           + "/versions/"
                                           + aiAgentVersionOne.getVersionString();
        assertEquals(expectedVersionExternalId, responseAiAgentVersions.get(0).get("externalLink").asText());
    }

    @Test
    @DisplayName("Test Successful execution of AiAgent using alias ID")
    public void testAiAgentByAliasId() throws IOException {

        validateDataSetup();

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_BY_ALIAS_ID.getFileName());

        assertNotNull(queryStr);

        String agentAliasGuid = aiAgentAlias.getGuid();
        queryStr = queryStr.replace("%aliasId%", agentAliasGuid);

        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        // Parse response
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        assertFalse(rootNode.has("errors"));
        JsonNode responseAiAgent = rootNode.get("data").get("aiAgentByAliasId");
        validateAiAgent(responseAiAgent);

        JsonNode responseAiAgentVersions = responseAiAgent.get("agentVersions");
        JsonNode agentAliases = responseAiAgentVersions.get(0).get("agentAliases");
        String expectedAliasExternalId = "https://us-east-1.console.aws.amazon.com/bedrock/home?region=us-east-1#"
                                         + "/agents/"
                                         + aiAgent.getExternalId()
                                         + "/alias/"
                                         + aiAgentAlias.getExternalId();
        assertEquals(expectedAliasExternalId, agentAliases.get(0).get("externalLink").asText());
        validateAiAgentVersionExternalLink(responseAiAgentVersions);
        validateAiAgentExternalLink(responseAiAgent);
    }

    @Test
    @DisplayName("Test Ai Agents filter by idpAccount")
    public void testAiAgentsFiltersByIdpAccount() throws IOException {
        validateDataSetup();
        assertEquals(2, _aiAgentRepository.findAll().size());

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENTS.getFileName());

        assertNotNull(queryStr);

        // pass empty array of provider Ids
        queryStr = queryStr.replace("[\"ai-agent-provider-account-id-1\"]", "[]");

        testGetAiAgentsCommon(queryStr);
    }

    @Test
    @DisplayName("Test Ai Agents filter by ipdAccount")
    public void testAiAgentsFiltersByIdpAccountId() throws IOException {
        validateDataSetup();
        String providerAccount = aiAgentProviderAccount.getGuid();

        aiAgent.setIdpAccountId("wrong-idp-account");
        _aiAgentRepository.save(aiAgent);

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENTS.getFileName());

        assertNotNull(queryStr);

        // pass empty array of provider Ids
        queryStr = queryStr.replace("ai-agent-provider-account-id-1", providerAccount);

        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        JsonNode aiAgentsData = rootNode.get("data").get("aiAgents");

        assertNotNull(aiAgentsData);

        // Assert Pagination data
        assertEquals(0, aiAgentsData.get("numberOfResults").asInt());

        aiAgent.setIdpAccountId(COMMON_ACCOUNT_ID);
        _aiAgentRepository.save(aiAgent);
    }

    @Test
    @DisplayName("Test Ai Agents filter by isDeleted")
    public void testAiAgentsFiltersByIsDeleted() throws IOException {
        validateDataSetup();
        String providerAccount = aiAgentProviderAccount.getGuid();

        aiAgent.setIsDeleted(true);
        _aiAgentRepository.save(aiAgent);

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENTS.getFileName());

        assertNotNull(queryStr);

        // pass empty array of provider Ids
        queryStr = queryStr.replace("ai-agent-provider-account-id-1", providerAccount);

        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        JsonNode aiAgentsData = rootNode.get("data").get("aiAgents");

        assertNotNull(aiAgentsData);

        // Assert Pagination data
        assertEquals(0, aiAgentsData.get("numberOfResults").asInt());

        aiAgent.setIsDeleted(false);
        _aiAgentRepository.save(aiAgent);
    }

    @Test
    @DisplayName("Test AiAgentByVersionId query for filter by Agent idpAccountId isDeleted and Version isDeleted")
    public void testAiAgentByVersionIdForAgentIdpAccountIdAndIsDeletedAndVersionIsDeletedFilter() throws IOException {
        validateDataSetup();

        AiAgentVersion deletedAiAgentVersion = testUtil.createAiAgentVersion(aiAgent,
                GuidUtil.createAIAgentVersionGuid(),
                "test-ai-agent-version-1", "2.0", "RUNNING", "Instructions", true, null);
        _aiAgentVersionRepository.save(deletedAiAgentVersion);

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_BY_VERSION_ID.getFileName());

        assertNotNull(queryStr);

        String agentVersionGuid = aiAgentVersionOne.getGuid();
        queryStr = queryStr.replace("%versionId%", agentVersionGuid);

        String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        JsonNode aiAgentData = rootNode.get("data").get("aiAgentByVersionId");

        assertNotNull(aiAgentData);
        validateAiAgent(aiAgentData);

        queryStr = queryStr.replace(agentVersionGuid, deletedAiAgentVersion.getGuid());

        response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertEquals(
                "{\"errors\":[{\"message\":\"The specified AI Agent version ID does not correspond to any existing AI"
                        + " Agent version in the registry. Please verify that the ID is correct and that the agent "
                        + "version exists.\",\"path\":[\"aiAgentByVersionId\"],"
                        + "\"extensions\":{\"errorCode\":\"AI_AGENT_VERSION_NOT_FOUND\",\"language\":\""
                        + getLanguageTag() + "\","
                        + "\"classification\":\"DataFetchingException\"}}],\"data\":{\"aiAgentByVersionId\":null}}",
                response);
    }

    @Test
    @DisplayName(
            "Test AiAgentByAliasId query for filter by Agent idpAccountId isDeleted , Version isDeleted and Alias "
                    + "isDeleted")
    public void testAiAgentByAliasIdForAgentIdpAccountIdAndIsDeletedAndVersionIsDeletedAndAliasIsDeletedFilter()
            throws IOException {
        validateDataSetup();

        AiAgentTestBuilder.AliasSetupParams params = AiAgentTestBuilder.AliasSetupParams.builder()
                .name("agent-alias-1")
                .externalId("agent-alias-external-id-1")
                .description("Agent Alias Description")
                .idpAccountId(aiAgent.getIdpAccountId())
                .providerAccount(aiAgent.getAiAgentProviderAccount())
                .updatedAt(Instant.now())
                .isDeleted(true)
                .build();

        AiAgentAlias deletedAiAgentAlias = testUtil.createVersionAlias(aiAgentVersionOne, params);

        deletedAiAgentAlias.setIsDeleted(true);
        _aiAgentAliasRepository.save(deletedAiAgentAlias);

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_BY_ALIAS_ID.getFileName());

        assertNotNull(queryStr);

        String agentAliasGuid = aiAgentAlias.getGuid();
        queryStr = queryStr.replace("%aliasId%", agentAliasGuid);

        String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        JsonNode aiAgentData = rootNode.get("data").get("aiAgentByAliasId");

        assertNotNull(aiAgentData);
        validateAiAgent(aiAgentData);

        queryStr = queryStr.replace(agentAliasGuid, deletedAiAgentAlias.getGuid());

        response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertEquals(
                "{\"errors\":[{\"message\":\"The specified AI Agent alias ID does not correspond to any existing AI"
                        + " Agent alias in the registry. Please verify that the ID is correct and that the agent "
                        + "alias exists.\",\"path\":[\"aiAgentByAliasId\"],"
                        + "\"extensions\":{\"errorCode\":\"AI_AGENT_ALIAS_NOT_FOUND\",\"language\":\""
                        + getLanguageTag() + "\","
                        + "\"classification\":\"DataFetchingException\"}}],\"data\":{\"aiAgentByAliasId\":null}}",
                response);
    }

    public void testGetDraftAwsAgentVersion() throws IOException {
        AiAgent aiAgentWithDraftVersion = testUtil.createAiAgent(aiAgentProviderAccount, GuidUtil.createAIAgentGuid(), COMMON_EXTERNAL_ID,
                 false);
        AiAgentVersion draftVersion = testUtil.createAiAgentVersion(aiAgentWithDraftVersion, GuidUtil.createAIAgentVersionGuid(),
                "test-ai-agent-version", "DRAFT", "RUNNING", "Instructions", false, null);

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_BY_VERSION_ID.getFileName());
        assertNotNull(queryStr);
        String agentVersionGuid = draftVersion.getGuid();
        queryStr = queryStr.replace("%versionId%", agentVersionGuid);

        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        // Parse response
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        assertFalse(rootNode.has("errors"));
        JsonNode responseAiAgent = rootNode.get("data").get("aiAgentByVersionId");
        JsonNode responseAiAgentVersions = responseAiAgent.get("agentVersions");
        assertNotNull(responseAiAgentVersions);
        JsonNode responseAiAgentVersion = responseAiAgentVersions.get(0);
        assertNotNull(responseAiAgentVersion);
        String expectedExternalId = "https://us-east-1.console.aws.amazon.com/bedrock/home?region=us-east-1#"
                                    + "/agents/"
                                    + aiAgentWithDraftVersion.getExternalId();
        assertEquals(expectedExternalId, responseAiAgentVersion.get("externalLink").asText());

        _aiAgentVersionRepository.delete(draftVersion);
        _aiAgentRepository.delete(aiAgentWithDraftVersion);
     }

    private void testGetAiAgentsCommon(String queryStr) throws IOException {
        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        // Parse response
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        assertFalse(rootNode.has("errors"));
        JsonNode aiAgentsData = rootNode.get("data").get("aiAgents");

        assertNotNull(aiAgentsData);

        // Assert Pagination data
        assertEquals(1, aiAgentsData.get("numberOfResults").asInt());
        assertEquals(10, aiAgentsData.get("currentPageSize").asInt());

        JsonNode responseAiAgents = aiAgentsData.get("aiAgents");

        JsonNode responseAiAgent = responseAiAgents.get(0);
        validateAiAgent(responseAiAgent);
    }

    private static void validateAuditData(JsonNode auditData) {
        assertEquals("<EMAIL>", auditData.get("createdByUserId").asText());
        assertNotNull(auditData.get("createdTime"));
        Timestamp actualCreatedDate = Timestamp.from(Instant.parse(auditData.get("createdTime").asText()));
        assertEquals("<EMAIL>", auditData.get("modifiedByUserId").asText());
        assertEquals(actualCreatedDate, CREATED_DATE);
        assertNotNull(auditData.get("modifiedTime"));
        Timestamp actualModifiedDate = Timestamp.from(Instant.parse(auditData.get("modifiedTime").asText()));
        assertEquals(actualModifiedDate, MODIFIED_DATE);
    }

    private static void validateDataSetup() {
        assertNotNull(aiAgentProviderAccount);
        assertNotNull(aiAgent);
        assertNotNull(aiAgentVersionOne);
        assertNotNull(aiAgentGuardrail);
        assertNotNull(aiAgentGuardrailAssociation);
        assertNotNull(aiAgentTool);
        assertNotNull(aiAgentToolAssociation);
    }

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    @Transactional
    @DisplayName("Test Successful execution of AiAgent using version ID with no tool tasks")
    public void testAiAgentVersionWithNoToolTasks() throws IOException {
        String response = executeAiAgentQuery(true, false);
        JsonNode rootNode = parseAndValidateResponse(response);
        JsonNode agentTasks = extractAgentTasks(rootNode);
        JsonNode instructions = agentTasks.get(0).get("instructions");
        assertNotNull(instructions, "Instructions should not be null");
        JsonNode tools = agentTasks.get(0).get("tools");
        assertEquals(0, tools.size(), "Tools size should be 0");
    }

    @Test
    @Transactional
    @DisplayName("Test Successful execution of AiAgent using version ID with no instructions")
    public void testAiAgentTasksWithNoInstructions() throws IOException {
        String response = executeAiAgentQuery(false, true);
        JsonNode rootNode = parseAndValidateResponse(response);
        JsonNode agentTasks = extractAgentTasks(rootNode);
        JsonNode instructions = agentTasks.get(0).get("instructions");
        assertEquals(0, instructions.size());
    }
    private String executeAiAgentQuery(boolean deleteToolTask, boolean deleteLargeText) throws IOException {
        validateDataSetup();
        if (deleteToolTask) {
            testUtil.deleteAllAiAgentToolTaskAssociation();
        }
        if (deleteLargeText) {
            testUtil.deleteAllLargeText();
        }
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(
                GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_BY_VERSION_ID.getFileName()
                                                                );
        assertNotNull(queryStr, "Query string should not be null");
        String agentVersionGuid = aiAgentVersionOne.getGuid();
        queryStr = queryStr.replace("%versionId%", agentVersionGuid);
        return executor.executeAtomSphereQuery(
                queryStr,
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES,
                TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES
                                              );
    }
    private JsonNode parseAndValidateResponse(String response) throws IOException {
        JsonNode rootNode = objectMapper.readTree(response);
        assertFalse(rootNode.has("errors"), "Response should not contain errors");
        return rootNode;
    }
    private JsonNode extractAgentTasks(JsonNode rootNode) {
        JsonNode responseAiAgent = rootNode.get("data").get("aiAgentByVersionId");
        JsonNode responseAiAgentVersions = responseAiAgent.get("agentVersions");
        assertNotNull(responseAiAgentVersions, "Agent versions should not be null");
        JsonNode responseAiAgentTasks = responseAiAgentVersions.get(0).get("agentTasks");
        assertNotNull(responseAiAgentTasks, "Agent tasks should not be null");
        validateTask(responseAiAgentTasks);
        return responseAiAgentTasks;
    }
}


