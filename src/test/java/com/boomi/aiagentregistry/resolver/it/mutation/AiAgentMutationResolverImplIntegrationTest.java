package com.boomi.aiagentregistry.resolver.it.mutation;

import reactor.core.publisher.Mono;
import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.service.sync.boomigarden.GardenService;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphQLQueriesEnum;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.gardenagents.model.AgentInstallResponse;
import com.boomi.gardenagents.model.AgentUninstallResponse;
import com.boomi.graphql.server.schema.types.AiAgent;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentRegistryTrustLevel;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.text.MessageFormat;

import static com.boomi.aiagentregistry.util.TestUtil.ACCOUNT_ID;
import static com.boomi.aiagentregistry.util.TestUtil.COMMON_ACCOUNT_ID;
import static com.boomi.aiagentregistry.util.TestUtil.COMMON_EXTERNAL_ID;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_CREDENTIALS;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_METADATA;
import static com.boomi.aiagentregistry.util.TestUtil.getLanguageTag;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.FAILED_TO_DISABLE_AGENT_VERSION;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.FAILED_TO_ENABLE_AGENT_VERSION;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class AiAgentMutationResolverImplIntegrationTest extends BaseMockWebServerTest {

    @Autowired
    private TestWebFluxGraphQLExecutor executor;

    @Autowired
    private TestUtil testUtil;

    @MockBean
    private GardenService gardenService;

    // This setup helps in getting the testing  port that changes dynamically
    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry r) throws IOException {
        r.add("api.boomi.platform.url", () -> "http://localhost:" + _mockWebServer.getPort());
    }

    public static com.boomi.aiagentregistry.entity.AiAgentProviderAccount aiAgentProviderAccount;
    public static com.boomi.aiagentregistry.entity.AiAgent aiAgent;
    public static AiAgentVersion aiAgentVersion;

    @BeforeEach
    public void setup() {
        /*_mockWebServer.setDispatcher(_serverHelper.getDispatcher().createDispatcher());*/

        if (aiAgentProviderAccount == null) {
            aiAgentProviderAccount = testUtil.saveAiAgentRegistryAccount("boomi-test-b", TestUtil.COMMON_ACCOUNT_ID,
                    PROVIDER_ACCOUNT_METADATA,
                    PROVIDER_ACCOUNT_CREDENTIALS,
                    AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                    AiAgentProviderAccountStatus.CONNECTED);
        }
        // create Agent
        if (aiAgent == null && aiAgentProviderAccount != null) {
            aiAgent = testUtil.createAiAgent(aiAgentProviderAccount, GuidUtil.createAIAgentGuid(), COMMON_EXTERNAL_ID,
                    false);
        }

        // create Agent version
        if (aiAgentVersion == null && aiAgent != null) {
            aiAgentVersion = testUtil.createAiAgentVersion(aiAgent, GuidUtil.createAIAgentVersionGuid(),
                    "test-ai-agent-version", "1.0", "RUNNING", "Instructions", false, null);
        }
    }

    @Test
    @DisplayName("Should successfully execute mutation to create AI agent")
    void testAiAgentCreate() throws Exception {
        AiAgentProviderAccount registryAccount = testUtil.saveAiAgentRegistryAccount("test-boomi",
                TestUtil.COMMON_ACCOUNT_ID, null, null, AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                AiAgentProviderAccountStatus.CONNECTED);

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_CREATE.getFileName());
        assertNotNull(queryStr);
        String registryAccountGuid = registryAccount.getGuid();
        queryStr = queryStr.replace("provider_account_id_variable", registryAccountGuid);

        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        AiAgent aiAgent = TestUtil.parseGraphqlResponse(response,
                "aiAgentCreate", AiAgent.class);
        assertNotNull(aiAgent);
        assertNotNull(aiAgent.getId());
        assertEquals("Draft", aiAgent.getAgentVersions().get(0).getVersion());
    }

    @Test
    @DisplayName("Test Invalid Registry Account ID format to create AI Agent")
    void testInvalidRegistryAccountIdFormatForAiAgentCreate() throws Exception {
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_CREATE.getFileName());
        assertNotNull(queryStr);
        String registryAccountId = "INVALID-FORMAT-UUID";
        queryStr = queryStr.replace("provider_account_id_variable", registryAccountId);

        final String response = executor.executeAtomSphereQuery(queryStr, COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertEquals("{\"errors\":[{\"message\":\"The provided provider account id is not in valid UUID format.\","
                + "\"path\":[\"aiAgentCreate\"],\"extensions\":{\"errorCode\":\"INVALID_PROVIDER_ACCOUNT_ID"
                + "\",\"language\":\"" + getLanguageTag() + "\",\"classification\":\"DataFetchingException\"}},"
                + "{\"message\":\"Provider account not found with the supplied ID INVALID-FORMAT-UUID\","
                + "\"path\":[\"aiAgentCreate\"],\"extensions\":{\"errorCode\":\"PROVIDER_ACCOUNT_NOT_FOUND\","
                + "\"parameters\":[\"INVALID-FORMAT-UUID\"],\"language\":\"" + getLanguageTag() + "\","
                + "\"classification\":\"DataFetchingException\"}}],\"data\":{\"aiAgentCreate\":null}}", response);
    }

    @Test
    @DisplayName("Test update AI agent is successful if version for that agent already exists")
    void testAiAgentUpdateForExistingVersion() throws Exception {
        AiAgentProviderAccount registryAccount = testUtil.saveAiAgentRegistryAccount("test-boomi",
                TestUtil.COMMON_ACCOUNT_ID, null, null, AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                AiAgentProviderAccountStatus.CONNECTED);

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_CREATE.getFileName());
        assertNotNull(queryStr);
        String registryAccountGuid = registryAccount.getGuid();
        queryStr = queryStr.replace("provider_account_id_variable", registryAccountGuid);

        String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        AiAgent aiAgent = TestUtil.parseGraphqlResponse(response,
                "aiAgentCreate", AiAgent.class);
        assertNotNull(aiAgent);
        assertNotNull(aiAgent.getId());
        assertEquals(1, aiAgent.getAgentVersions().size());

        // execute Update Agent mutation with same input version
        queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_UPDATE.getFileName());
        queryStr = queryStr
                .replace("agent_id_variable", aiAgent.getId())
                .replace("agent_version_id_variable", aiAgent.getAgentVersions().get(0).getId());
        response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        aiAgent = TestUtil.parseGraphqlResponse(response,
                "aiAgentUpdate", AiAgent.class);
        assertNotNull(aiAgent);
        assertNotNull(aiAgent.getId());
        assertEquals("Test Agent 1 - updated", aiAgent.getAgentVersions().get(0).getName());
        assertEquals(1, aiAgent.getAgentVersions().size());
    }

    @Test
    @DisplayName("Test update AI agent creates version if it does not exist")
    void testAiAgentUpdateForNewVersion() throws Exception {
        AiAgentProviderAccount registryAccount = testUtil.saveAiAgentRegistryAccount("test-boomi",
                TestUtil.COMMON_ACCOUNT_ID, null, null, AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                AiAgentProviderAccountStatus.CONNECTED);

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_CREATE.getFileName());
        assertNotNull(queryStr);
        String registryAccountGuid = registryAccount.getGuid();
        queryStr = queryStr.replace("provider_account_id_variable", registryAccountGuid);

        String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        AiAgent aiAgent = TestUtil.parseGraphqlResponse(response,
                "aiAgentCreate", AiAgent.class);
        assertNotNull(aiAgent);
        assertNotNull(aiAgent.getId());
        assertEquals(1, aiAgent.getAgentVersions().size());

        // execute Update Agent mutation with new input version
        queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_UPDATE.getFileName());
        queryStr = queryStr
                .replace("agent_id_variable", aiAgent.getId())
                .replace("Draft", "2.0")
                .replace("agent_version_id_variable", aiAgent.getAgentVersions().get(0).getId());
        response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        aiAgent = TestUtil.parseGraphqlResponse(response,
                "aiAgentUpdate", AiAgent.class);
        assertNotNull(aiAgent);
        assertNotNull(aiAgent.getId());
        assertEquals(2, aiAgent.getAgentVersions().size());
    }

    @Test
    @DisplayName("Test update AI Agent - non-exist AI Agent due to wrong idp accout id")
    void testNonExistAgentDueToIdpAccountIdForAiAgentUpdate() throws Exception {
        AiAgentProviderAccount registryAccount = testUtil.saveAiAgentRegistryAccount("test-boomi",
                TestUtil.COMMON_ACCOUNT_ID, null, null, AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                AiAgentProviderAccountStatus.CONNECTED);

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_CREATE.getFileName());
        assertNotNull(queryStr);
        String registryAccountGuid = registryAccount.getGuid();
        queryStr = queryStr.replace("provider_account_id_variable", registryAccountGuid);

        String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        AiAgent aiAgent = TestUtil.parseGraphqlResponse(response, "aiAgentCreate", AiAgent.class);
        assertNotNull(aiAgent);
        assertNotNull(aiAgent.getId());
        assertEquals(1, aiAgent.getAgentVersions().size());

        // execute Update Agent mutation with a different idp-account-id
        queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_UPDATE.getFileName());
        queryStr = queryStr
                .replace("agent_id_variable", aiAgent.getId())
                .replace("agent_version_id_variable", aiAgent.getAgentVersions().get(0).getId());
        response = executor.executeAtomSphereQuery(queryStr, ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES,
                TestUtil.COMMON_USER_NAME, CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertEquals("{\"errors\":[{\"message\":\""
                + com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.AI_AGENT_NOT_FOUND.getDetail()
                + "\",\"path\":[\"aiAgentUpdate\"]," + "\"extensions\":{\"errorCode\":\"AI_AGENT_NOT_FOUND\","
                + "\"language\":\"" + getLanguageTag() + "\",\"classification\":\"DataFetchingException\"}}],"
                + "\"data\":{\"aiAgentUpdate\":null}}", response);
    }

    @Test
    @DisplayName("Test add trust level for the ai agent version")
    void testAiAgentVersionTrustLevelAdd() throws Exception {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_VERSION_TRUST_LEVEL.getFileName());

        assertNotNull(queryStr);

        queryStr = queryStr.replace("agent-version-id", aiAgentVersion.getGuid());

        String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        final com.boomi.graphql.server.schema.types.AiAgentVersion updatedAiAgentVersion =
                TestUtil.parseGraphqlResponse(
                        response, "aiAgentVersionTrustLevelAdd",
                        com.boomi.graphql.server.schema.types.AiAgentVersion.class);

        assertNotNull(updatedAiAgentVersion);
        assertEquals(updatedAiAgentVersion.getId(), aiAgentVersion.getGuid());
        assertEquals(AiAgentRegistryTrustLevel.UNENDORSED, updatedAiAgentVersion.getTrustLevel());
    }

    @Test
    @DisplayName("Test validation for add trust level for the ai agent version")
    void testValidationForAiAgentVersionTrustLevelAdd() throws Exception {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_VERSION_TRUST_LEVEL.getFileName());

        assertNotNull(queryStr);

        queryStr = queryStr.replace("agent-version-id", "b873a5c1-c96c-44c2-94d2-376f6f203d3e");

        String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertEquals(
                "{\"errors\":[{\"message\":\"The specified AI Agent version ID does not correspond to any existing AI"
                        + " Agent version in the registry. Please verify that the ID is correct and that the agent "
                        + "version exists.\",\"path\":[\"aiAgentVersionTrustLevelAdd\"],"
                        + "\"extensions\":{\"errorCode\":\"AI_AGENT_VERSION_NOT_FOUND\",\"language\":\""
                        + getLanguageTag() + "\"," + "\"classification\":\"DataFetchingException\"}}],\"data\":null}",
                response);

        queryStr = queryStr.replace("b873a5c1-c96c-44c2-94d2-376f6f203d3e", "");

        response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertEquals("{\"errors\":[{\"message\":\"Failed to add trust level for the provided agent version \","
                + "\"path\":[\"aiAgentVersionTrustLevelAdd\"],"
                + "\"extensions\":{\"errorCode\":\"FAILED_TO_ADD_TRUST_LEVEL\",\"parameters\":[\"\"],"
                + "\"language\":\"" + getLanguageTag()
                + "\",\"classification\":\"DataFetchingException\"}}],\"data\":null}", response);
    }

    @Test
    @DisplayName("Test non agent owner for add trust level for the ai agent version")
    void testNonAgentOwnerForAiAgentVersionTrustLevelAdd() throws Exception {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_VERSION_TRUST_LEVEL.getFileName());

        assertNotNull(queryStr);

        queryStr = queryStr.replace("agent-version-id", aiAgentVersion.getGuid());

        String response = executor.executeAtomSphereQuery(queryStr, ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES,
                TestUtil.COMMON_USER_NAME, CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertEquals(
                "{\"errors\":[{\"message\":\"The specified AI Agent version ID does not correspond to any existing AI"
                        + " Agent version in the registry. Please verify that the ID is correct and that the agent "
                        + "version exists.\",\"path\":[\"aiAgentVersionTrustLevelAdd\"],"
                        + "\"extensions\":{\"errorCode\":\"AI_AGENT_VERSION_NOT_FOUND\",\"language\":\""
                        + getLanguageTag() + "\"," + "\"classification\":\"DataFetchingException\"}}],\"data\":null}",
                response);
    }

    @Test
    @DisplayName("Test aiAgentVersionEnable mutation - successfully enable agent version case")
    @Transactional
    public void testAiAgentVersionEnableSuccess() throws Exception {
        // Mock Garden agent install success response
        AgentInstallResponse mockInstallResponse = AgentInstallResponse.builder().success(true).message(
                "Agent installed successfully").agentId("version-external-id").agentStatus("ACTIVE").build();
        when(gardenService.installAgent(any(), anyString())).thenReturn(Mono.just(mockInstallResponse));

        // Execute the agent version enable mutation
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_VERSION_ENABLE_MUTATION.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("agent-version-id", aiAgentVersion.getGuid());
        queryStr = queryStr.replace("\"enable-indicator\"", "true");

        String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        // Verify
        assertNotNull(response);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(response);
        JsonNode errorsNode = jsonNode.path("errors");
        assertTrue(errorsNode.isMissingNode() || errorsNode.isEmpty(), "GraphQL response should not contain errors");
        String agentStatus = jsonNode.path("data").path("aiAgentVersionEnable").path("agentStatus").asText();
        assertEquals("ACTIVE", agentStatus);
    }

    @Test
    @DisplayName("Test aiAgentVersionEnable mutation - successfully disable agent version case")
    @Transactional
    public void testAiAgentVersionDisableSuccess() throws Exception {
        // Mock Garden agent uninstall success response
        AgentUninstallResponse mockUninstallResponse = AgentUninstallResponse.builder().success(true).message(
                "Agent " + "uninstalled successfully").agentId("version-external-id").agentStatus("DISABLE").build();
        when(gardenService.uninstallAgent(any(), anyString())).thenReturn(Mono.just(mockUninstallResponse));

        // Execute the agent version disable mutation
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_VERSION_ENABLE_MUTATION.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("agent-version-id", aiAgentVersion.getGuid());
        queryStr = queryStr.replace("\"enable-indicator\"", "false");

        String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        // Verify
        assertNotNull(response);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(response);
        JsonNode errorsNode = jsonNode.path("errors");
        assertTrue(errorsNode.isMissingNode() || errorsNode.isEmpty(), "GraphQL response should not contain errors");
        String agentStatus = jsonNode.path("data").path("aiAgentVersionEnable").path("agentStatus").asText();
        assertEquals("DISABLE", agentStatus);
    }

    @Test
    @DisplayName("Test aiAgentVersionEnable mutation - failed to enable agent version case")
    @Transactional
    public void testAiAgentVersionEnableFailure() throws Exception {
        // Mock Garden agent install failed response
        AgentInstallResponse mockInstallResponse = AgentInstallResponse.builder().success(false).error(
                "Agent must be in 'DRAFT' or 'DISABLED' state to be installed").build();
        when(gardenService.installAgent(any(), anyString())).thenReturn(Mono.just(mockInstallResponse));

        // Execute the agent version enable mutation
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_VERSION_ENABLE_MUTATION.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("agent-version-id", aiAgentVersion.getGuid());
        queryStr = queryStr.replace("\"enable-indicator\"", "true");

        String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        // Verify
        assertNotNull(response);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(response);
        JsonNode errorsNode = jsonNode.path("errors");
        assertNotNull(errorsNode);
        String resultErrorMessage = errorsNode.get(0).path("message").asText();
        String expectedErrorMessage = MessageFormat.format(FAILED_TO_ENABLE_AGENT_VERSION.getDetail(),
                aiAgentVersion.getGuid());
        assertEquals(expectedErrorMessage, resultErrorMessage);
        String agentStatus = jsonNode.path("data").path("aiAgentVersionEnable").path("agentStatus").asText();
        assertEquals(aiAgentVersion.getAgentStatus(), agentStatus);
    }

    @Test
    @DisplayName("Test aiAgentVersionEnable mutation - failed to disable agent version case")
    @Transactional
    public void testAiAgentVersionDisableFailure() throws Exception {
        // Mock Garden agent uninstall failed response
        AgentUninstallResponse mockUninstallResponse = AgentUninstallResponse.builder().success(false).error(
                "Agent must be in 'ACTIVE' state to be uninstalled").build();
        when(gardenService.uninstallAgent(any(), anyString())).thenReturn(Mono.just(mockUninstallResponse));

        // Execute the agent version enable mutation
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_VERSION_ENABLE_MUTATION.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("agent-version-id", aiAgentVersion.getGuid());
        queryStr = queryStr.replace("\"enable-indicator\"", "false");

        String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        // Verify
        assertNotNull(response);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(response);
        JsonNode errorsNode = jsonNode.path("errors");
        assertNotNull(errorsNode);
        String resultErrorMessage = errorsNode.get(0).path("message").asText();
        String expectedErrorMessage = MessageFormat.format(FAILED_TO_DISABLE_AGENT_VERSION.getDetail(),
                aiAgentVersion.getGuid());
        assertEquals(expectedErrorMessage, resultErrorMessage);
        String agentStatus = jsonNode.path("data").path("aiAgentVersionEnable").path("agentStatus").asText();
        assertEquals(aiAgentVersion.getAgentStatus(), agentStatus);
    }
}
