package com.boomi.aiagentregistry.resolver.it.query;

import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.util.*;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class AiAgentListingRefreshTrackerQueryResolverImplTest extends BaseMockWebServerTest {

    @Autowired
    private TestWebFluxGraphQLExecutor _executor;

    @Autowired
    private TestUtil _testUtil;

    @Test
    @Transactional
    @DisplayName("Should successfully execute query to fetch  AI agent listing refresh tracker")
    public void testAiAgentListingRefreshTracker() throws Exception {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LISTING_REFRESH_TRACKER.getFileName());

        assertNotNull(queryStr);

        final String response = _executor.executeAtomSphereQuery(queryStr, _testUtil.ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertEquals("{\"data\":{\"aiAgentListingRefreshTracker\":" +
                "{\"viewName\":\"\",\"lastRefreshStart\":\"1970-01-01T00:00:00.000Z\"," +
                "\"status\":\"IDLE\"}}}", response);
    }

}
