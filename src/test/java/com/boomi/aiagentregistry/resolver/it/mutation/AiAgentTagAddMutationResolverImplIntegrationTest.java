// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.resolver.it.mutation;

import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentTag;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.util.AiAgentTestBuilder;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphQLQueriesEnum;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.JsonNode;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.Set;
import java.util.UUID;

import static com.boomi.aiagentregistry.util.TestUtil.COMMON_EXTERNAL_ID;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_CREDENTIALS;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_METADATA;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class AiAgentTagAddMutationResolverImplIntegrationTest extends BaseMockWebServerTest {


    @Autowired
    private TestWebFluxGraphQLExecutor executor;

    @Autowired
    private TestUtil testUtil;

    // This setup helps in getting the testing  port that changes dynamically
    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry r) throws IOException {
        r.add("api.boomi.platform.url", () -> "http://localhost:" + _mockWebServer.getPort());
    }

    public static com.boomi.aiagentregistry.entity.AiAgentProviderAccount aiAgentProviderAccount;
    public static AiAgent aiAgent;
    public static AiAgentVersion aiAgentVersion;

    @BeforeEach
    public void setup() {
        if (aiAgentProviderAccount == null) {
            aiAgentProviderAccount = testUtil.saveAiAgentRegistryAccount("boomi-test-b", TestUtil.COMMON_ACCOUNT_ID,
                    PROVIDER_ACCOUNT_METADATA,
                    PROVIDER_ACCOUNT_CREDENTIALS,
                    AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                    AiAgentProviderAccountStatus.CONNECTED);
        }
        // create Agent
        if (aiAgent == null && aiAgentProviderAccount != null) {
            aiAgent = testUtil.createAiAgent(aiAgentProviderAccount, GuidUtil.createAIAgentGuid(), COMMON_EXTERNAL_ID,
                    false);
        }

        // create Agent version
        if (aiAgentVersion == null && aiAgent != null) {
            aiAgentVersion = testUtil.createAiAgentVersion(aiAgent, GuidUtil.createAIAgentVersionGuid(),
                    "test-ai-agent-version", "1.0", "RUNNING", "Instructions", false, null);
        }

        associateTagWithVersion("existing-key", "existing-value");
    }

    @Test
    @DisplayName("Test Sync Tag Mutation")
    public void addTagTest() throws IOException {
        assertNotNull(aiAgentProviderAccount);
        assertNotNull(aiAgent);
        assertNotNull(aiAgentVersion);

        String refEntityId = aiAgentVersion.getGuid();

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_TAG_SYNC_MUTATION.getFileName());
        assert queryStr != null;
        queryStr = queryStr.replace("related_entity_ref_id", refEntityId);

        queryStr = queryStr.replace("\"related_entity_ref_type\"", AiRegistryEntityType.VERSION.name());

        assertNotNull(queryStr);

        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertNotNull(response);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        JsonNode relatedEntityTypeNode = rootNode.get("data").get("aiAgentAddTags").get("tags").get(0).get(
                "relatedEntityType");
        JsonNode relatedEntityRefIdNode = rootNode.get("data").get("aiAgentAddTags").get("tags").get(0).get("relatedEntityRefId");
        assertTrue(isValidUUID(relatedEntityRefIdNode.textValue()));
        assertEquals(relatedEntityTypeNode.textValue(), AiRegistryEntityType.VERSION.name());

        JsonNode versionTagNode = rootNode.get("data").get("aiAgentAddTags").get("tags").get(0).get("tags");
        assertNotNull(versionTagNode);
        assertEquals(2, versionTagNode.size());
        Set<String> versionTags =  Set.of(versionTagNode.get(0).get("key").asText(), versionTagNode.get(1).get("key").asText());
        assertEquals(Set.of("buddy", "hello"), versionTags);
    }

    @Test
    @DisplayName("Test Sync Tag Mutation by a non agent owner")
    public void addTagByNonAgentOwnerTest() throws IOException {
        assertNotNull(aiAgentProviderAccount);
        assertNotNull(aiAgent);
        assertNotNull(aiAgentVersion);

        String refEntityId = aiAgentVersion.getGuid();

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_TAG_SYNC_MUTATION.getFileName());
        assert queryStr != null;
        queryStr = queryStr.replace("related_entity_ref_id", refEntityId);

        queryStr = queryStr.replace("\"related_entity_ref_type\"", AiRegistryEntityType.VERSION.name());

        assertNotNull(queryStr);

        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertNotNull(response);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        JsonNode versionTagNode = rootNode.get("data")
                                          .get("aiAgentAddTags")
                                          .get("tags")
                                          .get(0)
                                          .get("tags");
        assertTrue(versionTagNode.isNull());
    }

    private boolean isValidUUID(String uuidString) {
        if (uuidString == null || uuidString.isEmpty()) {
            return false;
        }

        try {
            UUID.fromString(uuidString);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    private void associateTagWithVersion(String key, String value) {
        // create Agent Tag
        AiAgentTestBuilder.TagParams tagParams = AiAgentTestBuilder.TagParams.builder()
                       .guid(GuidUtil.createAIAgentTagGuid())
                       .providerAccount(aiAgentProviderAccount)
                       .idpAccountId(aiAgentProviderAccount.getIdpAccountId())
                       .key(key)
                       .value(value)
                       .build();
        AiAgentTag aiAgentTag = testUtil.createAiAgentTag(tagParams);

        // create Agent Tag Association with Version
        AiAgentTestBuilder.TagAssociationParams tagAssociationParams = AiAgentTestBuilder.TagAssociationParams.builder()
                       .guid(GuidUtil.createAIAgentTagAssociationGuid())
                       .tagUid(aiAgentTag.getUid())
                       .relatedEntityUid(aiAgentVersion.getUid())
                       .relatedEntityType(AiRegistryEntityType.VERSION)
                       .build();
        testUtil.createAiAgentTagAssociation(tagAssociationParams);
    }
}
