// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.resolver.it.mutation;

import software.amazon.awssdk.services.oam.OamClient;
import software.amazon.awssdk.services.oam.model.PutSinkPolicyRequest;
import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.config.MockOamClientTestConfig;
import com.boomi.aiagentregistry.config.TaskExecutorTestConfiguration;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.repo.AiAgentRepository;
import com.boomi.aiagentregistry.service.FeatureManager;
import com.boomi.aiagentregistry.service.SecretsManagerService;
import com.boomi.aiagentregistry.service.auth.AuthorizationParsingService;
import com.boomi.aiagentregistry.service.auth.AwsAssumeRoleAuthorizationParserStrategy;
import com.boomi.aiagentregistry.service.s3.FileStorageService;
import com.boomi.aiagentregistry.util.BedrockTestUtil;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.task.TaskExecutor;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;
import java.util.Optional;

import static com.boomi.aiagentregistry.util.GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT_DELETE;
import static com.boomi.aiagentregistry.util.GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT_FOR_BEDROCK_ASSUME_ROLE_SUCCESS;
import static com.boomi.aiagentregistry.util.GraphQLQueriesEnum.AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = { TestApplication.class, TaskExecutorTestConfiguration.class, MockOamClientTestConfig.class }
        , webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
class AiAgentProviderAccountMutationResolverImplIntegrationSetTwoTest extends BaseMockWebServerTest {

    private static final String GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE = "aiAgentProviderAccountCreate";
    private static final String GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CONFIRM = "aiAgentCloudProviderAccountConfirm";
    private static final String GRAPHQL_OPERATION_PROVIDER_ACCOUNT_UPDATE = "aiAgentProviderAccountUpdate";
    private static final String GRAPHQL_OPERATION_PROVIDER_ACCOUNT_DELETE = "aiAgentProviderAccountDelete";

    @Value("${boomi.services.aiagentregistry.monitoring.account.id}")
    String monitoringAccountId;

    @Autowired
    private TestWebFluxGraphQLExecutor executor;

    @Autowired
    private TestUtil testUtil;

    @Autowired
    private BedrockTestUtil _bedrockTestUtil;

    @Autowired
    private AiAgentProviderAccountRepository _providerAccountRepository;

    @Autowired
    AiAgentRepository _aiAgentRepository;

    @Qualifier("testTaskExecutor")
    @Autowired
    private TaskExecutor taskExecutor;

    @Autowired
    private ObjectMapper _objectMapper;

    @Autowired
    private OamClient mockOamClient;

    @Autowired
    private String s3Bucket;

    @Captor
    private ArgumentCaptor<String> _argCaptorForSecret;

    @Captor
    private ArgumentCaptor<PutSinkPolicyRequest> _argCaptorForPutSinkPolicyRequest;

    @Captor ArgumentCaptor<byte[]> _argCaptorForS3File;

    // This setup helps in getting the testing port that changes dynamically
    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry r) throws IOException {
        r.add("api.boomi.platform.url", () -> "http://localhost:" + _mockWebServer.getPort());
    }

    // used spybean instead of mockbean so that only method(s) of the bean needed for the test are mocked
    @SpyBean
    private AuthorizationParsingService spyAuthorizationParsingService;
    @SpyBean
    private AwsAssumeRoleAuthorizationParserStrategy spyAwsAssumeRoleAuthorizationParserStrategy;
    @SpyBean
    private SecretsManagerService spySecretsManagerService;
    @SpyBean
    private FeatureManager spyFeatureManager;
    @SpyBean
    private FileStorageService spyFileStorageService;

    @BeforeEach
    public void setup() throws MalformedURLException {
        when(spyAuthorizationParsingService.requiresRegionCheck(any()))
                .thenReturn(true);
        doReturn(Optional.of(new URL("https://example.com")))
                .when(spyFileStorageService).putSmallObject(any(), any(), any());
        doReturn("https://example.com/presigned")
                .when(spyFileStorageService).createPresignedGetUrl(any(), any(), anyLong());
    }

    @AfterEach
    public void tearDown() {
        deleteAllProviderAccounts();
    }

    private void deleteAllProviderAccounts() {
        List<AiAgentProviderAccount> providerAccounts = _providerAccountRepository.findAll();
        providerAccounts.forEach(this::deleteProviderAccount);
        _providerAccountRepository.flush();
    }

    private boolean deleteProviderAccount(AiAgentProviderAccount providerAccount) {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_DELETE.getFileName());
        assertNotNull(queryStr);
        String providerAccountDeleteQueryString = queryStr
                .replace("ai-agent-provider-account-guid-variable", providerAccount.getGuid());
        try {
            executor.executeAtomSphereQuery(providerAccountDeleteQueryString,
                    TestUtil.COMMON_ACCOUNT_ID,
                    CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                    CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @Test
    @DisplayName("Create Mutation for AI agent provider account - duplicate account check with "
            + "allowOneExternalAccountWithOneIdpAccountOneAuthType property true")
    void testAiAgentProviderAccountCreateChecksForDuplicateAccount_with_allowOneExternalAccountWithOneIdpAccountOneAuthType_true()
            throws Exception {
        deleteAllProviderAccounts();
        doReturn(true).when(spyFeatureManager)
                      .isOneExternalAccountWithOneIdpAccountOneAuthType();
        when(spyAuthorizationParsingService.isDoCheckIfExternalAccountLinkedToAnotherProviderAccount(
                AiProviderAuthSchema.AWS_ASSUME_ROLE)).thenReturn(true);
        doReturn(true).when(spyFeatureManager)
                      .isDoProvisioningWithAssumeRole();

        // create BOOMIAPITOKEN provider account
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS.getFileName());
        assertNotNull(queryStr);
        final String boomiProviderAccountInIdpAccountOneResponse = executor.executeAtomSphereQuery(queryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount boomiProviderAccountInIdpAccountOne =
                TestUtil.parseGraphqlResponse(boomiProviderAccountInIdpAccountOneResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(boomiProviderAccountInIdpAccountOne, boomiProviderAccountInIdpAccountOneResponse);
        assertNotNull(boomiProviderAccountInIdpAccountOne.getId());

        //  creating the same BOOMIAPITOKEN provider account in a different Registry account should NOT work
        queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS.getFileName());
        final String boomiProviderAccountInIdpAccountTwoResponse = executor.executeAtomSphereQuery(queryStr,
                "test-different-acId", CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount boomiProviderAccountInIdpAccountTwo =
                TestUtil.parseGraphqlResponse(boomiProviderAccountInIdpAccountTwoResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(boomiProviderAccountInIdpAccountTwo);
        assertTrue(boomiProviderAccountInIdpAccountTwoResponse.contains(
                "\"errorCode\":\"PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT\""));

        // create AWS_ASSUME_ROLE provider account
        queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BEDROCK_ASSUME_ROLE_SUCCESS.getFileName());
        assertNotNull(queryStr);
        final String awsAssumeRoleProviderAccountInIdpAccountOneResponse = executor.executeAtomSphereQuery(queryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount awsAssumeRoleProviderAccountInIdpAccountOne =
                TestUtil.parseGraphqlResponse(awsAssumeRoleProviderAccountInIdpAccountOneResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(awsAssumeRoleProviderAccountInIdpAccountOne, awsAssumeRoleProviderAccountInIdpAccountOneResponse);
        assertNotNull(awsAssumeRoleProviderAccountInIdpAccountOne.getId());

        //  creating the same AWS_ASSUME_ROLE provider account with different region in a different Registry account
        //  should NOT work
        queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BEDROCK_ASSUME_ROLE_SUCCESS.getFileName());
        queryStr = queryStr.replace("us-east-1", "us-west-2");
        final String awsAssumeRoleProviderAccountInIdpAccountTwoResponse = executor.executeAtomSphereQuery(queryStr,
                "test-different-acId", CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount awsAssumeRoleProviderAccountInIdpAccountTwo =
                TestUtil.parseGraphqlResponse(awsAssumeRoleProviderAccountInIdpAccountTwoResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(awsAssumeRoleProviderAccountInIdpAccountTwo);
        assertTrue(awsAssumeRoleProviderAccountInIdpAccountTwoResponse.contains(
                "\"errorCode\":\"PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT\""));

        //  creating the same AWS_ASSUME_ROLE provider account with the same region in a different Registry account
        //  should NOT work
        queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BEDROCK_ASSUME_ROLE_SUCCESS.getFileName());
        final String awsAssumeRoleProviderAccountInIdpAccountThreeResponse = executor.executeAtomSphereQuery(queryStr,
                "test-different-acId", CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount awsAssumeRoleProviderAccountInIdpAccountThree =
                TestUtil.parseGraphqlResponse(awsAssumeRoleProviderAccountInIdpAccountThreeResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(awsAssumeRoleProviderAccountInIdpAccountThree);
        assertTrue(awsAssumeRoleProviderAccountInIdpAccountThreeResponse.contains(
                "\"errorCode\":\"PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT\""));

        //  creating the same AWS_ASSUME_ROLE provider account with the same region in the same Registry account
        //  should NOT work
        queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BEDROCK_ASSUME_ROLE_SUCCESS.getFileName());
        final String awsAssumeRoleProviderAccountInIdpAccountFourResponse = executor.executeAtomSphereQuery(queryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount awsAssumeRoleProviderAccountInIdpAccountFour =
                TestUtil.parseGraphqlResponse(awsAssumeRoleProviderAccountInIdpAccountFourResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNull(awsAssumeRoleProviderAccountInIdpAccountFour);
        assertTrue(awsAssumeRoleProviderAccountInIdpAccountFourResponse.contains(
                "\"errorCode\":\"PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT\""));

        //  creating the same AWS_ASSUME_ROLE provider account with a different region in the same Registry account
        //  should work
        queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.MUTATION.getGraphqlType(),
                AI_AGENT_PROVIDER_ACCOUNT_FOR_BEDROCK_ASSUME_ROLE_SUCCESS.getFileName());
        queryStr = queryStr.replace("us-east-1", "us-west-2");
        final String awsAssumeRoleProviderAccountInIdpAccountFiveResponse = executor.executeAtomSphereQuery(queryStr,
                TestUtil.COMMON_ACCOUNT_ID, CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount awsAssumeRoleProviderAccountInIdpAccountFive =
                TestUtil.parseGraphqlResponse(awsAssumeRoleProviderAccountInIdpAccountFiveResponse,
                        GRAPHQL_OPERATION_PROVIDER_ACCOUNT_CREATE,
                        com.boomi.graphql.server.schema.types.AiAgentProviderAccount.class);
        assertNotNull(awsAssumeRoleProviderAccountInIdpAccountFive);
    }
}
