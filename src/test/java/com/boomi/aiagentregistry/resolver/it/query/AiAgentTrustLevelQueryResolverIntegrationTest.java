// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.resolver.it.query;

import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphQLQueriesEnum;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiAgentRegistryTrustLevel;
import com.boomi.graphql.server.schema.types.AiAgentTrustLevelQueryResponse;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.JsonNode;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.List;
import java.util.UUID;

import static com.boomi.aiagentregistry.util.TestUtil.COMMON_EXTERNAL_ID;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_CREDENTIALS;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_METADATA;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class AiAgentTrustLevelQueryResolverIntegrationTest extends BaseMockWebServerTest {

    @Autowired
    private TestWebFluxGraphQLExecutor executor;

    @Autowired
    private TestUtil testUtil;

    // This setup helps in getting the testing port that changes dynamically
    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry r) throws IOException {
        r.add("api.boomi.platform.url", () -> "http://localhost:" + _mockWebServer.getPort());
    }

    private static AiAgentProviderAccount aiAgentProviderAccount;
    private static AiAgentProviderAccount aiAgentProviderAccountWithAnotherIdp;
    private static AiAgent aiAgentOne;
    private static AiAgent aiAgentTwo;

    @Test
    @DisplayName("Should throw an error if the specified provider account does not exist")
    @Transactional
    public void testAiAgentTrustLevelForNonExistingProviderAccount() throws IOException {
        testInvalidProviderAccount(UUID.randomUUID().toString(), AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_NOT_FOUND);
    }

    @Test
    @DisplayName("Should throw an error if the specified provider account id is invalid")
    @Transactional
    public void testAiAgentTrustLevelForInvalidProviderAccountId() throws IOException {
        testInvalidProviderAccount("Invalid Id", AiAgentRegistryErrorCode.INVALID_PROVIDER_ACCOUNT_ID);
    }

    private void testInvalidProviderAccount(String providerAccountId, AiAgentRegistryErrorCode errorCode) throws IOException {
        // Given
        String expectedErrorMessage = MessageFormat.format(errorCode.getDetail(), providerAccountId);
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_TRUST_LEVEL.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("provider_account_id_variable", providerAccountId);

        // When
        String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        // Then
        assertNotNull(response);
        AiAgentTrustLevelQueryResponse trustLevelResponse = TestUtil.parseGraphqlResponse(response,
                "aiAgentTrustLevel", AiAgentTrustLevelQueryResponse.class);
        assertNull(trustLevelResponse);
        validateErrorResponse(response, errorCode, expectedErrorMessage);
    }

    private void validateErrorResponse(String response, AiAgentRegistryErrorCode errorCode, String expectedMessage) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        String actualErrorCode = rootNode.get("errors").get(0).get("extensions").get("errorCode").asText();
        String actualMessage = rootNode.get("errors").get(0).get("message").asText();

        assertNotNull(actualErrorCode);
        assertTrue(actualErrorCode.contains(errorCode.toString()));
        assertNotNull(actualMessage);
        assertTrue(actualMessage.contains(expectedMessage));
    }

    @Test
    @DisplayName("Should successfully fetch the trust level metrics for the specified provider account")
    @Transactional
    public void testAiAgentTrustLevel() throws IOException {
        // Given
        setUpProviderAccountOne();
        assertNotNull(aiAgentProviderAccount);
        assertNotNull(aiAgentOne);

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_TRUST_LEVEL.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("provider_account_id_variable", aiAgentProviderAccount.getGuid());

        // When
        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        // Then
        verifySuccessResponse(response);
    }

    @Test
    @DisplayName("Should successfully fetch the trust level metrics for the idpAccount when providerAccountId is not specified")
    @Transactional
    public void testAiAgentTrustLevelForOptionalProviderAccountId() throws IOException {
        // Given
        setupProviderAccountTwo();
        assertNotNull(aiAgentProviderAccountWithAnotherIdp);
        assertNotNull(aiAgentTwo);

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_TRUST_LEVEL.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("providerAccountId: \"provider_account_id_variable\"", "");

        // When
        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        // Then
        verifySuccessResponse(response);
    }

    private void verifySuccessResponse(String response) throws IOException {
        assertNotNull(response);
        AiAgentTrustLevelQueryResponse aiAgentTrustLevelQueryResponse = TestUtil.parseGraphqlResponse(response,
                "aiAgentTrustLevel", AiAgentTrustLevelQueryResponse.class);

        assertNotNull(aiAgentTrustLevelQueryResponse);
        assertEquals(4, aiAgentTrustLevelQueryResponse.getTotalAgents());
        assertEquals(2, aiAgentTrustLevelQueryResponse.getEndorsedAgents());
        assertEquals(1, aiAgentTrustLevelQueryResponse.getUnendorsedAgents());
        assertEquals(1, aiAgentTrustLevelQueryResponse.getDeprecatedAgents());
    }

    private void setUpProviderAccountOne() {
        // Create Provider Account
        if (aiAgentProviderAccount == null) {
            aiAgentProviderAccount = testUtil.saveAiAgentRegistryAccount("boomi-test-b", TestUtil.COMMON_ACCOUNT_ID,
                    PROVIDER_ACCOUNT_METADATA,
                    PROVIDER_ACCOUNT_CREDENTIALS,
                    AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                    AiAgentProviderAccountStatus.CONNECTED);
        }

        // Create Agent
        if (aiAgentOne == null && aiAgentProviderAccount != null) {
            aiAgentOne = testUtil.createAiAgent(aiAgentProviderAccount, GuidUtil.createAIAgentGuid(), COMMON_EXTERNAL_ID,
                    false);
            createVersions(aiAgentOne);
        }
    }

    private void setupProviderAccountTwo() {
        // Create Provider Account with another idp
        if (aiAgentProviderAccountWithAnotherIdp == null) {
            aiAgentProviderAccountWithAnotherIdp = testUtil.saveAiAgentRegistryAccount("boomi-test-c", TestUtil.ACCOUNT_ID,
                    PROVIDER_ACCOUNT_METADATA,
                    PROVIDER_ACCOUNT_CREDENTIALS,
                    AiAgentProviderType.BOOMI, AiProviderAuthSchema.APITOKEN,
                    AiAgentProviderAccountStatus.CONNECTED);
        }

        // Create Agent
        if (aiAgentTwo == null && aiAgentProviderAccountWithAnotherIdp != null) {
            aiAgentTwo = testUtil.createAiAgent(aiAgentProviderAccountWithAnotherIdp, GuidUtil.createAIAgentGuid(), COMMON_EXTERNAL_ID,
                    false);
            createVersions(aiAgentTwo);
        }
    }

    private void createVersions(AiAgent aiAgent) {
        for (AiAgentRegistryTrustLevel level : List.of(
                AiAgentRegistryTrustLevel.ENDORSED,
                AiAgentRegistryTrustLevel.UNENDORSED,
                AiAgentRegistryTrustLevel.DEPRECATED,
                AiAgentRegistryTrustLevel.ENDORSED)) {

            AiAgentVersion version = testUtil.createAiAgentVersion(aiAgent, GuidUtil.createAIAgentVersionGuid(),
                    "test-ai-agent-version", "1.0", "RUNNING", "Instructions",
                    false, null);
            version.setTrustLevel(level);
        }
    }
}