// Copyright (c) 2025 Boomi, Inc.
package com.boomi.aiagentregistry.resolver.unit;

import com.boomi.aiagentregistry.repo.AgentListingViewRepository;
import com.boomi.aiagentregistry.repo.AiAgentListingRepository;
import com.boomi.aiagentregistry.resolver.query.AiAgentRegistryInvocationsMetricsResolverImpl;
import com.boomi.aiagentregistry.util.UserUtil;
import com.boomi.graphql.server.schema.types.AiAgentListing;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentRegistryAgentMetric;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class AiAgentRegistryRegionQueryResolverImplTest {

    @Test
    void testBuildCompositeLookupKeys_deduplicatesEqualLists() throws Exception {
        // Given
        String idpAccountId = "test-account";

        AiAgentListing listing1 = new AiAgentListing();
        listing1.setAliasExternalId(null);
        listing1.setAgentExternalId("agent-123");
        listing1.setProviderType(AiAgentProviderType.BOOMI);
        listing1.setProviderAccountGuId("pa-001");

        AiAgentListing listing2 = new AiAgentListing();
        listing2.setAliasExternalId(null);
        listing2.setAgentExternalId("agent-123");
        listing2.setProviderType(AiAgentProviderType.BOOMI);
        listing2.setProviderAccountGuId("pa-001");

        AiAgentRegistryAgentMetric metric1 = new AiAgentRegistryAgentMetric();
        metric1.setAiAgentListing(listing1);

        AiAgentRegistryAgentMetric metric2 = new AiAgentRegistryAgentMetric();
        metric2.setAiAgentListing(listing2);

        List<AiAgentRegistryAgentMetric> input = List.of(metric1, metric2);

        UserUtil userUtil = Mockito.mock(UserUtil.class);
        AiAgentListingRepository aiAgentListingRepository = Mockito.mock(AiAgentListingRepository.class);
        AgentListingViewRepository agentListingViewRepository = Mockito.mock(AgentListingViewRepository.class);
        ObjectMapper objectMapper = new ObjectMapper();

        // When
        AiAgentRegistryInvocationsMetricsResolverImpl invocationsResolver =
                new AiAgentRegistryInvocationsMetricsResolverImpl(userUtil, aiAgentListingRepository, agentListingViewRepository,objectMapper);
        Set<List<String>> result = invokeBuildCompositeLookupKeys(invocationsResolver, input, idpAccountId);

        // Then
        // Verify that the two identical key lists are deduplicated into one
        assertEquals(1, result.size());
    }

    @SuppressWarnings("unchecked")
    private Set<List<String>> invokeBuildCompositeLookupKeys(Object target, Collection<AiAgentRegistryAgentMetric> input, String idpAccountId) throws Exception {
        Method method = target.getClass()
                .getDeclaredMethod("buildCompositeLookupKeys", Collection.class, String.class);
        method.setAccessible(true);
        return (Set<List<String>>) method.invoke(target, input, idpAccountId);
    }
}
