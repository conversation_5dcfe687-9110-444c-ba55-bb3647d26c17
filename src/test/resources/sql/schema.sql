CREATE TABLE IF NOT EXISTS scheduled_tasks (
                                               task_name <PERSON><PERSON><PERSON><PERSON>(100),
    task_instance VARCHAR(100),
    task_data BINARY LARGE OBJECT,  -- Changed from BLOB
    execution_time TIMESTAMP(9) WITH TIME ZONE,
                                    picked <PERSON><PERSON><PERSON><PERSON><PERSON>,
                                    picked_by VA<PERSON>HA<PERSON>(50),
    last_success TIMESTAMP(9) WITH TIME ZONE,
                                    last_failure TIMESTAMP(9) WITH TIME ZONE,
                                    consecutive_failures INT,
                                    last_heartbeat TIMESTAMP(9) WITH TIME ZONE,
                                    version BIGINT,
                                    PRIMARY KEY (task_name, task_instance)
    );

CREATE INDEX IF NOT EXISTS idx_execution_time ON scheduled_tasks (execution_time);
CREATE INDEX IF NOT EXISTS idx_picked ON scheduled_tasks (picked);

