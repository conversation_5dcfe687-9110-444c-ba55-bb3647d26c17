query AiAgentRegistryInvocationsMetrics {
    aiAgentRegistryInvocationsMetrics(input: {
          requestTsStart: "2025-02-26T00:01:00"
          requestTsEnd: "2025-03-01T00:00:00"
          providerTypes: [providerTypesVariable]
          providerAccountIds: ["providerAccountsVariable"]
          modelIds: ["claude4", "llmV2"]
        }) {
            averageTime
            totalInvocations
            aiAgentListing {
                agentId
                agentExternalId
                version
                versionId
                versionExternalId
                versionName
                aliasId
                aliasExternalId
                aliasName
                providerAccountGuId
                providerType
                providerAccountName
                agentIsDeleted
                versionIsDeleted
                aliasIsDeleted
            }
        }
}