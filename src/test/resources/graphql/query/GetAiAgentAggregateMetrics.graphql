query {
    aiAgentRegistryAggregateMetrics(input: {
         requestTsStart: "2025-03-07T00:00:00Z"
         requestTsEnd: "2025-03-08T23:59:59Z"
         providerTypes: [AWS_BEDROCK]

    }) {
           activeAgents
           totalTokens
           avgResponseTime
           totalErrors
           totalInvocations
           avgModelLatency
           avgInvocationThrottles
           successRate
           errorRate
    }
}
