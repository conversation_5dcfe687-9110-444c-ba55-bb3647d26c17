query{
    aiAgents(input:{
        providerAccountIds: ["ai-agent-provider-account-id-1"]
        pageIndex: 0
        pageSize: 10
    }) {
        numberOfResults
        currentPageSize
        aiAgents {
            id
            externalId
            externalLink
            agentVersions {
                id
                externalId
                version
                name
                description
                region
                purpose
                personalityTraitsJson
                instructions
                agentStatus
                isDeleted
                trustLevel
                isCreatedInRegistry
                externalLink
                auditData {
                    createdByUserId
                    createdTime
                    modifiedByUserId
                    modifiedTime
                    updatedByOrigin
                    updatedAtProviderTime
                }
                agentTasks {
                    id
                    externalId
                    agentVersion {
                        id
                        name
                    }
                    version
                    name
                    description
                    instructions
                    tags {
                        key
                        id
                        value
                    }
                    taskStatus
                    auditData {
                        createdByUserId
                        createdTime
                        modifiedByUserId
                        modifiedTime
                        updatedByOrigin
                        updatedAtProviderTime
                    }
                    syncData {
                        lastSyncStatus
                        lastSyncStartDate
                        lastSyncEndDate
                    }
                    tools {
                        id
                        externalId
                        version
                        name
                        description
                        provider
                        type
                        json
                        status
                        resources {
                            id
                            name
                            description
                            resourceType
                            json
                            status
                            syncData {
                                lastSyncStatus
                                lastSyncStartDate
                                lastSyncEndDate
                            }
                            auditData {
                                createdByUserId
                                createdTime
                                modifiedByUserId
                                modifiedTime
                                updatedByOrigin
                                updatedAtProviderTime
                            }
                        }
                        tags {
                            key
                            id
                            value
                        }
                        syncData {
                            lastSyncStatus
                            lastSyncStartDate
                            lastSyncEndDate
                        }
                        auditData {
                            createdByUserId
                            createdTime
                            modifiedByUserId
                            modifiedTime
                            updatedByOrigin
                            updatedAtProviderTime
                        }

                    }
                }
                llms {
                    id
                    name
                    description
                }
                agentGuardrails {
                    id
                    externalId
                    externalVersion
                    name
                    description
                    configurationJson
                    status
                }
                agentTools {
                    id
                    name
                    externalId
                    version
                    description
                    provider
                    type
                    json
                    status
                    resources {
                        id
                        name
                        description
                        resourceType
                        json
                        status
                    }
                }
                agentAliases {
                    id
                    name
                    description
                    externalLink
                    auditData {
                        createdTime
                        modifiedTime
                        createdByUserId
                        modifiedByUserId
                    }
                }
            }
            createdByOrigin
            isDeleted
            providerAccountSummary {
                providerType
                providerAccountName
                externalProviderAccountId
                region
            }

        }
    }
}
