query GetAiAgentProviderAccounts {
    aiAgentProviderAccounts(input: {
        pageIndex: 1,
        pageSize: 1
        aiAgentProviderType: AWS_BEDROCK,
    }) {
        numberOfResults
        currentPageSize
        aiAgentProviderAccounts {
            id
            providerType
            providerAccountName
            externalProviderAccountId
            providerAccountStatus
            metadataJson
            idpAccountId
            auditData {
                createdByUserId
                createdTime
                modifiedByUserId
                modifiedTime
            }
            syncData {
                lastSyncEndDate
                lastSyncStartDate
                lastSyncStatus
            }
            numberOfAgents
        }
    }
}
