query {
    aiAgentProviderAccount(id: "ai-agent-provider-account-guid-variable") {
        id
        providerType
        providerAccountName
        region
        metadataJson
        idpAccountId
        providerAccountStatus
        syncData {
            lastSyncStatus
            lastSyncEndDate
            lastSyncStartDate
        }
        auditData {
            createdByUserId
            createdTime
            modifiedByUserId
            modifiedTime
            updatedByOrigin
            updatedAtProviderTime
        }
        agents {
            id
            externalId
            createdByOrigin
            isDeleted
            tags {
                key
                id
                value
            }
        }
        authSchema
        isDeleted
        trustLevel
        credential
        onboardingTemplate {
            externalId
            expiresIn
            templateUrl
        }
    }
}
