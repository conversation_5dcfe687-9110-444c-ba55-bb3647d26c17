query GetAiAgentRegistryAccounts {
    aiAgentProviderAccounts(input: {
        pageIndex: 0,
        pageSize: 2
    }) {
        numberOfResults
        currentPageSize
        aiAgentProviderAccounts {
            id
            providerType
            providerAccountName
            externalProviderAccountId
            providerAccountStatus
            metadataJson
            idpAccountId
            auditData {
                createdByUserId
                createdTime
                modifiedByUserId
                modifiedTime
            }
            syncData {
                lastSyncEndDate
                lastSyncStartDate
                lastSyncStatus
            }
            numberOfAgents
        }
    }
}
