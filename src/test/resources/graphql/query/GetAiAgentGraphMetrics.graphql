query AiAgentRegistryGraphMetrics {
        aiAgentRegistryGraphMetrics(input: {
          requestTsStart: "2025-02-26T00:01:00"
          requestTsEnd: "2025-03-01T00:00:00"
          providerTypes: [AWS_BEDROCK, BOOM<PERSON>]
          providerAccountIds: ["provider_account_id_variable"]
          modelIds: ["claude4", "llmV2"]
        }) {
              totalInvocations
              totalModelInvocations
              averageTimePerInvocation
              totalTokens
              totalInputTokens
              totalOutputTokens
              avgInputTokens
              avgOutputTokens
              totalInvocationServerErrors
              totalInvocationClientErrors
              totalInvocationThrottles
              bucketTs
        }
      }