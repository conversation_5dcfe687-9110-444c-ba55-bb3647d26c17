query {
    aiAgentByAliasId(aiAgentAliasId: "%aliasId%") {

        id
        externalId
        externalLink
        agentVersions {
            id
            externalId
            version
            name
            description
            region
            purpose
            personalityTraitsJson
            instructions
            agentStatus
            isDeleted
            trustLevel
            isCreatedInRegistry
            externalLink
            auditData {
                createdByUserId
                createdTime
                modifiedByUserId
                modifiedTime
                updatedByOrigin
                updatedAtProviderTime
            }
            llms {
                id
                name
                description
            }
            agentGuardrails {
                id
                externalId
                externalVersion
                name
                description
                configurationJson
                status
            }
            agentTools {
                id
                name
                externalId
                version
                description
                provider
                type
                json
                status
                resources {
                    id
                    name
                    description
                    resourceType
                    json
                    status
                }
            }
            agentAliases {
                id
                name
                description
                externalLink
                syncData {
                    lastSyncStatus
                    lastSyncStartDate
                    lastSyncEndDate
                }
                auditData {
                    createdTime
                    modifiedTime
                    createdByUserId
                    modifiedByUserId
                }
            }
        }
        createdByOrigin
        isDeleted
        providerAccountSummary {
            providerType
            providerAccountName
            externalProviderAccountId
            region
        }

    }
}
