mutation CreateAiAgentTool {
    aiAgentToolCreate(input: {
        providerAccountId: "provider_account_id_variable",
        version: "Draft",
        name: "Data Analysis Tool 1",
        description: "Tool for analyzing data inputs for AI agents 1.",
        updatedByOrigin: REGISTRY
    }) {
        id
        externalId
        name
        description
        auditData {
            createdByUserId
            createdTime
        }
    }
}
