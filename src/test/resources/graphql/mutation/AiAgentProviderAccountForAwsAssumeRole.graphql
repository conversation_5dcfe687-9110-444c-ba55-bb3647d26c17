mutation {
    aiAgentProviderAccountCreate(
        input: {
            providerType: AWS_BEDROCK
            authSchema: AWS_ASSUME_ROLE
            providerAccountName: "boomi-test-b"
            region: "us-east-1"
            externalAccountId: "************"
        }
    ) {
        id
        providerType
        providerAccountName
        externalProviderAccountId
        region
        metadataJson
        idpAccountId
        authSchema
        providerAccountStatus
        trustLevel
        isDeleted
    }
}
