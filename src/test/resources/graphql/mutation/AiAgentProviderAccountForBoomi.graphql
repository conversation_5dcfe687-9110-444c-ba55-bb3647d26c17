mutation {
    aiAgentProviderAccountCreate(
        input: {
            providerType: BOOMI
            metadataJson: "{\n  \"awsAccessKeyId\": \"Test Value\",\n  \"awsSecretAccessKey\": \"Test Key\",\n  \"awsRegion\": \"ab-east-1\"\n}"
            authSchema: BOOMIAPITOKEN
            providerAccountName: "boomi-test-b"
            credentials: "{\n  \"userName\": \"<EMAIL>\",\n  \"apiToken\": \"boomi\",\n  \"accountId\":\"boomi-internal\"\n}"
            region: "us-east-1"
        }
    ) {
        id
        providerType
        providerAccountName
        externalProviderAccountId
        region
        metadataJson
        idpAccountId
        authSchema
        providerAccountStatus
        trustLevel
        isDeleted
    }
}
