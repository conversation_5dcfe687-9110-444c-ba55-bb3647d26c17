mutation{
    aiAgentProviderAccountUpdate(input: {
        id: "ai-agent-provider-account-guid-variable"
        providerAccountName: "boomi-test-updated"
        metadataJson: "{\n  \"awsAccessKeyId\": \"Updated Test Value\",\n  \"awsSecretAccessKey\": \"Updated Test Key\",\n  \"awsRegion\": \"us-east-1\"\n}"
        credentials: "{\n  \"userName\": \"<EMAIL>\",\n  \"apiToken\": \"boomi\",\n  \"accountId\":\"boomi-internal\"\n}"
        providerAccountStatus: DISABLED
    }) {
        id
        providerType
        providerAccountName
        externalProviderAccountId
        region
        metadataJson
        idpAccountId
        providerAccountStatus
        authSchema
        isDeleted
        trustLevel
    }
}
