mutation UpdateAiAgentTool {
    aiAgentToolUpdate(input: {
        id: "tool_id_variable",
        version: "1",
        name: "tool update",
        description: "This is the update",
        updatedByOrigin: REGISTRY,
        updatedAtProviderTime: "updated_at_provider_time_variable"
        provider: AWS_BEDROCK
        type: AWS_ACTION_GROUP
        json: "{\"actionGroupNameHAHA\":\"OrderManagementAction\",\"description\":\"Action to get the order history, product details, product availability and to update the order\",\"actionGroupState\":\"ENABLED\",\"actionGroupExecutor\":{\"customControl\":\"RETURN_CONTROL\"},\"functionSchema\":{\"functions\":[{\"name\":\"GetOrderDetails\",\"description\":\"Retrieves the order history for a given OrderId and returns productId, color, productName, size, productType, quantity, and status.\",\"parameters\":{\"orderId\":{\"type\":\"string\",\"required\":true}}}]}}"
        status: DISABLED
        resources: [{
            name: "01 GetOrderDetails"
            description: "Retrieves the order history for a given OrderId"
            resourceType: AWS_FUNCTION
            json: "{\"name\":\"GetOrderDetails\",\"description\":\"Retrieves the order history for a given OrderId and returns productId, color, productName, size, productType, quantity, and status.\",\"parameters\":{\"orderId\":{\"type\":\"string\",\"required\":true}}}"
            status: ENABLED
        },
            {
                name: "02 updateOrderDetails"
                description: "update the order history for a given OrderId"
                resourceType: AWS_FUNCTION
                json: "{\"name\":\"GetOrderDetails\",\"description\":\"Retrieves the order history for a given OrderId and returns productId, color, productName, size, productType, quantity, and status.\",\"parameters\":{\"orderId\":{\"type\":\"string\",\"required\":true}}}"
                status: ENABLED
            }]
        tags: [{
            key: "data"
            value: "updated-analysis"
        }]
    }) {
        id
        externalId
        version
        name
        description
        provider
        type
        json
        status
        resources {
            name
            description
            resourceType
            json
            status
        }
        tags {
            id
            key
            value
        }
        syncData {
            lastSyncStatus
            lastSyncStartDate
            lastSyncEndDate
        }
        auditData {
            createdByUserId
            createdTime
            modifiedByUserId
            modifiedTime
            updatedByOrigin
            updatedAtProviderTime
        }
    }
}
