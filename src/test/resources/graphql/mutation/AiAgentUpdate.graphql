mutation UpdateAiAgent {
    aiAgentUpdate(input: {
        id: "agent_id_variable",
        versionId: "agent_version_id_variable",
        agentVersion: "Draft",
        agentName: "Test Agent 1 - updated",
        agentDescription: "Description for Test Agent 1 - updated.",
        agentStatus: PENDING
    }) {
        id
        externalId
        agentVersions {
            id
            externalId
            version
            name
            description
            region
            agentStatus
            isDeleted
            trustLevel
            isCreatedInRegistry
        }
        createdByOrigin
        isDeleted
        providerAccountSummary {
            providerType
            providerAccountName
            externalProviderAccountId
        }
    }
}
