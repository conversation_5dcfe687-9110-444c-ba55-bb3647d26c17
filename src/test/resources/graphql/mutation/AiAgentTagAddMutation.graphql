# Welcome to Altair GraphQL Client.
# You can send your request using CmdOrCtrl + Enter.

# Enter your graphQL query here.
mutation {
    aiAgentAddTags(input: {
        tags:[{
            relatedEntityType: "related_entity_ref_type",
            relatedEntityRefId: {
                id: "related_entity_ref_id"
            },
            tags: [
                {
                    key: "BuDdY"
                    value: "buddy"
                },
                {
                    key: "HeLlO"
                    value: "hello"
                },
                {
                    key: "Buddy"
                    value: "Buddy"
                },
                {
                    key: "hElLo"
                    value: "hElLo"
                }
            ]
        },{
            relatedEntityType: ALIAS,
            relatedEntityRefId: {
                id: "750abd54-0a95-419f-b7ad-10000000ce05"
            },
            tags: [
                {
                    key: "cookie1"
                    value: "cookie1"
                },
                {
                    key: "cookie2"
                    value: "cookie2"
                }
            ]
        }
        ]
    }) {
        tags{
            relatedEntityRefId
            relatedEntityType
            tags{
                id
                key
                value
            }
        }
    }
}
