mutation CreateAiAgent {
    aiAgentCreate(input: {
        providerAccountId: "provider_account_id_variable",
        agentVersion: "Draft",
        agentName: "Test Agent 1",
        agentDescription: "Description for Test Agent 1.",
        agentStatus: PENDING
    }) {
        id
        externalId
        agentVersions {
            id
            externalId
            version
            name
            description
            region
            agentStatus
            isDeleted
            trustLevel
            isCreatedInRegistry
        }
        createdByOrigin
        isDeleted
        providerAccountSummary {
            providerType
            providerAccountName
            externalProviderAccountId
        }
    }
}
