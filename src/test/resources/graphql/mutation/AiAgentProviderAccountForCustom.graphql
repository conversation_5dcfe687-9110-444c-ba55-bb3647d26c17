mutation {
    aiAgentProviderAccountCreate(
        input: {
            providerAccountName: "Custom Account"
            providerType: CUSTOM
            providerAccountDescription: "This is Custom Account Creation testing"
        }
    ) {
        id
        providerType
        providerAccountName
        providerAccountDescription
        externalProviderAccountId
        region
        metadataJson
        idpAccountId
        providerAccountStatus
        auditData {
            createdByUserId
            createdTime
            modifiedByUserId
            modifiedTime
            updatedByOrigin
            updatedAtProviderTime
        }
        authSchema
        isDeleted
        trustLevel
        numberOfAgents
        syncData {
            lastSyncStatus
            lastSyncStartDate
            lastSyncEndDate
        }
    }

}