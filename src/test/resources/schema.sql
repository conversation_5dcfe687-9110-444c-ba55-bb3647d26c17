DROP VIEW IF EXISTS v_agent_listing;

CREATE VIEW v_agent_listing AS
SELECT
    a.uid AS agent_id,
    a.guid AS agent_guid,
    a.external_id AS agent_external_id,
    pa.guid AS provider_guid,
    pa.idp_account_id,
    a.is_deleted AS agent_is_deleted,
    pa.uid AS provider_account_uid,
    pa.provider_type,
    pa.provider_account_name,
    av.uid AS version_id,
    av.guid AS version_guid,
    av.name AS version_name,
    av.version_string AS version,
    av.external_id AS version_external_id,
    av.description,
    av.agent_status,
    av.trust_level,
    av.is_deleted AS version_is_deleted,
    aa.uid AS alias_id,
    aa.guid AS alias_guid,
    aa.name AS alias_name,
    aa.is_deleted AS alias_is_deleted,
    aa.external_id AS alias_external_id,
    av.modified_time,
    av.updated_at_provider_time,
    CASE WHEN aa.uid IS NOT NULL THEN TRUE ELSE FALSE END AS has_alias
FROM ai_agent a
         JOIN ai_agent_version av ON a.uid = av.agent_uid AND av.is_deleted = false
         LEFT JOIN ai_agent_alias aa ON av.uid = aa.agent_version_uid AND aa.is_deleted = false
         JOIN ai_agent_provider_account pa ON a.provider_account_uid = pa.uid AND a.is_deleted = false;
