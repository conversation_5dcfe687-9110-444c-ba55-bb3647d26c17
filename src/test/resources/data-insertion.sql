-- ai_agent_provider_account

INSERT INTO ai_agent_provider_account (guid, provider_type, region, provider_metadata, idp_account_id,
                                       credentials_key, provider_account_name, created_by_user_id,
                                       created_time, modified_by_user_id, modified_time,
                                       provider_account_status, is_deleted, auth_schema,
                                       provider_account_description, external_provider_account_id,
                                       external_provider_account_name)
VALUES ('9b6802c8-a7ca-406c-ae8a-000000009f68', 'BOOMI', null,
        '{"awsRegion": "es-east-1", "awsAccessKeyId": "Test Value", "awsSecretAccessKey": "Test Key"}',
        'boomi-internal',
        'boomi-internal/9b6802c8-a7ca-406c-ae8a-000000009f68', 'boomi-test',
        null, null, null, null, 'CONNECTED', false,
        'BOOMIAPITOKEN', null, 'boomi-provider-account-id', 'boomi-provider');

INSERT INTO ai_agent_provider_account (guid, provider_type, region, provider_metadata, idp_account_id,
                                       credentials_key, provider_account_name, created_by_user_id,
                                       created_time, modified_by_user_id, modified_time,
                                       provider_account_status, is_deleted, auth_schema,
                                       provider_account_description, external_provider_account_id,
                                       external_provider_account_name)
VALUES ('9b6802c8-a7ca-406c-ae8a-000000009f78', 'BOOMI', null,
        '{"awsRegion": "es-east-1", "awsAccessKeyId": "Test Value", "awsSecretAccessKey": "Test Key-1"}',
        'boomi-internal',
        'boomi-internal/9b6802c8-a7ca-406c-ae8a-000000009f78', 'boomi-test-1',
        null, null, null, null, 'CONNECTED', false,
        'BOOMIAPITOKEN', null, 'boomi-provider-account-id-1', 'boomi-provider-1');

-- Ai Agent
INSERT INTO ai_agent (guid, external_id, provider_account_uid, idp_account_id, is_deleted)
VALUES ('fd005da0-0e8b-47dc-88b4-00000000af4d', 'external-id',
        (select uid from ai_agent_provider_account where guid = '9b6802c8-a7ca-406c-ae8a-000000009f68'),
        'boomi-internal', false);

INSERT INTO ai_agent (guid, external_id, provider_account_uid, idp_account_id, is_deleted)
VALUES ('fd005da0-0e8b-47dc-88b4-00000000af4e', 'external-id-1',
        (select uid from ai_agent_provider_account where guid = '9b6802c8-a7ca-406c-ae8a-000000009f68'),
        'boomi-internal', false);

INSERT INTO ai_agent (guid, external_id, provider_account_uid, idp_account_id, is_deleted)
VALUES ('fd005da0-0e8b-47dc-88b4-00000000af4f', 'external-id',
        (select uid from ai_agent_provider_account where guid = '9b6802c8-a7ca-406c-ae8a-000000009f78'),
        'boomi-internal', false);

INSERT INTO ai_agent (guid, external_id, provider_account_uid, idp_account_id, is_deleted)
VALUES ('fd005da0-0e8b-47dc-88b4-00000000af4a', 'external-id-1',
        (select uid from ai_agent_provider_account where guid = '9b6802c8-a7ca-406c-ae8a-000000009f78'),
        'boomi-internal', false);

-- Ai Agent Version
INSERT INTO ai_agent_version (guid, external_id, agent_uid, version_string, version_int, name,
                              description, region, purpose, personality_traits, created_in_registry, created_by_user_id,
                              created_time, modified_by_user_id, modified_time, agent_status,
                              updated_by_origin, updated_at_provider_time, is_deleted, trust_level,
                              created_by_origin)
VALUES ('68d656d5-b067-472f-8f23-0000000050ca', 'external-id',
        (select uid from ai_agent where ai_agent.guid = 'fd005da0-0e8b-47dc-88b4-00000000af4d'), '1.0',
        1, 'Alpha',
        'Initial version of the AI agent', null, null, '{
    "helpful": true,
    "friendly": true
  }',
        null, null, '2024-12-10 17:18:52.726580', null, null, 'RUNNING', null, null,
        false, null, null);

INSERT INTO ai_agent_version (guid, external_id, agent_uid, version_string, version_int, name,
                              description, region, purpose, personality_traits, created_in_registry, created_by_user_id,
                              created_time, modified_by_user_id, modified_time, agent_status,
                              updated_by_origin, updated_at_provider_time, is_deleted, trust_level,
                              created_by_origin)
VALUES ('68d656d5-b067-472f-8f23-0000000050cb', 'external-id',
        (select uid from ai_agent where ai_agent.guid = 'fd005da0-0e8b-47dc-88b4-00000000af4d'), '1.0',
        1, 'Beta',
        'Initial version of the AI agent-1', null, null, '{
    "helpful": true,
    "friendly": true
  }',
        null, null, '2024-12-10 17:18:52.726580', null, null, 'RUNNING', null, null,
        false, null, null);

INSERT INTO ai_agent_version (guid, external_id, agent_uid, version_string, version_int, name,
                              description, region, purpose, personality_traits, created_in_registry, created_by_user_id,
                              created_time, modified_by_user_id, modified_time, agent_status,
                              updated_by_origin, updated_at_provider_time, is_deleted, trust_level,
                              created_by_origin)
VALUES ('68d656d5-b067-472f-8f23-0000000050cc', 'external-id',
        (select uid from ai_agent where ai_agent.guid = 'fd005da0-0e8b-47dc-88b4-00000000af4e'), '1.0',
        1, 'Gama',
        'Initial version of the AI agent-2', null, null, '{
    "helpful": true,
    "friendly": true
  }',
        null, null, '2024-12-10 17:18:52.726580', null, null, 'RUNNING', null, null,
        false, null, null);

INSERT INTO ai_agent_version (guid, external_id, agent_uid, version_string, version_int, name,
                              description, region, purpose, personality_traits, created_in_registry, created_by_user_id,
                              created_time, modified_by_user_id, modified_time, agent_status,
                              updated_by_origin, updated_at_provider_time, is_deleted, trust_level,
                              created_by_origin)
VALUES ('68d656d5-b067-472f-8f23-0000000050cd', 'external-id',
        (select uid from ai_agent where ai_agent.guid = 'fd005da0-0e8b-47dc-88b4-00000000af4e'), '1.0',
        1, 'Omega',
        'Initial version of the AI agent-3', null, null, '{
    "helpful": true,
    "friendly": true
  }',
        null, null, '2024-12-10 17:18:52.726580', null, null, 'RUNNING', null, null,
        false, null, null);

INSERT INTO ai_agent_version (guid, external_id, agent_uid, version_string, version_int, name,
                              description, region, purpose, personality_traits, created_in_registry, created_by_user_id,
                              created_time, modified_by_user_id, modified_time, agent_status,
                              updated_by_origin, updated_at_provider_time, is_deleted, trust_level,
                              created_by_origin)
VALUES ('68d656d5-b067-472f-8f23-0000000050ce', 'external-id',
        (select uid from ai_agent where ai_agent.guid = 'fd005da0-0e8b-47dc-88b4-00000000af4f'), '1.0',
        1, 'Test Version',
        'Initial version of the AI agent-4', null, null, '{
    "helpful": true,
    "friendly": true
  }',
        null, null, '2024-12-10 17:18:52.726580', null, null, 'RUNNING', null, null,
        false, null, null);

INSERT INTO ai_agent_version (guid, external_id, agent_uid, version_string, version_int, name,
                              description, region, purpose, personality_traits, created_in_registry, created_by_user_id,
                              created_time, modified_by_user_id, modified_time, agent_status,
                              updated_by_origin, updated_at_provider_time, is_deleted, trust_level,
                              created_by_origin)
VALUES ('68d656d5-b067-472f-8f23-0000000050cf', 'external-id',
        (select uid from ai_agent where ai_agent.guid = 'fd005da0-0e8b-47dc-88b4-00000000af4a'), '1.0',
        1, 'Test Version-1',
        'Initial version of the AI agent-5', null, null, '{
    "helpful": true,
    "friendly": true
  }',
        null, null, '2024-12-10 17:18:52.726580', null, null, 'RUNNING', null, null,
        false, null, null);


-- ai_agent_guardrail

INSERT INTO ai_agent_guardrail (guid, external_id, idp_account_id, provider_account_uid,
                                version_string, version_int, name, description, created_by_user_id,
                                created_time, modified_by_user_id, modified_time, updated_by_origin,
                                updated_at_provider_time)
VALUES ('750abd54-0a95-419f-b7ad-00000000ce39', 'external-id', 'boomi-internal',
        (select uid from ai_agent_provider_account where guid = '9b6802c8-a7ca-406c-ae8a-000000009f68'),
        '1.0', 1, 'Data Privacy Guardrail', 'Prevents access to sensitive data.', null,
        null, null, null, null, null);

INSERT INTO ai_agent_guardrail (guid, external_id, idp_account_id, provider_account_uid,
                                version_string, version_int, name, description, created_by_user_id,
                                created_time, modified_by_user_id, modified_time, updated_by_origin,
                                updated_at_provider_time)
VALUES ('750abd54-0a95-419f-b7ad-00000000ce40', 'external-id-1', 'boomi-internal',
        (select uid from ai_agent_provider_account where guid = '9b6802c8-a7ca-406c-ae8a-000000009f68'),
        '1.0', 1, 'Data Privacy Guardrail-1', 'Prevents access to test sensitive data.', null,
        null, null, null, null, null);

INSERT INTO ai_agent_guardrail (guid, external_id, idp_account_id, provider_account_uid,
                                version_string, version_int, name, description, created_by_user_id,
                                created_time, modified_by_user_id, modified_time, updated_by_origin,
                                updated_at_provider_time)
VALUES ('750abd54-0a95-419f-b7ad-00000000ce41', 'external-id', 'boomi-internal',
        (select uid from ai_agent_provider_account where guid = '9b6802c8-a7ca-406c-ae8a-000000009f78'),
        '1.0', 1, 'Data Privacy Guardrail-2', 'Prevents access to sensitive data.', null,
        null, null, null, null, null);

INSERT INTO ai_agent_guardrail (guid, external_id, idp_account_id, provider_account_uid,
                                version_string, version_int, name, description, created_by_user_id,
                                created_time, modified_by_user_id, modified_time, updated_by_origin,
                                updated_at_provider_time)
VALUES ('750abd54-0a95-419f-b7ad-00000000ce42', 'external-id-1', 'boomi-internal',
        (select uid from ai_agent_provider_account where guid = '9b6802c8-a7ca-406c-ae8a-000000009f78'),
        '1.0', 1, 'Data Privacy Guardrail-3', 'Prevents access to test sensitive data.', null,
        null, null, null, null, null);

-- ai_agent_guardrail_association

INSERT INTO ai_agent_guardrail_association (guid, guardrail_uid, related_entity_uid, related_entity_type)
VALUES ('750abd54-0a95-419f-b7ad-00000000ce50',
        (select uid from ai_agent_guardrail where ai_agent_guardrail.guid = '750abd54-0a95-419f-b7ad-00000000ce39'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050ca'),
        'VERSION');

INSERT INTO ai_agent_guardrail_association (guid, guardrail_uid, related_entity_uid, related_entity_type)
VALUES ('750abd54-0a95-419f-b7ad-00000000ce51',
        (select uid from ai_agent_guardrail where ai_agent_guardrail.guid = '750abd54-0a95-419f-b7ad-00000000ce40'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050ca'),
        'VERSION');

INSERT INTO ai_agent_guardrail_association (guid, guardrail_uid, related_entity_uid, related_entity_type)
VALUES ('750abd54-0a95-419f-b7ad-00000000ce52',
        (select uid from ai_agent_guardrail where ai_agent_guardrail.guid = '750abd54-0a95-419f-b7ad-00000000ce41'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050cb'),
        'VERSION');

INSERT INTO ai_agent_guardrail_association (guid, guardrail_uid, related_entity_uid, related_entity_type)
VALUES ('750abd54-0a95-419f-b7ad-00000000ce53',
        (select uid from ai_agent_guardrail where ai_agent_guardrail.guid = '750abd54-0a95-419f-b7ad-00000000ce42'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050cb'),
        'VERSION');

INSERT INTO ai_agent_guardrail_association (guid, guardrail_uid, related_entity_uid, related_entity_type)
VALUES ('750abd54-0a95-419f-b7ad-00000000ce54',
        (select uid from ai_agent_guardrail where ai_agent_guardrail.guid = '750abd54-0a95-419f-b7ad-00000000ce41'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050cc'),
        'VERSION');

INSERT INTO ai_agent_guardrail_association (guid, guardrail_uid, related_entity_uid, related_entity_type)
VALUES ('750abd54-0a95-419f-b7ad-00000000ce55',
        (select uid from ai_agent_guardrail where ai_agent_guardrail.guid = '750abd54-0a95-419f-b7ad-00000000ce42'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050cd'),
        'VERSION');

INSERT INTO ai_agent_guardrail_association (guid, guardrail_uid, related_entity_uid, related_entity_type)
VALUES ('750abd54-0a95-419f-b7ad-00000000ce56',
        (select uid from ai_agent_guardrail where ai_agent_guardrail.guid = '750abd54-0a95-419f-b7ad-00000000ce41'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050cd'),
        'VERSION');

INSERT INTO ai_agent_guardrail_association (guid, guardrail_uid, related_entity_uid, related_entity_type)
VALUES ('750abd54-0a95-419f-b7ad-00000000ce57',
        (select uid from ai_agent_guardrail where ai_agent_guardrail.guid = '750abd54-0a95-419f-b7ad-00000000ce42'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050ce'),
        'VERSION');

INSERT INTO ai_agent_guardrail_association (guid, guardrail_uid, related_entity_uid, related_entity_type)
VALUES ('750abd54-0a95-419f-b7ad-00000000ce58',
        (select uid from ai_agent_guardrail where ai_agent_guardrail.guid = '750abd54-0a95-419f-b7ad-00000000ce39'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050ce'),
        'VERSION');

INSERT INTO ai_agent_guardrail_association (guid, guardrail_uid, related_entity_uid, related_entity_type)
VALUES ('750abd54-0a95-419f-b7ad-00000000ce59',
        (select uid from ai_agent_guardrail where ai_agent_guardrail.guid = '750abd54-0a95-419f-b7ad-00000000ce42'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050cf'),
        'VERSION');

-- Ai Agent Tool

INSERT INTO ai_agent_tool (guid, external_id, idp_account_id, provider_account_uid,
                           version_string, version_int, name, description, created_by_user_id,
                           created_time, modified_by_user_id, modified_time, updated_by_origin,
                           updated_at_provider_time, tool_provider, tool_type, tool_subtype,
                           tool_json, status)
VALUES ('ac54c8f9-0a3c-4ad3-99bb-00000000825d', 'external-id', 'boomi-internal',
        (select uid from ai_agent_provider_account where guid = '9b6802c8-a7ca-406c-ae8a-000000009f68'),
        '1.0', null, 'Action Group Tool', 'This is an Action Group which contains both API and Lambda
                    function.', 'boomi-internal',
        CURRENT_TIMESTAMP, null, null, 'REGISTRY', CURRENT_TIMESTAMP, 'AWS_BEDROCK', 'AWS_ACTION_GROUP',
        null, '{"actionGroupName":"OrderManagementAction"}', 'ENABLED');

INSERT INTO ai_agent_tool (guid, external_id, idp_account_id, provider_account_uid,
                           version_string, version_int, name, description, created_by_user_id,
                           created_time, modified_by_user_id, modified_time, updated_by_origin,
                           updated_at_provider_time, tool_provider, tool_type, tool_subtype,
                           tool_json, status)
VALUES ('ac54c8f9-0a3c-4ad3-99bb-00000000825e', 'external-id', 'boomi-internal',
        (select uid from ai_agent_provider_account where guid = '9b6802c8-a7ca-406c-ae8a-000000009f68'),
        '1.0', null, 'Action Group Tool 1', 'This is an Action Group which contains both API and Lambda function 1.',
        'boomi-internal',
        CURRENT_TIMESTAMP, null, null, 'REGISTRY', CURRENT_TIMESTAMP, 'AWS_BEDROCK', 'AWS_KNOWLEDGE_BASE',
        null, '{"actionGroupName":"OrderManagementAction"}', 'ENABLED');

INSERT INTO ai_agent_tool (guid, external_id, idp_account_id, provider_account_uid,
                           version_string, version_int, name, description, created_by_user_id,
                           created_time, modified_by_user_id, modified_time, updated_by_origin,
                           updated_at_provider_time, tool_provider, tool_type, tool_subtype,
                           tool_json, status)
VALUES ('ac54c8f9-0a3c-4ad3-99bb-00000000825f', 'external-id-1', 'boomi-internal',
        (select uid from ai_agent_provider_account where guid = '9b6802c8-a7ca-406c-ae8a-000000009f78'),
        '1.0', null, 'Action Group Tool-3', 'This is an Action Group which contains both API and Lambda
                    function 3.', 'boomi-internal',
        CURRENT_TIMESTAMP, null, null, 'REGISTRY', CURRENT_TIMESTAMP, 'AWS_BEDROCK', 'AWS_KNOWLEDGE_BASE',
        null, '{"actionGroupName":"OrderManagementAction"}', 'ENABLED');

INSERT INTO ai_agent_tool (guid, external_id, idp_account_id, provider_account_uid,
                           version_string, version_int, name, description, created_by_user_id,
                           created_time, modified_by_user_id, modified_time, updated_by_origin,
                           updated_at_provider_time, tool_provider, tool_type, tool_subtype,
                           tool_json, status)
VALUES ('ac54c8f9-0a3c-4ad3-99bb-00000000825a', 'external-id-1', 'boomi-internal',
        (select uid from ai_agent_provider_account where guid = '9b6802c8-a7ca-406c-ae8a-000000009f78'),
        '1.0', null, 'Action Group Tool 4', 'This is an Action Group which contains both API and Lambda function 4.',
        'boomi-internal',
        CURRENT_TIMESTAMP, null, null, 'REGISTRY', CURRENT_TIMESTAMP, 'AWS_BEDROCK', 'BOOMI_HUB',
        null, '{"actionGroupName":"OrderManagementAction"}', 'ENABLED');

-- Ai Agent Tool Association

INSERT INTO ai_agent_tool_association (guid, tool_uid, related_entity_uid, related_entity_type)
VALUES ('de48cc57-e2b5-4000-9c7c-00000000bba9',
        (select uid from ai_agent_tool where ai_agent_tool.guid = 'ac54c8f9-0a3c-4ad3-99bb-00000000825d'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050ca'),
        'VERSION');

INSERT INTO ai_agent_tool_association (guid, tool_uid, related_entity_uid, related_entity_type)
VALUES ('de48cc57-e2b5-4000-9c7c-00000000bbb9',
        (select uid from ai_agent_tool where ai_agent_tool.guid = 'ac54c8f9-0a3c-4ad3-99bb-00000000825e'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050ca'),
        'VERSION');

INSERT INTO ai_agent_tool_association (guid, tool_uid, related_entity_uid, related_entity_type)
VALUES ('de48cc57-e2b5-4000-9c7c-00000000bbc9',
        (select uid from ai_agent_tool where ai_agent_tool.guid = 'ac54c8f9-0a3c-4ad3-99bb-00000000825d'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050cb'),
        'VERSION');

INSERT INTO ai_agent_tool_association (guid, tool_uid, related_entity_uid, related_entity_type)
VALUES ('de48cc57-e2b5-4000-9c7c-00000000bbd9',
        (select uid from ai_agent_tool where ai_agent_tool.guid = 'ac54c8f9-0a3c-4ad3-99bb-00000000825e'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050cb'),
        'VERSION');

INSERT INTO ai_agent_tool_association (guid, tool_uid, related_entity_uid, related_entity_type)
VALUES ('de48cc57-e2b5-4000-9c7c-00000000bbe9',
        (select uid from ai_agent_tool where ai_agent_tool.guid = 'ac54c8f9-0a3c-4ad3-99bb-00000000825f'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050cc'),
        'VERSION');

INSERT INTO ai_agent_tool_association (guid, tool_uid, related_entity_uid, related_entity_type)
VALUES ('de48cc57-e2b5-4000-9c7c-00000000bbf9',
        (select uid from ai_agent_tool where ai_agent_tool.guid = 'ac54c8f9-0a3c-4ad3-99bb-00000000825a'),
        (select uid from ai_agent_version where ai_agent_version.guid = '68d656d5-b067-472f-8f23-0000000050cd'),
        'VERSION');

-- ai_agent_tool_resource

INSERT INTO ai_agent_tool_resource (guid, tool_uid, name, description, resource_type, tool_resource_json,
                                    status, created_time, modified_time)
VALUES ('4983d8ee-5533-4253-ac30-0000992d5b99',
        (select uid from ai_agent_tool where guid = 'ac54c8f9-0a3c-4ad3-99bb-00000000825d'),
        'API resource', 'This is an API resource', 'AWS_API', '{"apiSchema": {"s3": {"s3BucketName":
                    "apischema-s3", "s3ObjectKey": "it_agent_openapi.json"}}}', 'ENABLED', null, null);

INSERT INTO ai_agent_tool_resource (guid, tool_uid, name, description, resource_type, tool_resource_json,
                                    status, created_time, modified_time)
VALUES ('4983d8ee-5533-4253-ac30-0000992d5b90',
        (select uid from ai_agent_tool where guid = 'ac54c8f9-0a3c-4ad3-99bb-00000000825d'),
        'Lambda Function resource', 'This is an Lambda function', 'AWS_FUNCTION',
        '{"name": "GetOrderDetails"}', 'ENABLED', null, null);

INSERT INTO ai_agent_tool_resource (guid, tool_uid, name, description, resource_type, tool_resource_json,
                                    status, created_time, modified_time)
VALUES ('4983d8ee-5533-4253-ac30-0000992d5b89',
        (select uid from ai_agent_tool where guid = 'ac54c8f9-0a3c-4ad3-99bb-00000000825e'),
        'API Test resource', 'This Test is an API resource', 'AWS_API', '{"apiSchema": {"s3": {"s3BucketName":
                    "apischema-s3", "s3ObjectKey": "it_agent_openapi.json"}}}', 'ENABLED', null, null);

INSERT INTO ai_agent_tool_resource (guid, tool_uid, name, description, resource_type, tool_resource_json,
                                    status, created_time, modified_time)
VALUES ('4983d8ee-5533-4253-ac30-0000992d5b88',
        (select uid from ai_agent_tool where guid = 'ac54c8f9-0a3c-4ad3-99bb-00000000825f'),
        'Lambda Test Function resource', 'This Test is an Lambda function', 'AWS_FUNCTION',
        '{"name": "GetOrderDetails"}', 'ENABLED', null, null);

INSERT INTO ai_agent_tool_resource (guid, tool_uid, name, description, resource_type, tool_resource_json,
                                    status, created_time, modified_time)
VALUES ('4983d8ee-5533-4253-ac30-0000992d5b87',
        (select uid from ai_agent_tool where guid = 'ac54c8f9-0a3c-4ad3-99bb-00000000825f'),
        'Lambda Test Function resource -1', 'This Test is an Lambda function -1', 'AWS_FUNCTION',
        '{"name": "GetOrderDetails"}', 'ENABLED', null, null);

INSERT INTO ai_agent_tool_resource (guid, tool_uid, name, description, resource_type, tool_resource_json,
                                    status, created_time, modified_time)
VALUES ('4983d8ee-5533-4253-ac30-0000992d5b86',
        (select uid from ai_agent_tool where guid = 'ac54c8f9-0a3c-4ad3-99bb-00000000825a'),
        'Lambda Test Function resource -2', 'This Test is an Lambda function -2', 'AWS_FUNCTION',
        '{"name": "GetOrderDetails"}', 'ENABLED', null, null);

-- Ai Agent Alias

INSERT INTO ai_agent_alias (guid, external_id, agent_version_uid, name, description, created_by_user_id, created_time,
                            modified_by_user_id, modified_time, updated_by_origin, updated_at_provider_time, is_deleted)
VALUES ('750abd54-0a95-419f-b7ad-10000000ce00', 'external-id',
        (select uid from ai_agent_version where guid = '68d656d5-b067-472f-8f23-0000000050ca'),
        'Alias-1', 'Test Alias', '<EMAIL>',
        '2024-12-16 19:17:54.000000', null, null, null, null, false);

INSERT INTO ai_agent_alias (guid, external_id, agent_version_uid, name, description, created_by_user_id, created_time,
                            modified_by_user_id, modified_time, updated_by_origin, updated_at_provider_time, is_deleted)
VALUES ('750abd54-0a95-419f-b7ad-10000000ce01', 'external-id-1',
        (select uid from ai_agent_version where guid = '68d656d5-b067-472f-8f23-0000000050ca'), 'Alias-2',
        'Test Alias-1',
        '<EMAIL>', '2024-12-16 22:10:08.000000', null, null, null, null, false);

INSERT INTO ai_agent_alias (guid, external_id, agent_version_uid, name, description, created_by_user_id, created_time,
                            modified_by_user_id, modified_time, updated_by_origin, updated_at_provider_time, is_deleted)
VALUES ('750abd54-0a95-419f-b7ad-10000000ce02', 'external-id-3',
        (select uid from ai_agent_version where guid = '68d656d5-b067-472f-8f23-0000000050ca'), 'Alias-3', 'Test Alias',
        '<EMAIL>', '2024-12-16 22:12:27.000000', null, null, null, null, false);

INSERT INTO ai_agent_alias (guid, external_id, agent_version_uid, name, description, created_by_user_id, created_time,
                            modified_by_user_id, modified_time, updated_by_origin, updated_at_provider_time, is_deleted)
VALUES ('750abd54-0a95-419f-b7ad-10000000ce03', 'external-id',
        (select uid from ai_agent_version where guid = '68d656d5-b067-472f-8f23-0000000050cb'),
        'Alias-1', 'Test Alias', '<EMAIL>',
        '2024-12-16 19:17:54.000000', null, null, null, null, false);

INSERT INTO ai_agent_alias (guid, external_id, agent_version_uid, name, description, created_by_user_id, created_time,
                            modified_by_user_id, modified_time, updated_by_origin, updated_at_provider_time, is_deleted)
VALUES ('750abd54-0a95-419f-b7ad-10000000ce04', 'external-id-1',
        (select uid from ai_agent_version where guid = '68d656d5-b067-472f-8f23-0000000050cb'), 'Alias-2',
        'Test Alias-1',
        '<EMAIL>', '2024-12-16 22:10:08.000000', null, null, null, null, false);

INSERT INTO ai_agent_alias (guid, external_id, agent_version_uid, name, description, created_by_user_id, created_time,
                            modified_by_user_id, modified_time, updated_by_origin, updated_at_provider_time, is_deleted)
VALUES ('750abd54-0a95-419f-b7ad-10000000ce05', 'external-id-3',
        (select uid from ai_agent_version where guid = '68d656d5-b067-472f-8f23-0000000050cc'), 'Alias-3', 'Test Alias',
        '<EMAIL>', '2024-12-16 22:12:27.000000', null, null, null, null, false);

INSERT INTO ai_agent_alias (guid, external_id, agent_version_uid, name, description, created_by_user_id, created_time,
                            modified_by_user_id, modified_time, updated_by_origin, updated_at_provider_time, is_deleted)
VALUES ('750abd54-0a95-419f-b7ad-10000000ce06', 'external-id-3',
        (select uid from ai_agent_version where guid = '68d656d5-b067-472f-8f23-0000000050cc'), 'Alias-4', 'Test Alias',
        '<EMAIL>', '2024-12-16 22:12:27.000000', null, null, null, null, false);

INSERT INTO ai_agent_alias (guid, external_id, agent_version_uid, name, description, created_by_user_id, created_time,
                            modified_by_user_id, modified_time, updated_by_origin, updated_at_provider_time, is_deleted)
VALUES ('750abd54-0a95-419f-b7ad-10000000ce07', 'external-id-1',
        (select uid from ai_agent_version where guid = '68d656d5-b067-472f-8f23-0000000050cd'), 'Alias-5',
        'Test Alias-1',
        '<EMAIL>', '2024-12-16 22:10:08.000000', null, null, null, null, false);

INSERT INTO ai_agent_alias (guid, external_id, agent_version_uid, name, description, created_by_user_id, created_time,
                            modified_by_user_id, modified_time, updated_by_origin, updated_at_provider_time, is_deleted)
VALUES ('750abd54-0a95-419f-b7ad-10000000ce08', 'external-id-3',
        (select uid from ai_agent_version where guid = '68d656d5-b067-472f-8f23-0000000050ce'), 'Alias-6', 'Test Alias',
        '<EMAIL>', '2024-12-16 22:12:27.000000', null, null, null, null, false);

INSERT INTO ai_agent_alias (guid, external_id, agent_version_uid, name, description, created_by_user_id, created_time,
                            modified_by_user_id, modified_time, updated_by_origin, updated_at_provider_time, is_deleted)
VALUES ('750abd54-0a95-419f-b7ad-10000000ce09', 'external-id-3',
        (select uid from ai_agent_version where guid = '68d656d5-b067-472f-8f23-0000000050cf'), 'Alias-7', 'Test Alias',
        '<EMAIL>', '2024-12-16 22:12:27.000000', null, null, null, null, false);


-- Ai Agent Tag

INSERT INTO ai_agent_tag (guid, idp_account_id, tag_key, tag_value)
VALUES ('32cc6c20-ffdd-42a3-a78f-************',
        'aiagentregistryaccount-QU4TWM', 'tag_key_1', 'tag_1');

INSERT INTO ai_agent_tag (guid, idp_account_id, tag_key, tag_value)
VALUES ('32cc6c20-ffdd-42a3-a78f-************',
        'aiagentregistryaccount-QU4TWM', 'tag_key_2', 'tag_2');

INSERT INTO ai_agent_tag (guid, idp_account_id, tag_key, tag_value)
VALUES ('32cc6c20-ffdd-42a3-a78f-************',
        'aiagentregistryaccount-QU4TWM', 'tag_key_3', 'tag_3');

INSERT INTO ai_agent_tag (guid, idp_account_id, tag_key, tag_value)
VALUES ('32cc6c20-ffdd-42a3-a78f-************',
        'aiagentregistryaccount-QU4TWM', 'tag_key_5', 'tag_5');

INSERT INTO ai_agent_tag (guid, idp_account_id, tag_key, tag_value)
VALUES ('32cc6c20-ffdd-42a3-a78f-************',
        'aiagentregistryaccount-QU4TWM', 'tag_key_7', null);

-- Ai agent tag Association

INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-************',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_alias where guid = '750abd54-0a95-419f-b7ad-10000000ce00'), 'ALIAS');
INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-************',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_alias where guid = '750abd54-0a95-419f-b7ad-10000000ce00'), 'ALIAS');
INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-************',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_alias where guid = '750abd54-0a95-419f-b7ad-10000000ce01'), 'ALIAS');
INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-000000008010',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_alias where guid = '750abd54-0a95-419f-b7ad-10000000ce03'), 'ALIAS');
INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-000000008011',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_alias where guid = '750abd54-0a95-419f-b7ad-10000000ce03'), 'ALIAS');
INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-000000008013',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_alias where guid = '750abd54-0a95-419f-b7ad-10000000ce04'), 'ALIAS');
INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-000000008113',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_alias where guid = '750abd54-0a95-419f-b7ad-10000000ce05'), 'ALIAS');
VALUES ('10cc6c20-ffdd-42a3-a78f-000000008115',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_alias where guid = '750abd54-0a95-419f-b7ad-10000000ce06'), 'ALIAS');
INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-000000008116',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_alias where guid = '750abd54-0a95-419f-b7ad-10000000ce07'), 'ALIAS');

INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-000000008117',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_alias where guid = '750abd54-0a95-419f-b7ad-10000000ce08'), 'ALIAS');
INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-000000008118',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_alias where guid = '750abd54-0a95-419f-b7ad-10000000ce08'), 'ALIAS');
INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-000000008119',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_alias where guid = '750abd54-0a95-419f-b7ad-10000000ce09'), 'ALIAS');
INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-000000008120',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_alias where guid = '750abd54-0a95-419f-b7ad-10000000ce09'), 'ALIAS');

-- ai_agent_tag_association_ for version

INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-000000008121',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_version where guid = '68d656d5-b067-472f-8f23-0000000050ca'), 'VERSION');
INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-000000008122',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_version where guid = '68d656d5-b067-472f-8f23-0000000050ca'), 'VERSION');
INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-000000008123',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_version where guid = '68d656d5-b067-472f-8f23-0000000050cb'), 'VERSION');
INSERT INTO ai_agent_tag_association (guid, tag_uid, related_entity_uid, related_entity_type)
VALUES ('10cc6c20-ffdd-42a3-a78f-000000008124',
        (select uid from ai_agent_tag where guid = '32cc6c20-ffdd-42a3-a78f-************'),
        (select uid from ai_agent_version where guid = '68d656d5-b067-472f-8f23-0000000050cc'), 'VERSION');

