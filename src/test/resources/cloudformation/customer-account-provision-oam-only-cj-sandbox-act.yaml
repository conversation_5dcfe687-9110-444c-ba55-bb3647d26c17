AWSTemplateFormatVersion: '2010-09-09'
Description: >-
  The CloudFormation template enables cross-account access between the Boomi Agent Control Tower and your AWS account, 
  to manage and monitor your Amazon Bedrock agents. The template creates an IAM role with the necessary permissions 
  to invoke Bedrock APIs and retrieve agent metadata.
  
  Additionally, it establishes sharing of CloudWatch metrics, through AWS Cloudwatch Observability Access Manager (OAM).
  This enables centralized monitoring of your Bedrock agents.
  
  The template must be deployed in your AWS account to grant secure and controlled access to the Agent Control Tower 
  to manage your Bedrock agents.

Mappings:
  ACTVariables:
    Configuration:
      ServiceAccountId: "************"
      OamSinkArn: "arn:aws:oam:us-west-2:************:sink/c28e10c7-3253-4f99-a4cc-773407397c9b"

Conditions:
  # Skip OAM Link creation in the ACT service account to prevent self-monitoring
  SkipMonitoringAccount: !Not [!Equals [!Ref 'AWS::AccountId', !FindInMap [ACTVariables, Configuration, ServiceAccountId]]]

Resources:
  # OAM Link for sharing CloudWatch metrics with ACT monitoring account
  Link:
    Type: AWS::Oam::Link
    Condition: SkipMonitoringAccount
    Properties:
      LabelTemplate: "${AWS::AccountId}"
      LinkConfiguration:
        MetricConfiguration:
          Filter: "Namespace IN ('AWS/Bedrock/Agents')"
      ResourceTypes:
        - "AWS::CloudWatch::Metric"
      # Pre-configured OAM sink in ACT account for collecting metrics
      SinkIdentifier: !FindInMap [ACTVariables, Configuration, OamSinkArn]
