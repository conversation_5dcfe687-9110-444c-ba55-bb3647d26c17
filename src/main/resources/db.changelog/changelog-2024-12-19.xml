<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="sandeshbhat" id="ai-agent-tag-2024.12.19-CJ-3365-0" context="PRE">
        <tagDatabase tag="ai-agent-tag-2024.12.19-CJ-3365"/>
    </changeSet>

    <changeSet author="sandeshbhat" id="ai-agent-tag-2024.12.19-CJ-3365-1">

        <preConditions onFail="MARK_RAN">
            <columnExists tableName="AI_AGENT_TAG" columnName="PROVIDER_ACCOUNT_UID"/>
        </preConditions>

        <dropForeignKeyConstraint
                baseTableName="AI_AGENT_TAG"
                constraintName="fk_ai_agent_provider_account_tag"/>

        <dropColumn tableName="AI_AGENT_TAG" columnName="PROVIDER_ACCOUNT_UID"/>

        <rollback>
            <addColumn tableName="AI_AGENT_TAG">
                <column name="PROVIDER_ACCOUNT_UID" type="INTEGER"/>
            </addColumn>
            <addForeignKeyConstraint
                    baseTableName="AI_AGENT_TAG"
                    baseColumnNames="PROVIDER_ACCOUNT_UID"
                    constraintName="fk_ai_agent_provider_account_tag"
                    referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"
                    referencedColumnNames="UID"/>
        </rollback>

    </changeSet>

    <changeSet author="sandeshbhat" id="ai-agent-tag-2024.12.19-CJ-3365-2">

        <preConditions onFail="MARK_RAN">
            <columnExists tableName="AI_AGENT_TAG_ASSOCIATION" columnName="CREATED_BY_ORIGIN"/>
        </preConditions>

        <dropColumn tableName="AI_AGENT_TAG_ASSOCIATION" columnName="CREATED_BY_ORIGIN"/>

        <rollback>
            <addColumn tableName="AI_AGENT_TAG_ASSOCIATION">
                <column name="CREATED_BY_ORIGIN" type="VARCHAR(100)">
                    <constraints nullable="true"/>
                </column>
            </addColumn>
        </rollback>


    </changeSet>


</databaseChangeLog>
