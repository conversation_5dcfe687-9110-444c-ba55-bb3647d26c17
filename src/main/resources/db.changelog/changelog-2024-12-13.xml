<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="subramanyahegde" id="ai-agent-task-2024.12.13-CJ-3532-0" context="PRE">
        <tagDatabase tag="ai-agent-task-2024.12.13-CJ-3532"/>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-task-2024.12.13-CJ-3532-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <not>
                <tableExists tableName="AI_AGENT_TASK"/>
            </not>
        </preConditions>
        <createTable tableName="AI_AGENT_TASK">
            <column autoIncrement="true" name="UID" type="SERIAL">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="GUID" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="EXTERNAL_ID" type="VARCHAR(2048)">
                <constraints nullable="false"/>
            </column>
            <column name="PROVIDER_ACCOUNT_UID" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="IDP_ACCOUNT_ID" type="VARCHAR(200)"/>
            <column name="VERSION_STRING" type="VARCHAR(100)"/>
            <column name="VERSION_INT" type="INTEGER"/>
            <column name="NAME" type="VARCHAR(250)"/>
            <column name="DESCRIPTION" type="VARCHAR(2000)"/>
            <column name="INSTRUCTIONS" type="VARCHAR(2000)"/>
            <!-- PENDING, RUNNING, COMPLETED, ERROR etc -->
            <column name="TASK_STATUS" type="VARCHAR(50)"/>
            <column name="CREATED_BY_USER_ID" type="VARCHAR(100)"/>
            <column name="CREATED_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="MODIFIED_BY_USER_ID" type="VARCHAR(100)"/>
            <column name="MODIFIED_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <!-- PROVIDER, REGISTRY -->
            <column name="UPDATED_BY_ORIGIN" type="VARCHAR(100)"/>
            <column name="UPDATED_AT_PROVIDER_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>

        <addUniqueConstraint tableName="AI_AGENT_TASK" columnNames="GUID"
                             constraintName="AI_AGENT_TASK_UNQ_GUID"/>

        <addForeignKeyConstraint baseColumnNames="PROVIDER_ACCOUNT_UID"
                                 baseTableName="AI_AGENT_TASK"
                                 constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT_TASK"
                                 referencedColumnNames="UID"
                                 referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"/>
        <rollback>
            <dropUniqueConstraint tableName="AI_AGENT_TASK"
                                  constraintName="AI_AGENT_TASK_UNQ_GUID"/>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_TASK"
                                      constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT_TASK"/>
            <dropTable tableName="AI_AGENT_TASK"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-task-2024.12.13-CJ-3532-2">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <not>
                <tableExists tableName="AI_AGENT_TASK_ASSOCIATION"/>
            </not>
        </preConditions>
        <createTable tableName="AI_AGENT_TASK_ASSOCIATION">
            <column autoIncrement="true" name="UID" type="SERIAL">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="GUID" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="TASK_UID" type="SERIAL">
                <constraints nullable="false"/>
            </column>
            <column name="RELATED_ENTITY_UID" type="SERIAL">
                <constraints nullable="false"/>
            </column>
            <!-- Agent_Version, etc -->
            <column name="RELATED_ENTITY_TYPE" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint tableName="AI_AGENT_TASK_ASSOCIATION"
                             columnNames="GUID"
                             constraintName="AI_AGENT_TASK_ASSOCIATION_UNQ_GUID"/>

        <addForeignKeyConstraint baseColumnNames="TASK_UID"
                                 baseTableName="AI_AGENT_TASK_ASSOCIATION"
                                 constraintName="FK_AI_AGENT_TASK_ASSOCIATION_AI_AGENT_TASK"
                                 referencedColumnNames="UID"
                                 referencedTableName="AI_AGENT_TASK"/>

        <rollback>
            <dropUniqueConstraint tableName="AI_AGENT_TASK_ASSOCIATION"
                                  constraintName="AI_AGENT_TASK_ASSOCIATION_UNQ_GUID"/>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_TASK_ASSOCIATION"
                                      constraintName="FK_AI_AGENT_TASK_ASSOCIATION_AI_AGENT_TASK"/>
            <dropTable tableName="AI_AGENT_TASK_ASSOCIATION"/>
        </rollback>
    </changeSet>
</databaseChangeLog>
