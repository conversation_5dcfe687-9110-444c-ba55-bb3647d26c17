<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-drop-monthly-tables-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.03.26-CJ-4520-drop-monthly-tables"/>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-drop-monthly-tables-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'sync_user_audit_202502'
                                     AND table_schema = current_schema());
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT EXISTS (SELECT 1
                               FROM information_schema.tables
                               WHERE table_name = 'sync_user_audit_202503'
                                 AND table_schema = current_schema());
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT EXISTS (SELECT 1
                               FROM information_schema.tables
                               WHERE table_name = 'sync_user_audit_202504'
                                 AND table_schema = current_schema());
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT EXISTS (SELECT 1
                               FROM information_schema.tables
                               WHERE table_name = 'sync_user_audit_202505'
                                 AND table_schema = current_schema());
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT EXISTS (SELECT 1
                               FROM information_schema.tables
                               WHERE table_name = 'sync_user_audit_202506'
                                 AND table_schema = current_schema());
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT EXISTS (SELECT 1
                               FROM information_schema.tables
                               WHERE table_name = 'sync_user_audit_202507'
                                 AND table_schema = current_schema());
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT EXISTS (SELECT 1
                               FROM information_schema.tables
                               WHERE table_name = 'sync_user_audit_202508'
                                 AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_202502;
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_202503;
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_202504;
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_202505;
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_202506;
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_202507;
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_202508;
        </sql>
    </changeSet>
</databaseChangeLog>
