<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="llm-unique-constraint-external-id-version-provider-version-str" author="azookari">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="ai_agent_llm"/>
            <columnExists tableName="ai_agent_llm" columnName="external_id"/>
            <columnExists tableName="ai_agent_llm" columnName="version_string"/>
            <columnExists tableName="ai_agent_llm" columnName="provider_account_uid"/>
        </preConditions>

        <addUniqueConstraint
                tableName="ai_agent_llm"
                columnNames="external_id, version_string, provider_account_uid"
                constraintName="llm_uk_agent_llm_ext_id_ver_str_provider"/>

        <rollback>
            <dropUniqueConstraint
                    tableName="ai_agent_llm"
                    constraintName="llm_uk_agent_llm_ext_id_ver_str_provider"/>
        </rollback>
    </changeSet>

    <changeSet id="llm-unique-constraint-external-id-version-provider-version-int" author="azookari">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="ai_agent_llm"/>
            <columnExists tableName="ai_agent_llm" columnName="external_id"/>
            <columnExists tableName="ai_agent_llm" columnName="version_int"/>
            <columnExists tableName="ai_agent_llm" columnName="provider_account_uid"/>
        </preConditions>

        <addUniqueConstraint
                tableName="ai_agent_llm"
                columnNames="external_id, version_int, provider_account_uid"
                constraintName="llm_uk_agent_llm_ext_id_ver_int_provider"/>

        <rollback>
            <dropUniqueConstraint
                    tableName="ai_agent_llm"
                    constraintName="llm_uk_agent_llm_ext_id_ver_int_provider"/>
        </rollback>
    </changeSet>

    <changeSet id="guardrail-unique-constraint-external-id-version-provider-version-str" author="azookari">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="ai_agent_guardrail"/>
            <columnExists tableName="ai_agent_guardrail" columnName="external_id"/>
            <columnExists tableName="ai_agent_guardrail" columnName="version_string"/>
            <columnExists tableName="ai_agent_guardrail" columnName="provider_account_uid"/>
        </preConditions>

        <addUniqueConstraint
                tableName="ai_agent_guardrail"
                columnNames="external_id, version_string, provider_account_uid"
                constraintName="uk_guardrail_ext_id_ver_str_provider"/>

        <rollback>
            <dropUniqueConstraint
                    tableName="ai_agent_guardrail"
                    constraintName="uk_guardrail_ext_id_ver_str_provider"/>
        </rollback>
    </changeSet>

    <changeSet id="guardrail-unique-constraint-external-id-version-provider-version-int" author="azookari">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="ai_agent_guardrail"/>
            <columnExists tableName="ai_agent_guardrail" columnName="external_id"/>
            <columnExists tableName="ai_agent_guardrail" columnName="version_int"/>
            <columnExists tableName="ai_agent_guardrail" columnName="provider_account_uid"/>
        </preConditions>

        <addUniqueConstraint
                tableName="ai_agent_guardrail"
                columnNames="external_id, version_int, provider_account_uid"
                constraintName="uk_guardrail_ext_id_ver_int_provider"/>

        <rollback>
            <dropUniqueConstraint
                    tableName="ai_agent_guardrail"
                    constraintName="uk_guardrail_ext_id_ver_int_provider"/>
        </rollback>
    </changeSet>


</databaseChangeLog>


