<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="azookari" id="ai-agent-sync-CJ-4606" context="PRE">
        <!-- Preconditions to ensure the table and column exist and column is not null -->
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="ai_agent_llm"/>
            <columnExists tableName="ai_agent_llm" columnName="name"/>
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_NAME = 'ai_agent_llm'
                  AND COLUMN_NAME = 'name'
                  AND IS_NULLABLE = 'YES'
            </sqlCheck>
        </preConditions>

        <dropNotNullConstraint
                tableName="ai_agent_llm"
                columnName="name"
                columnDataType="varchar(255)"/>

        <rollback>
            <addNotNullConstraint
                    tableName="ai_agent_llm"
                    columnName="name"
                    columnDataType="varchar(255)"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
