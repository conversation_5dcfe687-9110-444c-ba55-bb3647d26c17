<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">


    <changeSet author="azookari" id="ai-agent-registry-2024.11.08-CJ-3327-1">
        <preConditions onFail="MARK_RAN" onError="CONTINUE">
            <not>
                <columnExists tableName="AI_AGENT_PROVIDER_ACCOUNT" columnName="AUTH_SCHEMA"/>
            </not>
        </preConditions>

        <addColumn tableName="AI_AGENT_PROVIDER_ACCOUNT">
            <column name="AUTH_SCHEMA" type="VARCHAR(255)" />
        </addColumn>
        <rollback>
            <dropColumn tableName="AI_AGENT_PROVIDER_ACCOUNT" columnName="AUTH_SCHEMA" />
        </rollback>
    </changeSet>
</databaseChangeLog>
