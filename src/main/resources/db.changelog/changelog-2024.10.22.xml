<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-1" context="PRE">
        <tagDatabase tag="ai-agent-registry-2024.10.22-CJ-3263"/>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-2">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <not>
                <tableExists tableName="AI_AGENT_PROVIDER_ACCOUNT"/>
            </not>
        </preConditions>
        <createTable tableName="AI_AGENT_PROVIDER_ACCOUNT">
            <column autoIncrement="true" name="UID" type="SERIAL">
                   <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="GUID" type="UUID">
                <constraints nullable="false"/>
            </column>
            <!--  AWS Bedrock, Boomi, etc-->
            <column name="PROVIDER_TYPE" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <!-- Region in which provider's account exists -->
            <column name="REGION" type="VARCHAR(256)"/>
            <column name="PROVIDER_METADATA" type="JSONB"/>
            <!-- Unique identifier for the Identity Provider (IDP) account, e.g., Boomi Platform Account ID -->
            <column name="IDP_ACCOUNT_ID" type="VARCHAR(200)"/>
            <!-- AWS secrets key name that has the provider specific credentials -->
            <column name="CREDENTIALS_KEY" type="VARCHAR(500)"/>
            <column name="PROVIDER_ACCOUNT_NAME" type="VARCHAR(200)"/>
            <column name="CREATED_BY_USER_ID" type="VARCHAR(36)"/>
            <column name="CREATED_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="MODIFIED_BY_USER_ID" type="VARCHAR(36)"/>
            <column name="MODIFIED_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <!-- enabled, disabled, syncing, preparing -->
            <column name="PROVIDER_ACCOUNT_STATUS" type="VARCHAR(50)"/>
            <column name="IS_DELETED" type="BOOLEAN" defaultValue="false" />
        </createTable>
        <addUniqueConstraint tableName="AI_AGENT_PROVIDER_ACCOUNT" columnNames="GUID"
                             constraintName="AI_AGENT_PROVIDER_ACCOUNT_UNQ_GUID" />
        <rollback>
            <dropUniqueConstraint tableName="AI_AGENT_PROVIDER_ACCOUNT"
                                          constraintName="AI_AGENT_PROVIDER_ACCOUNT_UNQ_GUID"/>
            <dropTable tableName="AI_AGENT_PROVIDER_ACCOUNT"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-3">
         <preConditions onFail="MARK_RAN" onError="HALT">
             <not>
                 <tableExists tableName="AGENT_ENTITY_SYNC_HISTORY"/>
             </not>
         </preConditions>
         <createTable tableName="AGENT_ENTITY_SYNC_HISTORY">
             <column autoIncrement="true" name="UID" type="SERIAL">
                    <constraints nullable="false" primaryKey="true"/>
             </column>
             <column name="PROVIDER_ACCOUNT_UID" type="INTEGER">
                 <constraints nullable="false"/>
             </column>
             <column name="SYNC_START_DATE" type="TIMESTAMP WITHOUT TIME ZONE">
                 <constraints nullable="false"/>
             </column>
             <column name="SYNC_END_DATE" type="TIMESTAMP WITHOUT TIME ZONE" />
             <column name="SYNC_STATUS" type="VARCHAR(50)">
                 <constraints nullable="false"/>
             </column>
             <column name="NUMBER_OF_ENTITIES_ADDED" type="INTEGER"/>
             <column name="NUMBER_OF_ENTITIES_REMOVED" type="INTEGER"/>
             <column name="NUMBER_OF_ENTITIES_UPDATED" type="INTEGER"/>
             <column name="TOTAL_ITERATIONS_TO_BE_DONE" type="INTEGER"/>
             <column name="ACTUAL_ITERATIONS_DONE" type="INTEGER"/>
             <column name="SYNC_DETAILS" type="VARCHAR(4000)"/>
             <!-- Store sync details in s3 if the DB column would not be sufficient -->
             <column name="SYNC_DETAILS_S3_KEY" type="VARCHAR(500)"/>
             <column name="SYNCED_ENTITY_UID" type="SERIAL">
                <constraints nullable="false"/>
             </column>
             <!-- PROVIDER, AGENT, TOOL, etc-->
             <column name="SYNCED_ENTITY_TYPE" type="VARCHAR(50)">
                 <constraints nullable="false"/>
             </column>
         </createTable>

         <addForeignKeyConstraint baseColumnNames="PROVIDER_ACCOUNT_UID"
                                  baseTableName="AGENT_ENTITY_SYNC_HISTORY"
                                  constraintName="FK_AGENT_PROVIDER_ACCOUNT_ENTITY_SYNC_HISTORY"
                                  referencedColumnNames="UID"
                                  referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"/>
         <rollback>
             <dropForeignKeyConstraint baseTableName="AGENT_ENTITY_SYNC_HISTORY"
                                       constraintName="FK_AGENT_PROVIDER_ACCOUNT_ENTITY_SYNC_HISTORY"/>
             <dropTable tableName="AGENT_ENTITY_SYNC_HISTORY"/>
         </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-4">
          <preConditions onFail="MARK_RAN" onError="HALT">
              <not>
                  <tableExists tableName="AI_AGENT"/>
              </not>
          </preConditions>
          <createTable tableName="AI_AGENT">
              <column autoIncrement="true" name="UID" type="SERIAL">
                     <constraints nullable="false" primaryKey="true"/>
              </column>
              <column name="GUID" type="UUID">
                  <constraints nullable="false"/>
              </column>
              <column name="EXTERNAL_ID" type="VARCHAR(2048)">
                  <constraints nullable="false"/>
              </column>
              <column name="PROVIDER_ACCOUNT_UID" type="INTEGER">
                  <constraints nullable="false"/>
              </column>
              <!-- Unique identifier for the Identity Provider (IDP) account, e.g., Boomi Platform Account ID -->
              <column name="IDP_ACCOUNT_ID" type="VARCHAR(200)"/>
              <column name="IS_DELETED" type="BOOLEAN" defaultValue="false" />
          </createTable>

          <addUniqueConstraint tableName="AI_AGENT" columnNames="GUID"
                                     constraintName="AI_AGENT_UNQ_GUID" />
          <addForeignKeyConstraint baseColumnNames="PROVIDER_ACCOUNT_UID"
                                 baseTableName="AI_AGENT"
                                 constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT"
                                 referencedColumnNames="UID"
                                 referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"/>
          <rollback>
              <dropUniqueConstraint tableName="AI_AGENT"
                                            constraintName="AI_AGENT_UNQ_GUID"/>
              <dropForeignKeyConstraint baseTableName="AI_AGENT"
                                        constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT"/>
              <dropTable tableName="AI_AGENT"/>
          </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-5">
          <preConditions onFail="MARK_RAN" onError="HALT">
              <not>
                  <tableExists tableName="AI_AGENT_LLM"/>
              </not>
          </preConditions>
          <createTable tableName="AI_AGENT_LLM">
              <column autoIncrement="true" name="UID" type="SERIAL">
                     <constraints nullable="false" primaryKey="true"/>
              </column>
              <column name="GUID" type="UUID">
                  <constraints nullable="false"/>
              </column>
              <column name="EXTERNAL_ID" type="VARCHAR(2048)">
                  <constraints nullable="false"/>
              </column>
              <column name="PROVIDER_ACCOUNT_UID" type="INTEGER">
                  <constraints nullable="false"/>
              </column>
              <column name="VERSION_STRING" type="VARCHAR(100)"/>
              <column name="VERSION_INT" type="INTEGER"/>
              <column name="NAME" type="VARCHAR(1000)">
                  <constraints nullable="false"/>
              </column>
              <column name="DESCRIPTION" type="VARCHAR(2000)"/>
          </createTable>

          <addUniqueConstraint tableName="AI_AGENT_LLM" columnNames="GUID"
                                             constraintName="AI_AGENT_LLM_UNQ_GUID" />
          <addForeignKeyConstraint baseColumnNames="PROVIDER_ACCOUNT_UID"
                                        baseTableName="AI_AGENT_LLM"
                                        constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_LLM"
                                        referencedColumnNames="UID"
                                        referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"/>
          <rollback>
              <dropUniqueConstraint tableName="AI_AGENT_LLM" constraintName="AI_AGENT_LLM_UNQ_GUID" />
              <dropForeignKeyConstraint baseTableName="AI_AGENT_LLM"
                                        constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_LLM"/>
              <dropTable tableName="AI_AGENT_LLM"/>
          </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-6">
          <preConditions onFail="MARK_RAN" onError="HALT">
              <not>
                  <tableExists tableName="AI_AGENT_VERSION"/>
              </not>
          </preConditions>
          <createTable tableName="AI_AGENT_VERSION">
              <column autoIncrement="true" name="UID" type="SERIAL">
                     <constraints nullable="false" primaryKey="true"/>
              </column>
              <column name="GUID" type="UUID">
                  <constraints nullable="false"/>
              </column>
              <column name="EXTERNAL_ID" type="VARCHAR(2048)">
                  <constraints nullable="false"/>
              </column>
              <column name="AGENT_UID" type="SERIAL">
                  <constraints nullable="false"/>
              </column>
              <column name="VERSION_STRING" type="VARCHAR(100)"/>
              <column name="VERSION_INT" type="INTEGER"/>
              <column name="NAME" type="VARCHAR(1000)">
                  <constraints nullable="false"/>
              </column>
              <column name="DESCRIPTION" type="VARCHAR(2000)"/>
              <column name="REGION" type="VARCHAR(256)"/>
              <column name="PURPOSE" type="jsonb"/>
              <column name="PERSONALITY_TRAITS" type="jsonb"/>
              <column name="INSTRUCTIONS" type="VARCHAR(2000)"/>
              <column name="INSTRUCTIONS_S3_KEY" type="VARCHAR(500)"/>
              <column name="CREATED_IN_REGISTRY" type="BOOLEAN"/>
              <column name="CREATED_BY_USER_ID" type="VARCHAR(100)"/>
              <column name="CREATED_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
              <column name="MODIFIED_BY_USER_ID" type="VARCHAR(100)"/>
              <column name="MODIFIED_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
              <!-- CREATING, PREPARED, UPDATING, etc -->
              <column name="AGENT_STATUS" type="VARCHAR(50)"/>
              <!-- PROVIDER, REGISTRY -->
              <column name="UPDATED_BY_ORIGIN" type="VARCHAR(100)"/>
              <column name="UPDATED_AT_PROVIDER_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
              <column name="IS_DELETED" type="BOOLEAN" defaultValue="false" />
              <!-- ENDORSED, UNENDORSED, etc-->
              <column name="TRUST_LEVEL" type="VARCHAR(50)" />
              <!-- PROVIDER, REGISTRY -->
              <column name="CREATED_BY_ORIGIN" type="VARCHAR(100)"/>
          </createTable>

          <addUniqueConstraint tableName="AI_AGENT_VERSION" columnNames="GUID"
                                             constraintName="AI_AGENT_VERSION_UNQ_GUID" />
          <addForeignKeyConstraint baseColumnNames="AGENT_UID"
                                 baseTableName="AI_AGENT_VERSION"
                                 constraintName="FK_AI_AGENT_VERSION_AI_AGENT"
                                 referencedColumnNames="UID"
                                 referencedTableName="AI_AGENT"/>
          <rollback>
              <dropUniqueConstraint tableName="AI_AGENT_VERSION"
                                           constraintName="AI_AGENT_VERSION_UNQ_GUID"/>
              <dropForeignKeyConstraint baseTableName="AI_AGENT_VERSION"
                                        constraintName="FK_AI_AGENT_VERSION_AI_AGENT"/>
              <dropTable tableName="AI_AGENT_VERSION"/>
          </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-7">
           <preConditions onFail="MARK_RAN" onError="HALT">
               <not>
                   <tableExists tableName="AI_AGENT_LLM_ASSOCIATION"/>
               </not>
           </preConditions>
           <createTable tableName="AI_AGENT_LLM_ASSOCIATION">
               <column autoIncrement="true" name="UID" type="SERIAL">
                      <constraints nullable="false" primaryKey="true"/>
               </column>
               <column name="GUID" type="UUID">
                   <constraints nullable="false"/>
               </column>
               <column name="LLM_UID" type="SERIAL">
                   <constraints nullable="false"/>
               </column>
               <column name="RELATED_ENTITY_UID" type="SERIAL">
                   <constraints nullable="false"/>
               </column>
               <!-- AGENT_VERSION, TOOL, etc -->
               <column name="RELATED_ENTITY_TYPE" type="VARCHAR(50)">
                   <constraints nullable="false"/>
               </column>
           </createTable>

           <addUniqueConstraint tableName="AI_AGENT_LLM_ASSOCIATION"
                                 columnNames="GUID"
                                 constraintName="AI_AGENT_LLM_ASSOCIATION_UNQ_GUID"/>

           <addForeignKeyConstraint baseColumnNames="LLM_UID"
                                 baseTableName="AI_AGENT_LLM_ASSOCIATION"
                                 constraintName="FK_LLM_ASSOC_LLM"
                                 referencedColumnNames="UID"
                                 referencedTableName="AI_AGENT_LLM"/>
           <rollback>
               <dropUniqueConstraint tableName="AI_AGENT_LLM_ASSOCIATION"
                                            constraintName="AI_AGENT_LLM_ASSOCIATION_UNQ_GUID"/>
               <dropForeignKeyConstraint baseTableName="AI_AGENT_LLM_ASSOCIATION"
                                         constraintName="FK_LLM_ASSOC_LLM"/>
               <dropTable tableName="AI_AGENT_LLM_ASSOCIATION"/>
           </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-8">
          <preConditions onFail="MARK_RAN" onError="HALT">
              <not>
                  <tableExists tableName="AI_AGENT_ALIAS"/>
              </not>
          </preConditions>
          <createTable tableName="AI_AGENT_ALIAS">
              <column autoIncrement="true" name="UID" type="SERIAL">
                     <constraints nullable="false" primaryKey="true"/>
              </column>
              <column name="GUID" type="UUID">
                  <constraints nullable="false"/>
              </column>
              <column name="EXTERNAL_ID" type="VARCHAR(2048)">
                  <constraints nullable="false"/>
              </column>
              <column name="AGENT_VERSION_UID" type="SERIAL">
                  <constraints nullable="false"/>
              </column>
              <column name="NAME" type="VARCHAR(1000)"/>
              <column name="DESCRIPTION" type="VARCHAR(2000)"/>
              <column name="CREATED_BY_USER_ID" type="VARCHAR(100)"/>
              <column name="CREATED_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
              <column name="MODIFIED_BY_USER_ID" type="VARCHAR(100)"/>
              <column name="MODIFIED_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
              <!-- PROVIDER, REGISTRY -->
              <column name="UPDATED_BY_ORIGIN" type="VARCHAR(100)"/>
              <column name="UPDATED_AT_PROVIDER_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
              <column name="IS_DELETED" type="BOOLEAN" defaultValue="false" />
          </createTable>

          <addUniqueConstraint tableName="AI_AGENT_ALIAS" columnNames="GUID"
                                                     constraintName="AI_AGENT_ALIAS_UNQ_GUID" />
          <addForeignKeyConstraint baseColumnNames="AGENT_VERSION_UID"
                                 baseTableName="AI_AGENT_ALIAS"
                                 constraintName="FK_AGENT_ALIAS_VERSION"
                                 referencedColumnNames="UID"
                                 referencedTableName="AI_AGENT_VERSION"/>
          <rollback>
              <dropUniqueConstraint tableName="AI_AGENT_ALIAS"
                                            constraintName="AI_AGENT_ALIAS_UNQ_GUID"/>
              <dropForeignKeyConstraint baseTableName="AI_AGENT_ALIAS"
                                        constraintName="FK_AGENT_ALIAS_VERSION"/>
              <dropTable tableName="AI_AGENT_ALIAS"/>
          </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-9">
           <preConditions onFail="MARK_RAN" onError="HALT">
               <not>
                   <tableExists tableName="AI_AGENT_METRICS"/>
               </not>
           </preConditions>
           <createTable tableName="AI_AGENT_METRICS">
               <column autoIncrement="true" name="UID" type="SERIAL">
                      <constraints nullable="false" primaryKey="true"/>
               </column>
               <column name="OBTAINED_AT" type="TIMESTAMP WITHOUT TIME ZONE"/>
               <column name="REQUEST" type="JSONB"/>
               <column name="PERFORMANCE" type="JSONB"/>
               <column name="RESOURCE_UTILIZATION" type="JSONB"/>
               <column name="ERROR_RATE" type="JSONB"/>
               <column name="COST" type="JSONB"/>
           </createTable>
           <rollback>
               <dropTable tableName="AI_AGENT_METRICS"/>
           </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-10">
           <preConditions onFail="MARK_RAN" onError="HALT">
               <not>
                   <tableExists tableName="AI_AGENT_METRICS_ASSOCIATION"/>
               </not>
           </preConditions>
           <createTable tableName="AI_AGENT_METRICS_ASSOCIATION">
               <column autoIncrement="true" name="UID" type="SERIAL">
                      <constraints nullable="false" primaryKey="true"/>
               </column>
               <column name="GUID" type="UUID">
                   <constraints nullable="false"/>
               </column>
               <column name="AI_AGENT_METRICS_UID" type="SERIAL">
                   <constraints nullable="false"/>
               </column>
               <column name="RELATED_ENTITY_UID" type="SERIAL">
                   <constraints nullable="false"/>
               </column>
               <!-- Alias, TOOL, etc -->
               <column name="RELATED_ENTITY_TYPE" type="VARCHAR(50)">
                   <constraints nullable="false"/>
               </column>
           </createTable>

           <addUniqueConstraint tableName="AI_AGENT_METRICS_ASSOCIATION"
                                columnNames="GUID"
                                constraintName="AI_AGENT_METRICS_ASSOCIATION_UNQ_GUID"/>

           <addForeignKeyConstraint baseColumnNames="AI_AGENT_METRICS_UID"
                                 baseTableName="AI_AGENT_METRICS_ASSOCIATION"
                                 constraintName="FK_AGENT_METRICS_ASSOC_METRICS"
                                 referencedColumnNames="UID"
                                 referencedTableName="AI_AGENT_METRICS"/>
           <rollback>
               <dropUniqueConstraint tableName="AI_AGENT_METRICS_ASSOCIATION"
                                             constraintName="AI_AGENT_METRICS_ASSOCIATION_UNQ_GUID"/>
               <dropForeignKeyConstraint baseTableName="AI_AGENT_METRICS_ASSOCIATION"
                                         constraintName="FK_AGENT_METRICS_ASSOC_METRICS"/>
               <dropTable tableName="AI_AGENT_METRICS_ASSOCIATION"/>
           </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-11">
           <preConditions onFail="MARK_RAN" onError="HALT">
               <not>
                   <tableExists tableName="AI_AGENT_TOOL"/>
               </not>
           </preConditions>
          <createTable tableName="AI_AGENT_TOOL">
              <column autoIncrement="true" name="UID" type="SERIAL">
                     <constraints nullable="false" primaryKey="true"/>
              </column>
              <column name="GUID" type="UUID">
                  <constraints nullable="false"/>
              </column>
              <column name="EXTERNAL_ID" type="VARCHAR(2048)">
                  <constraints nullable="false"/>
              </column>
              <column name="PROVIDER_ACCOUNT_UID" type="INTEGER">
                  <constraints nullable="false"/>
              </column>
              <!-- Unique identifier for the Identity Provider (IDP) account, e.g., Boomi Platform Account ID -->
              <column name="IDP_ACCOUNT_ID" type="VARCHAR(200)"/>
              <column name="VERSION_STRING" type="VARCHAR(100)"/>
              <column name="VERSION_INT" type="INTEGER"/>
              <column name="NAME" type="VARCHAR(1000)"/>
              <column name="DESCRIPTION" type="VARCHAR(2000)"/>
              <column name="CREATED_BY_USER_ID" type="VARCHAR(100)"/>
              <column name="CREATED_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
              <column name="MODIFIED_BY_USER_ID" type="VARCHAR(100)"/>
              <column name="MODIFIED_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
              <!-- PROVIDER, REGISTRY -->
              <column name="UPDATED_BY_ORIGIN" type="VARCHAR(100)"/>
              <column name="UPDATED_AT_PROVIDER_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
          </createTable>

          <addUniqueConstraint tableName="AI_AGENT_TOOL"
                                 columnNames="GUID"
                                 constraintName="AI_AGENT_TOOL_UNQ_GUID"/>

          <addForeignKeyConstraint baseColumnNames="PROVIDER_ACCOUNT_UID"
                                 baseTableName="AI_AGENT_TOOL"
                                 constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AGENT_TOOL"
                                 referencedColumnNames="UID"
                                 referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"/>

          <rollback>
              <dropUniqueConstraint tableName="AI_AGENT_TOOL"
                                         constraintName="AI_AGENT_TOOL_UNQ_GUID"/>
              <dropForeignKeyConstraint baseTableName="AI_AGENT_TOOL"
                                     constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AGENT_TOOL"/>
              <dropTable tableName="AI_AGENT_TOOL"/>
          </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-12">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <not>
                <tableExists tableName="AI_AGENT_TOOL_ASSOCIATION"/>
            </not>
        </preConditions>
        <createTable tableName="AI_AGENT_TOOL_ASSOCIATION">
            <column autoIncrement="true" name="UID" type="SERIAL">
                   <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="GUID" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="TOOL_UID" type="SERIAL">
                <constraints nullable="false"/>
            </column>
            <column name="RELATED_ENTITY_UID" type="SERIAL">
                <constraints nullable="false"/>
            </column>
            <!-- Agent_Version, etc -->
            <column name="RELATED_ENTITY_TYPE" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint tableName="AI_AGENT_TOOL_ASSOCIATION"
                                 columnNames="GUID"
                                 constraintName="AI_AGENT_TOOL_ASSOCIATION_UNQ_GUID"/>

        <addForeignKeyConstraint baseColumnNames="TOOL_UID"
                                 baseTableName="AI_AGENT_TOOL_ASSOCIATION"
                                 constraintName="FK_TOOL_ASSOC_TOOL"
                                 referencedColumnNames="UID"
                                 referencedTableName="AI_AGENT_TOOL"/>

        <rollback>
            <dropUniqueConstraint tableName="AI_AGENT_TOOL_ASSOCIATION"
                                  constraintName="AI_AGENT_TOOL_ASSOCIATION_UNQ_GUID"/>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_TOOL_ASSOCIATION"
                                      constraintName="FK_TOOL_ASSOC_TOOL"/>
            <dropTable tableName="AI_AGENT_TOOL_ASSOCIATION"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-13">
            <preConditions onFail="MARK_RAN" onError="HALT">
                <not>
                    <tableExists tableName="AI_AGENT_GUARDRAIL"/>
                </not>
            </preConditions>
           <createTable tableName="AI_AGENT_GUARDRAIL">
               <column autoIncrement="true" name="UID" type="SERIAL">
                      <constraints nullable="false" primaryKey="true"/>
               </column>
               <column name="GUID" type="UUID">
                   <constraints nullable="false"/>
               </column>
               <column name="EXTERNAL_ID" type="VARCHAR(2048)">
                   <constraints nullable="false"/>
               </column>
               <!-- Unique identifier for the Identity Provider (IDP) account, e.g., Boomi Platform Account ID -->
               <column name="IDP_ACCOUNT_ID" type="VARCHAR(200)"/>
               <column name="PROVIDER_ACCOUNT_UID" type="INTEGER">
                   <constraints nullable="false"/>
               </column>
               <column name="VERSION_STRING" type="VARCHAR(100)"/>
               <column name="VERSION_INT" type="INTEGER"/>
               <column name="NAME" type="VARCHAR(1000)"/>
               <column name="DESCRIPTION" type="VARCHAR(2000)"/>
               <column name="CREATED_BY_USER_ID" type="VARCHAR(100)"/>
               <column name="CREATED_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
               <column name="MODIFIED_BY_USER_ID" type="VARCHAR(100)"/>
               <column name="MODIFIED_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
               <!-- PROVIDER, REGISTRY -->
               <column name="UPDATED_BY_ORIGIN" type="VARCHAR(100)"/>
               <column name="UPDATED_AT_PROVIDER_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
           </createTable>

           <addUniqueConstraint tableName="AI_AGENT_GUARDRAIL"
                                 columnNames="GUID"
                                 constraintName="AI_AGENT_GUARDRAIL_UNQ_GUID"/>

           <addForeignKeyConstraint baseColumnNames="PROVIDER_ACCOUNT_UID"
                                  baseTableName="AI_AGENT_GUARDRAIL"
                                  constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AGENT_GUARDRAIL"
                                  referencedColumnNames="UID"
                                  referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"/>

           <rollback>
                <dropUniqueConstraint tableName="AI_AGENT_GUARDRAIL"
                                              constraintName="AI_AGENT_GUARDRAIL_UNQ_GUID"/>
                <dropForeignKeyConstraint baseTableName="AI_AGENT_GUARDRAIL"
                                          constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AGENT_GUARDRAIL"/>
                <dropTable tableName="AI_AGENT_GUARDRAIL"/>
           </rollback>
     </changeSet>

     <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-14">
         <preConditions onFail="MARK_RAN" onError="HALT">
             <not>
                 <tableExists tableName="AI_AGENT_GUARDRAIL_ASSOCIATION"/>
             </not>
         </preConditions>
         <createTable tableName="AI_AGENT_GUARDRAIL_ASSOCIATION">
             <column autoIncrement="true" name="UID" type="SERIAL">
                    <constraints nullable="false" primaryKey="true"/>
             </column>
             <column name="GUID" type="UUID">
                 <constraints nullable="false"/>
             </column>
             <column name="GUARDRAIL_UID" type="SERIAL">
                 <constraints nullable="false"/>
             </column>
             <column name="RELATED_ENTITY_UID" type="SERIAL">
                 <constraints nullable="false"/>
             </column>
             <!-- Agent_Version, etc -->
             <column name="RELATED_ENTITY_TYPE" type="VARCHAR(50)" >
                 <constraints nullable="false"/>
             </column>
         </createTable>

         <addUniqueConstraint tableName="AI_AGENT_GUARDRAIL_ASSOCIATION"
                                 columnNames="GUID"
                                 constraintName="AI_AGENT_GUARDRAIL_ASSOCIATION_UNQ_GUID"/>

         <addForeignKeyConstraint baseColumnNames="GUARDRAIL_UID"
                                  baseTableName="AI_AGENT_GUARDRAIL_ASSOCIATION"
                                  constraintName="FK_GUARDRAIL_ASSOC_GUARDRAIL"
                                  referencedColumnNames="UID"
                                  referencedTableName="AI_AGENT_GUARDRAIL"/>

         <rollback>
             <dropUniqueConstraint tableName="AI_AGENT_GUARDRAIL_ASSOCIATION"
                                           constraintName="AI_AGENT_GUARDRAIL_ASSOCIATION_UNQ_GUID"/>
             <dropForeignKeyConstraint baseTableName="AI_AGENT_GUARDRAIL_ASSOCIATION"
                                       constraintName="FK_GUARDRAIL_ASSOC_GUARDRAIL"/>
             <dropTable tableName="AI_AGENT_GUARDRAIL_ASSOCIATION"/>
         </rollback>
     </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-15">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <not>
                <tableExists tableName="AI_AGENT_TAG"/>
            </not>
        </preConditions>

        <createTable tableName="AI_AGENT_TAG">
            <column autoIncrement="true" name="UID" type="SERIAL">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="GUID" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="PROVIDER_ACCOUNT_UID" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <!-- Unique identifier for the Identity Provider (IDP) account, e.g., Boomi Platform Account ID -->
            <column name="IDP_ACCOUNT_ID" type="VARCHAR(200)"/>
            <column name="KEY" type="VARCHAR(255)" />
            <column name="VALUE" type="VARCHAR(255)" >
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint tableName="AI_AGENT_TAG"
                                 columnNames="GUID"
                                 constraintName="AI_AGENT_TAG_UNQ_GUID"/>
        <addForeignKeyConstraint baseColumnNames="PROVIDER_ACCOUNT_UID"
                               baseTableName="AI_AGENT_TAG"
                               constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_TAG"
                               referencedColumnNames="UID"
                               referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"/>
        <rollback>
            <dropUniqueConstraint tableName="AI_AGENT_TAG"
                                  constraintName="AI_AGENT_TAG_UNQ_GUID"/>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_TAG"
                                      constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_TAG"/>
            <dropTable tableName="AI_AGENT_TAG"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-16">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <not>
                <tableExists tableName="AI_AGENT_TAG_ASSOCIATION"/>
            </not>
        </preConditions>

        <createTable tableName="AI_AGENT_TAG_ASSOCIATION">
            <column autoIncrement="true" name="UID" type="SERIAL">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="GUID" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="TAG_UID" type="SERIAL">
                <constraints nullable="false"/>
            </column>
            <column name="RELATED_ENTITY_UID" type="SERIAL">
                <constraints nullable="false"/>
            </column>
            <!-- The type of the related entity such as agent_version, etc -->
            <column name="RELATED_ENTITY_TYPE" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <!-- PROVIDER, REGISTRY -->
            <column name="CREATED_BY_ORIGIN" type="VARCHAR(100)"/>
        </createTable>

        <addUniqueConstraint tableName="AI_AGENT_TAG_ASSOCIATION"
                                 columnNames="GUID"
                                 constraintName="AI_AGENT_TAG_ASSOCIATION_UNQ_GUID"/>

        <addForeignKeyConstraint baseColumnNames="TAG_UID"
                                 baseTableName="AI_AGENT_TAG_ASSOCIATION"
                                 constraintName="FK_TAG_ASSOC_TAG"
                                 referencedColumnNames="UID"
                                 referencedTableName="AI_AGENT_TAG"/>

        <rollback>
            <dropUniqueConstraint tableName="AI_AGENT_TAG_ASSOCIATION"
                                         constraintName="AI_AGENT_TAG_ASSOCIATION_UNQ_GUID"/>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_TAG_ASSOCIATION"
                                      constraintName="FK_TAG_ASSOC_TAG"/>
            <dropTable tableName="AI_AGENT_TAG_ASSOCIATION"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.10.22-CJ-3263-17">
         <preConditions onFail="MARK_RAN" onError="HALT">
             <not>
                 <tableExists tableName="AGENT_ENTITY_SYNC_LATEST"/>
             </not>
         </preConditions>
         <createTable tableName="AGENT_ENTITY_SYNC_LATEST">
             <column autoIncrement="true" name="UID" type="SERIAL">
                    <constraints nullable="false" primaryKey="true"/>
             </column>
             <column name="AGENT_ENTITY_SYNC_HISTORY_UID" type="SERIAL">
                 <constraints nullable="false"/>
             </column>
             <column name="SYNC_START_DATE" type="TIMESTAMP WITHOUT TIME ZONE">
                 <constraints nullable="false"/>
             </column>
             <column name="SYNC_END_DATE" type="TIMESTAMP WITHOUT TIME ZONE" />
             <column name="SYNC_STATUS" type="VARCHAR(50)">
                 <constraints nullable="false"/>
             </column>
             <column name="SYNCED_ENTITY_UID" type="SERIAL">
                <constraints nullable="false"/>
             </column>
             <!-- PROVIDER, AGENT, TOOL, etc-->
             <column name="SYNCED_ENTITY_TYPE" type="VARCHAR(50)">
                 <constraints nullable="false"/>
             </column>
         </createTable>

         <addForeignKeyConstraint baseColumnNames="AGENT_ENTITY_SYNC_HISTORY_UID"
                                  baseTableName="AGENT_ENTITY_SYNC_LATEST"
                                  constraintName="FK_AGENT_ENTITY_SYNC_LATEST_HISTORY"
                                  referencedColumnNames="UID"
                                  referencedTableName="AGENT_ENTITY_SYNC_HISTORY"/>
         <rollback>
             <dropForeignKeyConstraint baseTableName="AGENT_ENTITY_SYNC_LATEST"
                                       constraintName="FK_AGENT_ENTITY_SYNC_LATEST_HISTORY"/>
             <dropTable tableName="AGENT_ENTITY_SYNC_LATEST"/>
         </rollback>
    </changeSet>
</databaseChangeLog>
