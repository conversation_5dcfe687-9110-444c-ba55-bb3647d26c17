<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.01.13-CJ-3698-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.01.13-CJ-3698"/>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.01.13-CJ-3698-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
               <indexExists
                   indexName="AGENT_PROVIDER_ACCOUNT_UNQ_EXTERNAL_PROVIDER_ACCOUNT_ID_TYPE"/>
        </preConditions>

        <!-- drop existing constraint for EXTERNAL_PROVIDER_ACCOUNT_ID and TYPE -->
        <dropUniqueConstraint
                            tableName="AI_AGENT_PROVIDER_ACCOUNT"
                            constraintName="AGENT_PROVIDER_ACCOUNT_UNQ_EXTERNAL_PROVIDER_ACCOUNT_ID_TYPE"/>

        <rollback>
            <addUniqueConstraint
                    tableName="AI_AGENT_PROVIDER_ACCOUNT"
                    columnNames="EXTERNAL_PROVIDER_ACCOUNT_ID, PROVIDER_TYPE"
                    constraintName="AGENT_PROVIDER_ACCOUNT_UNQ_EXTERNAL_PROVIDER_ACCOUNT_ID_TYPE"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.01.13-CJ-3698-2">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <and>
                <not>
                   <indexExists
                       indexName="AGENT_PROVIDER_ACCOUNT_UNQ_EXT_PROVIDER_ACCOUNT_ID_IDP_ID_TYPE"/>
                </not>
                <not>
                   <indexExists
                       indexName="AGENT_PROVIDER_ACCOUNT_UNQ_EXTERNAL_PROVIDER_ACCOUNT_ID_TYPE"/>
                </not>
                <tableExists tableName="AI_AGENT_PROVIDER_ACCOUNT"/>
                <columnExists tableName="AI_AGENT_PROVIDER_ACCOUNT" columnName="EXTERNAL_PROVIDER_ACCOUNT_ID"/>
                <columnExists tableName="AI_AGENT_PROVIDER_ACCOUNT" columnName="PROVIDER_TYPE"/>
                <columnExists tableName="AI_AGENT_PROVIDER_ACCOUNT" columnName="IDP_ACCOUNT_ID"/>
            </and>
        </preConditions>

        <!-- add new constraint for EXTERNAL_PROVIDER_ACCOUNT_ID, IDP_ACCOUNT_ID,  and TYPE-->
        <addUniqueConstraint
                tableName="AI_AGENT_PROVIDER_ACCOUNT"
                columnNames="EXTERNAL_PROVIDER_ACCOUNT_ID, IDP_ACCOUNT_ID, PROVIDER_TYPE"
                constraintName="AGENT_PROVIDER_ACCOUNT_UNQ_EXT_PROVIDER_ACCOUNT_ID_IDP_ID_TYPE"/>

        <rollback>
            <dropUniqueConstraint
                    tableName="AI_AGENT_PROVIDER_ACCOUNT"
                    constraintName="AGENT_PROVIDER_ACCOUNT_UNQ_EXT_PROVIDER_ACCOUNT_ID_IDP_ID_TYPE"/>
        </rollback>
    </changeSet>
</databaseChangeLog>
