<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="snagandla" id="ai-agent-registry-2024.12.10-CJ-3508-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2024.12.10-CJ-3508"/>
    </changeSet>

    <changeSet author="snagandla" id="ai-agent-registry-2024.12.10-CJ3508-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <not>
                <tableExists tableName="AI_AGENT_LARGE_TEXT_CONTENT"/>
            </not>
        </preConditions>

        <createTable tableName="AI_AGENT_LARGE_TEXT_CONTENT">
            <column autoIncrement="true" name="UID" type="SERIAL">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="GUID" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="CONTENT" type="CLOB">
                <constraints nullable="false"/>
            </column>
            <column name="RELATED_ENTITY_UID" type="SERIAL">
                <constraints nullable="false"/>
            </column>
            <!-- The type of the related entity such as agent_version, etc -->
            <column name="RELATED_ENTITY_TYPE" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint tableName="AI_AGENT_LARGE_TEXT_CONTENT"
                             columnNames="GUID"
                             constraintName="CLOB_CONTENT_UNQ_GUID"/>
        <rollback>
            <dropUniqueConstraint tableName="AI_AGENT_LARGE_TEXT_CONTENT"
                                  constraintName="CLOB_CONTENT_UNQ_GUID"/>
            <dropTable tableName="AI_AGENT_LARGE_TEXT_CONTENT"/>
        </rollback>
    </changeSet>
</databaseChangeLog>
