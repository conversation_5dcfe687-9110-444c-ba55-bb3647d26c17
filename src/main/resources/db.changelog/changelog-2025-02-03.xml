<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.02.03-CJ-3900"/>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-1">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_agent_provider_account_entity_sync_history"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AGENT_ENTITY_SYNC_HISTORY"
                                  constraintName="fk_agent_provider_account_entity_sync_history"/>

        <addForeignKeyConstraint baseTableName="AGENT_ENTITY_SYNC_HISTORY" baseColumnNames="PROVIDER_ACCOUNT_UID"
                                 constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AGENT_ENTITY_SYNC_HISTORY"
                                 referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AGENT_ENTITY_SYNC_HISTORY"
                                      constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AGENT_ENTITY_SYNC_HISTORY" />

            <addForeignKeyConstraint baseColumnNames="PROVIDER_ACCOUNT_UID"
                                     baseTableName="AGENT_ENTITY_SYNC_HISTORY"
                                     constraintName="FK_AGENT_PROVIDER_ACCOUNT_ENTITY_SYNC_HISTORY"
                                     referencedColumnNames="UID"
                                     referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-2">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_ai_agent_provider_account_ai_agent"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AI_AGENT"
                                  constraintName="fk_ai_agent_provider_account_ai_agent"/>

        <addForeignKeyConstraint baseTableName="AI_AGENT" baseColumnNames="PROVIDER_ACCOUNT_UID"
                                 constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT"
                                 referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AI_AGENT"
                                      constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT" />

            <addForeignKeyConstraint baseColumnNames="PROVIDER_ACCOUNT_UID"
                                     baseTableName="AI_AGENT"
                                     constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT"
                                     referencedColumnNames="UID"
                                     referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-3">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_ai_agent_provider_account_llm"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AI_AGENT_LLM"
                                  constraintName="fk_ai_agent_provider_account_llm"/>

        <addForeignKeyConstraint baseTableName="AI_AGENT_LLM" baseColumnNames="PROVIDER_ACCOUNT_UID"
                                 constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT_LLM"
                                 referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_LLM"
                                      constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT_LLM" />

            <addForeignKeyConstraint baseColumnNames="PROVIDER_ACCOUNT_UID"
                                     baseTableName="AI_AGENT_LLM"
                                     constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_LLM"
                                     referencedColumnNames="UID"
                                     referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-4">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_ai_agent_version_ai_agent"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AI_AGENT_VERSION"
                                  constraintName="fk_ai_agent_version_ai_agent"/>

        <addForeignKeyConstraint baseTableName="AI_AGENT_VERSION" baseColumnNames="AGENT_UID"
                                 constraintName="FK_AI_AGENT_AI_AGENT_VERSION"
                                 referencedTableName="AI_AGENT"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_VERSION"
                                      constraintName="FK_AI_AGENT_AI_AGENT_VERSION" />

            <addForeignKeyConstraint baseColumnNames="AGENT_UID"
                                     baseTableName="AI_AGENT_VERSION"
                                     constraintName="FK_AI_AGENT_VERSION_AI_AGENT"
                                     referencedColumnNames="UID"
                                     referencedTableName="AI_AGENT"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-5">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_llm_assoc_llm"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AI_AGENT_LLM_ASSOCIATION"
                                  constraintName="fk_llm_assoc_llm"/>

        <addForeignKeyConstraint baseTableName="AI_AGENT_LLM_ASSOCIATION" baseColumnNames="LLM_UID"
                                 constraintName="FK_AI_AGENT_LLM_AI_AGENT_LLM_ASSOCIATION"
                                 referencedTableName="AI_AGENT_LLM"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_LLM_ASSOCIATION"
                                      constraintName="FK_AI_AGENT_LLM_AI_AGENT_LLM_ASSOCIATION" />

            <addForeignKeyConstraint baseColumnNames="LLM_UID"
                                     baseTableName="AI_AGENT_LLM_ASSOCIATION"
                                     constraintName="FK_LLM_ASSOC_LLM"
                                     referencedColumnNames="UID"
                                     referencedTableName="AI_AGENT_LLM"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-6">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_agent_alias_version"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AI_AGENT_ALIAS"
                                  constraintName="fk_agent_alias_version"/>

        <addForeignKeyConstraint baseTableName="AI_AGENT_ALIAS" baseColumnNames="AGENT_VERSION_UID"
                                 constraintName="FK_AI_AGENT_VERSION_AI_AGENT_ALIAS"
                                 referencedTableName="AI_AGENT_VERSION"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_ALIAS"
                                      constraintName="FK_AI_AGENT_VERSION_AI_AGENT_ALIAS" />

            <addForeignKeyConstraint baseColumnNames="AGENT_VERSION_UID"
                                     baseTableName="AI_AGENT_ALIAS"
                                     constraintName="FK_AGENT_ALIAS_VERSION"
                                     referencedColumnNames="UID"
                                     referencedTableName="AI_AGENT_VERSION"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-7">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_agent_metrics_assoc_metrics"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AI_AGENT_METRICS_ASSOCIATION"
                                  constraintName="fk_agent_metrics_assoc_metrics"/>

        <addForeignKeyConstraint baseTableName="AI_AGENT_METRICS_ASSOCIATION" baseColumnNames="AI_AGENT_METRICS_UID"
                                 constraintName="FK_AI_AGENT_METRICS_AI_AGENT_METRICS_ASSOCIATION"
                                 referencedTableName="AI_AGENT_METRICS"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_METRICS_ASSOCIATION"
                                      constraintName="FK_AI_AGENT_METRICS_AI_AGENT_METRICS_ASSOCIATION" />

            <addForeignKeyConstraint baseColumnNames="AI_AGENT_METRICS_UID"
                                     baseTableName="AI_AGENT_METRICS_ASSOCIATION"
                                     constraintName="FK_AGENT_METRICS_ASSOC_METRICS"
                                     referencedColumnNames="UID"
                                     referencedTableName="AI_AGENT_METRICS"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-8">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_ai_agent_provider_account_agent_tool"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AI_AGENT_TOOL"
                                  constraintName="fk_ai_agent_provider_account_agent_tool"/>

        <addForeignKeyConstraint baseTableName="AI_AGENT_TOOL" baseColumnNames="PROVIDER_ACCOUNT_UID"
                                 constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT_TOOL"
                                 referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_TOOL"
                                      constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT_TOOL" />

            <addForeignKeyConstraint baseColumnNames="PROVIDER_ACCOUNT_UID"
                                     baseTableName="AI_AGENT_TOOL"
                                     constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AGENT_TOOL"
                                     referencedColumnNames="UID"
                                     referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-9">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_tool_assoc_tool"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AI_AGENT_TOOL_ASSOCIATION"
                                  constraintName="fk_tool_assoc_tool"/>

        <addForeignKeyConstraint baseTableName="AI_AGENT_TOOL_ASSOCIATION" baseColumnNames="TOOL_UID"
                                 constraintName="FK_AI_AGENT_TOOL_AI_AGENT_TOOL_ASSOCIATION"
                                 referencedTableName="AI_AGENT_TOOL"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_TOOL_ASSOCIATION"
                                      constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT_TOOL" />

            <addForeignKeyConstraint baseColumnNames="TOOL_UID"
                                     baseTableName="AI_AGENT_TOOL_ASSOCIATION"
                                     constraintName="FK_TOOL_ASSOC_TOOL"
                                     referencedColumnNames="UID"
                                     referencedTableName="AI_AGENT_TOOL"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-10">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_ai_agent_provider_account_agent_guardrail"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AI_AGENT_GUARDRAIL"
                                  constraintName="fk_ai_agent_provider_account_agent_guardrail"/>

        <addForeignKeyConstraint baseTableName="AI_AGENT_GUARDRAIL" baseColumnNames="PROVIDER_ACCOUNT_UID"
                                 constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT_GUARDRAIL"
                                 referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_GUARDRAIL"
                                      constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT_GUARDRAIL" />

            <addForeignKeyConstraint baseColumnNames="PROVIDER_ACCOUNT_UID"
                                     baseTableName="AI_AGENT_GUARDRAIL"
                                     constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AGENT_GUARDRAIL"
                                     referencedColumnNames="UID"
                                     referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-11">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_guardrail_assoc_guardrail"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AI_AGENT_GUARDRAIL_ASSOCIATION"
                                  constraintName="fk_guardrail_assoc_guardrail"/>

        <addForeignKeyConstraint baseTableName="AI_AGENT_GUARDRAIL_ASSOCIATION" baseColumnNames="GUARDRAIL_UID"
                                 constraintName="FK_AI_AGENT_GUARDRAIL_AI_AGENT_GUARDRAIL_ASSOCIATION"
                                 referencedTableName="AI_AGENT_GUARDRAIL"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_GUARDRAIL_ASSOCIATION"
                                      constraintName="FK_AI_AGENT_GUARDRAIL_AI_AGENT_GUARDRAIL_ASSOCIATION" />

            <addForeignKeyConstraint baseColumnNames="GUARDRAIL_UID"
                                     baseTableName="AI_AGENT_GUARDRAIL_ASSOCIATION"
                                     constraintName="FK_GUARDRAIL_ASSOC_GUARDRAIL"
                                     referencedColumnNames="UID"
                                     referencedTableName="AI_AGENT_GUARDRAIL"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-12">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_tag_assoc_tag"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AI_AGENT_TAG_ASSOCIATION"
                                  constraintName="fk_tag_assoc_tag"/>

        <addForeignKeyConstraint baseTableName="AI_AGENT_TAG_ASSOCIATION" baseColumnNames="TAG_UID"
                                 constraintName="FK_AI_AGENT_TAG_AI_AGENT_TAG_ASSOCIATION"
                                 referencedTableName="AI_AGENT_TAG"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_TAG_ASSOCIATION"
                                      constraintName="FK_AI_AGENT_TAG_AI_AGENT_TAG_ASSOCIATION" />

            <addForeignKeyConstraint baseColumnNames="TAG_UID"
                                     baseTableName="AI_AGENT_TAG_ASSOCIATION"
                                     constraintName="FK_TAG_ASSOC_TAG"
                                     referencedColumnNames="UID"
                                     referencedTableName="AI_AGENT_TAG"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-13">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_agent_entity_sync_latest_history"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AGENT_ENTITY_SYNC_LATEST"
                                  constraintName="fk_agent_entity_sync_latest_history"/>

        <addForeignKeyConstraint baseTableName="AGENT_ENTITY_SYNC_LATEST" baseColumnNames="AGENT_ENTITY_SYNC_HISTORY_UID"
                                 constraintName="FK_AGENT_ENTITY_SYNC_HISTORY_AGENT_ENTITY_SYNC_LATEST"
                                 referencedTableName="AGENT_ENTITY_SYNC_HISTORY"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AGENT_ENTITY_SYNC_LATEST"
                                      constraintName="FK_AGENT_ENTITY_SYNC_HISTORY_AGENT_ENTITY_SYNC_LATEST" />

            <addForeignKeyConstraint baseColumnNames="AGENT_ENTITY_SYNC_HISTORY_UID"
                                     baseTableName="AGENT_ENTITY_SYNC_LATEST"
                                     constraintName="FK_AGENT_ENTITY_SYNC_LATEST_HISTORY"
                                     referencedColumnNames="UID"
                                     referencedTableName="AGENT_ENTITY_SYNC_HISTORY"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-14">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_tool_resource_tool"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AI_AGENT_TOOL_RESOURCE"
                                  constraintName="fk_tool_resource_tool"/>

        <addForeignKeyConstraint baseTableName="AI_AGENT_TOOL_RESOURCE" baseColumnNames="TOOL_UID"
                                 constraintName="FK_AI_AGENT_TOOL_AI_AGENT_TOOL_RESOURCE"
                                 referencedTableName="AI_AGENT_TOOL"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_TOOL_RESOURCE"
                                      constraintName="FK_AI_AGENT_TOOL_AI_AGENT_TOOL_RESOURCE" />

            <addForeignKeyConstraint baseTableName="AI_AGENT_TOOL_RESOURCE"
                                     baseColumnNames="TOOL_UID"
                                     constraintName="FK_TOOL_RESOURCE_TOOL"
                                     referencedTableName="AI_AGENT_TOOL"
                                     referencedColumnNames="UID"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-15">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_ai_agent_provider_account_ai_agent_task"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AI_AGENT_TASK"
                                  constraintName="fk_ai_agent_provider_account_ai_agent_task"/>

        <addForeignKeyConstraint baseTableName="AI_AGENT_TASK" baseColumnNames="PROVIDER_ACCOUNT_UID"
                                 constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT_TASK"
                                 referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_TASK"
                                      constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT_TASK" />

            <addForeignKeyConstraint baseColumnNames="PROVIDER_ACCOUNT_UID"
                                     baseTableName="AI_AGENT_TASK"
                                     constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AI_AGENT_TASK"
                                     referencedColumnNames="UID"
                                     referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.03-CJ-3900-16">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="fk_ai_agent_task_association_ai_agent_task"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AI_AGENT_TASK_ASSOCIATION"
                                  constraintName="fk_ai_agent_task_association_ai_agent_task"/>

        <addForeignKeyConstraint baseTableName="AI_AGENT_TASK_ASSOCIATION" baseColumnNames="TASK_UID"
                                 constraintName="FK_AI_AGENT_TASK_AI_AGENT_TASK_ASSOCIATION"
                                 referencedTableName="AI_AGENT_TASK"
                                 referencedColumnNames="UID"
                                 onDelete="CASCADE"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_TASK_ASSOCIATION"
                                      constraintName="FK_AI_AGENT_TASK_AI_AGENT_TASK_ASSOCIATION" />

            <addForeignKeyConstraint baseColumnNames="TASK_UID"
                                     baseTableName="AI_AGENT_TASK_ASSOCIATION"
                                     constraintName="FK_AI_AGENT_TASK_ASSOCIATION_AI_AGENT_TASK"
                                     referencedColumnNames="UID"
                                     referencedTableName="AI_AGENT_TASK"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
