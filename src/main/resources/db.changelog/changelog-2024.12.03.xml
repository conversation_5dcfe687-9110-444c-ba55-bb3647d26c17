<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="subramanyahegde" id="ai-agent-registry-2024.12.03-CJ-3455-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2024.12.03-CJ-3455"/>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2024.12.03-CJ-3455-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_PROVIDER_ACCOUNT"/>
            <not>
                <columnExists tableName="AI_AGENT_PROVIDER_ACCOUNT" columnName="EXTERNAL_PROVIDER_ACCOUNT_ID"/>
            </not>
            <not>
                <columnExists tableName="AI_AGENT_PROVIDER_ACCOUNT" columnName="EXTERNAL_PROVIDER_ACCOUNT_NAME"/>
            </not>
        </preConditions>

        <!-- Add EXTERNAL_PROVIDER_ACCOUNT_ID column -->
        <addColumn tableName="AI_AGENT_PROVIDER_ACCOUNT">
            <column name="EXTERNAL_PROVIDER_ACCOUNT_ID" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </addColumn>

        <!-- Add EXTERNAL_PROVIDER_ACCOUNT_NAME column -->
        <addColumn tableName="AI_AGENT_PROVIDER_ACCOUNT">
            <column name="EXTERNAL_PROVIDER_ACCOUNT_NAME" type="VARCHAR(255)"/>
        </addColumn>

        <rollback>
            <dropColumn tableName="AI_AGENT_PROVIDER_ACCOUNT" columnName="EXTERNAL_PROVIDER_ACCOUNT_NAME"/>
            <dropColumn tableName="AI_AGENT_PROVIDER_ACCOUNT" columnName="EXTERNAL_PROVIDER_ACCOUNT_ID"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2024.12.03-CJ-3455-2">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_PROVIDER_ACCOUNT"/>
            <columnExists tableName="AI_AGENT_PROVIDER_ACCOUNT" columnName="EXTERNAL_PROVIDER_ACCOUNT_ID"/>
            <columnExists tableName="AI_AGENT_PROVIDER_ACCOUNT" columnName="PROVIDER_TYPE"/>
        </preConditions>

        <addUniqueConstraint
                tableName="AI_AGENT_PROVIDER_ACCOUNT"
                columnNames="EXTERNAL_PROVIDER_ACCOUNT_ID, PROVIDER_TYPE"
                constraintName="AGENT_PROVIDER_ACCOUNT_UNQ_EXTERNAL_PROVIDER_ACCOUNT_ID_TYPE"/>

        <rollback>
            <dropUniqueConstraint
                    tableName="AI_AGENT_PROVIDER_ACCOUNT"
                    constraintName="AGENT_PROVIDER_ACCOUNT_UNQ_EXTERNAL_PROVIDER_ACCOUNT_ID_TYPE"/>
        </rollback>
    </changeSet>
</databaseChangeLog>
