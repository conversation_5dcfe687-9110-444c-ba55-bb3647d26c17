<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables"/>
    </changeSet>

    <!-- For all the partition changesets below, the end date is exclusive -->
    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202513'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202513 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-03-24 00:00:00'
            ) TO
            (
                '2025-03-31 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202513;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-2">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202514'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202514 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-03-31 00:00:00'
            ) TO
            (
                '2025-04-07 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202514;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-3">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202515'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202515 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-04-07 00:00:00'
            ) TO
            (
                '2025-04-14 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202515;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-4">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202516'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202516 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-04-14 00:00:00'
            ) TO
            (
                '2025-04-21 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202516;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-5">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202517'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202517 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-04-21 00:00:00'
            ) TO
            (
                '2025-04-28 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202517;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-6">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202518'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202518 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-04-28 00:00:00'
            ) TO
            (
                '2025-05-05 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202518;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-7">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202519'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202519 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-05-05 00:00:00'
            ) TO
            (
                '2025-05-12 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202519;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-8">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202520'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202520 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-05-12 00:00:00'
            ) TO
            (
                '2025-05-19 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202520;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-9">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202521'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202521 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-05-19 00:00:00'
            ) TO
            (
                '2025-05-26 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202521;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-10">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202522'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202522 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-05-26 00:00:00'
            ) TO
            (
                '2025-06-02 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202522;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-11">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202523'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202523 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-06-02 00:00:00'
            ) TO
            (
                '2025-06-09 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202523;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-12">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202524'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202524 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-06-09 00:00:00'
            ) TO
            (
                '2025-06-16 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202524;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-13">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202525'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202525 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-06-16 00:00:00'
            ) TO
            (
                '2025-06-23 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202525;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-14">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202526'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202526 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-06-23 00:00:00'
            ) TO
            (
                '2025-06-30 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202526;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-15">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202527'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202527 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-06-30 00:00:00'
            ) TO
            (
                '2025-07-07 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202527;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-16">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202528'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202528 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-07-07 00:00:00'
            ) TO
            (
                '2025-07-14 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202528;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-17">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202529'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202529 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-07-14 00:00:00'
            ) TO
            (
                '2025-07-21 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202529;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-18">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202530'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202530 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-07-21 00:00:00'
            ) TO
            (
                '2025-07-28 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202530;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-19">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202531'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202531 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-07-28 00:00:00'
            ) TO
            (
                '2025-08-04 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202531;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-20">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202532'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202532 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-08-04 00:00:00'
            ) TO
            (
                '2025-08-11 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202532;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-21">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202533'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202533 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-08-11 00:00:00'
            ) TO
            (
                '2025-08-18 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202533;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-22">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202534'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202534 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-08-18 00:00:00'
            ) TO
            (
                '2025-08-25 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202534;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-23">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202535'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202535 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-08-25 00:00:00'
            ) TO
            (
                '2025-09-01 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202535;
        </rollback>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.26-CJ-4520-create-weekly-tables-24">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'SYNC_USER_AUDIT_W202536'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_W202536 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-09-01 00:00:00'
            ) TO
            (
                '2025-09-08 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_W202536;
        </rollback>
    </changeSet>
</databaseChangeLog>
