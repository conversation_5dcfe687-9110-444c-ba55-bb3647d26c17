<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.03.10-CJ-4366-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.03.10-CJ-4366"/>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.03.10-CJ-4366-1">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="FK_AI_AGENT_PROVIDER_ACCOUNT_AGENT_ENTITY_SYNC_HISTORY"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AGENT_ENTITY_SYNC_HISTORY"
                                  constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AGENT_ENTITY_SYNC_HISTORY"/>

        <rollback>
            <addForeignKeyConstraint baseTableName="AGENT_ENTITY_SYNC_HISTORY" baseColumnNames="PROVIDER_ACCOUNT_UID"
                                     constraintName="FK_AI_AGENT_PROVIDER_ACCOUNT_AGENT_ENTITY_SYNC_HISTORY"
                                     referencedTableName="AI_AGENT_PROVIDER_ACCOUNT"
                                     referencedColumnNames="UID"
                                     onDelete="CASCADE"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.03.10-CJ-4366-2">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <foreignKeyConstraintExists foreignKeyName="FK_AGENT_ENTITY_SYNC_HISTORY_AGENT_ENTITY_SYNC_LATEST"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableName="AGENT_ENTITY_SYNC_LATEST"
                                  constraintName="FK_AGENT_ENTITY_SYNC_HISTORY_AGENT_ENTITY_SYNC_LATEST"/>

        <rollback>

            <addForeignKeyConstraint baseTableName="AGENT_ENTITY_SYNC_LATEST"
                                     baseColumnNames="AGENT_ENTITY_SYNC_HISTORY_UID"
                                     constraintName="FK_AGENT_ENTITY_SYNC_HISTORY_AGENT_ENTITY_SYNC_LATEST"
                                     referencedTableName="AGENT_ENTITY_SYNC_HISTORY"
                                     referencedColumnNames="UID"
                                     onDelete="CASCADE"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
