<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.01.15-CJ-3689-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.01.15-CJ-3689"/>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.01.15-CJ-3689-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_TOOL_ASSOCIATION"/>

            <and>
                <not>
                    <columnExists tableName="AI_AGENT_TOOL_ASSOCIATION" columnName="ASSOCIATION_STATUS"/>
                </not>
            </and>
        </preConditions>

        <addColumn tableName="AI_AGENT_TOOL_ASSOCIATION">
            <column name="ASSOCIATION_STATUS" type="varchar(50)"/>
        </addColumn>

        <rollback>
            <dropColumn tableName="AI_AGENT_TOOL_ASSOCIATION" columnName="ASSOCIATION_STATUS"/>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.01.15-CJ-3689-2">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_GUARDRAIL_ASSOCIATION"/>
            <and>
                <not>
                    <columnExists tableName="AI_AGENT_GUARDRAIL_ASSOCIATION" columnName="ASSOCIATION_STATUS"/>
                </not>
            </and>
        </preConditions>

        <addColumn tableName="AI_AGENT_GUARDRAIL_ASSOCIATION">
            <column name="ASSOCIATION_STATUS" type="varchar(50)"/>
        </addColumn>

        <rollback>
            <dropColumn tableName="AI_AGENT_GUARDRAIL_ASSOCIATION" columnName="ASSOCIATION_STATUS"/>
        </rollback>
    </changeSet>


</databaseChangeLog>
