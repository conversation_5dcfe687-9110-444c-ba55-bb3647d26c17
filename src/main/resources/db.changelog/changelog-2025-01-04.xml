<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="unique-constraint-synced-entity-uuid-entity-type" author="azookari">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="agent_entity_sync_latest"/>
            <columnExists tableName="agent_entity_sync_latest" columnName="synced_entity_uid"/>
            <columnExists tableName="agent_entity_sync_latest" columnName="synced_entity_type"/>
        </preConditions>

        <addUniqueConstraint
                tableName="agent_entity_sync_latest"
                columnNames="synced_entity_uid, synced_entity_type"
                constraintName="uk_sync_latest_entity_uuid_entity_type"/>

        <rollback>
            <dropUniqueConstraint
                    tableName="agent_entity_sync_latest"
                    constraintName="uk_sync_latest_entity_uuid_entity_type"/>
        </rollback>
    </changeSet>
</databaseChangeLog>


