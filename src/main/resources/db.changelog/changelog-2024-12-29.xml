<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="subramanyahegde" id="ai-agent-registry-2024.12.29-CJ-3588-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2024.12.29-CJ-3588"/>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2024.12.29-CJ-3588-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_ALIAS"/>
            <not>
                <columnExists tableName="AI_AGENT_ALIAS" columnName="TRUST_LEVEL"/>
            </not>
        </preConditions>

        <!-- Add TRUST_LEVEL column -->
        <addColumn tableName="AI_AGENT_ALIAS">
            <column name="TRUST_LEVEL" type="VARCHAR(50)" />
        </addColumn>

        <rollback>
            <dropColumn tableName="AI_AGENT_ALIAS" columnName="TRUST_LEVEL"/>
        </rollback>
    </changeSet>
</databaseChangeLog>
