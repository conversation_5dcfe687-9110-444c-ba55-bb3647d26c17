<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="weiwang" id="ai-agent-registry-2025.05.15-CJ-5153-index-update-0"
               context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.05.15-CJ-5153-index-update"/>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.05.15-CJ-5153-index-update-1">
        <!-- Check if indexes exist before attempting to drop them -->
        <preConditions onFail="MARK_RAN" onError="HALT">
            <and>
                <indexExists tableName="SYNC_USER_AUDIT" indexName="IDX_SYNC_USER_AUDIT_LAST_UPDATED_DATE"/>
                <indexExists tableName="SYNC_USER_AUDIT" indexName="IDX_SYNC_USER_AUDIT_ACTION_ENTITY_SYNC_STATUS"/>
                <indexExists tableName="SYNC_USER_AUDIT" indexName="IDX_SYNC_USER_AUDIT_ENTITY_NAME"/>
                <indexExists tableName="SYNC_USER_AUDIT" indexName="IDX_SYNC_USER_AUDIT_USER_ID"/>
                <indexExists tableName="SYNC_USER_AUDIT" indexName="IDX_SYNC_USER_AUDIT_PROVIDER_TYPE"/>
                <indexExists tableName="SYNC_USER_AUDIT" indexName="IDX_SYNC_USER_AUDIT_PROVIDER_ACCOUNT_NAME"/>
                <indexExists tableName="SYNC_USER_AUDIT" indexName="IDX_SYNC_USER_AUDIT_TRANSACTION_ENTITY"/>
            </and>
        </preConditions>

        <!-- Drop existing indexes which were added in changelog-2025-02-26.xml -->
        <dropIndex tableName="SYNC_USER_AUDIT" indexName="IDX_SYNC_USER_AUDIT_LAST_UPDATED_DATE"/>
        <dropIndex tableName="SYNC_USER_AUDIT" indexName="IDX_SYNC_USER_AUDIT_ACTION_ENTITY_SYNC_STATUS"/>
        <dropIndex tableName="SYNC_USER_AUDIT" indexName="IDX_SYNC_USER_AUDIT_ENTITY_NAME"/>
        <dropIndex tableName="SYNC_USER_AUDIT" indexName="IDX_SYNC_USER_AUDIT_USER_ID"/>
        <dropIndex tableName="SYNC_USER_AUDIT" indexName="IDX_SYNC_USER_AUDIT_PROVIDER_TYPE"/>
        <dropIndex tableName="SYNC_USER_AUDIT" indexName="IDX_SYNC_USER_AUDIT_PROVIDER_ACCOUNT_NAME"/>
        <dropIndex tableName="SYNC_USER_AUDIT" indexName="IDX_SYNC_USER_AUDIT_TRANSACTION_ENTITY"/>

        <!-- Create new index for CJ-5153 -->
        <createIndex tableName="sync_user_audit" indexName="IDX_SYNC_USER_AUDIT_ENTITY_UID_TYPE_SYNC_END_DATE">
            <column name="entity_uid"/>
            <column name="entity_type"/>
            <column name="sync_end_date"/>
        </createIndex>

        <rollback>
            <!-- Drop new index -->
            <dropIndex tableName="SYNC_USER_AUDIT" indexName="IDX_SYNC_USER_AUDIT_ENTITY_UID_TYPE_SYNC_END_DATE"/>
        </rollback>
    </changeSet>
</databaseChangeLog>
