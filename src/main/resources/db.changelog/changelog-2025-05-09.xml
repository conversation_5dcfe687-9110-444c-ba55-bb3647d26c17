<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.9.xsd">

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.05.09-CJ-5075-1">
        <preConditions>
            <tableExists tableName="ai_agent_registry_license"/>
        </preConditions>

        <update tableName="ai_agent_registry_license">
            <column name="tier_default_soft_limit" value="5"/>
            <where>TIER_ID=0</where>
        </update>
        <update tableName="ai_agent_registry_license">
            <column name="tier_default_soft_limit" value="100"/>
            <where>TIER_ID=1</where>
        </update>
        <update tableName="ai_agent_registry_license">
            <column name="tier_default_soft_limit" value="1000"/>
            <where>TIER_ID=2</where>
        </update>
        <update tableName="ai_agent_registry_license">
            <column name="system_default_limit" value="200"/>
            <where>TIER_ID in(0,1,2)</where>
        </update>
    </changeSet>

</databaseChangeLog>
