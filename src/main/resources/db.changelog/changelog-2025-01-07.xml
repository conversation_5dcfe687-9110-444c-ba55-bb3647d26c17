<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.01.07-CJ-3625-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.01.07-CJ-CJ-3625"/>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.01.07-CJ-3625-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_TAG"/>
            <not>
                <indexExists indexName="AI_AGENT_TAG_UNQ_IDP_ACCOUNT_ID_TAG_KEY" />
            </not>
        </preConditions>

        <addUniqueConstraint tableName="AI_AGENT_TAG" columnNames="IDP_ACCOUNT_ID, TAG_KEY"
                             constraintName="AI_AGENT_TAG_UNQ_IDP_ACCOUNT_ID_TAG_KEY" />

        <rollback>
            <dropUniqueConstraint tableName="AI_AGENT_TAG"
                                  constraintName="AI_AGENT_TAG_UNQ_IDP_ACCOUNT_ID_TAG_KEY"/>
        </rollback>
    </changeSet>
</databaseChangeLog>
