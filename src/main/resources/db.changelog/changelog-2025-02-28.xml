<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="dikshasatpute" id="ai-agent-registry-2025.02.28-CJ-4277-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.02.28-CJ-4277"/>
    </changeSet>

    <!-- For all the partition changesets below, the end date is exclusive -->
    <changeSet author="dikshasatpute" id="ai-agent-registry-2025.02.28-CJ-4277-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'sync_user_audit_202502'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_202502 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-02-01 00:00:00'
            ) TO
            (
                '2025-03-01 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_202502;
        </rollback>
    </changeSet>
    <changeSet author="dikshasatpute" id="ai-agent-registry-2025.02.28-CJ-4277-2">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'sync_user_audit_202503'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_202503 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-03-01 00:00:00'
            ) TO
            (
                '2025-04-01 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_202503;
        </rollback>
    </changeSet>
    <changeSet author="dikshasatpute" id="ai-agent-registry-2025.02.28-CJ-4277-3">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'sync_user_audit_202504'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_202504 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-04-01 00:00:00'
            ) TO
            (
                '2025-05-01 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_202504;
        </rollback>
    </changeSet>
    <changeSet author="dikshasatpute" id="ai-agent-registry-2025.02.28-CJ-4277-4">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'sync_user_audit_202505'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_202505 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-05-01 00:00:00'
            ) TO
            (
                '2025-06-01 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_202505;
        </rollback>
    </changeSet>
    <changeSet author="dikshasatpute" id="ai-agent-registry-2025.02.28-CJ-4277-5">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'sync_user_audit_202506'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_202506 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-06-01 00:00:00'
            ) TO
            (
                '2025-07-01 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_202506;
        </rollback>
    </changeSet>
    <changeSet author="dikshasatpute" id="ai-agent-registry-2025.02.28-CJ-4277-6">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'sync_user_audit_202507'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_202507 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-07-01 00:00:00'
            ) TO
            (
                '2025-08-01 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_202507;
        </rollback>
    </changeSet>
    <changeSet author="dikshasatpute" id="ai-agent-registry-2025.02.28-CJ-4277-7">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = 'sync_user_audit'
            </sqlCheck>
            <sqlCheck expectedResult="t">
                SELECT NOT EXISTS (SELECT 1
                                   FROM information_schema.tables
                                   WHERE table_name = 'sync_user_audit_202508'
                                     AND table_schema = current_schema());
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE TABLE SYNC_USER_AUDIT_202508 PARTITION OF SYNC_USER_AUDIT
                FOR VALUES FROM
            (
                '2025-08-01 00:00:00'
            ) TO
            (
                '2025-09-01 00:00:00'
            );
        </sql>
        <rollback>
            DROP TABLE IF EXISTS SYNC_USER_AUDIT_202508;
        </rollback>
    </changeSet>
</databaseChangeLog>
