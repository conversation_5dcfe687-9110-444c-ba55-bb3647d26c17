<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="snagandla" id="ai-agent-registry-2025.01.08-CJ-3640-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.01.08-CJ-3640"/>
    </changeSet>

    <changeSet author="snagandla" id="ai-agent-registry-2025.01.08-CJ-3640-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_VERSION"/>
            <columnExists tableName="AI_AGENT_VERSION" columnName="MODIFIED_TIME"/>
            <columnExists tableName="AI_AGENT_VERSION" columnName="CREATED_TIME"/>
        </preConditions>

        <!-- Add not null constraint to modifiedTime -->
        <addNotNullConstraint
                tableName="AI_AGENT_VERSION"
                columnName="MODIFIED_TIME"
                columnDataType="TIMESTAMP"/>

        <!-- Add not null constraint to createdTime -->
        <addNotNullConstraint
                tableName="AI_AGENT_VERSION"
                columnName="CREATED_TIME"
                columnDataType="TIMESTAMP"/>

        <rollback>
            <!-- Remove not null constraints in case of rollback -->
            <dropNotNullConstraint
                    tableName="AI_AGENT_VERSION"
                    columnName="MODIFIED_TIME"
                    columnDataType="TIMESTAMP"/>
            <dropNotNullConstraint
                    tableName="AI_AGENT_VERSION"
                    columnName="CREATED_TIME"
                    columnDataType="TIMESTAMP"/>
        </rollback>
    </changeSet>

    <changeSet author="snagandla" id="ai-agent-registry-2025.01.08-CJ-3640-2">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_PROVIDER_ACCOUNT"/>
            <columnExists tableName="AI_AGENT_PROVIDER_ACCOUNT" columnName="MODIFIED_TIME"/>
            <columnExists tableName="AI_AGENT_PROVIDER_ACCOUNT" columnName="CREATED_TIME"/>
        </preConditions>

        <!-- Add not null constraint to modifiedTime -->
        <addNotNullConstraint
                tableName="AI_AGENT_PROVIDER_ACCOUNT"
                columnName="MODIFIED_TIME"
                columnDataType="TIMESTAMP"/>

        <!-- Add not null constraint to createdTime -->
        <addNotNullConstraint
                tableName="AI_AGENT_PROVIDER_ACCOUNT"
                columnName="CREATED_TIME"
                columnDataType="TIMESTAMP"/>

        <rollback>
            <!-- Remove not null constraints in case of rollback -->
            <dropNotNullConstraint
                    tableName="AI_AGENT_PROVIDER_ACCOUNT"
                    columnName="MODIFIED_TIME"
                    columnDataType="TIMESTAMP"/>
            <dropNotNullConstraint
                    tableName="AI_AGENT_PROVIDER_ACCOUNT"
                    columnName="CREATED_TIME"
                    columnDataType="TIMESTAMP"/>
        </rollback>
    </changeSet>

    <changeSet author="snagandla" id="ai-agent-registry-2025.01.08-CJ-3640-3">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_TOOL"/>
            <columnExists tableName="AI_AGENT_TOOL" columnName="MODIFIED_TIME"/>
            <columnExists tableName="AI_AGENT_TOOL" columnName="CREATED_TIME"/>
        </preConditions>

        <!-- Add not null constraint to modifiedTime -->
        <addNotNullConstraint
                tableName="AI_AGENT_TOOL"
                columnName="MODIFIED_TIME"
                columnDataType="TIMESTAMP"/>

        <!-- Add not null constraint to createdTime -->
        <addNotNullConstraint
                tableName="AI_AGENT_TOOL"
                columnName="CREATED_TIME"
                columnDataType="TIMESTAMP"/>

        <rollback>
            <!-- Remove not null constraints in case of rollback -->
            <dropNotNullConstraint
                    tableName="AI_AGENT_TOOL"
                    columnName="MODIFIED_TIME"
                    columnDataType="TIMESTAMP"/>
            <dropNotNullConstraint
                    tableName="AI_AGENT_TOOL"
                    columnName="CREATED_TIME"
                    columnDataType="TIMESTAMP"/>
        </rollback>
    </changeSet>
    <changeSet author="snagandla" id="ai-agent-registry-2025.01.08-CJ-3640-4">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_TOOL_RESOURCE "/>
            <columnExists tableName="AI_AGENT_TOOL_RESOURCE" columnName="MODIFIED_TIME"/>
            <columnExists tableName="AI_AGENT_TOOL_RESOURCE" columnName="CREATED_TIME"/>
            <columnExists tableName="AI_AGENT_TOOL_RESOURCE" columnName="NAME"/>
        </preConditions>

        <!-- Add not null constraint to modifiedTime -->
        <addNotNullConstraint
                tableName="AI_AGENT_TOOL_RESOURCE"
                columnName="MODIFIED_TIME"
                columnDataType="TIMESTAMP"/>

        <!-- Add not null constraint to createdTime -->
        <addNotNullConstraint
                tableName="AI_AGENT_TOOL_RESOURCE"
                columnName="CREATED_TIME"
                columnDataType="TIMESTAMP"/>

        <!-- Add not null constraint to name -->
        <addNotNullConstraint
                tableName="AI_AGENT_TOOL_RESOURCE"
                columnName="NAME"
                columnDataType="VARCHAR(255)"/>

        <rollback>
            <!-- Remove not null constraints in case of rollback -->
            <dropNotNullConstraint
                    tableName="AI_AGENT_TOOL_RESOURCE"
                    columnName="MODIFIED_TIME"
                    columnDataType="TIMESTAMP"/>
            <dropNotNullConstraint
                    tableName="AI_AGENT_TOOL_RESOURCE"
                    columnName="CREATED_TIME"
                    columnDataType="TIMESTAMP"/>
        </rollback>
    </changeSet>
    <changeSet author="snagandla" id="ai-agent-registry-2025.01.08-CJ-3640-5">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_ALIAS"/>
                <columnExists tableName="AI_AGENT_ALIAS" columnName="MODIFIED_TIME"/>
                <columnExists tableName="AI_AGENT_ALIAS" columnName="CREATED_TIME"/>
        </preConditions>

        <!-- Add not null constraint to modifiedTime -->
        <addNotNullConstraint
                tableName="AI_AGENT_ALIAS"
                columnName="MODIFIED_TIME"
                columnDataType="TIMESTAMP"/>

        <!-- Add not null constraint to createdTime -->
        <addNotNullConstraint
                tableName="AI_AGENT_ALIAS"
                columnName="CREATED_TIME"
                columnDataType="TIMESTAMP"/>

        <rollback>
            <!-- Remove not null constraints in case of rollback -->
            <dropNotNullConstraint
                    tableName="AI_AGENT_ALIAS"
                    columnName="MODIFIED_TIME"
                    columnDataType="TIMESTAMP"/>
            <dropNotNullConstraint
                    tableName="AI_AGENT_ALIAS"
                    columnName="CREATED_TIME"
                    columnDataType="TIMESTAMP"/>
        </rollback>
    </changeSet>
    <changeSet author="snagandla" id="ai-agent-registry-2025.01.08-CJ-3640-6">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_TASK"/>
            <columnExists tableName="AI_AGENT_TASK" columnName="MODIFIED_TIME"/>
            <columnExists tableName="AI_AGENT_TASK" columnName="CREATED_TIME"/>
        </preConditions>

        <!-- Add not null constraint to modifiedTime -->
        <addNotNullConstraint
                tableName="AI_AGENT_TASK"
                columnName="MODIFIED_TIME"
                columnDataType="TIMESTAMP"/>

        <!-- Add not null constraint to createdTime -->
        <addNotNullConstraint
                tableName="AI_AGENT_TASK"
                columnName="CREATED_TIME"
                columnDataType="TIMESTAMP"/>

        <rollback>
            <!-- Remove not null constraints in case of rollback -->
            <dropNotNullConstraint
                    tableName="AI_AGENT_TASK"
                    columnName="MODIFIED_TIME"
                    columnDataType="TIMESTAMP"/>
            <dropNotNullConstraint
                    tableName="AI_AGENT_TASK"
                    columnName="CREATED_TIME"
                    columnDataType="TIMESTAMP"/>
        </rollback>
    </changeSet>
    <changeSet author="snagandla" id="ai-agent-registry-2025.01.08-CJ-3640-7">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_GUARDRAIL" />
            <columnExists tableName="AI_AGENT_GUARDRAIL" columnName="MODIFIED_TIME"/>
            <columnExists tableName="AI_AGENT_GUARDRAIL" columnName="CREATED_TIME"/>
        </preConditions>

        <!-- Add not null constraint to modifiedTime -->
        <addNotNullConstraint
                tableName="AI_AGENT_GUARDRAIL"
                columnName="MODIFIED_TIME"
                columnDataType="TIMESTAMP"/>

        <!-- Add not null constraint to createdTime -->
        <addNotNullConstraint
                tableName="AI_AGENT_GUARDRAIL"
                columnName="CREATED_TIME"
                columnDataType="TIMESTAMP"/>

        <rollback>
            <!-- Remove not null constraints in case of rollback -->
            <dropNotNullConstraint
                    tableName="AI_AGENT_GUARDRAIL"
                    columnName="MODIFIED_TIME"
                    columnDataType="TIMESTAMP"/>
            <dropNotNullConstraint
                    tableName="AI_AGENT_GUARDRAIL"
                    columnName="CREATED_TIME"
                    columnDataType="TIMESTAMP"/>
        </rollback>
    </changeSet>
</databaseChangeLog>
