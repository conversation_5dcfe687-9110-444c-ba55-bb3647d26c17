<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    
    <changeSet id="add-idp-account-id-to-ai-agent-llm" author="your-name">
        <addColumn tableName="ai_agent_llm">
            <column name="idp_account_id" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>


        <createIndex tableName="ai_agent_llm" indexName="idx_ai_agent_llm_idp_account_id">
            <column name="idp_account_id"/>
        </createIndex>

        <rollback>
            <dropIndex tableName="ai_agent_llm" indexName="idx_ai_agent_llm_idp_account_id"/>
            <dropColumn tableName="ai_agent_llm" columnName="idp_account_id"/>
        </rollback>
    </changeSet>

</databaseChangeLog>

