<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.10-CJ-4211-uid-remove-0"
               context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.03.10-CJ-4211-uid-remove"/>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.10-CJ-4211-uid-remove-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <and>
                <sqlCheck expectedResult="1">
                    SELECT COUNT(*)
                    FROM information_schema.table_constraints
                    WHERE constraint_name = 'sync_user_audit_unq_guid_date'
                    AND table_name = 'sync_user_audit';
                </sqlCheck>
                <sqlCheck expectedResult="1">
                    SELECT COUNT(*)
                    FROM information_schema.table_constraints
                    WHERE constraint_name = 'sync_user_audit_pkey'
                    AND table_name = 'sync_user_audit';
                </sqlCheck>
                <sqlCheck expectedResult="1">
                    SELECT COUNT(*)
                    FROM information_schema.columns
                    WHERE table_name = 'sync_user_audit'
                    AND column_name = 'uid';
                </sqlCheck>
            </and>
        </preConditions>

        <sql>
            -- Drop the unique constraint first
            ALTER TABLE SYNC_USER_AUDIT
            DROP CONSTRAINT IF EXISTS SYNC_USER_AUDIT_UNQ_GUID_DATE;

            -- Drop the existing primary key from parent and all partitions
            ALTER TABLE SYNC_USER_AUDIT
            DROP CONSTRAINT IF EXISTS SYNC_USER_AUDIT_PKEY;

            -- Drop the UID column from parent and all partitions
            ALTER TABLE SYNC_USER_AUDIT
            DROP COLUMN IF EXISTS UID;

            -- Add new primary key and propagate to all partitions
            ALTER TABLE SYNC_USER_AUDIT
                ADD CONSTRAINT SYNC_USER_AUDIT_PKEY PRIMARY KEY (GUID, LAST_UPDATED_DATE);
        </sql>

        <rollback>
            <sql>
                ALTER TABLE SYNC_USER_AUDIT
                DROP CONSTRAINT IF EXISTS SYNC_USER_AUDIT_PKEY;

                ALTER TABLE SYNC_USER_AUDIT
                ADD COLUMN UID SERIAL NOT NULL;

                ALTER TABLE SYNC_USER_AUDIT
                ADD CONSTRAINT SYNC_USER_AUDIT_PKEY PRIMARY KEY (UID, LAST_UPDATED_DATE);

                ALTER TABLE SYNC_USER_AUDIT
                ADD CONSTRAINT SYNC_USER_AUDIT_UNQ_GUID_DATE UNIQUE (GUID, LAST_UPDATED_DATE);
            </sql>
        </rollback>
    </changeSet>
</databaseChangeLog>
