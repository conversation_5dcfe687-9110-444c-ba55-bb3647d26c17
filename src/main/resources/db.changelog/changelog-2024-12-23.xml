<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="drop-columns-sync-details" author="azookari">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="agent_entity_sync_history"/>
            <columnExists tableName="agent_entity_sync_history" columnName="sync_details"/>
        </preConditions>

        <dropColumn tableName="agent_entity_sync_history" columnName="sync_details"/>

        <rollback>
            <addColumn tableName="agent_entity_sync_history">
                <column name="sync_details" type="text">
                    <constraints nullable="true"/>
                </column>
            </addColumn>
        </rollback>

    </changeSet>

    <changeSet id="drop-version-instructions-and-s3-key" author="azookari">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="ai_agent_version"/>
            <columnExists tableName="ai_agent_version" columnName="instructions"/>
            <columnExists tableName="ai_agent_version" columnName="instructions_s3_key"/>
        </preConditions>

        <dropColumn tableName="ai_agent_version" columnName="instructions"/>
        <dropColumn tableName="ai_agent_version" columnName="instructions_s3_key"/>

        <rollback>
            <addColumn tableName="ai_agent_version">
                <column name="instructions" type="text">
                    <constraints nullable="true"/>
                </column>
            </addColumn>
            <addColumn tableName="ai_agent_version">
                <column name="instructions_s3_key" type="varchar(500)">
                    <constraints nullable="true"/>
                </column>
            </addColumn>
        </rollback>

    </changeSet>

    <changeSet id="drop-large-content-guid" author="azookari">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="ai_agent_large_text_content"/>
            <columnExists tableName="ai_agent_large_text_content" columnName="guid"/>
        </preConditions>

        <dropColumn tableName="ai_agent_large_text_content" columnName="guid"/>
        
        <rollback>
            <addColumn tableName="ai_agent_large_text_content">
                <column name="guid" type="uuid">
                    <constraints nullable="true"/>
                </column>
            </addColumn>

        </rollback>
    </changeSet>

</databaseChangeLog>


