<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="azookari" id="ai-agent-registry-2024.12.10-add-provider-timestamp">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_TOOL_RESOURCE"/>
            <not>
                <columnExists tableName="AI_AGENT_TOOL_RESOURCE" columnName="UPDATED_AT_PROVIDER_TIME"/>
            </not>
        </preConditions>

        <addColumn tableName="AI_AGENT_TOOL_RESOURCE">
            <column name="UPDATED_AT_PROVIDER_TIME" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <rollback>
            <dropColumn tableName="AI_AGENT_TOOL_RESOURCE" columnName="UPDATED_AT_PROVIDER_TIME"/>
        </rollback>
    </changeSet>
</databaseChangeLog>




