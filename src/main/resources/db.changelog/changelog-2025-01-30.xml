<?xml version="1.1" encoding="UTF-8" standalone="no"?>
        <databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="unique-constraint-tool-uid-external-id" author="azookari">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="ai_agent_tool_resource"/>
            <columnExists tableName="ai_agent_tool_resource" columnName="tool_uid"/>
            <columnExists tableName="ai_agent_tool_resource" columnName="external_id"/>
        </preConditions>

        <addUniqueConstraint
                tableName="ai_agent_tool_resource"
                columnNames="tool_uid, external_id"
                constraintName="uk_tool_resource_tool_uid_external_id"/>

        <rollback>
            <dropUniqueConstraint
                    tableName="ai_agent_tool_resource"
                    constraintName="uk_tool_resource_tool_uid_external_id"/>
        </rollback>
    </changeSet>
</databaseChangeLog>
