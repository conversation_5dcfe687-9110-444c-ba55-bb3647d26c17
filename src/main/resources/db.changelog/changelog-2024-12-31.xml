<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="dikshasatpute" id="ai-agent-registry-2024.12.31-CJ-3562-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2024.12.31-CJ-3562"/>
    </changeSet>

    <changeSet author="dikshasatpute" id="ai-agent-registry-2024.12.31-CJ-3562-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_TASK"/>
            <columnExists tableName="AI_AGENT_TASK" columnName="INSTRUCTIONS"/>
        </preConditions>

        <dropColumn tableName="AI_AGENT_TASK" columnName="INSTRUCTIONS"/>

        <rollback>
            <addColumn tableName="AI_AGENT_TASK">
                <column name="INSTRUCTIONS" type="VARCHAR(2000)">
                </column>
            </addColumn>
        </rollback>
    </changeSet>
</databaseChangeLog>
