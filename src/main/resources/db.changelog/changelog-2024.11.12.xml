<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="weiwang" id="ai-agent-registry-2024.11.12-CJ-3343-1" context="PRE">
        <tagDatabase tag="ai-agent-registry-2024.11.12-CJ-3343"/>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2024.11.12-CJ-3343-2">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <not>
                <tableExists tableName="AUDIT_LOG"/>
            </not>
        </preConditions>
        <createTable tableName="AUDIT_LOG">
            <column autoIncrement="true" name="UID" type="SERIAL">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="GUID" type="UUID">
                <constraints nullable="false"/>
            </column>
            <!-- The column for the entity name. e.g. Provider, Agent -->
            <column name="ENTITY_NAME" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <!-- The column for the entity ID. e.g. GUID of Provider, GUID of Agent -->
            <column name="ENTITY_GUID" type="UUID">
                <constraints nullable="false"/>
            </column>
            <!-- The column for the action name e.g. create, update, delete -->
            <column name="ACTION" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <!-- The column for the login user ID e.g. Boomi login user email address -->
            <column name="USER_ID" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <!-- The identifier for the AI Registry account. e.g. Boomi Account Id -->
            <column name="ACCOUNT_ID" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="ACTION_DATE" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <!-- The column for helping to identify the data structure of the change set data. -->
            <column name="ENTITY_VERSION" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="CHANGE_SET" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <!-- The column for grouping related changes in a single request/transaction -->
            <column name="REQUEST_ID" type="VARCHAR(100)" />
            <!-- Capture if the action was successful or not -->
            <column name="ACTION_RESULT" type="VARCHAR(50)"/>
        </createTable>
        <createIndex indexName="AUDIT_LOG_ACCOUNT_DATE_IDX" tableName="AUDIT_LOG">
            <column name="ACCOUNT_ID"/>
            <column name="ACTION_DATE"/>
        </createIndex>

        <addUniqueConstraint tableName="AUDIT_LOG"
                             columnNames="GUID"
                             constraintName="AUDIT_LOG_UNQ_GUID"/>
        <rollback>
            <dropIndex indexName="AUDIT_LOG_ACCOUNT_DATE_IDX" tableName="AUDIT_LOG"/>
            <dropUniqueConstraint tableName="AUDIT_LOG"
                                  constraintName="AUDIT_LOG_UNQ_GUID"/>
            <dropTable tableName="AUDIT_LOG"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
