<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.01.31-CJ-3905-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.01.31-CJ-3905"/>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.01.31-CJ-3905-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <and>
                <tableExists tableName="AI_AGENT_PROVIDER_ACCOUNT"/>
                <indexExists
                    indexName="AGENT_PROVIDER_ACCOUNT_UNQ_EXT_PROVIDER_ACCOUNT_ID_IDP_ID_TYPE"/>
            </and>
        </preConditions>

        <dropUniqueConstraint
                tableName="AI_AGENT_PROVIDER_ACCOUNT"
                constraintName="AGENT_PROVIDER_ACCOUNT_UNQ_EXT_PROVIDER_ACCOUNT_ID_IDP_ID_TYPE"/>

        <rollback>
            <addUniqueConstraint
                    tableName="AI_AGENT_PROVIDER_ACCOUNT"
                    columnNames="EXTERNAL_PROVIDER_ACCOUNT_ID, IDP_ACCOUNT_ID, PROVIDER_TYPE"
                    constraintName="AGENT_PROVIDER_ACCOUNT_UNQ_EXT_PROVIDER_ACCOUNT_ID_IDP_ID_TYPE"/>
        </rollback>
    </changeSet>

</databaseChangeLog>