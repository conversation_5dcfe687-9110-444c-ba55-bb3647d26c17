<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="weiwang" id="ai-agent-registry-2025.02.26-CJ-4210-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.02.26-CJ-4210"/>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.02.26-CJ-4210-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <not>
                <tableExists tableName="SYNC_USER_AUDIT"/>
            </not>
        </preConditions>

        <sql>
            CREATE TABLE SYNC_USER_AUDIT (
                 UID SERIAL NOT NULL,
                 GUID UUID NOT NULL,
                 PROVIDER_ACCOUNT_UID INTEGER NOT NULL,
                 PROVIDER_ACCOUNT_GUID UUID NOT NULL,
                 PROVIDER_TYPE VARCHAR(50) NOT NULL,
                 PROVIDER_ACCOUNT_NAME VARCHAR(200),
                 SYNC_TRANSACTION_GUID UUID,
                 SYNC_START_DATE TIMESTAMP WITHOUT TIME ZONE,
                 SYNC_END_DATE TIMESTAMP WITHOUT TIME ZONE,
                 LAST_UPDATED_DATE TIMESTAMP WITHOUT TIME ZONE NOT NULL,
                 SYNC_STATUS VARCHAR(50),
                 SYNC_NUMBER_OF_ENTITIES_ADDED INTEGER,
                 SYNC_NUMBER_OF_ENTITIES_REMOVED INTEGER,
                 SYNC_NUMBER_OF_ENTITIES_UPDATED INTEGER,
                 ENTITY_UID INTEGER NOT NULL,
                 ENTITY_GUID UUID NOT NULL,
                 ENTITY_TYPE VARCHAR(50) NOT NULL,
                 ENTITY_NAME VARCHAR(1000),
                 ENTITY_VERSION VARCHAR(100),
                 ACTION_TYPE VARCHAR(50) NOT NULL,
                 USER_ID VARCHAR(100) NOT NULL,
                 IDP_ACCOUNT_ID VARCHAR(200),
                 CONTENT TEXT,
                 CHANGE_ORIGIN VARCHAR(50) NOT NULL,
                 PRIMARY KEY (UID, LAST_UPDATED_DATE)
            ) PARTITION BY RANGE (LAST_UPDATED_DATE);
        </sql>

        <!-- Add unique constraint on GUID and LAST_UPDATED_DATE-->
        <addUniqueConstraint tableName="SYNC_USER_AUDIT"
                             columnNames="GUID, LAST_UPDATED_DATE"
                             constraintName="SYNC_USER_AUDIT_UNQ_GUID_DATE"/>

        <!-- Create indexes -->
        <createIndex indexName="IDX_SYNC_USER_AUDIT_LAST_UPDATED_DATE" tableName="SYNC_USER_AUDIT">
            <column name="LAST_UPDATED_DATE"/>
        </createIndex>

        <createIndex indexName="IDX_SYNC_USER_AUDIT_ACTION_ENTITY_SYNC_STATUS" tableName="SYNC_USER_AUDIT">
            <column name="ACTION_TYPE"/>
            <column name="ENTITY_TYPE"/>
            <column name="SYNC_STATUS"/>
        </createIndex>

        <createIndex indexName="IDX_SYNC_USER_AUDIT_ENTITY_NAME" tableName="SYNC_USER_AUDIT">
            <column name="ENTITY_NAME"/>
        </createIndex>

        <createIndex indexName="IDX_SYNC_USER_AUDIT_USER_ID" tableName="SYNC_USER_AUDIT">
            <column name="USER_ID"/>
        </createIndex>
        <createIndex indexName="IDX_SYNC_USER_AUDIT_PROVIDER_TYPE" tableName="SYNC_USER_AUDIT">
            <column name="PROVIDER_TYPE"/>
        </createIndex>
        <createIndex indexName="IDX_SYNC_USER_AUDIT_PROVIDER_ACCOUNT_NAME" tableName="SYNC_USER_AUDIT">
            <column name="PROVIDER_ACCOUNT_NAME"/>
        </createIndex>

        <createIndex indexName="IDX_SYNC_USER_AUDIT_TRANSACTION_ENTITY" tableName="SYNC_USER_AUDIT">
            <column name="SYNC_TRANSACTION_GUID"/>
            <column name="ENTITY_TYPE"/>
        </createIndex>

        <rollback>
            <dropTable tableName="SYNC_USER_AUDIT"/>
        </rollback>
    </changeSet>
</databaseChangeLog>
