<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="sandsandsand" id="ai-agent-registry-2025.05.16-CJ-5144-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.05.16-CJ-5144"/>
    </changeSet>

    <changeSet author="sandsandsand" id="ai-agent-registry-2025.05.16-CJ-5144-1" runOnChange="true">
        <createView viewName="v_agent_listing" replaceIfExists="true">
            SELECT
                a.uid AS agent_id,
                a.guid AS agent_guid,
                a.external_id AS agent_external_id,
                pa.guid AS provider_guid,
                pa.idp_account_id,
                a.is_deleted AS agent_is_deleted,
                pa.uid AS provider_account_uid,
                pa.provider_type,
                pa.provider_account_name,
                av.uid AS version_id,
                av.guid AS version_guid,
                av.name AS version_name,
                av.version_string AS version,
                av.external_id AS version_external_id,
                av.description AS description,
                av.agent_status,
                av.trust_level,
                av.is_deleted AS version_is_deleted,
                COALESCE(aa.uid, 0) AS alias_id,
                aa.guid AS alias_guid,
                aa.name AS alias_name,
                aa.is_deleted AS alias_is_deleted,
                aa.external_id AS alias_external_id,
                av.modified_time AS modified_time,
                av.updated_at_provider_time,
                CASE WHEN aa.uid IS NOT NULL THEN TRUE ELSE FALSE END AS has_alias
            FROM ai_agent a
                     JOIN ai_agent_version av ON a.uid = av.agent_uid AND av.is_deleted=false
                     LEFT JOIN ai_agent_alias aa ON av.uid = aa.agent_version_uid AND aa.is_deleted = false
                     JOIN ai_agent_provider_account pa ON a.provider_account_uid = pa.uid AND a.is_deleted = false;
        </createView>

        <rollback>
            DROP VIEW IF EXISTS v_agent_listing CASCADE;
        </rollback>
    </changeSet>

    <!-- Optimized indexes for the modified query -->
    <changeSet author="sandsandsand" id="ai-agent-registry-2025.05.16-CJ-5144-2">
        <!-- Index for ai_agent_version join condition -->
        <createIndex indexName="idx_agent_version_agent_uid_deleted" tableName="ai_agent_version">
            <column name="agent_uid"/>
            <column name="is_deleted"/>
        </createIndex>

        <!-- Index for ai_agent_alias join condition -->
        <createIndex indexName="idx_agent_alias_version_uid_deleted" tableName="ai_agent_alias">
            <column name="agent_version_uid"/>
            <column name="is_deleted"/>
        </createIndex>

        <!-- Index for ai_agent provider account join condition -->
        <createIndex indexName="idx_agent_provider_account_deleted" tableName="ai_agent">
            <column name="provider_account_uid"/>
            <column name="is_deleted"/>
        </createIndex>

        <!-- Additional indexes for commonly filtered columns -->
        <createIndex indexName="idx_agent_version_status" tableName="ai_agent_version">
            <column name="agent_status"/>
        </createIndex>

        <createIndex indexName="idx_provider_account_type" tableName="ai_agent_provider_account">
            <column name="provider_type"/>
        </createIndex>

        <createIndex indexName="idx_provider_account_idp" tableName="ai_agent_provider_account">
            <column name="idp_account_id"/>
        </createIndex>

        <rollback>
            <dropIndex indexName="idx_agent_version_agent_uid_deleted" tableName="ai_agent_version"/>
            <dropIndex indexName="idx_agent_alias_version_uid_deleted" tableName="ai_agent_alias"/>
            <dropIndex indexName="idx_agent_provider_account_deleted" tableName="ai_agent"/>
            <dropIndex indexName="idx_agent_version_status" tableName="ai_agent_version"/>
            <dropIndex indexName="idx_provider_account_type" tableName="ai_agent_provider_account"/>
            <dropIndex indexName="idx_provider_account_idp" tableName="ai_agent_provider_account"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
