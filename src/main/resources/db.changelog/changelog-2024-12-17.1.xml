<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="sandeshbhat" id="ai-agent-tag-2024.12.17.1-CJ-3365-0" context="PRE">
        <tagDatabase tag="ai-agent-tag-2024.12.17.1-CJ-3365"/>
    </changeSet>

    <!-- Rename 'key' column -->
    <changeSet author="sandeshbhat" id="ai-agent-tag-2024.12.17.1-CJ-3365-1">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="AI_AGENT_TAG"/>
            <columnExists tableName="AI_AGENT_TAG" columnName="KEY"/>
        </preConditions>

        <renameColumn
                tableName="AI_AGENT_TAG"
                oldColumnName="KEY"
                newColumnName="TAG_KEY"/>

        <rollback>
            <renameColumn
                    tableName="AI_AGENT_TAG"
                    oldColumnName="TAG_KEY"
                    newColumnName="KEY"/>
        </rollback>
    </changeSet>

    <!-- Rename 'value' column -->
    <changeSet author="sandeshbhat" id="ai-agent-tag-2024.12.17.1-CJ-3365-2">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="AI_AGENT_TAG"/>
            <columnExists tableName="AI_AGENT_TAG" columnName="VALUE"/>
        </preConditions>

        <renameColumn
                tableName="AI_AGENT_TAG"
                oldColumnName="VALUE"
                newColumnName="TAG_VALUE"/>

        <rollback>
            <renameColumn
                    tableName="AI_AGENT_TAG"
                    oldColumnName="TAG_VALUE"
                    newColumnName="VALUE"/>
        </rollback>
    </changeSet>

    <!-- Composite index for idp_account_id and tag_key lookup -->
    <changeSet author="sandeshbhat" id="ai-agent-tag-2024.12.17.1-CJ-3365-3">

        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists
                        tableName="AI_AGENT_TAG"
                        indexName="idx_ai_agent_tag_idp_tagkey"/>
            </not>
        </preConditions>

        <createIndex
                tableName="AI_AGENT_TAG"
                indexName="idx_ai_agent_tag_idp_tagkey">
            <column name="IDP_ACCOUNT_ID"/>
            <column name="TAG_KEY"/>
        </createIndex>

        <rollback>
            <dropIndex
                    tableName="AI_AGENT_TAG"
                    indexName="idx_ai_agent_tag_idp_tagkey"/>
        </rollback>
    </changeSet>

    <!-- Composite index for related entity lookups -->
    <changeSet author="sandeshbhat" id="ai-agent-tag-2024.12.17.1-CJ-3365-4">

        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists
                        tableName="AI_AGENT_TAG_ASSOCIATION"
                        indexName="idx_tag_assoc_entity_lookup"/>
            </not>
        </preConditions>

        <createIndex
                tableName="AI_AGENT_TAG_ASSOCIATION"
                indexName="idx_tag_assoc_entity_lookup">
            <column name="RELATED_ENTITY_TYPE"/>
            <column name="RELATED_ENTITY_UID"/>
        </createIndex>

        <rollback>
            <dropIndex
                    tableName="AI_AGENT_TAG_ASSOCIATION"
                    indexName="idx_tag_assoc_entity_lookup"/>
        </rollback>
    </changeSet>



</databaseChangeLog>
