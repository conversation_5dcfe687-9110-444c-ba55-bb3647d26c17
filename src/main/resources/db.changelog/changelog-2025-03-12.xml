<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.12-CJ-4211-remove-agent_entity_sync_history_uid-column-0"
               context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.03.12-CJ-4211-remove-agent_entity_sync_history_uid-column"/>
    </changeSet>

    <changeSet author="weiwang" id="ai-agent-registry-2025.03.12-CJ-4211-remove-agent_entity_sync_history_uid-column-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <and>
                <tableExists tableName="agent_entity_sync_latest"/>
                <columnExists tableName="agent_entity_sync_latest" columnName="agent_entity_sync_history_uid"/>
            </and>
        </preConditions>

        <dropColumn tableName="agent_entity_sync_latest" columnName="agent_entity_sync_history_uid"/>

        <rollback>
            <addColumn tableName="agent_entity_sync_latest">
                <column name="AGENT_ENTITY_SYNC_HISTORY_UID" type="SERIAL">
                    <constraints nullable="false"/>
                </column>
            </addColumn>
        </rollback>
    </changeSet>
</databaseChangeLog>
