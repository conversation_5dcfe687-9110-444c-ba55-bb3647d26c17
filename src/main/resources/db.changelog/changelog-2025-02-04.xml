<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.04-CJ-3922-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.02.04-CJ-3922"/>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.04-CJ-3922-1">

        <preConditions onFail="MARK_RAN" onError="HALT">
           <indexExists indexName="idx_mv_ai_agent_tags_by_idp_by_key_unique" tableName="mv_ai_agent_tags_by_idp" />
        </preConditions>

        <dropIndex tableName="mv_ai_agent_tags_by_idp" indexName="idx_mv_ai_agent_tags_by_idp_by_key_unique"/>

        <rollback>
            <createIndex indexName="idx_mv_ai_agent_tags_by_idp_by_key_unique"
                         tableName="mv_ai_agent_tags_by_idp"
                         unique="true">
                <column name="tag_key"/>
            </createIndex>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.04-CJ-3922-2">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <indexExists indexName="idx_mv_ai_agent_tags_by_idp" tableName="mv_ai_agent_tags_by_idp" />
        </preConditions>

        <dropIndex tableName="mv_ai_agent_tags_by_idp" indexName="idx_mv_ai_agent_tags_by_idp"/>

        <rollback>
            <createIndex indexName="idx_mv_ai_agent_tags_by_idp"
                         tableName="mv_ai_agent_tags_by_idp"
                         unique="false">
                <column name="idp_account_id"/>
            </createIndex>
        </rollback>
    </changeSet>

    <changeSet author="subramanyahegde" id="ai-agent-registry-2025.02.04-CJ-3922-3">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <not>
                <indexExists tableName="mv_ai_agent_tags_by_idp" indexName="idx_mv_ai_agent_idp_account_id_tag_key_unique"/>
            </not>
        </preConditions>

        <createIndex tableName="mv_ai_agent_tags_by_idp" indexName="idx_mv_ai_agent_idp_account_id_tag_key_unique"
                     unique="true">
            <column name="idp_account_id"/>
            <column name="tag_key"/>
        </createIndex>

        <rollback>
            <dropIndex tableName="mv_ai_agent_tags_by_idp" indexName="idx_mv_ai_agent_idp_account_id_tag_key_unique"/>
        </rollback>
    </changeSet>
</databaseChangeLog>
