<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="add-guardrail-json-column" author="azookari">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="ai_agent_guardrail"/>
            <not>
                <columnExists tableName="ai_agent_guardrail" columnName="guardrail_json"/>
            </not>
        </preConditions>

        <addColumn tableName="ai_agent_guardrail">
            <column name="guardrail_json" type="CLOB">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <rollback>
            <dropColumn tableName="ai_agent_guardrail" columnName="guardrail_json"/>
        </rollback>
    </changeSet>
</databaseChangeLog>


