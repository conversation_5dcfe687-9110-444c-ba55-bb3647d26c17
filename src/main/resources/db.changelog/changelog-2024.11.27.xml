<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.11.27-CJ-3366-1" context="PRE">
        <tagDatabase tag="ai-agent-registry-2024.11.27-CJ-3366"/>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.11.27-CJ-3366-2">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_TAG"/>
        </preConditions>

        <!-- Remove not null constraint from VALUE column -->
        <dropNotNullConstraint tableName="AI_AGENT_TAG"
                               columnName="VALUE"
                               columnDataType="VARCHAR(255)"/>

        <!-- Add not null constraint to KEY column -->
        <addNotNullConstraint tableName="AI_AGENT_TAG"
                              columnName="KEY"
                              columnDataType="VARCHAR(255)"/>

        <rollback>
            <!-- Restore original state -->
            <addNotNullConstraint tableName="AI_AGENT_TAG"
                                  columnName="VALUE"
                                  columnDataType="VARCHAR(255)"/>
            <dropNotNullConstraint tableName="AI_AGENT_TAG"
                                   columnName="KEY"
                                   columnDataType="VARCHAR(255)"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.11.27-CJ-3366-3">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_PROVIDER_ACCOUNT"/>
        </preConditions>

        <addColumn tableName="AI_AGENT_PROVIDER_ACCOUNT">
            <column name="PROVIDER_ACCOUNT_DESCRIPTION" type="VARCHAR(1000)">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <rollback>
            <dropColumn tableName="AI_AGENT_PROVIDER_ACCOUNT"
                       columnName="PROVIDER_ACCOUNT_DESCRIPTION"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
