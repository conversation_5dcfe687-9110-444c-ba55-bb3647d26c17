<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.11.23-CJ-3392-1" context="PRE">
        <tagDatabase tag="ai-agent-registry-2024.11.23-CJ-3392"/>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2024.11.23-CJ-3392-2">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_TOOL"/>
            <not>
                <columnExists tableName="AI_AGENT_TOOL" columnName="TOOL_PROVIDER"/>
            </not>
            <not>
                <columnExists tableName="AI_AGENT_TOOL" columnName="TOOL_TYPE"/>
            </not>
            <not>
                <columnExists tableName="AI_AGENT_TOOL" columnName="TOOL_SUBTYPE"/>
            </not>
            <not>
                <columnExists tableName="AI_AGENT_TOOL" columnName="TOOL_JSON"/>
            </not>
            <not>
                <columnExists tableName="AI_AGENT_TOOL" columnName="STATUS"/>
            </not>
        </preConditions>

        <addColumn tableName="AI_AGENT_TOOL">
            <!-- The tool might belong to a provider other than the agent's provider -->
            <column name="TOOL_PROVIDER" type="VARCHAR(50)" />

            <!-- AWS BEDROCK has the following tools: ACTION_GROUPS, KNOWLEDGE BASE
                 OpenAI has the following tools: CODE_INTERPRETER, FILE_SEARCH, FUNCTION
                 BOOMI has the following tools: INTEGRATION, HUB, OPEN_API
            -->
            <column name="TOOL_TYPE" type="VARCHAR(50)"/>

            <!-- AWS BEDROCK has the following subtypes: FUNCTION, API for ACTION_GROUPS
            -->
            <column name="TOOL_SUBTYPE" type="VARCHAR(50)"/>

            <!--  Store the entire tool response as json -->
            <column name="TOOL_JSON" type="CLOB"/>

            <!-- ENABLED, DISABLED, etc -->
            <column name="STATUS" type="VARCHAR(50)"/>
        </addColumn>

        <rollback>
            <dropColumn tableName="AI_AGENT_TOOL" columnName="TOOL_PROVIDER"/>
            <dropColumn tableName="AI_AGENT_TOOL" columnName="TOOL_TYPE"/>
            <dropColumn tableName="AI_AGENT_TOOL" columnName="TOOL_SUBTYPE"/>
            <dropColumn tableName="AI_AGENT_TOOL" columnName="TOOL_JSON"/>
            <dropColumn tableName="AI_AGENT_TOOL" columnName="STATUS"/>
        </rollback>
    </changeSet>

    <!--This table will store the  resources need for an agent tool. e.g.:
    store the Datsources needed by a Bedrock knowledge base tool-->
    <changeSet id="ai-agent-registry-2024.11.23-CJ-3392-3" author="johnvarghese">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <not>
                <tableExists tableName="AI_AGENT_TOOL_RESOURCE"/>
            </not>
            <tableExists tableName="AI_AGENT_TOOL"/>
        </preConditions>

        <createTable tableName="AI_AGENT_TOOL_RESOURCE">
            <column autoIncrement="true"  name="UID" type="SERIAL">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_AI_AGENT_TOOL_RESOURCE"/>
            </column>
            <column name="GUID" type="UUID"/>
            <column name="TOOL_UID" type="SERIAL"/>
            <column name="NAME" type="VARCHAR(255)"/>
            <column name="DESCRIPTION" type="VARCHAR(2000)"/>
            <column name="RESOURCE_TYPE" type="VARCHAR(100)"/>

            <!-- This will store the entire tool resource json -->
            <column name="TOOL_RESOURCE_JSON" type="CLOB"/>

            <column name="STATUS" type="VARCHAR(50)"/>
            <column name="CREATED_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="MODIFIED_TIME" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>

        <addUniqueConstraint tableName="AI_AGENT_TOOL_RESOURCE" columnNames="GUID"
                                            constraintName="AI_AGENT_TOOL_RESOURCE_UNQ_GUID" />

        <addForeignKeyConstraint baseTableName="AI_AGENT_TOOL_RESOURCE"
                                 baseColumnNames="TOOL_UID"
                                 constraintName="FK_TOOL_RESOURCE_TOOL"
                                 referencedTableName="AI_AGENT_TOOL"
                                 referencedColumnNames="UID"/>
        <rollback>
            <dropUniqueConstraint constraintName="AI_AGENT_TOOL_RESOURCE_UNQ_GUID" tableName="AI_AGENT_TOOL_RESOURCE"/>
            <dropForeignKeyConstraint baseTableName="AI_AGENT_TOOL_RESOURCE" constraintName="FK_TOOL_RESOURCE_TOOL"/>
            <dropTable tableName="AI_AGENT_TOOL_RESOURCE"/>
        </rollback>
    </changeSet>
</databaseChangeLog>
