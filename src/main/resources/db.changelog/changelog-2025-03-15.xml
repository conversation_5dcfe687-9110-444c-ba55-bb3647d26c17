<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.03.15-CJ-4443-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.03.15-CJ-4443"/>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.03.15-CJ-4443-1">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_PROVIDER_ACCOUNT"/>
            <columnExists tableName="AI_AGENT_PROVIDER_ACCOUNT" columnName="CREATED_BY_USER_ID"/>
        </preConditions>
        <modifyDataType
                columnName="CREATED_BY_USER_ID"
                newDataType="varchar(255)"
                tableName="AI_AGENT_PROVIDER_ACCOUNT"/>

        <rollback>
            <modifyDataType
                    columnName="CREATED_BY_USER_ID"
                    newDataType="varchar(36)"
                    tableName="AI_AGENT_PROVIDER_ACCOUNT"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.03.15-CJ-4443-2">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_PROVIDER_ACCOUNT"/>
            <columnExists tableName="AI_AGENT_PROVIDER_ACCOUNT" columnName="MODIFIED_BY_USER_ID"/>
        </preConditions>
        <modifyDataType
                columnName="MODIFIED_BY_USER_ID"
                newDataType="varchar(255)"
                tableName="AI_AGENT_PROVIDER_ACCOUNT"/>

        <rollback>
            <modifyDataType
                   columnName="MODIFIED_BY_USER_ID"
                   newDataType="varchar(36)"
                   tableName="AI_AGENT_PROVIDER_ACCOUNT"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.03.15-CJ-4443-3">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_VERSION"/>
            <columnExists tableName="AI_AGENT_VERSION" columnName="CREATED_BY_USER_ID"/>
        </preConditions>
        <modifyDataType
                columnName="CREATED_BY_USER_ID"
                newDataType="varchar(255)"
                tableName="AI_AGENT_VERSION"/>

        <rollback>
            <modifyDataType
                  columnName="CREATED_BY_USER_ID"
                  newDataType="varchar(100)"
                  tableName="AI_AGENT_VERSION"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.03.15-CJ-4443-4">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_VERSION"/>
            <columnExists tableName="AI_AGENT_VERSION" columnName="MODIFIED_BY_USER_ID"/>
        </preConditions>
        <modifyDataType
                columnName="MODIFIED_BY_USER_ID"
                newDataType="varchar(255)"
                tableName="AI_AGENT_VERSION"/>

        <rollback>
            <modifyDataType
                    columnName="MODIFIED_BY_USER_ID"
                    newDataType="varchar(100)"
                    tableName="AI_AGENT_VERSION"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.03.15-CJ-4443-5">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_ALIAS"/>
            <columnExists tableName="AI_AGENT_ALIAS" columnName="CREATED_BY_USER_ID"/>
        </preConditions>
        <modifyDataType
                columnName="CREATED_BY_USER_ID"
                newDataType="varchar(255)"
                tableName="AI_AGENT_ALIAS"/>

        <rollback>
            <modifyDataType
                    columnName="CREATED_BY_USER_ID"
                    newDataType="varchar(100)"
                    tableName="AI_AGENT_ALIAS"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.03.15-CJ-4443-6">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_ALIAS"/>
            <columnExists tableName="AI_AGENT_ALIAS" columnName="MODIFIED_BY_USER_ID"/>
        </preConditions>
        <modifyDataType
                columnName="MODIFIED_BY_USER_ID"
                newDataType="varchar(255)"
                tableName="AI_AGENT_ALIAS"/>

        <rollback>
            <modifyDataType
                    columnName="MODIFIED_BY_USER_ID"
                    newDataType="varchar(100)"
                    tableName="AI_AGENT_ALIAS"/>

        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.03.15-CJ-4443-7">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_TOOL"/>
            <columnExists tableName="AI_AGENT_TOOL" columnName="CREATED_BY_USER_ID"/>
        </preConditions>
        <modifyDataType
                columnName="CREATED_BY_USER_ID"
                newDataType="varchar(255)"
                tableName="AI_AGENT_TOOL"/>

        <rollback>
            <modifyDataType
                    columnName="CREATED_BY_USER_ID"
                    newDataType="varchar(100)"
                    tableName="AI_AGENT_TOOL"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.03.15-CJ-4443-8">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_TOOL"/>
            <columnExists tableName="AI_AGENT_TOOL" columnName="MODIFIED_BY_USER_ID"/>
        </preConditions>
        <modifyDataType
                columnName="MODIFIED_BY_USER_ID"
                newDataType="varchar(255)"
                tableName="AI_AGENT_TOOL"/>

        <rollback>
            <modifyDataType
                    columnName="MODIFIED_BY_USER_ID"
                    newDataType="varchar(100)"
                    tableName="AI_AGENT_TOOL"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.03.15-CJ-4443-9">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_GUARDRAIL"/>
            <columnExists tableName="AI_AGENT_GUARDRAIL" columnName="CREATED_BY_USER_ID"/>
        </preConditions>
        <modifyDataType
                columnName="CREATED_BY_USER_ID"
                newDataType="varchar(255)"
                tableName="AI_AGENT_GUARDRAIL"/>

        <rollback>
            <modifyDataType
                    columnName="CREATED_BY_USER_ID"
                    newDataType="varchar(100)"
                    tableName="AI_AGENT_GUARDRAIL"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.03.15-CJ-4443-10">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_GUARDRAIL"/>
            <columnExists tableName="AI_AGENT_GUARDRAIL" columnName="MODIFIED_BY_USER_ID"/>
        </preConditions>
        <modifyDataType
                columnName="MODIFIED_BY_USER_ID"
                newDataType="varchar(255)"
                tableName="AI_AGENT_GUARDRAIL"/>

        <rollback>
            <modifyDataType
                    columnName="MODIFIED_BY_USER_ID"
                    newDataType="varchar(100)"
                    tableName="AI_AGENT_GUARDRAIL"/>
        </rollback>
    </changeSet>


    <changeSet author="johnvarghese" id="ai-agent-registry-2025.03.15-CJ-4443-11">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_TASK"/>
            <columnExists tableName="AI_AGENT_TASK" columnName="CREATED_BY_USER_ID"/>
        </preConditions>
        <modifyDataType
                columnName="CREATED_BY_USER_ID"
                newDataType="varchar(255)"
                tableName="AI_AGENT_TASK"/>

        <rollback>
            <modifyDataType
                    columnName="CREATED_BY_USER_ID"
                    newDataType="varchar(100)"
                    tableName="AI_AGENT_TASK"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.03.15-CJ-4443-12">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_TASK"/>
            <columnExists tableName="AI_AGENT_TASK" columnName="MODIFIED_BY_USER_ID"/>
        </preConditions>
        <modifyDataType
                columnName="MODIFIED_BY_USER_ID"
                newDataType="varchar(255)"
                tableName="AI_AGENT_TASK"/>

        <rollback>
            <modifyDataType
                    columnName="MODIFIED_BY_USER_ID"
                    newDataType="varchar(100)"
                    tableName="AI_AGENT_TASK"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.03.15-CJ-4443-13">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="SYNC_USER_AUDIT"/>
            <columnExists tableName="SYNC_USER_AUDIT" columnName="USER_ID"/>
        </preConditions>
        <modifyDataType
                columnName="USER_ID"
                newDataType="varchar(255)"
                tableName="SYNC_USER_AUDIT"/>

        <rollback>
            <modifyDataType
                    columnName="USER_ID"
                    newDataType="varchar(100)"
                    tableName="SYNC_USER_AUDIT"/>
        </rollback>
    </changeSet>

    <changeSet author="johnvarghese" id="ai-agent-registry-2025.03.15-CJ-4443-14">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AUDIT_LOG"/>
            <columnExists tableName="AUDIT_LOG" columnName="USER_ID"/>
        </preConditions>
        <modifyDataType
                columnName="USER_ID"
                newDataType="varchar(255)"
                tableName="AUDIT_LOG"/>

        <rollback>
            <modifyDataType
                    columnName="USER_ID"
                    newDataType="varchar(100)"
                    tableName="AUDIT_LOG"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
