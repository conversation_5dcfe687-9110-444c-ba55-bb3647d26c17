<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="sandeshbhat" id="ai-agent-tag-2024.12.17.1-CJ-3365-0" context="PRE">
        <tagDatabase tag="ai-agent-tag-2024.12.17.2-CJ-3365"/>
    </changeSet>

    <changeSet author="sandeshbhat" id="ai-agent-tag-2024.12.17.2-CJ-3365-1">

        <!-- Drop trigger and function if they exist -->
        <sql>
            DROP TRIGGER IF EXISTS trg_refresh_ai_agent_tags_mv ON ai_agent_tag;
            DROP FUNCTION IF EXISTS refresh_ai_agent_tags_mv();
        </sql>

        <!-- Drop the materialized view if it exists -->
        <sql>
            DROP MATERIALIZED VIEW IF EXISTS mv_ai_agent_tags_by_idp;
        </sql>

        <!-- Create the materialized view with function & trigger -->
        <sql>
            CREATE MATERIALIZED VIEW mv_ai_agent_tags_by_idp
            AS
            SELECT DISTINCT
                t.idp_account_id,
                t.tag_key
            FROM ai_agent_tag t
            ORDER BY t.idp_account_id;

            CREATE OR REPLACE FUNCTION refresh_ai_agent_tags_mv()
            RETURNS TRIGGER
            LANGUAGE plpgsql
            AS '
            BEGIN
                REFRESH MATERIALIZED VIEW CONCURRENTLY mv_ai_agent_tags_by_idp;
            RETURN NULL;
            END;
            ';

            CREATE TRIGGER trg_refresh_ai_agent_tags_mv
                AFTER INSERT OR UPDATE OR DELETE OR TRUNCATE
                                ON ai_agent_tag
                                    FOR EACH STATEMENT
                                    EXECUTE FUNCTION refresh_ai_agent_tags_mv();
        </sql>
        <rollback>
            DROP TRIGGER IF EXISTS trg_refresh_ai_agent_tags_mv ON ai_agent_tag;
            DROP FUNCTION IF EXISTS refresh_ai_agent_tags_mv();
            DROP MATERIALIZED VIEW IF EXISTS mv_ai_agent_tags_by_idp;
        </rollback>

        <!-- Create a unique index (required for concurrent refresh) -->
        <createIndex indexName="idx_mv_ai_agent_tags_by_idp_by_key_unique"
                     tableName="mv_ai_agent_tags_by_idp"
                     unique="true">
            <column name="tag_key"/>
        </createIndex>
        <rollback>
            DROP INDEX IF EXISTS idx_mv_ai_agent_tags_by_idp_by_key_unique;
        </rollback>

        <!-- Create an index on the materialized view -->
        <createIndex indexName="idx_mv_ai_agent_tags_by_idp"
                     tableName="mv_ai_agent_tags_by_idp"
                     unique="false">
            <column name="idp_account_id"/>
        </createIndex>
        <rollback>
            DROP INDEX IF EXISTS idx_mv_ai_agent_tags_by_idp;
        </rollback>

    </changeSet>

</databaseChangeLog>
