<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="azookari" id="ai-agent-registry-2024.12.11-add-tool-unique-constraint">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_TOOL"/>
            <columnExists tableName="AI_AGENT_TOOL" columnName="PROVIDER_ACCOUNT_UID"/>
            <columnExists tableName="AI_AGENT_TOOL" columnName="EXTERNAL_ID"/>
            <columnExists tableName="AI_AGENT_TOOL" columnName="TOOL_TYPE"/>
        </preConditions>

        <addUniqueConstraint
                tableName="AI_AGENT_TOOL"
                columnNames="PROVIDER_ACCOUNT_UID, EXTERNAL_ID, TOOL_TYPE"
                constraintName="UK_TOOL_ACCOUNT_EXTERNAL_TYPE"/>

        <rollback>
            <dropUniqueConstraint
                    tableName="AI_AGENT_TOOL"
                    constraintName="ai_agent_tool_unq_acct_external_id_entity_type"/>
        </rollback>
    </changeSet>
    <changeSet author="azookari" id="ai-agent-registry-2024.12.11-add-external-id-to-tool-resource">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="AI_AGENT_TOOL_RESOURCE"/>
        </preConditions>

        <addColumn tableName="AI_AGENT_TOOL_RESOURCE">
            <column name="EXTERNAL_ID" type="varchar(50)">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <rollback>
            <dropColumn tableName="AI_AGENT_TOOL_RESOURCE" columnName="EXTERNAL_ID"/>
        </rollback>
    </changeSet>


</databaseChangeLog>




