<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.9.xsd">

    <changeSet author="sandeshbhat" id="ai-agent-registry-2025.04.15-CJ-4512-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.04.15-CJ-4512-license-tables"/>
    </changeSet>

    <changeSet author="sandeshbhat" id="ai-agent-registry-2025.04.15-CJ-4512-1">
        <createTable tableName="AI_AGENT_REGISTRY_LICENSE">
            <column name="UID" type="SERIAL">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="TIER_ID" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="TIER_TYPE" type="VARCHAR(50)"/>
            <column name="TIER_DEFAULT_SOFT_LIMIT" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="SYSTEM_DEFAULT_LIMIT" type="INTEGER">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <rollback>
            <dropTable tableName="AI_AGENT_REGISTRY_LICENSE"/>
        </rollback>
    </changeSet>

    <changeSet author="sandeshbhat" id="ai-agent-registry-2025.04.15-CJ-4512-2">
        <createTable tableName="AI_AGENT_REGISTRY_LICENSE_ASSOCIATION">
            <column name="UID" type="SERIAL">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="IDP_ACCOUNT_ID" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="AI_AGENT_REGISTRY_LICENSE_UID" type="INTEGER"/>
            <column name="TIER_SOFT_LIMIT" type="INTEGER"/>
            <column name="SYSTEM_LIMIT" type="INTEGER"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="AI_AGENT_REGISTRY_LICENSE_ASSOCIATION"
                                 baseColumnNames="AI_AGENT_REGISTRY_LICENSE_UID"
                                 constraintName="AI_AGENT_REGISTRY_LICENSE_ASSOCIATION_FK"
                                 referencedTableName="AI_AGENT_REGISTRY_LICENSE"
                                 referencedColumnNames="UID"/>

        <rollback>
            <dropTable tableName="AI_AGENT_REGISTRY_LICENSE_ASSOCIATION"/>
        </rollback>
    </changeSet>

    <changeSet author="sandeshbhat" id="ai-agent-registry-2025.04.15-CJ-4512-3">
        <insert tableName="AI_AGENT_REGISTRY_LICENSE">
            <column name="UID" value="1"/>
            <column name="TIER_ID" value="0"/>
            <column name="TIER_TYPE" value="FREE"/>
            <column name="TIER_DEFAULT_SOFT_LIMIT" value="10"/>
            <column name="SYSTEM_DEFAULT_LIMIT" value="250"/>
        </insert>
        <insert tableName="AI_AGENT_REGISTRY_LICENSE">
            <column name="UID" value="2"/>
            <column name="TIER_ID" value="1"/>
            <column name="TIER_TYPE" value="PRO"/>
            <column name="TIER_DEFAULT_SOFT_LIMIT" value="50"/>
            <column name="SYSTEM_DEFAULT_LIMIT" value="250"/>
        </insert>
        <insert tableName="AI_AGENT_REGISTRY_LICENSE">
            <column name="UID" value="3"/>
            <column name="TIER_ID" value="2"/>
            <column name="TIER_TYPE" value="ENTERPRISE"/>
            <column name="TIER_DEFAULT_SOFT_LIMIT" value="100"/>
            <column name="SYSTEM_DEFAULT_LIMIT" value="250"/>
        </insert>

        <rollback>
            <delete tableName="AI_AGENT_REGISTRY_LICENSE">
                <where>UID in (1, 2, 3)</where>
            </delete>
        </rollback>
    </changeSet>

</databaseChangeLog>
