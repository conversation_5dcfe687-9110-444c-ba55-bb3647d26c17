<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="sandeshabhat" id="ai-agent-registry-2025.03.10-CJ-4244-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.03.10-CJ-4244"/>
    </changeSet>

    <changeSet author="sandeshabhat" id="ai-agent-registry-2025.03.10-CJ-4244-1" runOnChange="true">

        <preConditions onFail="MARK_RAN" onError="HALT">
            <tableExists tableName="ai_agent"/>
            <tableExists tableName="ai_agent_version"/>
            <tableExists tableName="ai_agent_provider_account"/>
            <tableExists tableName="ai_agent_alias"/>
            <tableExists tableName="ai_agent_tool_association"/>
            <tableExists tableName="ai_agent_tool"/>
            <tableExists tableName="ai_agent_llm_association"/>
            <tableExists tableName="ai_agent_llm"/>
        </preConditions>

        <!-- Add columns -->
        <addColumn tableName="ai_agent">
            <column name="modified_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP"/>
        </addColumn>


        <rollback>
            <!-- Important: Drop dependent views first -->
            <sql>
                <!-- Drop modified_time columns if they exist -->
                ALTER TABLE IF EXISTS ai_agent_llm_association DROP COLUMN IF EXISTS modified_time;
                ALTER TABLE IF EXISTS ai_agent_llm DROP COLUMN IF EXISTS modified_time;
                ALTER TABLE IF EXISTS ai_agent_tool_association DROP COLUMN IF EXISTS modified_time;
                ALTER TABLE IF EXISTS ai_agent DROP COLUMN IF EXISTS modified_time;
            </sql>

        </rollback>

    </changeSet>
</databaseChangeLog>
