<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="sandeshabhat" id="ai-agent-registry-2025.03.10-CJ-4244-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.03.10-CJ-4244"/>
    </changeSet>

    <changeSet author="sandeshabhat" id="ai-agent-registry-2025.03.10-CJ-4244-1" runOnChange="true">

        <!-- Load and execute the procedure SQL file -->
        <sqlFile path="refresh_agent_data_high_frequency.sql"
                 encoding="UTF-8"
                 splitStatements="false"
                 relativeToChangelogFile="true"/>

        <rollback>
            DROP PROCEDURE IF EXISTS refresh_agent_data_high_frequency(OUT text);
        </rollback>

    </changeSet>
</databaseChangeLog>
