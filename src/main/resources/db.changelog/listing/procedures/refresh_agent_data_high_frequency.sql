CREATE OR REPLACE PROCEDURE refresh_agent_data_high_frequency(
    OUT p_result text
)
LANGUAGE plpgsql
AS $$
DECLARE
v_lock_acquired BOOLEAN;
    v_lock_key INT;
    v_count_inserted INT := 0;
    v_count_updated INT := 0;
    v_count_deleted INT := 0;
    v_start_time TIMESTAMP;
    v_end_time TIMESTAMP;
    v_duration_ms INT;
    v_temp_count INT := 0;
    v_status TEXT := 'COMPLETED';
    v_agent_core_count INT := 0;
    v_hard_deleted_orphaned INT:= 0;
    v_soft_deleted_orphaned INT:= 0;
    v_soft_count_deleted INT:=0;
    v_hard_count_deleted INT:=0;
    v_orphaned_alias_deleted INT:=0;
    v_redundant_alias_count INT:=0;

BEGIN
    -- set timezone to UTC
    --SET timezone = 'UTC';

    v_start_time := clock_timestamp();

    -- Create a unique lock key
    v_lock_key := 5;

    -- Try to acquire advisory lock with a timeout
    v_lock_acquired := pg_try_advisory_lock(v_lock_key);

    IF NOT v_lock_acquired THEN
        p_result := jsonb_build_object(
                'status', 'SKIPPED',
                'message', 'Another process is already refreshing',
                'timestamp', now()
               )::text;
        RETURN;
END IF;

BEGIN
        -- Mark refresh as in progress
UPDATE v_refresh_tracker
SET status = 'RUNNING'
WHERE view_name IN ('v_agent_refresh');

SELECT COUNT(*) INTO v_agent_core_count FROM v_agent_core;
--v_agent_core_count := (SELECT COUNT(*) FROM v_agent_core);

RAISE NOTICE 'Agent core count : %', v_agent_core_count;

        -- Create temporary tables instead of using views directly
        -- This materializes the data once and allows for better query planning
        IF v_agent_core_count > 0 THEN
            -- Clean up temporary table
            DROP TABLE IF EXISTS temp_incremental_data;
            CREATE TEMPORARY TABLE temp_incremental_data AS
SELECT
    c.agent_id,
    c.agent_guid,
    c.agent_external_id,
    c.guid,
    c.provider_account_uid,
    c.provider_type,
    c.idp_account_id,
    c.provider_account_name,
    c.version_id,
    c.version_guid,
    c.version_external_id,
    c.version_name,
    c.version,
    c.description,
    c.agent_status,
    c.trust_level,
    c.alias_id ,
    c.alias_guid,
    c.alias_external_id,
    c.alias_name,
    c.modified_time,
    c.updated_at_provider_time,
    c.has_alias,
    c.agent_is_deleted,
    c.version_is_deleted,
    c.alias_is_deleted
FROM v_agent_core c;

-- Create index on the temp table for better join performance
CREATE INDEX idx_temp_version_alias ON temp_incremental_data(version_id, alias_id);

-- Get total count for batching
SELECT COUNT(*) INTO v_temp_count FROM temp_incremental_data;
--v_temp_count := (SELECT COUNT(*) FROM temp_incremental_data);

RAISE NOTICE 'Temp incremental data: temp_incremental_data : %', v_temp_count;

            -- Process inserts and updates in batches
            -- This reduces transaction size and lock contention
WITH upsert_result AS (
INSERT INTO agent_flattened (
    agent_id,
    agent_guid,
    agent_external_id,
    provider_account_uid,
    provider_type,
    provider_guid,
    idp_account_id,
    provider_account_name,
    version_id,
    version_guid,
    version_external_id,
    version_name,
    version,
    description,
    agent_status,
    trust_level,
    alias_id,
    alias_guid,
    alias_external_id,
    alias_name,
    modified_time,
    updated_at_provider_time,
    has_alias,
    agent_is_deleted,
    version_is_deleted,
    alias_is_deleted
)
SELECT
    agent_id,
    agent_guid,
    agent_external_id,
    provider_account_uid,
    provider_type,
    guid,
    idp_account_id,
    provider_account_name,
    version_id,
    version_guid,
    version_external_id,
    version_name,
    version,
    description,
    agent_status,
    trust_level,
    -- Handle NULL alias_id by using COALESCE to match the NOT NULL constraint
    COALESCE(alias_id, 0),
    alias_guid,
    alias_external_id,
    alias_name,
    modified_time,
    updated_at_provider_time,
    has_alias,
    agent_is_deleted,
    version_is_deleted,
    alias_is_deleted
FROM temp_incremental_data
    ON CONFLICT (version_id, alias_id) DO UPDATE SET
    agent_id = EXCLUDED.agent_id,
                                              agent_guid = EXCLUDED.agent_guid,
                                              agent_external_id = EXCLUDED.agent_external_id,
                                              provider_guid = EXCLUDED.provider_guid,
                                              provider_type = EXCLUDED.provider_type,
                                              provider_account_uid = EXCLUDED.provider_account_uid,
                                              provider_account_name = EXCLUDED.provider_account_name,
                                              idp_account_id = EXCLUDED.idp_account_id,
                                              version_external_id = EXCLUDED.version_external_id,
                                              version_name = EXCLUDED.version_name,
                                              version = EXCLUDED.version,
                                              description = EXCLUDED.description,
                                              agent_status = EXCLUDED.agent_status,
                                              trust_level = EXCLUDED.trust_level,
                                              alias_name = EXCLUDED.alias_name,
                                              alias_external_id = EXCLUDED.alias_external_id,
                                              modified_time = EXCLUDED.modified_time,
                                              updated_at_provider_time = EXCLUDED.updated_at_provider_time,
                                              has_alias = EXCLUDED.has_alias,
                                              agent_is_deleted = EXCLUDED.agent_is_deleted,
                                              version_is_deleted = EXCLUDED.version_is_deleted,
                                              alias_is_deleted = EXCLUDED.alias_is_deleted
                                              RETURNING
                                              (xmax = 0) AS inserted,
                                              (xmax <> 0) AS updated
                                              )
SELECT
    COUNT(*) FILTER (WHERE inserted),
        COUNT(*) FILTER (WHERE updated)
INTO v_count_inserted, v_count_updated
FROM upsert_result;

-- Delete records with alias_id = 0 that have corresponding records with actual aliases
WITH zero_alias_deletions AS (
    DELETE FROM agent_flattened af1
        WHERE (af1.alias_id = 0)
            AND EXISTS (
                SELECT 1
                FROM agent_flattened af2
                WHERE af2.version_id = af1.version_id
                  AND af2.alias_id > 0
            )
        RETURNING af1.version_id
)
SELECT COUNT(*) INTO v_orphaned_alias_deleted FROM zero_alias_deletions;
END IF;

-- First check for hard orphaned records
WITH hard_orphaned_count AS (
    SELECT COUNT(*) as count
FROM agent_flattened af
WHERE NOT EXISTS (
    SELECT 1
    FROM ai_agent_provider_account pa
    WHERE pa.uid = af.provider_account_uid
    )
    )
SELECT count INTO v_hard_deleted_orphaned FROM hard_orphaned_count;

-- Only proceed with deletion if orphaned records exist
IF v_hard_deleted_orphaned > 0 THEN
            WITH hard_orphaned_records AS (
                DELETE FROM agent_flattened af
                    WHERE NOT EXISTS (
                        SELECT 1
                        FROM ai_agent_provider_account pa
                        WHERE pa.uid = af.provider_account_uid
                    )
                    RETURNING af.provider_account_uid
            )
SELECT COUNT(*) INTO v_hard_count_deleted FROM hard_orphaned_records;

-- Update the total deleted count
v_count_deleted := v_count_deleted + (COALESCE(v_hard_count_deleted, 0));
END IF;


--Add cleanup for redundant aliases
        WITH cleanup_stats AS (
            -- First cleanup: Delete deleted aliases where non-deleted exists
            DELETE FROM agent_flattened af1
                WHERE alias_is_deleted = true
                    AND EXISTS (
                        SELECT 1
                        FROM agent_flattened af2
                        WHERE af2.agent_id = af1.agent_id
                          AND af2.version_id = af1.version_id
                          AND af2.alias_is_deleted = false
                    )
                RETURNING 1
        ),
             second_cleanup AS (
                 -- Second cleanup: For remaining deleted aliases, keep only newest
                 DELETE FROM agent_flattened af1
                     WHERE alias_is_deleted = true
                         AND EXISTS (
                             SELECT 1
                             FROM agent_flattened af2
                             WHERE af2.agent_id = af1.agent_id
                               AND af2.version_id = af1.version_id
                               AND af2.alias_is_deleted = true
                               AND af2.alias_id > af1.alias_id
                         )
                     RETURNING 1
             )
        SELECT
            (SELECT COUNT(*) FROM cleanup_stats) +
            (SELECT COUNT(*) FROM second_cleanup)
        INTO v_redundant_alias_count;

        RAISE NOTICE 'Cleaned up % redundant alias records', v_redundant_alias_count;

        -- First check for records that should be deleted based on hierarchical rules
WITH soft_orphaned_count AS (
    SELECT COUNT(*) as count
FROM agent_flattened af
WHERE
    af.agent_is_deleted = true
   OR af.version_is_deleted = true
    )
SELECT count INTO v_soft_deleted_orphaned FROM soft_orphaned_count;

-- Only proceed with deletion if orphaned records exist
IF v_soft_deleted_orphaned > 0 THEN
            WITH soft_orphaned_records AS (
                DELETE FROM agent_flattened af
                    WHERE
                        af.agent_is_deleted = true
                        OR af.version_is_deleted = true
                    RETURNING af.version_id, af.alias_id
            )
SELECT COUNT(*) INTO v_soft_count_deleted FROM soft_orphaned_records;

-- Update the total deleted count
v_count_deleted := v_count_deleted + (COALESCE(v_soft_count_deleted, 0));

END IF;

-- Clean up temporary table
DROP TABLE IF EXISTS temp_incremental_data;

v_end_time := clock_timestamp();
v_duration_ms := EXTRACT(EPOCH FROM (v_end_time - v_start_time)) * 1000;

UPDATE v_refresh_tracker
SET status = 'COMPLETED',
    last_refresh_start = v_start_time,
    last_refresh_end = v_end_time
WHERE view_name IN ('v_agent_refresh');

        RAISE NOTICE 'Refresh completed in % ms. Inserted: %, Updated: %, Deleted: %, temp count: %',
            v_duration_ms, v_count_inserted, v_count_updated, v_count_deleted, v_temp_count;

        -- Release lock before returning
        PERFORM pg_advisory_unlock(v_lock_key);

        -- Log completion status
        -- Build detailed JSON response
        p_result := jsonb_build_object(
                'status', v_status,
                'startTime', v_start_time,
                'endTime', v_end_time,
                'durationMs', v_duration_ms,
                'metrics', jsonb_build_object(
                        'recordsInserted', v_count_inserted,
                        'recordsUpdated', v_count_updated,
                        'recordsDeleted', v_count_deleted,
                        'tempRecordsProcessed', v_temp_count,
                        'hardOrphanedRecordsDeleted', v_hard_deleted_orphaned,
                        'softOrphanedRecordsDeleted', v_soft_deleted_orphaned,
                        'orphanedAliasRecordsDeleted', v_orphaned_alias_deleted,
                        'redundantAliasRecordsDeleted',v_redundant_alias_count
                           ),
                'counts', jsonb_build_object(
                        'agentCore', v_agent_core_count
                          )
                    )::text;

EXCEPTION
        WHEN OTHERS THEN
        -- No ROLLBACK statement needed here - PostgreSQL will automatically roll back
        -- on error when the procedure exits with an exception

        -- Update refresh tracker with error status
UPDATE v_refresh_tracker
SET status = 'ERROR',
    last_refresh_end = clock_timestamp(),
    error_message = SQLERRM
WHERE view_name IN ('v_agent_refresh');

-- Clean up temporary table
DROP TABLE IF EXISTS temp_incremental_data;

-- Handle errors
v_end_time := clock_timestamp();
        v_duration_ms := EXTRACT(EPOCH FROM (v_end_time - v_start_time)) * 1000;

        -- Release lock before returning
        PERFORM pg_advisory_unlock(v_lock_key);

        p_result := jsonb_build_object(
                'status', 'ERROR',
                'message', SQLERRM,
                'startTime', v_start_time,
                'endTime', v_end_time,
                'durationMs', v_duration_ms
                    )::text;
END;
END;
$$;
