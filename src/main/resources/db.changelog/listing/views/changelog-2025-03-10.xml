<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="sandeshabhat" id="ai-agent-registry-2025.03.10-CJ-4244-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.03.10-CJ-4244"/>
    </changeSet>

    <changeSet author="sandeshabhat" id="ai-agent-registry-2025.03.10-CJ-4244-1" runOnChange="true">

        <!-- Create v_agent_core view -->
        <createView viewName="v_agent_core" replaceIfExists="true">
            SELECT
                a.uid AS agent_id,
                a.guid AS agent_guid,
                a.external_id AS agent_external_id,
                pa.guid,
                pa.idp_account_id,
                a.is_deleted AS agent_is_deleted,
                pa.uid AS provider_account_uid,
                pa.provider_type,
                pa.provider_account_name,
                av.uid AS version_id,
                av.guid AS version_guid,
                av.name AS version_name,
                av.version_string AS version,
                av.external_id AS version_external_id,
                av.description,
                av.agent_status,
                av.trust_level,
                av.is_deleted AS version_is_deleted,
                aa.uid AS alias_id,
                aa.guid AS alias_guid,
                aa.name AS alias_name,
                aa.is_deleted AS alias_is_deleted,
                aa.external_id AS alias_external_id,
                av.modified_time,
                av.updated_at_provider_time,
                CASE WHEN aa.uid IS NOT NULL THEN TRUE ELSE FALSE END AS has_alias
            FROM ai_agent a
                     INNER JOIN ai_agent_version av ON a.uid = av.agent_uid
                     INNER JOIN ai_agent_provider_account pa ON a.provider_account_uid = pa.uid
                     LEFT JOIN ai_agent_alias aa ON av.uid = aa.agent_version_uid
                     CROSS JOIN (SELECT last_refresh_start FROM v_refresh_tracker where view_name = 'v_agent_refresh') rt
            WHERE a.modified_time >= rt.last_refresh_start
                OR av.modified_time >= rt.last_refresh_start
                OR pa.modified_time >= rt.last_refresh_start
                OR aa.modified_time >= rt.last_refresh_start
        </createView>

        <rollback>
            DROP VIEW IF EXISTS v_agent_core CASCADE;
        </rollback>

    </changeSet>
</databaseChangeLog>
