<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="sandeshabhat" id="ai-agent-registry-2025.03.10-CJ-4244-0" context="PRE">
        <tagDatabase tag="ai-agent-registry-2025.03.10-CJ-4244"/>
    </changeSet>

    <changeSet author="sandeshabhat" id="ai-agent-registry-2025.03.10-CJ-4244-1" runOnChange="true">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <not>
                <tableExists tableName="v_refresh_tracker"/>
            </not>
        </preConditions>
        <createTable tableName="v_refresh_tracker">
            <column name="id" type="SERIAL">
                <constraints primaryKey="true"/>
            </column>
            <column name="view_name" type="TEXT">
                <constraints nullable="false" unique="true" uniqueConstraintName="unique_v_name"/>
            </column>
            <column name="last_refresh_start" type="TIMESTAMP"/>
            <column name="last_refresh_end" type="TIMESTAMP"/>
            <column name="status" type="TEXT" defaultValue="IDLE">
                <constraints nullable="false"/>
            </column>
            <column name="error_message" type="TEXT"/>
        </createTable>

        <insert tableName="v_refresh_tracker">
            <column name="view_name" value="v_agent_refresh"/>
            <column name="last_refresh_start" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="last_refresh_end" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="status" value="IDLE"/>
        </insert>

        <rollback>
            DROP TABLE IF EXISTS v_refresh_tracker;
        </rollback>

    </changeSet>

    <changeSet author="sandeshabhat" id="ai-agent-registry-2025.03.10-CJ-4244-2" runOnChange="true">
        <preConditions onFail="MARK_RAN" onError="HALT">
            <not>
                <tableExists tableName="agent_flattened"/>
            </not>
        </preConditions>
        <sql>
            CREATE TABLE agent_flattened (
                                             agent_id integer,
                                             agent_guid VARCHAR(50),
                                             agent_external_id VARCHAR(2048),
                                             provider_account_uid integer,
                                             provider_type VARCHAR(50),
                                             provider_guid VARCHAR(50),
                                             idp_account_id VARCHAR(50),
                                             provider_account_name VARCHAR(255),
                                             version_id integer,
                                             version_guid VARCHAR(50),
                                             version_external_id VARCHAR(2048),
                                             version_name VARCHAR(255),
                                             version VARCHAR(50),
                                             description TEXT,
                                             agent_status VARCHAR(50),
                                             trust_level VARCHAR(50),
                                             alias_id integer NOT NULL DEFAULT 0,
                                             alias_guid VARCHAR(50),
                                             alias_external_id VARCHAR(2048),
                                             alias_name VARCHAR(255),
                                             modified_time TIMESTAMP,
                                             updated_at_provider_time TIMESTAMP,
                                             has_alias BOOLEAN,
                                             version_is_deleted BOOLEAN,
                                             alias_is_deleted BOOLEAN,
                                             agent_is_deleted BOOLEAN,
                                             PRIMARY KEY (version_id, alias_id)
            );
        </sql>

        <rollback>
            DROP TABLE IF EXISTS agent_flattened CASCADE;
        </rollback>

    </changeSet>

</databaseChangeLog>
