AWSTemplateFormatVersion: '2010-09-09'
Description: >-
  The CloudFormation template enables cross-account access between the Boomi Agent Control Tower and your AWS account, 
  to manage and monitor your Amazon Bedrock agents. The template creates an IAM role with the necessary permissions 
  to invoke Bedrock APIs and retrieve agent metadata.
  
  Additionally, it establishes sharing of CloudWatch metrics, through AWS Cloudwatch Observability Access Manager (OAM).
  This enables centralized monitoring of your Bedrock agents.
  
  The template must be deployed in your AWS account to grant secure and controlled access to the Agent Control Tower 
  to manage your Bedrock agents.

Parameters:
  ExternalId:
    Type: String
    Description: "Unique identifier to secure cross-account role assumption"
    NoEcho: true

Mappings:
  ACTVariables:
    Configuration:
      ActAccountId: "%ACT_SERVICE_ACCOUNT_ID%"
      RegistryRoleName: "%ACT_SERVICE_ROLE_NAME%"
      BedrockCustomerPolicyName: "%ACT_BEDROCK_CUSTOMER_POLICY_NAME%"
      BedrockCustomerRoleName: "%ACT_BEDROCK_CUSTOMER_ROLE_NAME%"
      OamCustomerPolicyName: "%ACT_OAM_CUSTOMER_POLICY_NAME%"
      OamCustomerRoleName: "%ACT_OAM_CUSTOMER_ROLE_NAME%"
      SyncAutoRoleName: "%ACT_SERVICE_SYNC_AUTO_ROLE_NAME%"
      SyncManualRoleName: "%ACT_SERVICE_SYNC_MANUAL_ROLE_NAME%"

Rules:
  ValidateAccount:
    RuleCondition: !Not
      - !Equals
        - !Ref AWS::AccountId
        - "%EXPECTED_CUSTOMER_ACCOUNT_ID%"
    Assertions:
      - Assert: !Equals [ "false", "true" ]
        AssertDescription: "This template must be deployed in AWS Account %EXPECTED_CUSTOMER_ACCOUNT_ID%. Current account is not allowed."
  ValidateRegion:
    RuleCondition: !Not
      - !Equals
        - !Ref AWS::Region
        - "%EXPECTED_CUSTOMER_REGION%"
    Assertions:
      - Assert: !Equals [ "false", "true" ]
        AssertDescription: "This template must be deployed in %EXPECTED_CUSTOMER_REGION% region. Current region is not allowed."

Resources:
  # IAM role that ACT service will assume to manage Bedrock agents in customer account
  BedrockCustomerRole:
    Type: AWS::IAM::Role
    Properties:
      # Role name is fully determined by ACT during template generation
      RoleName: !FindInMap [ACTVariables, Configuration, BedrockCustomerRoleName]
      Description: "Role for cross-account Bedrock access for ACT customer"
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              AWS:
                #registry service application role that will assume this role that is being created in customer's account
                - !Sub
                  - 'arn:aws:iam::${AccountId}:role/${RoleName}'
                  - AccountId: !FindInMap [ACTVariables, Configuration, ActAccountId]
                    RoleName: !FindInMap [ACTVariables, Configuration, RegistryRoleName]

                #Auto sync lambda function execution  role that will assume this role that is being created in customer's account
                - !Sub
                  - 'arn:aws:iam::${AccountId}:role/${RoleName}'
                  - AccountId: !FindInMap [ACTVariables, Configuration, ActAccountId]
                    RoleName: !FindInMap [ACTVariables, Configuration, SyncAutoRoleName]

                #Manual sync lambda function execution  role that will assume this role that is being created in customer's account
                - !Sub
                  - 'arn:aws:iam::${AccountId}:role/${RoleName}'
                  - AccountId: !FindInMap [ACTVariables, Configuration, ActAccountId]
                    RoleName: !FindInMap [ACTVariables, Configuration, SyncManualRoleName]
            Action: sts:AssumeRole
            Condition:
              StringEquals:
                sts:ExternalId: !Ref ExternalId

  # Policy granting necessary Bedrock permissions to the customer role
  BedrockAccessPolicy:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: !FindInMap [ACTVariables, Configuration, BedrockCustomerPolicyName]
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Action:
              - "bedrock:*"
              - "bedrock-runtime:*"
              - "bedrock-agent:*"
              - "bedrock-agent-runtime:*"
            Resource: "*"
      Roles:
        - !Ref BedrockCustomerRole

  # IAM role that ACT service will assume to create and delete OAM link in customer's account
  OamLinkCustomerRole:
    Type: AWS::IAM::Role
    Properties:
      # Role name is fully determined by ACT during template generation
      RoleName: !FindInMap [ACTVariables, Configuration, OamCustomerRoleName]
      Description: "Role for cross-account OAM link creation/deletion in customer's account"
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              # ACT service account role that will assume this role
              AWS: !Sub
                - 'arn:aws:iam::${AccountId}:role/${RoleName}'
                - AccountId: !FindInMap [ACTVariables, Configuration, ActAccountId ]
                  RoleName: !FindInMap [ACTVariables, Configuration, RegistryRoleName ]
            Action: sts:AssumeRole
            Condition:
              StringEquals:
                # Using the ExternalId parameter for enhanced security
                sts:ExternalId: !Ref ExternalId

  # Policy granting necessary Bedrock permissions to the customer role
  OamLinkCustomerAccessPolicy:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: !FindInMap [ACTVariables, Configuration, OamCustomerPolicyName]
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Action:
              - "oam:CreateLink"
              - "oam:UpdateLink"
              - "oam:DeleteLink"
              - "cloudwatch:Link"
            Resource: "*"
      Roles:
        - !Ref OamLinkCustomerRole

Outputs:
  # Output the role ARN for reference
  RoleArn:
    Description: "ARN of the role created in the customer's account"
    Value: !GetAtt BedrockCustomerRole.Arn
