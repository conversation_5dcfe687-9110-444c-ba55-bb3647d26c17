final_unified_providers_query AS (
    SELECT
        bin(time, <BIN_DURATION>) AS bucketTs,
        COUNT(DISTINCT ExtAgentId) as activeAgents,
        COUNT(*) AS totalInvocations,
        SUM(ModelInvocationCount) AS totalModelInvocations,
        ROUND(AVG(CAST(TotalTime AS DOUBLE)), 2) AS averageTimePerInvocation,
        SUM(InputTokenCount) + SUM (OutputTokenCount) AS totalTokens,
        SUM(InputTokenCount) AS totalInputTokens,
        SUM(OutputTokenCount) AS totalOutputTokens,
        ROUND(AVG(CAST(InputTokenCount AS DOUBLE)), 2) AS avgInputTokens,
        ROUND(AVG(CAST(OutputTokenCount AS DOUBLE)), 2) AS avgOutputTokens,
        SUM(InvocationServerErrors) AS totalInvocationServerErrors,
        SUM(InvocationClientErrors) AS totalInvocationClientErrors,
        SUM(InvocationThrottles) AS totalInvocationThrottles
    FROM
        <AI_AGENT_REGISTRY_METRICS_TABLE>
    WHERE
        <WHERE_CLAUSE_FILTERS>
        <PROVIDER_TYPE_FILTER>
    GROUP BY
        bin(time, <BIN_DURATION>)
),