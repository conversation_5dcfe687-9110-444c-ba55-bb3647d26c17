final_unified_providers_query AS (
    SELECT
        ExtAgentId,
        InputTokenCount,
        OutputTokenCount,
        TotalTime,
        InvocationServerErrors,
        InvocationClientErrors,
        ModelInvocationUnclassifiedErrors,
        ModelLatency,
        InvocationThrottles,
        1 AS Invocations
    FROM
        <AI_AGENT_REGISTRY_METRICS_TABLE>
    WHERE
        <WHERE_CLAUSE_FILTERS>
        <PROVIDER_TYPE_FILTER>
),