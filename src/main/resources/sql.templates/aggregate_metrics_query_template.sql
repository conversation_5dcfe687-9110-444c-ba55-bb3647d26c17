WITH
    <FINAL_BEDROCK_QUERY>
    <FINAL_UNIFIED_PROVIDERS_QUERY>
    <COMBINED_QUERY>
SELECT
    COUNT(DISTINCT ExtAgentId) as activeAgents,
    SUM(COALESCE(InputTokenCount, 0)) + SUM(COALESCE(OutputTokenCount, 0)) AS totalTokens,
    -- ROUND(AVG(CAST(COALESCE(TotalTime, 0) AS DOUBLE)), 2) as avgResponseTime,
    ROUND(
            SUM(CAST(COALESCE(TotalTime, 0) AS DOUBLE)) /
            GREATEST(SUM(COALESCE(Invocations, 1)), 1),
            2
    ) as avgResponseTime,
    SUM(COALESCE(InvocationServerErrors, 0)) + SUM(COALESCE(InvocationClientErrors, 0)) + SUM(COALESCE(ModelInvocationUnclassifiedErrors, 0)) AS totalErrors,
    SUM(COALESCE(Invocations, 1)) as totalInvocations,
    -- ROUND(AVG(CAST(COALESCE(ModelLatency, 0) AS DOUBLE)), 2) as avgModelLatency,
    ROUND(
            SUM(CAST(COALESCE(ModelLatency, 0) AS DOUBLE)) /
            GREATEST(SUM(COALESCE(Invocations, 1)), 1),
            2
    ) as avgModelLatency,
    -- ROUND(AVG(CAST(COALESCE(InvocationThrottles, 0) AS DOUBLE)), 2) as avgInvocationThrottles,
    ROUND(
            SUM(CAST(COALESCE(InvocationThrottles, 0) AS DOUBLE)) /
            GREATEST(SUM(COALESCE(Invocations, 1)), 1),
            2
    ) as avgInvocationThrottles,
    ROUND(
            (SUM(CASE
                     WHEN COALESCE(InvocationServerErrors, 0) = 0
                         AND COALESCE(InvocationClientErrors, 0) = 0
                         AND COALESCE(ModelInvocationUnclassifiedErrors, 0) = 0 THEN 1
                     ELSE 0
                END
             ) * 100.0 / NULLIF(COUNT(*), 0)),
            2
    ) as successRate, -- Note (by Abhay Saswade): With bedrock data being pre aggregated for multiple
    -- invocations we do not know if error count applies to
    -- all invocations or few, so we use that as a batch. That means
    -- for bedrock we consider whole row with no error as success. That
    -- also means to be successful there should not be any error in
    -- all the aggregate values for a given row (row = batch). That is
    -- also the reason why the successRate is calculated against total rows in the
    -- query and not with total invocations (as it's done with error rate)
    -- Because of this the error rate mention below will look lot bigger
    -- than the successRate. We will need to live with this until we
    -- start ingesting per invocation data in the future for bedrock.
    ROUND(
            (SUM(CASE
                     WHEN COALESCE(InvocationServerErrors, 0) > 0
                         OR COALESCE(InvocationClientErrors, 0) > 0
                         OR COALESCE(ModelInvocationUnclassifiedErrors, 0) > 0 THEN 1
                     ELSE 0
                END
             ) * 100.0 / NULLIF(COUNT(*), 0)),
            2
    ) as errorRateToIgnore, -- Note (by Abhay Saswade): read successRate comment for why to
    -- ignore this errorRate
    ROUND(CAST(
                  SUM(COALESCE(InvocationServerErrors, 0)) +
                  SUM(COALESCE(InvocationClientErrors, 0)) +
                  SUM(COALESCE(ModelInvocationUnclassifiedErrors, 0))
              AS DOUBLE)
              / SUM(COALESCE(Invocations, 1)) * 100,
          2
    ) as errorRate  -- Note (by Abhay Saswade): read successRate comment for why the error rate
-- is calculated against total invocations and not total rows in the query.
-- Also note that, since one invocation can have multiple errors the error
-- rate can go beyond 100%
FROM
    combined_query
LIMIT 1000