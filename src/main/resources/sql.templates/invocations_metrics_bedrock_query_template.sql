dimensions AS (
        SELECT
            count(*) as agg_row_count,
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId,
            ProviderType
        FROM
            <BEDROCK_TABLE>
        WHERE
            <WHERE_CLAUSE_FILTERS>
            AND ProviderType = 'AWS_BEDROCK'
            AND ExtAgentId IS NOT NULL
        GROUP BY
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId,
            ProviderType
    ),
    metric_TotalTime AS (
        SELECT
            coalesce(count(*), 0) as agg_row_count,
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId,
            min(Min) AS min,
            max(Max) AS max,
            sum(Sum) AS sum,
            sum(Count) AS invoc_count,
            max(Unit)
        FROM
            <BEDROCK_TABLE>
        WHERE
            <WHERE_CLAUSE_FILTERS>
            AND Metric = 'TotalTime'
            AND ProviderType = 'AWS_BEDROCK'
            AND ExtAgentId IS NOT NULL
        GROUP BY
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId
    ),
    base_bedrock_query AS (
        SELECT
            dim.time AS time_bucket, dim.agg_row_count AS dim_agg_row_count, dim.ExtAgentId AS ExtAgentId, dim.ExtAgentAliasId AS ExtAgentAliasId, dim.ExtModelId AS ExtModelId, dim.ProviderType AS ProviderType, dim.ProviderAccountId AS ProviderAccountId,
            mtt.agg_row_count AS mtt_agg_row_count, mtt.sum AS mtt_sum, mtt.invoc_count AS mtt_invoc_count
        FROM
            dimensions dim
                LEFT OUTER JOIN metric_TotalTime mtt ON dim.time = mtt.time AND dim.ProviderAccountId = mtt.ProviderAccountId AND dim.ExtAgentId = mtt.ExtAgentId AND dim.ExtAgentAliasId = mtt.ExtAgentAliasId AND dim.ExtModelId = mtt.ExtModelId
        -- ORDER BY 1, 2, 3
    ),
    final_bedrock_query AS (
        SELECT
            ExtAgentAliasId as aliasExternalId,
            ExtAgentId as agentExternalId,
            ProviderType as providerType,
            ProviderAccountId as providerAccountGuId,
            sum(COALESCE(mtt_invoc_count, 0)) as totalInvocations,
            ROUND(AVG(COALESCE(mtt_sum, 0) / COALESCE(mtt_invoc_count, 1)), 2) as averageTime
        FROM
            base_bedrock_query
        GROUP BY
            ExtAgentAliasId, ExtAgentId, ProviderType, ProviderAccountId
    ),