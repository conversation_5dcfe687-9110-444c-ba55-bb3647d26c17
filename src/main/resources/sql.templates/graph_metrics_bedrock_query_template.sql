dimensions AS (
    SELECT
        count(*) as agg_row_count,
        time,
        ProviderAccountId,
        ExtAgentId,
        ExtAgentAliasId,
        ExtModelId,
        ProviderType
    FROM
        <BEDROCK_TABLE>
    WHERE
        <WHERE_CLAUSE_FILTERS>
        AND ProviderType = 'AWS_BEDROCK'
    GROUP BY
        time,
        ProviderAccountId,
        ExtAgentId,
        ExtAgentAliasId,
        ExtModelId,
        ProviderType
    ),
    metric_InputTokenCount AS (
        SELECT
            coalesce(count(*), 0) as agg_row_count,
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId,
            min(Min) AS min,
            max(Max) AS max,
            sum(Sum) AS sum,
            sum(Count) AS invoc_count,
            max(Unit)
        FROM
            <BEDROCK_TABLE>
        WHERE
            <WHERE_CLAUSE_FILTERS>
            AND ProviderType = 'AWS_BEDROCK'
            AND Metric = 'InputTokenCount'
        GROUP BY
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId
    ),
    metric_OutputTokenCount AS (
        SELECT
            coalesce(count(*), 0) as agg_row_count,
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId,
            min(Min) AS min,
            max(Max) AS max,
            sum(Sum) AS sum,
            sum(Count) AS invoc_count,
            max(Unit)
        FROM
            <BEDROCK_TABLE>
        WHERE
            <WHERE_CLAUSE_FILTERS>
            AND ProviderType = 'AWS_BEDROCK'
            AND Metric = 'OutputTokenCount'
        GROUP BY
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId
    ),
    metric_InvocationClientErrors AS (
        SELECT
            coalesce(count(*), 0) as agg_row_count,
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId,
            min(Min) AS min,
            max(Max) AS max,
            sum(Sum) AS sum,
            sum(Count) AS invoc_count,
            max(Unit)
        FROM
            <BEDROCK_TABLE>
        WHERE
            <WHERE_CLAUSE_FILTERS>
            AND ProviderType = 'AWS_BEDROCK'
            AND Metric = 'InvocationClientErrors'
        GROUP BY
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId
    ),
    metric_InvocationServerErrors AS (
        SELECT
            coalesce(count(*), 0) as agg_row_count,
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId,
            min(Min) AS min,
            max(Max) AS max,
            sum(Sum) AS sum,
            sum(Count) AS invoc_count,
            max(Unit)
        FROM
            <BEDROCK_TABLE>
        WHERE
            <WHERE_CLAUSE_FILTERS>
            AND ProviderType = 'AWS_BEDROCK'
            AND Metric = 'InvocationServerErrors'
        GROUP BY
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId
    ),
    metric_InvocationThrottles AS (
        SELECT
            coalesce(count(*), 0) as agg_row_count,
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId,
            min(Min) AS min,
            max(Max) AS max,
            sum(Sum) AS sum,
            sum(Count) AS invoc_count,
            max(Unit)
        FROM
            <BEDROCK_TABLE>
        WHERE
            <WHERE_CLAUSE_FILTERS>
            AND ProviderType = 'AWS_BEDROCK'
            AND Metric = 'InvocationThrottles'
        GROUP BY
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId
    ),
    metric_ModelInvocationCount AS (
        SELECT
            coalesce(count(*), 0) as agg_row_count,
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId,
            min(Min) AS min,
            max(Max) AS max,
            sum(Sum) AS sum,
            sum(Count) AS invoc_count,
            max(Unit)
        FROM
            <BEDROCK_TABLE>
        WHERE
            <WHERE_CLAUSE_FILTERS>
            AND ProviderType = 'AWS_BEDROCK'
            AND Metric = 'ModelInvocationCount'
        GROUP BY
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId
    ),
    metric_TotalTime AS (
        SELECT
            coalesce(count(*), 0) as agg_row_count,
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId,
            min(Min) AS min,
            max(Max) AS max,
            sum(Sum) AS sum,
            sum(Count) AS invoc_count,
            max(Unit)
        FROM
            <BEDROCK_TABLE>
        WHERE
            <WHERE_CLAUSE_FILTERS>
            AND ProviderType = 'AWS_BEDROCK'
            AND Metric = 'TotalTime'
        GROUP BY
            time,
            ProviderAccountId,
            ExtAgentId,
            ExtAgentAliasId,
            ExtModelId
    ),
    base_bedrock_query AS (
        SELECT
            dim.time AS time_bucket, dim.agg_row_count AS dim_agg_row_count, dim.ExtAgentId AS ExtAgentId, dim.ExtAgentAliasId AS ExtAgentAliasId, dim.ExtModelId AS ExtModelId, dim.ProviderType AS ProviderType,
            mitc.agg_row_count AS mitc_agg_row_count, mitc.sum AS mitc_sum, mitc.invoc_count AS mitc_invoc_count,
            motc.agg_row_count AS motc_agg_row_count, motc.sum AS motc_sum, motc.invoc_count AS motc_invoc_count,
            mice.agg_row_count AS mice_agg_row_count, mice.sum AS mice_sum, mice.invoc_count AS mice_invoc_count,
            mise.agg_row_count AS mise_agg_row_count, mise.sum AS mise_sum, mise.invoc_count AS mise_invoc_count,
            mith.agg_row_count AS mith_agg_row_count, mith.sum AS mith_sum, mith.invoc_count AS mith_invoc_count,
            mmic.agg_row_count AS mmic_agg_row_count, mmic.sum AS mmic_sum, mmic.invoc_count AS mmic_invoc_count,
            mtt.agg_row_count AS mtt_agg_row_count, mtt.sum AS mtt_sum, mtt.invoc_count AS mtt_invoc_count
        FROM
            dimensions dim
                LEFT OUTER JOIN metric_InputTokenCount mitc ON dim.time = mitc.time AND dim.ProviderAccountId = mitc.ProviderAccountId AND dim.ExtAgentId = mitc.ExtAgentId AND dim.ExtAgentAliasId = mitc.ExtAgentAliasId AND dim.ExtModelId = mitc.ExtModelId
                LEFT OUTER JOIN metric_OutputTokenCount motc ON dim.time = motc.time AND dim.ProviderAccountId = motc.ProviderAccountId AND dim.ExtAgentId = motc.ExtAgentId AND dim.ExtAgentAliasId = motc.ExtAgentAliasId AND dim.ExtModelId = motc.ExtModelId
                LEFT OUTER JOIN metric_InvocationClientErrors mice ON dim.time = mice.time AND dim.ProviderAccountId = mice.ProviderAccountId AND dim.ExtAgentId = mice.ExtAgentId AND dim.ExtAgentAliasId = mice.ExtAgentAliasId AND dim.ExtModelId = mice.ExtModelId
                LEFT OUTER JOIN metric_InvocationServerErrors mise ON dim.time = mise.time AND dim.ProviderAccountId = mise.ProviderAccountId AND dim.ExtAgentId = mise.ExtAgentId AND dim.ExtAgentAliasId = mise.ExtAgentAliasId AND dim.ExtModelId = mise.ExtModelId
                LEFT OUTER JOIN metric_InvocationThrottles mith ON dim.time = mith.time AND dim.ProviderAccountId = mith.ProviderAccountId AND dim.ExtAgentId = mith.ExtAgentId AND dim.ExtAgentAliasId = mith.ExtAgentAliasId AND dim.ExtModelId = mith.ExtModelId
                LEFT OUTER JOIN metric_ModelInvocationCount mmic ON dim.time = mmic.time AND dim.ProviderAccountId = mmic.ProviderAccountId AND dim.ExtAgentId = mmic.ExtAgentId AND dim.ExtAgentAliasId = mmic.ExtAgentAliasId AND dim.ExtModelId = mmic.ExtModelId
                LEFT OUTER JOIN metric_TotalTime mtt ON dim.time = mtt.time AND dim.ProviderAccountId = mtt.ProviderAccountId AND dim.ExtAgentId = mtt.ExtAgentId AND dim.ExtAgentAliasId = mtt.ExtAgentAliasId AND dim.ExtModelId = mtt.ExtModelId
        -- ORDER BY 1, 2, 3
    ),
    final_bedrock_query AS (
        SELECT
            bin(bbq.time_bucket, <BIN_DURATION>) AS bucketTs,
            COUNT(DISTINCT bbq.ExtAgentId) as activeAgents,
            SUM(COALESCE(bbq.mtt_invoc_count, 0)) AS totalInvocations,
            SUM(COALESCE(bbq.mmic_invoc_count, 0)) AS totalModelInvocations,
            ROUND(AVG(COALESCE(bbq.mtt_sum, 0) / COALESCE(bbq.mtt_invoc_count, 1)), 2) AS averageTimePerInvocation,
            SUM(COALESCE(bbq.mitc_sum, 0)) + SUM (COALESCE(bbq.motc_sum, 0)) AS totalTokens,
            SUM(COALESCE(bbq.mitc_sum, 0)) AS totalInputTokens,
            SUM(COALESCE(bbq.motc_sum, 0)) AS totalOutputTokens,
            ROUND(AVG(COALESCE(bbq.mitc_sum, 0) / COALESCE(bbq.mitc_invoc_count, 1)), 2) AS avgInputTokens,
            ROUND(AVG(COALESCE(bbq.motc_sum, 0) / COALESCE(bbq.motc_invoc_count, 1)), 2) AS avgOutputTokens,
            SUM(COALESCE(bbq.mise_sum, 0)) AS totalInvocationServerErrors,
            SUM(COALESCE(bbq.mice_sum, 0)) AS totalInvocationClientErrors,
            SUM(COALESCE(bbq.mith_sum, 0)) AS totalInvocationThrottles
        FROM
            base_bedrock_query bbq
        GROUP BY
            bin(bbq.time_bucket, <BIN_DURATION>)
    ),