final_unified_providers_query AS (
    SELECT
        ExtAgentAliasId as aliasExternalId,
        ExtAgentId as agentExternalId,
        ProviderType as providerType,
        ProviderAccountId as providerAccountGuId,
        COUNT(*) as totalInvocations,
        ROUND(AVG(CAST(TotalTime AS DOUBLE)), 2) as averageTime
    FROM
        <AI_AGENT_REGISTRY_METRICS_TABLE>
    WHERE
        <WHERE_CLAUSE_FILTERS>
        <PROVIDER_TYPE_FILTER>
        AND ExtAgentId IS NOT NULL
    GROUP BY
        ExtAgentAliasId, ExtAgentId, ProviderType, ProviderAccountId
),