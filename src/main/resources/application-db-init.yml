server:
  port: 3037

spring:
  liquibase:
    enabled: false
  datasource:
    url: ${boomi.services.aiagentregistry.db.default.url}
    username: ${boomi.services.aiagentregistry.db.username}
    password: ${boomi.services.aiagentregistry.db.password}
    driver-class-name: software.aws.rds.jdbc.postgresql.Driver
  jpa:
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    properties:
        hibernate:
          dialect:
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.mail.MailSenderAutoConfiguration

boomi:
  services:
    liquibase:
      changelog: db.changelog/changelog-db-init.xml

