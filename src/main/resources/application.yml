spring:
  webflux:
    base-path: /service/ai-agent-registry
  application:
    name: ai-agent-registry-service
    spring-retry:
      backoffMillis: 1000
      maxAttempts: 3
      maxBackoffSeconds: 8
      jitter: 1.0
  aws:
    region: ${com.boomi.aiagentregistry.aws.region}
    secondaryregion: ${com.boomi.aiagentregistry.aws.secondary.region}

  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${boomi.services.jwks.url}
          issuer-uri: ${boomi.services.jwks.issuer}

  liquibase:
    enabled: false

  datasource:
    driver-class-name: software.aws.rds.jdbc.postgresql.Driver
    url: ${boomi.services.aiagentregistry.db.url}
    username: ${boomi.services.aiagentregistry.db.username}
    password: ${boomi.services.aiagentregistry.db.password}
  jpa:
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        type:
          descriptor:
            sql:
    show-sql: false
  mail:
    host: ${boomi.services.aiagentregistry.mail.host}
    username: ${boomi.services.aiagentregistry.mail.username}
    password: ${boomi.services.aiagentregistry.mail.password}
    properties:
      mail:
        transport:
          protocol: smtp
        smtp:
          port: ${boomi.services.aiagentregistry.mail.port}
          auth: true
          starttls:
            enable: true
            required: true

server:
  port: 3037


agent:
  refresh:
    enabled: ${boomi.services.aiagentregistry.refresh.enabled:true}

  endpoints:
    web:
      base-path: /monitor
      exposure:
        include: health,loggers
  endpoint:
    health:
      show-details: always
    loggers:
      enabled: true

boomi:
  services:
    service-name: '@project.artifactId@'
    timestream:
        max_rows: 1000
        agent_metrics_table_name: "ai_agent_registry_service_metrics"
        bedrock_metrics_table_name: "ai_agent_registry_service_bedrock_metrics"
        region: "us-east-1"
        database_name: "Metering-Shared"
        roleArn: "arn:aws:iam::************:role/SharedTimeStreamRole-Metering-Shared"
    aiagentregistry:
      garden:
        apiUrl:
        jwtUrl:
      pattern:
        externalLink:
          AGENT:
            AWS_BEDROCK: https://{REGION}.console.aws.amazon.com/bedrock/home?region={REGION}#/agents/{AGENT_ID}
            BOOMI: https://qa.boomi.com/BoomiAI.html#ai;accountId={ACCOUNT_ID}
          VERSION:
            AWS_BEDROCK: https://{REGION}.console.aws.amazon.com/bedrock/home?region={REGION}#/agents/{AGENT_ID}/versions/{AGENT_VERSION}
            BOOMI: https://qa.boomi.com/BoomiAI.html#ai;accountId={ACCOUNT_ID}
          ALIAS:
            AWS_BEDROCK: https://{REGION}.console.aws.amazon.com/bedrock/home?region={REGION}#/agents/{AGENT_ID}/alias/{AGENT_ALIAS_ID}
info:
  app:
    name: '@project.name@'
    version: '@project.version@'
    build: '@buildNumber@'

app:
  platform:
    platformUrl: http://localhost:8081
  mail:
    enabled: ${boomi.services.aiagentregistry.mail.enabled}
    registryDl: ${boomi.services.aiagentregistry.mail.dl}
    cron: ${boomi.services.aiagentregistry.scheduler.cron.expression}
management:
  endpoints:
    web:
      base-path: /monitor
      exposure:
        include: health,loggers
  metrics:
    export:
      newrelic:
        enabled: true
        log-extension:
          add-mdc: true
  endpoint:
    health:
      show-details: always
    loggers:
      enabled: true



graphql:
  security:
    allowAnonymousAccess: true
  log:
    sensitiveArgumentNames: password,secret,token,credentials,apiToken,awsAccessKeyId,awsSecretAccessKey,sessionToken

api:
  boomi:
    platform:
      url: ${boomi.services.aiagentregistry.platform.url}

sync:
  queue:
    url: ${boomi.services.aiagentregistry.sync.queue.url}
    region: ${boomi.services.aiagentregistry.sync.queue.region}

garden:
  service:
    agents-path: /api/v1/agent-garden/agents
    agent-path: /api/v1/agent-garden/agents/{agentId}
    tool-path: /api/v1/agent-garden/tools/{toolType}/{toolId}
    agent-install-path: /api/v1/agent-garden/agents/{agentId}/install
    agent-uninstall-path: /api/v1/agent-garden/agents/{agentId}/uninstall

logging:
  level:
    software.amazon.awssdk: OFF
    software.amazon.awssdk.requestId: OFF
    org.apache.http.wire: OFF
    # Show actual sql query parameters
    org.hibernate.orm.jdbc.bind: OFF
    org.hibernate.engine.jdbc.spi.SqlExceptionHelper: OFF
