// Copyright (c) 2025 Boomi, LP.
package com.boomi.aiagentregistry.util;

import graphql.schema.DataFetchingEnvironment;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.entity.AgentListingView;
import com.boomi.aiagentregistry.entity.AiAgentListing;
import com.boomi.aiagentregistry.search.AiAgentSearchCriteria;
import com.boomi.aiagentregistry.search.AiAgentSpecifications;
import com.boomi.aiagentregistry.search.AiAgentViewSpecifications;
import com.boomi.aiagentregistry.search.SearchByLLMStrategy;
import com.boomi.aiagentregistry.search.SearchByProviderNameStrategy;
import com.boomi.aiagentregistry.search.SearchByProviderTypeStrategy;
import com.boomi.aiagentregistry.search.SearchByTagStrategy;
import com.boomi.aiagentregistry.search.SearchByTrustLevelStrategy;
import com.boomi.aiagentregistry.search.SearchStrategy;
import com.boomi.aiagentregistry.search.ViewSearchByLLMStrategy;
import com.boomi.aiagentregistry.search.ViewSearchByProviderNameStrategy;
import com.boomi.aiagentregistry.search.ViewSearchByProviderTypeStrategy;
import com.boomi.aiagentregistry.search.ViewSearchByTagStrategy;
import com.boomi.aiagentregistry.search.ViewSearchByTrustLevelStrategy;
import com.boomi.graphql.server.schema.types.AiAgentFilterInput;
import com.boomi.graphql.server.schema.types.AiAgentListingFilterCondition;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryInput;
import com.boomi.util.StringUtil;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;

import static com.boomi.aiagentregistry.util.ValidationUtil.isInputFieldPresent;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PAGE_INDEX_INVALID;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PAGE_SIZE_INVALID;
import static com.boomi.graphql.server.schema.types.AiAgentsListingQueryInput.PROP_PROVIDER_ACCOUNT_IDS;
import static com.boomi.graphql.server.schema.types.AiAgentsListingQueryInput.PROP_SEARCH_INPUT;

@Service
@Slf4j
public class SearchUtil {

    private final UserUtil _userUtil;

    public SearchUtil(UserUtil userUtil) {
        _userUtil = userUtil;
    }
public List<SearchStrategy> getFilterStrategies(AiAgentsListingQueryInput searchInput) {

    if (searchInput == null || searchInput.getAgentListingFilter() == null ||
            searchInput.getAgentListingFilter().getFilterInput() == null) {
        return Collections.emptyList();
    }

    List<SearchStrategy> searchStrategyList = new ArrayList<>();
    AiAgentFilterInput filterInput = searchInput.getAgentListingFilter().getFilterInput();

    // Add strategies based on non-null filters collections
    addStrategyIfNotEmpty(filterInput.getAccounts(), searchStrategyList,
            SearchByProviderNameStrategy::new);
    addStrategyIfNotEmpty(filterInput.getTags(), searchStrategyList,
            SearchByTagStrategy::new);
    addStrategyIfNotEmpty(filterInput.getModels(), searchStrategyList,
            SearchByLLMStrategy::new);
    addStrategyIfNotEmpty(filterInput.getProviders(), searchStrategyList,
            SearchByProviderTypeStrategy::new);
    addStrategyIfNotEmpty(filterInput.getTrustLevel(), searchStrategyList,
            SearchByTrustLevelStrategy::new);

    return searchStrategyList;
}


    public List<SearchStrategy> getViewFilterStrategies(AiAgentsListingQueryInput searchInput) {

        if (searchInput == null || searchInput.getAgentListingFilter() == null ||
                searchInput.getAgentListingFilter().getFilterInput() == null) {
            return Collections.emptyList();
        }

        List<SearchStrategy> viewSearchStrategyList = new ArrayList<>();
        AiAgentFilterInput filterInput = searchInput.getAgentListingFilter().getFilterInput();

        // Add strategies based on non-null filters collections
        addStrategyIfNotEmpty(filterInput.getAccounts(), viewSearchStrategyList,
                ViewSearchByProviderNameStrategy::new);
        addStrategyIfNotEmpty(filterInput.getTags(), viewSearchStrategyList,
                ViewSearchByTagStrategy::new);
        addStrategyIfNotEmpty(filterInput.getModels(), viewSearchStrategyList,
                ViewSearchByLLMStrategy::new);
        addStrategyIfNotEmpty(filterInput.getProviders(), viewSearchStrategyList,
                ViewSearchByProviderTypeStrategy::new);
        addStrategyIfNotEmpty(filterInput.getTrustLevel(), viewSearchStrategyList,
                ViewSearchByTrustLevelStrategy::new);

        return viewSearchStrategyList;
    }

    /**
     * Helper method to add a strategy if the collection is not empty
     */
    private static <T> void addStrategyIfNotEmpty(Collection<T> collection,
            List<SearchStrategy> strategies,
            Supplier<SearchStrategy> strategySupplier) {
        if (collection != null && !collection.isEmpty()) {
            strategies.add(strategySupplier.get());
        }
    }


    public Specification<AiAgentListing> buildSpecification(List<SearchStrategy> searchStrategyList,
            String condition, AiAgentsListingQueryInput input, DataFetchingEnvironment dfe) {

        // Create search criteria
        AiAgentSearchCriteria searchCriteria = buildSearchCriteria(input, dfe);

        //  case when no filters
        if (searchStrategyList == null || searchStrategyList.isEmpty()) {
            // Return a specification that matches all records that match searchCriteria
            return AiAgentSpecifications.createSpecification(searchCriteria);
        }
        // Initialize the final specification with the first strategy in the filter list and add searchCriteria
        Specification<AiAgentListing> finalSpec = searchStrategyList.get(0).createSpecification(input);

        // Process remaining specifications if any exist
        // Skip first element as it's already processed

        if (searchStrategyList.size() > 1) {
            Specification<AiAgentListing> result = finalSpec;

            // Process all strategies except the first one
            for (int i = 1; i < searchStrategyList.size(); i++) {
                SearchStrategy currentStrategy = searchStrategyList.get(i);
                Specification<AiAgentListing> currentSpec = currentStrategy.createSpecification(input);
                log.info(" Combine specifications based on condition currentStrategy "+currentStrategy.toString());
                // Combine specifications based on condition
                Specification<AiAgentListing> combinedSpec = combineSpecifications(result, currentSpec, condition);
                // Add search criteria specification
                result = combinedSpec.and(AiAgentSpecifications.createSpecification(searchCriteria));
            }
            log.info(" returning result {} ", result);
            return result;
        }
        else {
            finalSpec=finalSpec.and(AiAgentSpecifications.createSpecification(searchCriteria));
        }
        log.info("returning finalSpec {}", finalSpec);
        return finalSpec;
    }


    public Specification<AgentListingView> buildViewSpecification(List<SearchStrategy> searchStrategyList,
            String condition, AiAgentsListingQueryInput input, DataFetchingEnvironment dfe) {

        // Create search criteria
        AiAgentSearchCriteria searchCriteria = buildSearchCriteria(input, dfe);

        //  case when no filters
        if (searchStrategyList == null || searchStrategyList.isEmpty()) {
            // Return a specification that matches all records that match searchCriteria
            return AiAgentViewSpecifications.createSpecification(searchCriteria);
        }
        // Initialize the final specification with the first strategy in the filter list and add searchCriteria
        Specification<AgentListingView> finalSpec = searchStrategyList.get(0).createViewSpecification(input);

        // Process remaining specifications if any exist
        // Skip first element as it's already processed

        if (searchStrategyList.size() > 1) {
            Specification<AgentListingView> result = finalSpec;

            // Process all strategies except the first one
            for (int i = 1; i < searchStrategyList.size(); i++) {
                SearchStrategy currentStrategy = searchStrategyList.get(i);
                Specification<AgentListingView> currentSpec = currentStrategy.createViewSpecification(input);
                log.info(" Combine specifications based on condition currentStrategy "+currentStrategy.toString());
                // Combine specifications based on condition
                Specification<AgentListingView> combinedSpec = combineViewSpecifications
                        (result, currentSpec, condition);
                // Add search criteria specification
                result = combinedSpec.and(AiAgentViewSpecifications.createSpecification(searchCriteria));
            }
            log.info(" returning result {} ", result);
            return result;
        }
        else {
            finalSpec=finalSpec.and(AiAgentViewSpecifications.createSpecification(searchCriteria));
        }
        log.info("returning finalSpec {}", finalSpec);
        return finalSpec;
    }

    private Specification<AgentListingView> combineViewSpecifications(
            Specification<AgentListingView> spec,
            Specification<AgentListingView> currentSpec,
            String condition) {

        if (AiAgentListingFilterCondition.AND.name().equals(condition)) {
            log.info("Adding Search filter with AND");
            return spec.and(currentSpec);
        } else if (AiAgentListingFilterCondition.OR.name().equals(condition)) {
            log.info("Adding Search filter with OR");
            return spec.or(currentSpec);
        }
        return spec;
    }

    private  AiAgentSearchCriteria buildSearchCriteria(AiAgentsListingQueryInput input,
            DataFetchingEnvironment dfe) {
        // if searchInput and providerAccountIds not present then only idpAccountId criteria will be added
        return AiAgentSearchCriteria.builder()
                .providerAccountIds(input.getProviderAccountIds())
                .searchInput(input.getSearchInput())
                .idpAccountId(_userUtil.getAccountId(dfe))
                .build();
    }

    private Specification<AiAgentListing> combineSpecifications(
            Specification<AiAgentListing> spec,
            Specification<AiAgentListing> currentSpec,
            String condition) {

        if (AiAgentListingFilterCondition.AND.name().equals(condition)) {
            log.info("Adding Search filter with AND");
            return spec.and(currentSpec);
        } else if (AiAgentListingFilterCondition.OR.name().equals(condition)) {
            log.info("Adding Search filter with OR");
            return spec.or(currentSpec);
        }
        return spec;
    }

    public  boolean validateInput(AiAgentsListingQueryInput input,
            DataFetchingEnvironment dfe) {
        // Validate all provider account IDs
        boolean isValidAccount = false;
    log.debug(" Validating AiAgentsListingQueryInput");

        if(isInputFieldPresent(dfe, PROP_SEARCH_INPUT) && StringUtil.isBlank(input.getSearchInput()))
        {
            return false;
        }
        if(isInputFieldPresent(dfe, PROP_PROVIDER_ACCOUNT_IDS) && !input.getProviderAccountIds().isEmpty()) {

            isValidAccount = input.getProviderAccountIds().stream()
                    .allMatch(providerAccountId ->
                            ValidationUtil.validateProviderAccountInputId(providerAccountId, dfe));

            if (!isValidAccount) {
                log.debug(" invalid AiAgentsListingQueryInput");
                return false;
            }
        }
        return true;
    }
}
