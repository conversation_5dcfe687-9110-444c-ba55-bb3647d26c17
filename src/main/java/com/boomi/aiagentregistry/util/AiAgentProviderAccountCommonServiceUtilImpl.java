// Copyright (c) 2024 Boomi, LP

package com.boomi.aiagentregistry.util;

import com.boomi.aiagentregistry.aop.IdpAccountFilter;
import com.boomi.aiagentregistry.constant.ActionEnum;
import com.boomi.aiagentregistry.entity.AgentEntitySyncLatest;
import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.service.FeatureManager;
import com.boomi.aiagentregistry.service.SecretsManagerService;
import com.boomi.aiagentregistry.service.auth.AuthorizationParsingService;
import com.boomi.aiagentregistry.service.sqs.AwsSqsService;
import com.boomi.aiagentregistry.service.sync.SyncHistoryService;
import com.boomi.aiagentregistry.service.sync.model.BedrockSyncContext;
import com.boomi.aiagentregistry.service.sync.model.GardenSyncContext;
import com.boomi.aiagentregistry.service.sync.model.SyncContext;
import com.boomi.aiagentregistry.service.sync.model.SyncEntityPointInTimeInfo;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccount;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountUpdateInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus;
import com.boomi.graphql.server.servlet.ErrorUtil;
import com.boomi.util.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import graphql.schema.DataFetchingEnvironment;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;
import software.amazon.awssdk.services.secretsmanager.model.SecretsManagerResponse;
import software.amazon.awssdk.services.secretsmanager.model.UpdateSecretResponse;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.AGENT_PROVIDER_DESCRIPTION_LENGTH;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.AI_AGENT_PROVIDER_NAME_LENGTH;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.DELIMITER;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.SECRET_FOUND;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ACCOUNT_NAME_TOO_LONG_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.DUPLICATE_PROVIDER_ACCOUNT_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.EMPTY_EXTERNAL_ACCOUNT_ID;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_ACCOUNT_NAME_EMPTY;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_EMPTY_CREDENTIALS_INPUT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_INVALID_CREDENTIALS_INPUT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_INVALID_CREDENTIALS_KEY;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_INVALID_METADATA_JSON;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_INVALID_REGISTRY_ACCOUNT_STATUS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_ON_ADDING_PROVIDER_ACCOUNT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_REGISTRY_ACCOUNT_ID_EMPTY;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_SSM_UPDATE_FAILED;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_VALIDATING_CONNECTION;
import static com.boomi.aiagentregistry.constant.ErrorMessages.IMMUTABLE_FIELD_VALIDATION_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INVALID_CREDENTIALS_KEYS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INVALID_PROVIDER_TYPE_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.PROVIDER_ACCOUNT_DESCRIPTION_TOO_LONG_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.SYNC_FAILED_AFTER_CREATING_PROVIDER;
import static com.boomi.aiagentregistry.constant.FieldConstant.FIELD_CREDENTIALS;
import static com.boomi.aiagentregistry.mapper.AIAgentProviderAccountMapper.PROVIDER_MAPPER;
import static com.boomi.aiagentregistry.util.ValidationUtil.isInputFieldPresent;
import static com.boomi.graphql.server.schema.types.AiAgentProviderAccount.PROP_METADATA_JSON;
import static com.boomi.graphql.server.schema.types.AiAgentProviderAccount.PROP_PROVIDER_ACCOUNT_DESCRIPTION;
import static com.boomi.graphql.server.schema.types.AiAgentProviderAccount.PROP_PROVIDER_ACCOUNT_NAME;
import static com.boomi.graphql.server.schema.types.AiAgentProviderAccount.PROP_PROVIDER_ACCOUNT_STATUS;
import static com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus.DISABLED;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.ACCOUNT_NOT_FOUND;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.AUTHENTICATED_ACCOUNT_ID_MISSING;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.DIFFERENT_EXTERNAL_PROVIDER_ACCOUNT_ID;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_CONNECTION_DETAILS;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_CREDENTIALS_INPUT;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_CREDENTIALS_KEY;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_PROVIDER_TYPE;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_DESCRIPTION_TOO_LONG;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_DUPLICATE_NAME_CONSTRAINT;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_SYNC_IN_PROGRESS;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.REGION_REQUIRED_IF_AUTH;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.SSM_UPDATE_FAILED;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.SYNC_PROVIDER_ACCOUNT_ERROR;
import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.COMPLETED;
import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.FAILED;
import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.IN_PROGRESS;
import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.IN_QUEUE;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.PROVIDER_ACCOUNT;
import static com.boomi.graphql.server.servlet.ErrorUtil.GeneralErrorCode.SYSTEM_ERROR;

@Service
@Slf4j
public class AiAgentProviderAccountCommonServiceUtilImpl implements AiAgentProviderAccountCommonServiceUtil {

    @Autowired
    @Lazy
    AiAgentProviderAccountCommonServiceUtilImpl self;

    @Autowired
    private UserUtil _userUtil;

    @Autowired
    private GeneralUtil _generalUtil;

    @Autowired
    private AuthorizationParsingService _authParseService;

    @Autowired
    private AiAgentProviderAccountRepository _accountRepo;

    @Autowired
    private AuditUtil _auditUtil;

    @Autowired
    private SecretsManagerService _secretsManagerService;

    @Autowired
    private FeatureManager _featureManager;

    @Autowired
    private SyncHistoryService _syncHistory;


    @Autowired
    private  AwsSqsService _sqsService;

    @Autowired
    private SqsMessageBuilder _messageBuilder;

    // class specific constants
    public static final String VALIDATING_KEYS = "Validating If all keys are present {}";
    public static final String VALIDATING_HEALTH_CHECK = "Validating the connection Health check for provider type {}.";
    private static final String DUPLICATE_PROVIDER_ACCOUNT_CHECK_MESSAGE_WITH_REGION =
            "Checking for duplicate account idpAccountId = {}, provider type = {}, externalProviderAccountId = {},"
                    + " region = {}";
    private static final String DUPLICATE_PROVIDER_ACCOUNT_CHECK_MESSAGE =
            "Checking for duplicate account idpAccountId = {}, provider type = {}, externalProviderAccountId = {}";
    private static final String DUPLICATE_ONE_EXTERNAL_ACCOUNT_WITH_ONE_IDP_ONE_AUTH_CHECK =
            "Checking for constraint of one external account with one idpAccountId and auth type. idpAccountId = {}, "
                    + "authSchema = {}, " + "externalProviderAccountId = {} " + "region = {}";
    private static final long SYNC_SPAN_TEN_MINUTES = 10;
    private static final long SYNC_SPAN_ONE_MINUTE = 1;
    // 10 minutes
    private static final long SYNC_SPAN_IN_PROGRESS_MILLIS = SYNC_SPAN_TEN_MINUTES * 60 * 1000;
    // 1 minute
    private static final long SYNC_SPAN_DEFAULT_MILLIS = SYNC_SPAN_ONE_MINUTE * 60 * 1000;
    public static final String ERROR_WHILE_SYNCING_PROVIDER_ACCOUNT = "Error while syncing provider account: {}";
    public static final String ACCOUNT_NOT_FOUND_FOR_ID = "Account not found for id: {}";
    private static final String VALIDATING_EXTERNAL_PROVIDER_ACCOUNT_ID = "validating external provider account Id {}";
    private static final String VALIDATING_IMMUTABLE_FIELDS = "Validating immutable fields {}";
    private static final String IMMUTABLE_FIELDS_HAVE_CHANGED =
            "Immutable fields have changed for account id {}. Errors: {}";
    private static final String UPDATING_PROVIDER = "Updating provider {}";

    @Override
    public boolean isValidProviderType(AiAgentProviderType aiAgentProviderType, DataFetchingEnvironment dfe) {
        if (aiAgentProviderType == null) {
            ErrorUtil.addError(dfe, INVALID_PROVIDER_TYPE);
            log.warn(INVALID_PROVIDER_TYPE_ERROR);
            return false;
        }
        return true;
    }

    @Override
    public String getIdpAccountId(DataFetchingEnvironment dfe) {
        return _userUtil.getAccountId(dfe);
    }

    @Override
    public boolean validateAiAgentProviderAccountCreateInput(AiAgentProviderAccountCreateInput input,
            String idpAccountId, AiProviderAuthSchema inputAuthSchema, DataFetchingEnvironment dfe) {
        log.info("Validating AiAgentProviderAccountCreateInput");

        if (input.getCredentials() == null || !_generalUtil.isValidJson(input.getCredentials(), true)) {
            ErrorUtil.addError(dfe, INVALID_CREDENTIALS_INPUT);
            log.warn(ERROR_INVALID_CREDENTIALS_INPUT);
        } else {
            log.info(VALIDATING_KEYS, idpAccountId);
            boolean validationCompletion = _authParseService.validateCredentialFormat(input.getCredentials(),
                    inputAuthSchema);
            if (!validationCompletion) {
                ErrorUtil.addError(dfe, INVALID_CREDENTIALS_KEY);
                log.warn(ERROR_INVALID_CREDENTIALS_KEY);
            } else {
                log.info("Checking for duplicate account name for provider type {}", input.getProviderType().name());
                final boolean isProviderAccountExists =
                        _accountRepo.existsByIdpAccountIdAndProviderAccountNameAndIsDeletedFalse(idpAccountId,
                                input.getProviderAccountName());
                if (isProviderAccountExists) {
                    log.warn(PROVIDER_ACCOUNT_DUPLICATE_NAME_CONSTRAINT.name());
                    ErrorUtil.addError(dfe, PROVIDER_ACCOUNT_DUPLICATE_NAME_CONSTRAINT);
                }
            }
        }

        String region = input.getRegion();

        if (validateRegionIsPresentIfAuthSchema(region, inputAuthSchema, dfe)) {
            log.info(VALIDATING_HEALTH_CHECK, input.getProviderType().name());
            final boolean isValidConnection = _authParseService.validateConnection(input.getCredentials(),
                    inputAuthSchema);
            if (!isValidConnection) {
                log.warn(ERROR_VALIDATING_CONNECTION);
                ErrorUtil.addError(dfe, INVALID_CONNECTION_DETAILS);
            }
        }

        if (ErrorUtil.hasErrors(dfe)) {
            return false;
        }
        return true;
    }

    @Override
    @Transactional
    @IdpAccountFilter
    public CompletionStage<AiRegistryEntitySyncStatus> syncProviderAccount(
            String id,
            DataFetchingEnvironment dfe) {
        return _accountRepo.findByGuidAndIsDeletedFalseAndProviderAccountStatusNotIn(id, Set.of(DISABLED))
                .map(existingAccount -> handleExistingAccount(existingAccount, dfe))
                .orElseGet(() -> handleAccountNotFound(id, dfe));
    }

    private CompletableFuture<AiRegistryEntitySyncStatus> handleExistingAccount(
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingAccount,
            DataFetchingEnvironment dfe) {

        if (syncInProgress(existingAccount)) {
            AgentEntitySyncLatest syncLatest = existingAccount.getLatestSync();
            long syncSpan;

            if(syncLatest != null &&
               (syncLatest.getSyncStatus()== IN_PROGRESS || syncLatest.getSyncStatus() == IN_QUEUE)) {
                syncSpan = SYNC_SPAN_TEN_MINUTES;
            } else {
                syncSpan = SYNC_SPAN_ONE_MINUTE;
            }
            log.warn(PROVIDER_ACCOUNT_SYNC_IN_PROGRESS.name(), syncSpan);
            ErrorUtil.addError(dfe, PROVIDER_ACCOUNT_SYNC_IN_PROGRESS, syncSpan);
            return CompletableFuture.completedFuture(FAILED);
        }

        updateAuditInformation(existingAccount, dfe);

        return createSyncUserAudit(existingAccount)
                .flatMap(syncUserAudit -> processSyncMessage(existingAccount, syncUserAudit, dfe))
                .onErrorResume(error -> handleSyncError(error, existingAccount.getGuid(), dfe))
                .toFuture();
    }

    private void updateAuditInformation(
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingAccount,
            DataFetchingEnvironment dfe) {
        existingAccount.setSyncAuditActionType(ActionEnum.SYNC);
        existingAccount.setSyncAuditUserId(_userUtil.getUserName(dfe));
        existingAccount.setSyncAuditIdpAccountId(_userUtil.getAccountId(dfe));
    }

    private Mono<AiRegistryEntitySyncStatus> processSyncMessage(
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingAccount,
            SyncUserAudit syncUserAudit,
            DataFetchingEnvironment dfe) {

        SendMessageRequest sqsMessage;

        try {
            sqsMessage = _messageBuilder.createMessage(existingAccount, syncUserAudit);
        } catch (JsonProcessingException e) {
            log.error("Failed to create sync message payload: {}", e.getMessage(), e);
            return handleSyncFailure(
                    syncUserAudit,
                    "Failed to parse account info into message",
                    dfe);
        }

        try {
            _sqsService.sendMessage(sqsMessage);
            return Mono.just(COMPLETED);
        } catch (Exception e) {
            log.error("Failed to send sync message to SQS: {}", e.getMessage(), e);
            return handleSyncFailure(
                    syncUserAudit,
                    "Failed to send message to SQS queue",
                    dfe);
        }
    }


    @Override
    public boolean syncInProgress(com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingAccount) {
        AgentEntitySyncLatest syncLatest = existingAccount.getLatestSync();
        if (syncLatest == null) {
            return false;
        }

        long currentTime = System.currentTimeMillis();
        long syncStartTime = syncLatest.getSyncStartDate().getTime();
        AiRegistryEntitySyncStatus status = syncLatest.getSyncStatus();

        if (status == null) {
            return false;
        }

        if (status == IN_PROGRESS ||  status == IN_QUEUE) {
            return (currentTime - syncStartTime) < SYNC_SPAN_IN_PROGRESS_MILLIS;
        }
        return (currentTime - syncStartTime) < SYNC_SPAN_DEFAULT_MILLIS;
    }


    @Override
    public com.boomi.aiagentregistry.entity.AiAgentProviderAccount getExistingProviderAccountForId(
            String providerAccountId) {
        return _accountRepo.findByGuid(providerAccountId).orElse(null);
    }

    @Override
    @IdpAccountFilter
    public com.boomi.aiagentregistry.entity.AiAgentProviderAccount getExistingProviderAccountForIdAndStatus(String id,
            Set<AiAgentProviderAccountStatus> status) {
        return _accountRepo
                .findByGuidAndIsDeletedFalseAndProviderAccountStatusNotIn(id, status)
                .orElse(null);
    }

    @Override
    public CompletionStage<AiAgentProviderAccount> createAiAgentProviderAccount(AiAgentProviderAccountCreateInput input,
            String idpAccountId, AiProviderAuthSchema inputAuthSchema, DataFetchingEnvironment dfe) {
        log.info("Creating provider account with name {} and type {}", input.getProviderAccountName(),
                input.getProviderType().name());

        CompletableFuture<AiAgentProviderAccount> completion = new CompletableFuture<>();

        return _authParseService.fetchAccountId(input.getCredentials(), inputAuthSchema).toFuture().thenCompose(
                externalProviderAccountId -> {
                    log.info("External provider account id {}", externalProviderAccountId);
                    if (StringUtils.isBlank(externalProviderAccountId)) {
                        log.warn(EMPTY_EXTERNAL_ACCOUNT_ID);
                        ErrorUtil.addError(dfe, INVALID_CONNECTION_DETAILS);
                        return CompletableFuture.completedFuture(null);
                    }
                    return CompletableFuture.supplyAsync(() -> {
                        if (_featureManager.isOneExternalAccountWithOneIdpAccountOneAuthType() &&
                             _authParseService.checkIsOneExternalAccountWithOneIdpAccountOneAuthType(inputAuthSchema)) {
                            return isProviderAccountExistsForOneExternalIdWithOneIdpOneAuthCheck(
                                    externalProviderAccountId, idpAccountId, inputAuthSchema, input.getRegion());
                        }
                        if (_authParseService.requiresRegionCheck(inputAuthSchema)) {
                            log.info(DUPLICATE_PROVIDER_ACCOUNT_CHECK_MESSAGE_WITH_REGION, idpAccountId,
                                    input.getProviderType(), externalProviderAccountId, input.getRegion());

                            return _accountRepo.existsByIdpAccountIdAndProviderTypeAndExternalProviderAccountIdAndRegionAndIsDeletedFalse(
                                    idpAccountId, input.getProviderType(), externalProviderAccountId,
                                    input.getRegion());
                        } else {
                            log.info(DUPLICATE_PROVIDER_ACCOUNT_CHECK_MESSAGE, idpAccountId, input.getProviderType(),
                                    externalProviderAccountId);

                            return _accountRepo.existsByIdpAccountIdAndProviderTypeAndExternalProviderAccountIdAndIsDeletedFalse(
                                    idpAccountId, input.getProviderType(), externalProviderAccountId);
                        }
                    }).thenCompose(exists -> {
                        if (Boolean.TRUE.equals(exists)) {
                            log.warn(PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT.name());
                            ErrorUtil.addError(dfe, PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT);
                            completion.complete(null);
                            return completion;
                        }

                        return createProviderAccount(input, completion, externalProviderAccountId, idpAccountId,
                                dfe).thenCompose(createdProvider -> handleAfterProviderCreation(createdProvider, dfe));
                    });
                }).exceptionally(error -> {
            log.error(INVALID_CREDENTIALS_KEYS, error);
            ErrorUtil.addError(dfe, INVALID_CONNECTION_DETAILS);
            completion.complete(null);
            return null;
        });
    }

    @Override
    public CompletableFuture<com.boomi.graphql.server.schema.types.AiAgentProviderAccount> updateAiAgentProviderAccount(
            AiAgentProviderAccountUpdateInput providerAccountInput,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            CompletableFuture<com.boomi.graphql.server.schema.types.AiAgentProviderAccount> completion,
            DataFetchingEnvironment dfe) {

        log.info(UPDATING_PROVIDER, existingProviderAccount.getGuid());
        populateProviderAccountForUpdateInputs(providerAccountInput, existingProviderAccount, dfe);
        _auditUtil.setUpdatedAudit(existingProviderAccount, dfe);
        final com.boomi.aiagentregistry.entity.AiAgentProviderAccount updatedAiAgentProviderAccount =
                _accountRepo.saveAndFlush(existingProviderAccount);

        // this will not applicable for CUSTOM provider
        if (!existingProviderAccount.getProviderType().name().equals(AiAgentProviderType.CUSTOM.name())
                && StringUtils.isNotBlank(providerAccountInput.getCredentials())) {
            String secretName = validateSSMUpdate(providerAccountInput, providerAccountInput.getCredentials(), dfe);
            if (secretName == null) {
                log.warn(ERROR_SSM_UPDATE_FAILED);
                ErrorUtil.addError(dfe, SSM_UPDATE_FAILED);
                completion.complete(null);
                return completion;
            }
        }
        // because of the reactive chain the hibernate session is lost, and it gave LazyInitialization
        // error for the agents list
        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentProvider =
                PROVIDER_MAPPER.toAiAgentProviderAccountWithAgents(updatedAiAgentProviderAccount,
                        Collections.emptyList());
        completion.complete(aiAgentProvider);
        return completion;
    }

    @Override
    public void validateProvideAccountDescription(String description, DataFetchingEnvironment dfe) {

        if (isInputFieldPresent(dfe, PROP_PROVIDER_ACCOUNT_DESCRIPTION)
                && description.length() > AGENT_PROVIDER_DESCRIPTION_LENGTH) {
            ErrorUtil.addError(dfe, PROVIDER_ACCOUNT_DESCRIPTION_TOO_LONG, description);
            log.warn(PROVIDER_ACCOUNT_DESCRIPTION_TOO_LONG_ERROR, description);
        }
    }


    private boolean validateRegionIsPresentIfAuthSchema(String inputRegion, AiProviderAuthSchema inputAuthSchema,
            DataFetchingEnvironment dfe) {
        if (_authParseService.requiresRegionCheck(inputAuthSchema) && StringUtils.isBlank(inputRegion)) {
            ErrorUtil.addError(dfe, REGION_REQUIRED_IF_AUTH, inputAuthSchema);
            return false;
        }
        return true;
    }

    @Override
    public CompletionStage<com.boomi.graphql.server.schema.types.AiAgentProviderAccount> createProviderAccount(
            AiAgentProviderAccountCreateInput input,
            CompletableFuture<com.boomi.graphql.server.schema.types.AiAgentProviderAccount> completion,
            String externalAccountId, String idpAccountId, DataFetchingEnvironment dfe) {
        String secretName = StringUtil.EMPTY_STRING;
        try {
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount providerAccount =
                    PROVIDER_MAPPER.toAiAgentProviderAccountEntity(input);

            String guid = GuidUtil.createAIAgentProviderGuid();

            // to avoid different method for Custom , AWS and Boomi used if condition
            if (input.getProviderType() == AiAgentProviderType.AWS_BEDROCK
                    || input.getProviderType() == AiAgentProviderType.BOOMI) {

                secretName = idpAccountId.concat(DELIMITER.concat(guid));
                SecretsManagerResponse createSecret = _secretsManagerService.createSecret(secretName,
                        input.getCredentials());
                if (createSecret == null) {
                    log.error(ERROR_SSM_UPDATE_FAILED);
                    ErrorUtil.addError(dfe, SSM_UPDATE_FAILED);
                    completion.complete(null);
                    return completion;
                }
                log.info("SSM Credentials Updated for Provider Account name: {}", input.getProviderAccountName());

                log.info("Saving Providers Details into the Database for provider type {}",
                        input.getProviderType().name());

                providerAccount.setProviderAccountStatus(AiAgentProviderAccountStatus.CONNECTED);
                providerAccount.setCredentialsKey(secretName);
            } else if (input.getProviderType() == AiAgentProviderType.CUSTOM) {
                log.info("Saving Providers Details into the Database for provider type CUSTOM");
                providerAccount.setProviderAccountDescription(input.getProviderAccountDescription());
            }

            providerAccount.setExternalProviderAccountId(externalAccountId);
            providerAccount.setGuid(guid);
            providerAccount.setIdpAccountId(idpAccountId);
            _auditUtil.setCreatedAudit(providerAccount, dfe);
            _auditUtil.setUpdatedAudit(providerAccount, dfe);
            providerAccount = _accountRepo.save(providerAccount);
            com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentProvider =
                    PROVIDER_MAPPER.toAiAgentProviderAccount(providerAccount);
            completion.complete(aiAgentProvider);
        } catch (DataIntegrityViolationException e) {
            log.warn(DUPLICATE_PROVIDER_ACCOUNT_ERROR, e.getMessage(), e);
            ErrorUtil.addError(dfe, PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT);
            deleteSecretOnProviderAccountCreateError(secretName);
            completion.complete(null);
            return completion;
        } catch (Exception e) {
            ErrorUtil.addError(dfe, SYSTEM_ERROR);
            log.error(ERROR_ON_ADDING_PROVIDER_ACCOUNT, e.getMessage(), e);
            deleteSecretOnProviderAccountCreateError(secretName);
            completion.complete(null);
            return completion;
        }
        return completion;
    }

    @Override
    public CompletableFuture<com.boomi.graphql.server.schema.types.AiAgentProviderAccount> handleAfterProviderCreation(
            com.boomi.graphql.server.schema.types.AiAgentProviderAccount createdProviderAccount,
            DataFetchingEnvironment dfe) {
        if (createdProviderAccount == null) {
            return CompletableFuture.completedFuture(null);
        }
        triggerAsyncSync(createdProviderAccount, dfe);
        return CompletableFuture.completedFuture(createdProviderAccount);
    }

    private void triggerAsyncSync(com.boomi.graphql.server.schema.types.AiAgentProviderAccount providerAccount,
            DataFetchingEnvironment dfe) {
        log.info("triggering the sync asynchronously right after the provider account {} is created",
                providerAccount.getId());

        CompletableFuture.runAsync(() -> {
            self.syncProviderAccount(providerAccount.getId(), dfe).handle((syncResult, throwable) -> {
                if (throwable != null) {
                    log.warn(SYNC_FAILED_AFTER_CREATING_PROVIDER, providerAccount.getId(),
                            throwable);
                } else {
                    log.info("provider account {} sync is completed", providerAccount.getId());
                }
                return syncResult;
            });
        });
    }

    private Mono<AiRegistryEntitySyncStatus> handleSyncFailure(SyncUserAudit syncUserAudit, String error,
            DataFetchingEnvironment dfe) {
        syncUserAudit.setSyncStatus(FAILED);
        ErrorUtil.addError(dfe, SYNC_PROVIDER_ACCOUNT_ERROR, error);
        return _syncHistory.completeSyncFailure(syncUserAudit, error)
                .map(updatedAudit -> FAILED);
    }

    private Mono<AiRegistryEntitySyncStatus> handleSyncError(Throwable error, String id, DataFetchingEnvironment dfe) {
        log.warn(ERROR_WHILE_SYNCING_PROVIDER_ACCOUNT, id, error);
        ErrorUtil.addError(dfe, SYNC_PROVIDER_ACCOUNT_ERROR, id);
        return Mono.just(FAILED);
    }

    private CompletableFuture<AiRegistryEntitySyncStatus> handleAccountNotFound(String id,
            DataFetchingEnvironment dfe) {
        log.warn(ACCOUNT_NOT_FOUND_FOR_ID, id);
        ErrorUtil.addError(dfe, ACCOUNT_NOT_FOUND, id);
        return CompletableFuture.completedFuture(FAILED);
    }

    private void deleteSecretOnProviderAccountCreateError(String secretName) {
        int isProviderSecretExists = _secretsManagerService.isProviderSecretExists(secretName);
        if (isProviderSecretExists == SECRET_FOUND) {
            log.info("Deleting provider secret");
            _secretsManagerService.deleteSecret(secretName);
        }
    }

    @Override
    public void validateIdpAccountId(DataFetchingEnvironment dfe) {
        if (StringUtils.isBlank(_userUtil.getAccountId(dfe))) {
            ErrorUtil.addError(dfe, AUTHENTICATED_ACCOUNT_ID_MISSING);
            log.warn(ERROR_REGISTRY_ACCOUNT_ID_EMPTY);
        }
    }

    @Override
    public void validateProviderAccountName(String providerAccountName,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {
        if (isInputFieldPresent(dfe, PROP_PROVIDER_ACCOUNT_NAME)) {
            if (StringUtils.isBlank(providerAccountName)) {
                ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.ACCOUNT_NAME_EMPTY);
                log.warn(ERROR_ACCOUNT_NAME_EMPTY);
            } else if (providerAccountName.length() > AI_AGENT_PROVIDER_NAME_LENGTH) {
                ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.ACCOUNT_NAME_TOO_LONG, providerAccountName);
                log.warn(ACCOUNT_NAME_TOO_LONG_ERROR, providerAccountName);
            }
            // validate new account name is not already present in the registry on update provider account
            else if (null != existingProviderAccount
                    && !existingProviderAccount.getProviderAccountName().equals(providerAccountName)
                    && Boolean.TRUE.equals(isProviderAccountExists(providerAccountName,_userUtil.getAccountId(dfe)))) {
                log.warn(PROVIDER_ACCOUNT_DUPLICATE_NAME_CONSTRAINT.name());
                ErrorUtil.addError(dfe, PROVIDER_ACCOUNT_DUPLICATE_NAME_CONSTRAINT);
            }
        }
    }

    @Override
    public Boolean isProviderAccountExists(String providerAccountName, String idpAccountId) {
        return _accountRepo.existsByIdpAccountIdAndProviderAccountNameAndIsDeletedFalse(idpAccountId,
                providerAccountName);
    }

    @Override
    public boolean isProviderAccountExistsForProviderTypeAndIdpAccountId(String providerAccountName,
            String idpAccountId, AiAgentProviderType providerType) {
        return _accountRepo.existsByIdpAccountIdAndProviderTypeAndProviderAccountNameAndIsDeletedFalse(idpAccountId,
                providerType, providerAccountName);
    }

    @Override
    public void validateMetadataJson(String metadataJson, DataFetchingEnvironment dfe) {
        if (isInputFieldPresent(dfe, PROP_METADATA_JSON) && (StringUtils.isBlank(metadataJson)
                || !_generalUtil.isValidJson(metadataJson, false))) {
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.INVALID_METADATA_JSON);
            log.warn(ERROR_INVALID_METADATA_JSON);
        }
    }

    @Override
    public void validateAiAgentProviderAccountStatus(AiAgentProviderAccountStatus status, DataFetchingEnvironment dfe) {
        if (isInputFieldPresent(dfe, PROP_PROVIDER_ACCOUNT_STATUS) && status == null) {
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.INVALID_PROVIDER_ACCOUNT_STATUS);
            log.warn(ERROR_INVALID_REGISTRY_ACCOUNT_STATUS);
        }
    }

    @Override
    public Mono<Void> validateCredentialsForUpdateProviderAccount(AiAgentProviderAccountUpdateInput input,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {
        if (isInputFieldPresent(dfe, FIELD_CREDENTIALS)) {
            boolean isValidConnection = _authParseService.validateConnection(input.getCredentials(),
                    existingProviderAccount.getAuthSchema());
            if (!isValidConnection) {
                log.warn(ERROR_VALIDATING_CONNECTION);
                ErrorUtil.addError(dfe, INVALID_CONNECTION_DETAILS);
                return Mono.empty();
            } else {
                return validateExternalProviderAccountId(input, existingProviderAccount, dfe);
            }
        }
        return Mono.empty();
    }

    @Override
    public Boolean isProviderAccountExistsForOneExternalIdWithOneIdpOneAuthCheck(String externalProviderAccountId,
            String idpAccountId, AiProviderAuthSchema authSchema, String region) {
        log.info(DUPLICATE_ONE_EXTERNAL_ACCOUNT_WITH_ONE_IDP_ONE_AUTH_CHECK, idpAccountId, authSchema,
                externalProviderAccountId, region);
        List<String> existingIdpAccountIds =
                _accountRepo.findIdpAccountIdsByExternalProviderAccountIdAndAuthSchemaAndIsDeletedFalse(
                        externalProviderAccountId, authSchema);
        if ((existingIdpAccountIds != null) && !existingIdpAccountIds.isEmpty() && !existingIdpAccountIds.contains(
                idpAccountId)) {
            return true;
        }

        return _accountRepo.existsByAuthSchemaAndExternalProviderAccountIdAndRegionAndIsDeletedFalse(authSchema,
                externalProviderAccountId, region);
    }

    private Mono<Void> validateExternalProviderAccountId(AiAgentProviderAccountUpdateInput input,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {
        log.info(VALIDATING_EXTERNAL_PROVIDER_ACCOUNT_ID, input.getId());
        if (StringUtils.isBlank(input.getCredentials())) {
            ErrorUtil.addError(dfe, INVALID_CREDENTIALS_INPUT);
            log.warn(ERROR_EMPTY_CREDENTIALS_INPUT);
            return Mono.empty();
        } else {
            if (!_generalUtil.isValidJson(input.getCredentials(), true)) {
                ErrorUtil.addError(dfe, INVALID_CREDENTIALS_INPUT);
                log.warn(ERROR_INVALID_CREDENTIALS_INPUT);
                return Mono.empty();
            }
        }

        log.info(VALIDATING_KEYS, input.getId());
        boolean validationCompletion = _authParseService.validateCredentialFormat(input.getCredentials(),
                existingProviderAccount.getAuthSchema());
        if (!validationCompletion) {
            ErrorUtil.addError(dfe, INVALID_CREDENTIALS_KEY);
            log.warn(ERROR_INVALID_CREDENTIALS_KEY);
            return Mono.empty();
        }

        log.info(VALIDATING_IMMUTABLE_FIELDS, input.getId());
        return _secretsManagerService.getSecret(existingProviderAccount.getCredentialsKey()).singleOrEmpty().flatMap(
                existingCredentials -> {
                    Optional<List<AiAgentRegistryErrorCode>> optionalErrorCodes =
                            _authParseService.verifyImmutableFieldsAreNotChanged(existingCredentials,
                                    input.getCredentials(), existingProviderAccount.getAuthSchema());
                    if (optionalErrorCodes.isPresent()) {
                        log.warn(IMMUTABLE_FIELDS_HAVE_CHANGED, existingProviderAccount.getGuid(),
                                optionalErrorCodes.get());
                        optionalErrorCodes.get().forEach(errorCode -> ErrorUtil.addError(dfe, errorCode));
                        return Mono.empty();
                    } else {
                        return verifyProviderAccountExternalIdHasNotChanged(input, existingProviderAccount, dfe);
                    }
                }).onErrorResume(error -> {
            ErrorUtil.addError(dfe, INVALID_CONNECTION_DETAILS);
            log.warn(IMMUTABLE_FIELD_VALIDATION_ERROR, existingProviderAccount.getGuid(), error);
            return Mono.empty();
        });
    }

    private Mono<Void> verifyProviderAccountExternalIdHasNotChanged(AiAgentProviderAccountUpdateInput input,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {
        log.info(VALIDATING_HEALTH_CHECK, existingProviderAccount.getProviderType().name());
        return _authParseService.fetchAccountId(input.getCredentials(), existingProviderAccount.getAuthSchema())
                .flatMap(externalAccountId -> {
                    if (StringUtil.isBlank(externalAccountId)) {
                        log.warn(EMPTY_EXTERNAL_ACCOUNT_ID);
                        ErrorUtil.addError(dfe, INVALID_CONNECTION_DETAILS);
                        log.warn(ERROR_VALIDATING_CONNECTION);
                    } else {
                        String existingExternalProviderAccountId =
                                existingProviderAccount.getExternalProviderAccountId();
                        if (!existingExternalProviderAccountId.equals(externalAccountId)) {
                            ErrorUtil.addError(dfe, DIFFERENT_EXTERNAL_PROVIDER_ACCOUNT_ID,
                                    existingExternalProviderAccountId);
                            log.warn(DIFFERENT_EXTERNAL_PROVIDER_ACCOUNT_ID.name() + ", "
                                    + existingExternalProviderAccountId);
                        }
                    }
                    return Mono.empty();
                }).onErrorResume(error -> {
                    ErrorUtil.addError(dfe, INVALID_CONNECTION_DETAILS);
                    log.warn("Error during connection validation: {}", input.getId(), error);
                    return Mono.empty();
                })
                // .then() discards the upstream value and returns a Mono<Void>
                // that completes when the upstream completes
                .then();
    }

    private String validateSSMUpdate(AiAgentProviderAccountUpdateInput input, String credential,
            DataFetchingEnvironment dfe) {
        String accountId = _userUtil.getAccountId(dfe);
        String secretName = accountId.concat(DELIMITER.concat(input.getId()));
        final UpdateSecretResponse updateSecretResponse = _secretsManagerService.updateSecret(secretName, credential);
        if (updateSecretResponse == null) {
            return null;
        }
        return secretName;
    }

    private static void populateProviderAccountForUpdateInputs(
            AiAgentProviderAccountUpdateInput providerAccountUpdateInput,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount providerAccount, DataFetchingEnvironment dfe) {
        if (isInputFieldPresent(dfe, PROP_PROVIDER_ACCOUNT_NAME)) {
            providerAccount.setProviderAccountName(providerAccountUpdateInput.getProviderAccountName());
        }
        if (isInputFieldPresent(dfe, PROP_METADATA_JSON)) {
            providerAccount.setProviderMetadata(providerAccountUpdateInput.getMetadataJson());
        }
        if (isInputFieldPresent(dfe, PROP_PROVIDER_ACCOUNT_STATUS)) {
            providerAccount.setProviderAccountStatus(
                    AiAgentProviderAccountStatus.valueOf(providerAccountUpdateInput.getProviderAccountStatus().name()));
        }
        if (isInputFieldPresent(dfe, PROP_PROVIDER_ACCOUNT_DESCRIPTION)) {
            providerAccount.setProviderAccountDescription(providerAccountUpdateInput.getProviderAccountDescription());
        }
    }

    public Mono<SyncUserAudit> createSyncUserAudit(
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount providerAccount) {
        log.info("Processing account {}", providerAccount.getUid());
        SyncContext  syncContext;

        if(providerAccount.getProviderType().equals(AiAgentProviderType.BOOMI)) {
            syncContext = new BedrockSyncContext(providerAccount);
        } else {
            syncContext = new GardenSyncContext(providerAccount);
        }

        SyncEntityPointInTimeInfo entityInfo = SyncEntityPointInTimeInfo.builder()
                .entityUid(providerAccount.getUid())
                .entityType(PROVIDER_ACCOUNT)
                .entityGuid(providerAccount.getGuid())
                .entityName(providerAccount.getProviderAccountName())
                .build();

        return _syncHistory.startSync(entityInfo, syncContext);


    }
}
