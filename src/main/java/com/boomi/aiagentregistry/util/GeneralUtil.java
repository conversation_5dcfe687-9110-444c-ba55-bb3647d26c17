// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.util;

import reactor.core.publisher.Mono;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Optional;

public interface GeneralUtil {

    ObjectWriter OBJECT_WRITER = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .writerWithDefaultPrettyPrinter();
    boolean isValidJson(String json, boolean hideSensitiveInfo);

    static <T> Mono<T> optionalToMono(Optional<T> optional) {
        return optional.map(Mono::just)
                .orElseGet(Mono::empty);
    }

    static Timestamp tmStmpFromInst(Instant date) {
        return Timestamp.from(date.truncatedTo(ChronoUnit.SECONDS));
    }

    static Timestamp tmStmpFromString(String timestampString) {
        return Optional.ofNullable(timestampString)
                .map(timestamp -> {
                    try {
                        return Timestamp.from(Instant.parse(timestampString));
                    } catch (DateTimeParseException e) {
                        return Timestamp.from(Instant.now());
                    }
                })
                .orElse(null);
    }

    static String combineVersion(String versionString, Integer versionInt) {
        if (StringUtils.isNotBlank(versionString)) {
            return versionString;
        }
        if (versionInt != null) {
            return versionInt.toString();
        }
        return null;
    }

    static java.sql.Timestamp dateToTimestamp(java.util.Date date) {
        return date != null ? new Timestamp(date.getTime()) : null;
    }

    /**
     * Checks if the input update timestamp is newer than the existing timestamp
     *
     * @param inputUpdateTime    the update at provider's timestamp
     * @param existingUpdateTime the existing update at provider's timestamp in DB
     * @return true if the input timestamp is newer than existing timestamp, false otherwise
     */
    static boolean isNewerTimestamp(Date inputUpdateTime, Timestamp existingUpdateTime) {
        if (inputUpdateTime != null && existingUpdateTime != null) {
            return inputUpdateTime.getTime() > existingUpdateTime.getTime();
        }
        return true;
    }

    static Instant getInstantFromString(String stringInstant) {
        Instant instantUpdatedAt;
        try {
            instantUpdatedAt = Instant.parse(stringInstant);
        } catch (DateTimeParseException e) {
            // or throw exception, or handle differently
            instantUpdatedAt = Instant.now();
        }
        return instantUpdatedAt;
    }
}

