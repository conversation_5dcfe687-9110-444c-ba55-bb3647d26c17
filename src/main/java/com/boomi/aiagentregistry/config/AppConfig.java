// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.config;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@ToString
@Accessors(prefix = { "_" })
@Configuration
@AllArgsConstructor
@NoArgsConstructor
public class AppConfig {

    @Value("${spring.aws.region}")
    private String _awsregion;

    @Value("${spring.aws.secondaryregion}")
    private String _awssecondaryregion;

    @Value("${com.boomi.aiagentregistry.boomiContact}")
    private String _boomiContact;

    @Value("${com.boomi.aiagentregistry.customer}")
    private String _registryCustomer;

    @Value("${com.boomi.aiagentregistry.environment}")
    private String _environment;

    @Value("${com.boomi.aiagentregistry.serviceName}")
    private String _serviceName;

    @Value("${com.boomi.aiagentregistry.stackName}")
    private String _stackName;

    @Value("${com.boomi.aiagentregistry.stackType}")
    private String _stackType;
}
