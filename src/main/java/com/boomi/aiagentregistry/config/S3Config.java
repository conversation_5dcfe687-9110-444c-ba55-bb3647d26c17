// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.config;

import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class S3Config {

    @Value("${spring.aws.region}")
    private String _region;

    @Value("${boomi.services.aiagentregistry.s3.bucket}")
    private String _s3Bucket;

    @Bean(destroyMethod = "close")
    public S3Client s3Client(){
        return S3Client
                .builder()
                .region(Region.of(_region))
                .build();
    }

    @Bean(name = "s3Bucket")
    public String s3Bucket(){
        log.info("s3Bucket is {} in region {}", _s3Bucket, _region);
        return _s3Bucket;
    }
}
