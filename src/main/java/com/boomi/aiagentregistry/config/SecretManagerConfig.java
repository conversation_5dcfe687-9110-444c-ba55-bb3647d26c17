// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.config;

import com.boomi.aiagentregistry.service.SecretsManageLocalServiceImpl;
import com.boomi.aiagentregistry.service.SecretsManagerService;
import com.boomi.aiagentregistry.service.SecretsManagerServiceImpl;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class SecretManagerConfig {
    @Value("${aws.secretsmanager.local.enabled:false}")
    private boolean _sSMLocalEnabled;
    @Value("${com.boomi.aiagentregistry.secretManagerRole}")
    private String _secretManagerRole;

    @Bean
    @Primary
    public SecretsManagerService secretsManagerService(AppConfig appConfig){
        if(_sSMLocalEnabled) {
            return new SecretsManageLocalServiceImpl();
        }else {
            return new SecretsManagerServiceImpl(appConfig, _secretManagerRole);
        }
    }
}
