// Copyright (c) 2025 Boomi, Inc.
package com.boomi.aiagentregistry.search;

import lombok.experimental.Accessors;
import com.boomi.aiagentregistry.entity.AgentListingView;
import com.boomi.aiagentregistry.entity.AiAgentListing;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryInput;

import org.springframework.data.jpa.domain.Specification;

@Accessors(prefix = "_")
public interface SearchStrategy {

    String UID = "uid";
    String GUID = "guid";
    String RELATED_ENTITY_UID = "relatedEntityUid";
    String LLM = "llm";
    String LLM_NAME = "name";
    String RELATED_ENTITY_TYPE = "relatedEntityType";
    String ALIAS = "ALIAS";
    String VERSION = "VERSION";
    String ID = "id";
    String ALIAS_ID = "aliasId";
    String VERSION_ID = "versionId";
    String ALIAS_NAME = "aliasName";
    String VERSION_NAME = "versionName";
    String PROVIDER_ACCOUNT_NAME = "providerAccountName";
    String PROVIDER_TYPE = "providerType";
    String TRUST_LEVEL = "trustLevel";
    String IDP_ACCOUNT_ID = "idpAccountId";
    String PROVIDER_ACCOUNT_GU_ID = "providerAccountGuId";
    String ACCOUNT = "ACCOUNT";
    String TAG_UID = "tagUid";
    String TAGS = "tags";
    String MODELS = "models";

    default Specification<AiAgentListing> createSpecification(
            AiAgentsListingQueryInput input) {
        return null;
    }

    default Specification<AgentListingView> createViewSpecification(
            AiAgentsListingQueryInput input) {
        return null;
    }
}
