// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.search;

import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.entity.AiAgentListing;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryInput;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
@AllArgsConstructor
public class SearchByProviderTypeStrategy implements SearchStrategy {

    public static final String PROVIDER = "PROVIDER";
    public static final String PROVIDER_TYPE = "providerType";

    @Override
    public Specification<AiAgentListing> createSpecification(
            AiAgentsListingQueryInput input) {
        return (root, query, criteriaBuilder) -> {
            List<String> providerTypes = getProviderTypes(input);
            log.info(" providerTypes "+providerTypes);
            Subquery<AiAgentListing> artifactSubquery = query.subquery(AiAgentListing.class);
            Root<AiAgentListing> subRoot = artifactSubquery.from(AiAgentListing.class);
            artifactSubquery.select(subRoot);

            jakarta.persistence.criteria.Predicate agentNamePredicate = SearchByProviderTypeStrategy
                    .createProviderTypeInPredicate(root,
                            providerTypes);

            artifactSubquery.where(agentNamePredicate);
            return criteriaBuilder.exists(artifactSubquery);
        };
    }

    private List<String> getProviderTypes(AiAgentsListingQueryInput input) {
        if (input == null || input.getAgentListingFilter() == null) {
            return Collections.emptyList();
        }
        log.info("getting ProviderTypes");
        List<String> list = input.getAgentListingFilter().getFilterInput().getProviders().stream().map(
                aiAgentProviderType -> aiAgentProviderType.name()).toList();
        return list;

    }

    public static <T, I> Predicate createProviderTypeInPredicate(Root<T> root, Collection<I> providerTypes) {
        log.info("createProviderTypeInPredicate");
        if (CollectionUtils.isEmpty(providerTypes)) {
            return root.isNotNull();
        }

        return root.get(PROVIDER_TYPE).in(providerTypes);
    }
}