// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.search;

import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.entity.AgentListingView;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryInput;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Component
@Slf4j
@AllArgsConstructor
public class ViewSearchByProviderNameStrategy implements SearchStrategy {

    @Override
    public Specification<AgentListingView> createViewSpecification(
            AiAgentsListingQueryInput input) {
        return (root, query, criteriaBuilder) -> {
            List<String> providerIds = getProviderAccountIds(input);
            Subquery<AgentListingView> artifactSubquery = query.subquery(AgentListingView.class);
            Root<AgentListingView> subRoot = artifactSubquery.from(AgentListingView.class);
            artifactSubquery.select(subRoot);

            Predicate agentNamePredicate = ViewSearchByProviderNameStrategy
                    .createNameInPredicate(root,
                            providerIds);
            artifactSubquery.where(agentNamePredicate);
            return criteriaBuilder.exists(artifactSubquery);
        };
    }

    private static List<String> getProviderAccountIds(AiAgentsListingQueryInput input) {
        if (input == null || input.getAgentListingFilter() == null) {
            return Collections.emptyList();
        }

        return input.getAgentListingFilter().getFilterInput().getAccounts();
    }

    public static <T, I> Predicate createNameInPredicate(Root<T> root, Collection<I> ids) {

        if (CollectionUtils.isEmpty(ids)) {
            return root.isNotNull();
        }

        return root.get(PROVIDER_ACCOUNT_GU_ID).in(ids);
    }
}



