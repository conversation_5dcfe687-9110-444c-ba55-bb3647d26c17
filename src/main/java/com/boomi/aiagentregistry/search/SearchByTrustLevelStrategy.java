// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.search;

import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.entity.AiAgentListing;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryInput;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Component
@Slf4j
@AllArgsConstructor
public class SearchByTrustLevelStrategy implements SearchStrategy {

    @Override
    public Specification<AiAgentListing> createSpecification(
            AiAgentsListingQueryInput input) {
        return (root, query, criteriaBuilder) -> {
            List<String> trustLevels = getTrustLevel(input);
            log.info(" Creating trust level Specification " + trustLevels);
            Subquery<AiAgentListing> artifactSubquery = query.subquery(AiAgentListing.class);
            Root<AiAgentListing> subRoot = artifactSubquery.from(AiAgentListing.class);
            artifactSubquery.select(subRoot);

            jakarta.persistence.criteria.Predicate agentNamePredicate = SearchByTrustLevelStrategy
                    .createTrustTypeInPredicate(root,
                            trustLevels);

            artifactSubquery.where(agentNamePredicate);
            return criteriaBuilder.exists(artifactSubquery);
        };
    }

    private List<String> getTrustLevel(AiAgentsListingQueryInput input) {
        if (input == null || input.getAgentListingFilter() == null) {
            return Collections.emptyList();
        }
        log.info("getting ProviderTypes");
        return input.getAgentListingFilter().getFilterInput().getTrustLevel().stream().map(
                Enum::name).toList();
    }

    public static <T, I> Predicate createTrustTypeInPredicate(Root<T> root, Collection<I> trustLevels) {
        log.info("createTrustTypeInPredicate");
        if (CollectionUtils.isEmpty(trustLevels)) {
            return root.isNotNull();
        }

        return root.get(TRUST_LEVEL).in(trustLevels);
    }
}
