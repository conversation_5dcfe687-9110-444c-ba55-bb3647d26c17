// Copyright (c) 2025 Boom<PERSON>, LP.
package com.boomi.aiagentregistry.search;

import com.boomi.aiagentregistry.entity.AiAgentListing;
import com.github.kagkarlsson.shaded.cronutils.utils.StringUtils;

import org.springframework.data.jpa.domain.Specification;

import java.util.List;

import static com.boomi.aiagentregistry.search.SearchByProviderNameStrategy.PROVIDER_ACCOUNT_GU_ID;
import static com.boomi.aiagentregistry.search.SearchStrategy.ALIAS_NAME;
import static com.boomi.aiagentregistry.search.SearchStrategy.IDP_ACCOUNT_ID;
import static com.boomi.aiagentregistry.search.SearchStrategy.PROVIDER_ACCOUNT_NAME;
import static com.boomi.aiagentregistry.search.SearchStrategy.PROVIDER_TYPE;
import static com.boomi.aiagentregistry.search.SearchStrategy.TRUST_LEVEL;
import static com.boomi.aiagentregistry.search.SearchStrategy.VERSION_NAME;

public class AiAgentSpecifications {

    private AiAgentSpecifications() {
    }

    public static Specification<AiAgentListing> createSpecification(AiAgentSearchCriteria criteria) {

        return Specification
                .where(withProviderAccountIds(criteria.getProviderAccountIds()))
                .and(withSearchInput(criteria.getSearchInput()))
                .and(withIdpAccountId(criteria.getIdpAccountId()));
    }

    public static Specification<AiAgentListing> withIdpAccountId(String idpAccountId) {
        return (root, query, cb) -> {

            return root.get(IDP_ACCOUNT_ID).in(idpAccountId);
        };
    }

    private static Specification<AiAgentListing> withProviderAccountIds(List<String> providerAccountIds) {
        return (root, query, cb) -> {
            if (providerAccountIds == null || providerAccountIds.isEmpty()) {
                return null;
            }
            return root.get(PROVIDER_ACCOUNT_GU_ID).in(providerAccountIds);
        };
    }

    private static Specification<AiAgentListing> withSearchInput(String searchInput) {
        return (root, query, cb) -> {
            if (StringUtils.isEmpty(searchInput)) {
                return null;
            }
            String searchPattern = "%" + searchInput.toLowerCase() + "%";
            return cb.or(
                    cb.like(cb.lower(root.get(ALIAS_NAME)), searchPattern),
                    cb.like(cb.lower(root.get(VERSION_NAME)), searchPattern),
                    cb.like(cb.lower(root.get(PROVIDER_ACCOUNT_NAME)), searchPattern),
                    cb.like(cb.lower(root.get(PROVIDER_TYPE)), searchPattern),
                    cb.equal(root.get(TRUST_LEVEL), searchInput));
        };
    }
}

