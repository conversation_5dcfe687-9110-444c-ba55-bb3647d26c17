// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.search;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.entity.AiAgentListing;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryInput;
import com.github.kagkarlsson.shaded.cronutils.utils.StringUtils;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@AllArgsConstructor
public class SearchByTextStrategy implements SearchStrategy {

    @Override
    public Specification<AiAgentListing> createSpecification(
            AiAgentsListingQueryInput input) {
        return (root, query, cb) -> {
            if (StringUtils.isEmpty(input.getSearchInput())) {
                return null;
            }
            String searchPattern = "%" + input.getSearchInput().toLowerCase() + "%";
            return cb.or(
                    cb.like(cb.lower(root.get(ALIAS_NAME)), searchPattern),
                    cb.like(cb.lower(root.get(VERSION_NAME)), searchPattern),
                    cb.like(cb.lower(root.get(PROVIDER_ACCOUNT_NAME)), searchPattern),
                    cb.like(cb.lower(root.get(PROVIDER_TYPE)), searchPattern),
                    cb.equal(root.get(TRUST_LEVEL), input.getSearchInput()));
        };
    }
}



