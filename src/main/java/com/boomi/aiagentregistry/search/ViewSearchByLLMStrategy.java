// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.search;

import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.entity.AgentListingView;
import com.boomi.aiagentregistry.entity.AiAgentLlm;
import com.boomi.aiagentregistry.entity.AiAgentLlmAssociation;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryInput;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@Slf4j
@AllArgsConstructor
public class ViewSearchByLLMStrategy implements SearchStrategy {

    @Override
    public Specification<AgentListingView> createViewSpecification(
            AiAgentsListingQueryInput input) {
        List<String> llms = getLlmInput(input);
        return findByLlmNames(llms);
    }

    private static List<String> getLlmInput(AiAgentsListingQueryInput input) {
        if (input == null || input.getAgentListingFilter() == null) {
            return Collections.emptyList();
        }
        return input.getAgentListingFilter().getFilterInput().getModels();
    }

    /**
            SELECT al.* FROM v_agent_listing al WHERE al.version_id IN (
                        SELECT ala.related_entity_uid FROM ai_agent_llm_association ala WHERE ala.llm_uid IN (
                                    SELECT l.uid FROM ai_agent_llm l WHERE l.name IN (?1, ?2, ...))
                        AND ala.related_entity_type = 'VERSION')
     */

    public static Specification<AgentListingView> findByLlmNames(List<String> llmNames) {
        return (root, query, cb) -> {
            if (llmNames == null || llmNames.isEmpty()) {
                return null;
            }

            // Create subquery for LLMs to get LLM UIDs
            Subquery<Integer> llmUidsSubquery = query.subquery(Integer.class);
            Root<AiAgentLlm> llmRoot = llmUidsSubquery.from(AiAgentLlm.class);
            llmUidsSubquery.select(llmRoot.get(UID))
                    .where(llmRoot.get(LLM_NAME).in(llmNames));

            // Create subquery for version associations
            Subquery<Integer> versionSubquery = query.subquery(Integer.class);
            Root<AiAgentLlmAssociation> llmAssociationRoot = versionSubquery.from(AiAgentLlmAssociation.class);
            versionSubquery.select(llmAssociationRoot.get(RELATED_ENTITY_UID).as(Integer.class))
                    .where(cb.and(
                            llmAssociationRoot.get(LLM).get(UID).in(llmUidsSubquery),
                            cb.equal(llmAssociationRoot.get(RELATED_ENTITY_TYPE), VERSION)
                    ));

            // Create predicate for version condition
            return cb.in(root.get("id").get("versionId")).value(versionSubquery);
        };
    }

}
