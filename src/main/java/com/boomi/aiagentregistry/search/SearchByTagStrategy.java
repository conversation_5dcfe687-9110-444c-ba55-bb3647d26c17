// Copyright (c) 2025 Boom<PERSON>, LP
package com.boomi.aiagentregistry.search;

import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.entity.AiAgentListing;
import com.boomi.aiagentregistry.entity.AiAgentTag;
import com.boomi.aiagentregistry.entity.AiAgentTagAssociation;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryInput;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@Slf4j
@AllArgsConstructor
public class SearchByTagStrategy implements SearchStrategy {

    @Override
    public Specification<AiAgentListing> createSpecification(
            AiAgentsListingQueryInput input) {
        List<String> tags = getTagGuIds(input);
        return findByTagGuids(tags);
    }

    private static List<String> getTagGuIds(AiAgentsListingQueryInput input) {
        if (input == null || input.getAgentListingFilter() == null) {
            return Collections.emptyList();
        }

        return input.getAgentListingFilter().getFilterInput().getTags();
    }

    public static Specification<AiAgentListing> findByTagGuids(List<String> tagGuids) {
        return (root, query, cb) -> {
            // Create subquery for tags to get tag UIDs
            Subquery<Integer> tagUidsSubquery = query.subquery(Integer.class);
            Root<AiAgentTag> tagRoot = tagUidsSubquery.from(AiAgentTag.class);
            tagUidsSubquery.select(tagRoot.get(UID))
                    .where(tagRoot.get(GUID).in(tagGuids));

            // Create subquery for alias associations
            Subquery<Integer> aliasSubquery = query.subquery(Integer.class);
            Root<AiAgentTagAssociation> aliasAssociationRoot = aliasSubquery.from(AiAgentTagAssociation.class);
            aliasSubquery.select(aliasAssociationRoot.get(RELATED_ENTITY_UID).as(Integer.class))
                    .where(cb.and(
                            aliasAssociationRoot.get(TAG_UID).in(tagUidsSubquery),
                            cb.equal(aliasAssociationRoot.get(RELATED_ENTITY_TYPE), ALIAS)));
            /**
             * SELECT ta.relatedEntityUid
             * FROM AiAgentTagAssociation ta
             * WHERE ta.tagUid IN (
             *     -- tagUidsSubquery: subquery that returns tag UIDs for given GUIDs
             *     SELECT t.uid
             *     FROM AiAgentTag t
             *     WHERE t.guid IN (:tagGuids)
             * )
             * AND ta.relatedEntityType = 'ALIAS' */
            // Create subquery for version associations
            Subquery<Integer> versionSubquery = query.subquery(Integer.class);
            Root<AiAgentTagAssociation> versionAssociationRoot = versionSubquery.from(AiAgentTagAssociation.class);
            versionSubquery.select(versionAssociationRoot.get(RELATED_ENTITY_UID).as(Integer.class))
                    .where(cb.and(
                            versionAssociationRoot.get(TAG_UID).in(tagUidsSubquery),
                            cb.equal(versionAssociationRoot.get(RELATED_ENTITY_TYPE), VERSION)
                                 ));

            // Create predicates for both conditions
            Predicate aliasCondition = cb.in(root.get(ID).get(ALIAS_ID)).value(aliasSubquery);
            Predicate versionCondition = cb.in(root.get(ID).get(VERSION_ID)).value(versionSubquery);

            // Combine conditions with OR
            return cb.or(aliasCondition, versionCondition);
        };
    }
}



