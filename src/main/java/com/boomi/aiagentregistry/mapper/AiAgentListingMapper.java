// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AiAgentListing;
import com.boomi.aiagentregistry.util.GeneralUtil;
import com.boomi.graphql.server.schema.types.LlmInfo;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Mapper(uses = { AiAgentListing.class, GeneralUtil.class })
public interface AiAgentListingMapper {

    AiAgentListingMapper AI_AGENT_LISTING_MAPPER = Mappers.getMapper(AiAgentListingMapper.class);

    @Mapping(target = "agentId", source = "agentGuid")
    @Mapping(target = "versionId", source = "versionGuid")

    @Mapping(target = "aliasExternalId",expression = "java((aiAgentListing.getAliasIsDeleted()==null ||"
            + " aiAgentListing.getAliasIsDeleted()) ? null : aiAgentListing.getAliasExternalId())")
    @Mapping(target = "aliasId",expression = "java((aiAgentListing.getAliasIsDeleted()==null ||"
            + " aiAgentListing.getAliasIsDeleted()) ? null : aiAgentListing.getAliasGuid())")
    @Mapping(target = "aliasName",expression = "java((aiAgentListing.getAliasIsDeleted()==null ||"
            + "aiAgentListing.getAliasIsDeleted()) ? null : aiAgentListing.getAliasName())")
    com.boomi.graphql.server.schema.types.AiAgentListing
    toAiAgentListing(com.boomi.aiagentregistry.entity.AiAgentListing aiAgentListing);

    List<com.boomi.graphql.server.schema.types.AiAgentListing>
    toAiAgentListings(List<com.boomi.aiagentregistry.entity.AiAgentListing> aiAgentListings);

}

