// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AgentListingView;
import com.boomi.aiagentregistry.util.GeneralUtil;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = { AgentListingView.class, GeneralUtil.class })
public interface AgentListingViewMapper {

    AgentListingViewMapper AGENT_LISTING_VIEW_MAPPER = Mappers.getMapper(AgentListingViewMapper.class);

    @Mapping(target = "agentId", source = "agentGuid")
    @Mapping(target = "versionId", source = "versionGuid")

    @Mapping(target = "aliasExternalId",expression = "java((aiAgentListing.getAliasIsDeleted()==null ||"
            + " aiAgentListing.getAliasIsDeleted()) ? null : aiAgentListing.getAliasExternalId())")
    @Mapping(target = "aliasId",expression = "java((aiAgentListing.getAliasIsDeleted()==null ||"
            + " aiAgentListing.getAliasIsDeleted()) ? null : aiAgentListing.getAliasGuid())")
    @Mapping(target = "aliasName",expression = "java((aiAgentListing.getAliasIsDeleted()==null ||"
            + "aiAgentListing.getAliasIsDeleted()) ? null : aiAgentListing.getAliasName())")
    com.boomi.graphql.server.schema.types.AiAgentListing
    toAiAgentListing(AgentListingView aiAgentListing);

    List<com.boomi.graphql.server.schema.types.AiAgentListing>
    toAiAgentListings(List<AgentListingView> aiAgentListings);

}

