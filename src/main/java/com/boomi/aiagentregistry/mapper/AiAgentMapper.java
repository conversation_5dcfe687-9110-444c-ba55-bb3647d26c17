// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.gardenagents.model.AgentSummary;
import com.boomi.graphql.server.schema.types.AiAgentCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentUpdateInput;
import com.boomi.graphql.server.schema.types.AiAgentVersion;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Set;

/**
 * <AUTHOR>
 */

@Mapper(uses = {AiAgent.class}, imports = {GuidUtil.class})
public interface AiAgentMapper {
    AiAgentMapper AI_AGENT_MAPPER = org.mapstruct.factory.Mappers.getMapper(AiAgentMapper.class);

    @Mapping(target = "id", source = "guid")
    @Mapping(target = "agentVersions", source = "versions")
    com.boomi.graphql.server.schema.types.AiAgent toAIAgent(AiAgent entity);


    @Mapping(target = "guid", expression = "java(com.boomi.aiagentregistry.util.GuidUtil.createAIAgentGuid())")
    @Mapping(target = "idpAccountId", source = "aiAgentProviderAccount.idpAccountId")
    @Mapping(target = "aiAgentProviderAccount", source = "aiAgentProviderAccount")
    @Mapping(target = "isDeleted", constant = "false")
    @Mapping(
            target = "modifiedTime",
            expression = "java(com.boomi.aiagentregistry.util.AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "uid", ignore = true)
    AiAgent toEntityFromAgentCreateInput(AiAgentCreateInput input, AiAgentProviderAccount aiAgentProviderAccount);

    @Mapping(target = "id", source = "aiAgentEntity.guid")
    @Mapping(target = "externalId", source = "aiAgentEntity.externalId")
    @Mapping(target = "agentVersions", source = "versions")
    @Mapping(target = "providerAccountSummary", source = "aiAgentEntity.aiAgentProviderAccount")
    com.boomi.graphql.server.schema.types.AiAgent toAIAgent(AiAgent aiAgentEntity, Set<AiAgentVersion> versions);

    AiAgentCreateInput toAiAgentCreateInput(AiAgentUpdateInput input);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "externalId", expression = "java(awsAgent.agentId())")
    @Mapping(target = "guid", expression = "java(GuidUtil.createAIAgentGuid())")
    @Mapping(target = "idpAccountId", source = "aiAgentProviderAccount.idpAccountId")
    @Mapping(target = "aiAgentProviderAccount", source = "aiAgentProviderAccount")
    @Mapping(target = "isDeleted", constant = "false")
    @Mapping(target = "uid", ignore = true)
    AiAgent toEntityFromAwsAgent(software.amazon.awssdk.services.bedrockagent.model.AgentSummary awsAgent,
            AiAgentProviderAccount aiAgentProviderAccount);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "externalId", expression = "java(gardenAgent.getId())")
    @Mapping(target = "guid", expression = "java(GuidUtil.createAIAgentGuid())")
    @Mapping(target = "idpAccountId", source = "aiAgentProviderAccount.idpAccountId")
    @Mapping(target = "aiAgentProviderAccount", source = "aiAgentProviderAccount")
    @Mapping(target = "isDeleted", constant = "false")
    @Mapping(target = "uid", ignore = true)
    AiAgent toEntityFromGardenAgent(AgentSummary gardenAgent, AiAgentProviderAccount aiAgentProviderAccount);

}

