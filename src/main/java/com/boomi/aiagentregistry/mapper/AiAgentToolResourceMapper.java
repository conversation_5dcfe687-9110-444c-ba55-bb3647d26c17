// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.entity.AiAgentToolResource;
import com.boomi.aiagentregistry.exception.SyncException;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GeneralUtil;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;
import software.amazon.awssdk.services.bedrockagent.model.APISchema;
import software.amazon.awssdk.services.bedrockagent.model.AgentActionGroup;
import software.amazon.awssdk.services.bedrockagent.model.DataSource;
import software.amazon.awssdk.services.bedrockagent.model.Function;


/**
 * <AUTHOR>
 */

@Mapper(uses = {AiAgentTool.class},
        imports = {GuidUtil.class, AuditUtilImpl.class, GeneralUtil.class})
public interface AiAgentToolResourceMapper {
    String FAILED_TO_PARSE_OBJECT_TO_JSON = "Failed to parse object to json";

    AiAgentToolResourceMapper TOOL_RES_MAPPER = Mappers.getMapper(AiAgentToolResourceMapper.class);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "updatedAtProviderTime",
            expression = "java(GeneralUtil.tmStmpFromInst(actionGroup.updatedAt()))")
    @Mapping(target = "guid", source = "toolResourceGuid")
    @Mapping(target = "externalId", source = "toolResourceGuid")
    @Mapping(target = "name", expression = "java(function.name())")
    @Mapping(target = "description", expression = "java(function.description())")
    @Mapping(target = "resourceType", constant = "AWS_FUNCTION")
    @Mapping(target = "tool", source = "tool")
    @Mapping(target = "toolResourceJson", source = "function", qualifiedByName = "parseFunction")
    @Mapping(target = "createdTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    AiAgentToolResource functionToToolResource(
            Function function,
            AgentActionGroup actionGroup,
            AiAgentTool tool,
            String toolResourceGuid);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "updatedAtProviderTime",
            expression = "java(GeneralUtil.tmStmpFromInst(actionGroup.updatedAt()))")
    @Mapping(target = "guid", source = "toolResourceGuid")
    @Mapping(target = "externalId", source = "toolResourceGuid")
    @Mapping(target = "tool", source = "tool")
    @Mapping(target = "toolResourceJson", source = "schema", qualifiedByName = "parseApi")
    @Mapping(target = "resourceType", constant = "AWS_API")
    @Mapping(target = "createdTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    AiAgentToolResource apiSchemaToToolResource(
            APISchema schema,
            AgentActionGroup actionGroup,
            AiAgentTool tool,
            String toolResourceGuid);


    @BeanMapping(ignoreByDefault = true)
    //Audit
    @Mapping(target = "updatedAtProviderTime",
            expression = "java(GeneralUtil.tmStmpFromInst(ds.updatedAt()))")
    @Mapping(target = "createdTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "status", expression = "java(ds.statusAsString())")

    //Identity
    @Mapping(target = "externalId", expression = "java(ds.dataSourceId())")
    @Mapping(target = "guid", expression = "java(GuidUtil.createToolResourceGuid())")

    //Relationships
    @Mapping(target = "tool", source = "tool")

    @Mapping(target = "name", expression = "java(ds.name())")
    @Mapping(target = "description", expression = "java(ds.description())")
    @Mapping(target = "resourceType", constant = "AWS_DATA_SOURCE")
    @Mapping(target = "toolResourceJson", source = "ds", qualifiedByName = "parseDataSource")
    AiAgentToolResource dsToResource(DataSource ds, AiAgentTool tool);


    @Named("parseApi")
    default String parseResource(APISchema schema) {
        if (schema == null) {
            return null;
        }
        return deserialize(schema.toBuilder());
    }

    @Named("parseFunction")
    default String parseFunction(Function function) {
        if (function == null) {
            return null;
        }
        return deserialize(function.toBuilder());
    }

    @Named("parseDataSource")
    default String parseDataSource(DataSource dataSource) {
        if (dataSource == null) {
            return null;
        }

        return deserialize(dataSource.toBuilder());

    }

    private String deserialize(Object obj) {
        try {
            return GeneralUtil.OBJECT_WRITER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new SyncException(FAILED_TO_PARSE_OBJECT_TO_JSON, e);
        }
    }

}
