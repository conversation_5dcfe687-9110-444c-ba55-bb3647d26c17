// Copyright (c) 2024 Boomi, LP

package com.boomi.aiagentregistry.mapper;

import software.amazon.awssdk.services.bedrock.model.GetGuardrailResponse;
import com.boomi.aiagentregistry.entity.AiAgentGuardrail;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.exception.SyncException;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GeneralUtil;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.gardenagents.model.Guardrail;
import com.fasterxml.jackson.core.JsonProcessingException;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

@Mapper(uses = {AiAgentGuardrail.class, GeneralUtil.class},
        imports = {AuditUtilImpl.class, GuidUtil.class, GeneralUtil.class})
public interface AiAgentGuardrailMapper {
    String FAILED_TO_PARSE_OBJECT_TO_JSON = "Failed to parse object to json";

    AiAgentGuardrailMapper AI_AGENT_GUARDRAIL_MAPPER =
            org.mapstruct.factory.Mappers.getMapper(AiAgentGuardrailMapper.class);

    @Mapping(target = "id", source = "aiAgentGuardrail.guid")
    @Mapping(target = "associationStatus", source = "associationStatus")
    @Mapping(target = "configurationJson", source = "aiAgentGuardrail.guardrailJson")
    @Mapping(target = "auditData.createdByUserId", source = "aiAgentGuardrail.createdByUserId")
    @Mapping(target = "auditData.createdTime", source = "aiAgentGuardrail.createdTime")
    @Mapping(target = "auditData.modifiedByUserId", source = "aiAgentGuardrail.modifiedByUserId")
    @Mapping(target = "auditData.modifiedTime", source = "aiAgentGuardrail.modifiedTime")
    @Mapping(target = "auditData.updatedByOrigin", source = "aiAgentGuardrail.updatedByOrigin")
    @Mapping(target = "auditData.updatedAtProviderTime", source = "aiAgentGuardrail.updatedAtProviderTime")
    com.boomi.graphql.server.schema.types.AiAgentGuardrail toAiAgentGuardrail
            (AiAgentGuardrail aiAgentGuardrail, String associationStatus);

    @BeanMapping(ignoreByDefault = true)
    //audit
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "updatedAtProviderTime", expression = "java(GeneralUtil.tmStmpFromInst(awsGr.updatedAt()))")
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")

    //other properties including name, desc
    @Mapping(target = "name", expression = "java(awsGr.name())")
    @Mapping(target = "description", expression = "java(awsGr.description())")
    @Mapping(target = "guardrailJson", source = "awsGr", qualifiedByName = "parseGuardrail")
    void updatedEntityFromAwsGuardRail(@MappingTarget AiAgentGuardrail guardrail, GetGuardrailResponse awsGr);

    @BeanMapping(ignoreByDefault = true)
    //identity
    @Mapping(target = "guid", expression = "java(GuidUtil.createAIAgentGuardrailGuid())")
    @Mapping(target = "externalId", expression = "java(awsGr.guardrailId())")
    //audit
    @Mapping(target = "versionString", expression = "java(awsGr.version())")
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "createdTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "updatedAtProviderTime", expression = "java(GeneralUtil.tmStmpFromInst(awsGr.updatedAt()))")
    //relationships
    @Mapping(target = "aiAgentProviderAccount", source = "account")
    @Mapping(target = "idpAccountId", source = "account.idpAccountId")

    //other properties including name, desc
    @Mapping(target = "name", expression = "java(awsGr.name())")
    @Mapping(target = "description", expression = "java(awsGr.description())")
    @Mapping(target = "guardrailJson", source = "awsGr", qualifiedByName = "parseGuardrail")
    AiAgentGuardrail toEntityFrmAwsGuardrails(GetGuardrailResponse awsGr, AiAgentProviderAccount account);

    @BeanMapping(ignoreByDefault = true)
    //identity
    @Mapping(target = "uid", ignore = true)
    @Mapping(target = "guid", expression = "java(GuidUtil.createAIAgentGuardrailGuid())")
    @Mapping(target = "externalId", source = "gardenGuardrail.id")
    //audit
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "createdTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    //relationships
    @Mapping(target = "aiAgentProviderAccount", source = "account")
    @Mapping(target = "idpAccountId", source = "account.idpAccountId")
    //other properties including name, desc
    @Mapping(target = "guardrailJson", source = "gardenGuardrail.guardrailJson")
    AiAgentGuardrail toEntityFromGardenGuardrails(AiAgentProviderAccount account, Guardrail gardenGuardrail);

    @BeanMapping(ignoreByDefault = true)
    //audit
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    //other properties including name, desc
    @Mapping(target = "guardrailJson", source = "gardenGuardrail.guardrailJson")
    void updatedEntityFromGardenGuardrail(@MappingTarget AiAgentGuardrail guardrail, Guardrail gardenGuardrail);

    @Named("parseGuardrail")
    default String parseGuardrail(GetGuardrailResponse guardrail) {
        if (guardrail == null) {
            return null;
        }

        return deserialize(guardrail.toBuilder());

    }

    private String deserialize(Object obj) {
        try {
            return GeneralUtil.OBJECT_WRITER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new SyncException(FAILED_TO_PARSE_OBJECT_TO_JSON, e);
        }
    }

}
