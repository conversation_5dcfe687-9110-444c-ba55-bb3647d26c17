// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AiAgentTask;
import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.entity.AiAgentToolAssociation;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GuidUtil;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.TASK;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.VERSION;

/**
 * <AUTHOR>
 */

@Mapper(uses = {AiAgentToolAssociationMapper.class}, imports = {GuidUtil.class, AuditUtilImpl.class})

public interface AiAgentToolAssociationMapper {
    AiAgentToolAssociationMapper TOOL_ASSOC_MAPPER = Mappers.getMapper(AiAgentToolAssociationMapper.class);

    default AiAgentToolAssociation associateWithVersion(AiAgentVersion version, AiAgentTool tool, String status) {
        AiAgentToolAssociation association = new AiAgentToolAssociation();
        association.setGuid(GuidUtil.createToolAssociationGuid());
        association.setTool(tool);
        association.setRelatedEntityUid(version.getUid());
        association.setRelatedEntityType(VERSION);
        association.setAssociationStatus(status);
        return association;
    }

    default AiAgentToolAssociation associateWithTask(AiAgentTask task, AiAgentTool tool) {
        AiAgentToolAssociation association = new AiAgentToolAssociation();
        association.setGuid(GuidUtil.createToolAssociationGuid());
        association.setTool(tool);
        association.setRelatedEntityUid(task.getUid());
        association.setRelatedEntityType(TASK);
        return association;
    }

}
