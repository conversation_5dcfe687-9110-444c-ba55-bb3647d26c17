// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AiAgentGuardrail;
import com.boomi.aiagentregistry.entity.AiAgentGuardrailAssociation;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */

@Mapper(uses = {AiAgentGuardrailAssociationMapper.class}, imports = {GuidUtil.class, AuditUtilImpl.class})

public interface AiAgentGuardrailAssociationMapper {

    AiAgentGuardrailAssociationMapper GRD_RL_ASSOC = Mappers.getMapper(AiAgentGuardrailAssociationMapper.class);

    default AiAgentGuardrailAssociation associateWithVersion(AiAgentVersion entity, AiAgentGuardrail gr) {
        AiAgentGuardrailAssociation association = new AiAgentGuardrailAssociation();
        association.setGuid(GuidUtil.createAIAgentGuardrailAssociationGuid());
        association.setGuardrail(gr);
        association.setRelatedEntityUid(entity.getUid());
        association.setRelatedEntityType(AiRegistryEntityType.VERSION);
        return association;
    }


}
