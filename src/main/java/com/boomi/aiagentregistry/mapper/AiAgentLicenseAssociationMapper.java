// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AiAgentRegistryLicense;
import com.boomi.aiagentregistry.entity.AiAgentRegistryLicenseAssociation;
import com.boomi.graphql.server.schema.types.AiAgentRegistryLicenseIdpAccountAssociation;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(uses = { AiAgentRegistryLicenseAssociation.class})
public interface AiAgentLicenseAssociationMapper {

    AiAgentLicenseAssociationMapper AI_AGENT_LICENSE_ASSOCIATION_MAPPER
            = Mappers.getMapper(AiAgentLicenseAssociationMapper.class);

    /**
     * Maps from JPA entity to GraphQL type
     *
     * @param input                  the JPA entity
     * @param aiAgentRegistryLicense
     * @return the GraphQL type
     */
    AiAgentRegistryLicenseIdpAccountAssociation toAiAgentRegistryLicenseIdpAccountAssociationType(
            AiAgentRegistryLicenseAssociation updatedAssociation, AiAgentRegistryLicense aiAgentRegistryLicense);
}
