// Copyright (c) 2025 Boom<PERSON>, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AiAgentRegistryLicense;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(uses = { AiAgentRegistryLicense.class})
public interface AiAgentLicenseMapper {

    AiAgentLicenseMapper AI_AGENT_LICENSE_MAPPER = Mappers.getMapper(AiAgentLicenseMapper.class);

    /**
     * Maps from JPA entity to GraphQL type
     * @param input the JPA entity
     * @return the GraphQL type
     */
    com.boomi.graphql.server.schema.types.AiAgentRegistryLicense toAiAgentRegistryLicenseType(
            com.boomi.aiagentregistry.entity.AiAgentRegistryLicense input);


}
