// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AiAgentTask;
import com.boomi.aiagentregistry.entity.AiAgentTaskAssociation;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GuidUtil;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.VERSION;

@Mapper(uses = { AiAgentTaskAssociationMapper.class}, imports = {GuidUtil.class, AuditUtilImpl.class})

public interface AiAgentTaskAssociationMapper {
    AiAgentTaskAssociationMapper TASK_ASSOCIATION_MAPPER = Mappers.getMapper(AiAgentTaskAssociationMapper.class);

    default AiAgentTaskAssociation associateWithVersion(AiAgentVersion version, AiAgentTask task) {
        AiAgentTaskAssociation association = new AiAgentTaskAssociation();
        association.setGuid(GuidUtil.createTaskAssociationGuid());
        association.setTask(task);
        association.setRelatedEntityUid(version.getUid());
        association.setRelatedEntityType(VERSION);
        return association;
    }

}
