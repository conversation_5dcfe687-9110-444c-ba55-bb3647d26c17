// Copyright (c) 2025 Boom<PERSON>, LP.
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AgentEntitySyncLatest;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 */
@Mapper(uses = { AgentEntitySyncLatest.class }, imports = { AiRegistryEntitySyncStatus.class })
public interface AiAgentSyncLatestMapper {

    AiAgentSyncLatestMapper AI_AGENT_ALIAS_SYNC_LATEST_MAPPER =
            org.mapstruct.factory.Mappers.getMapper(AiAgentSyncLatestMapper.class);

    @Mapping(target = "lastSyncStartDate", source = "syncStartDate")
    @Mapping(target = "lastSyncEndDate", source = "syncEndDate")
    @Mapping(target = "lastSyncStatus",
            expression = "java(AiRegistryEntitySyncStatus.fromString(syncLatest.getSyncStatus().toString()))")
    com.boomi.graphql.server.schema.types.AiRegistryEntitySyncData toAiRegistrySyncData(
            AgentEntitySyncLatest syncLatest);
}
