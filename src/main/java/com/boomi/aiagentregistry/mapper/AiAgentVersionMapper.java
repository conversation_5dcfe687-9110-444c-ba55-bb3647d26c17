// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import software.amazon.awssdk.services.bedrockagent.model.Agent;
import software.amazon.awssdk.services.bedrockagent.model.AgentVersion;
import software.amazon.awssdk.services.bedrockagent.model.AgentVersionSummary;
import com.boomi.aiagentregistry.constant.ApplicationConstant;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.model.AiAgentVersionEnableAuditLogChangeSetV1;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GeneralUtil;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.StringUtil;
import com.boomi.gardenagents.model.AgentSummary;
import com.boomi.graphql.server.schema.types.AiAgentAlias;
import com.boomi.graphql.server.schema.types.AiAgentCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentRegistryTrustLevel;
import com.boomi.graphql.server.schema.types.AiAgentUpdateInput;
import com.boomi.graphql.server.schema.types.AiAgentVersionEnableInput;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.sql.Timestamp;
import java.util.Set;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.AGENT_VERSION_STATUS_DRAFT;


/**
 * <AUTHOR>
 */

@Mapper(
        uses = {AiAgentVersion.class, GeneralUtil.class},
        imports = {AuditUtilImpl.class, GuidUtil.class, StringUtil.class, ApplicationConstant.class,
                AiAgentRegistryTrustLevel.class})
public interface AiAgentVersionMapper {
    AiAgentVersionMapper AI_AGENT_VERSION_MAPPER = org.mapstruct.factory.Mappers.getMapper(AiAgentVersionMapper.class);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "versionString", source = "input.agentVersion")
    @Mapping(target = "name", source = "input.agentName")
    @Mapping(target = "description", source = "input.agentDescription")
    @Mapping(target = "personalityTraits", source = "input.personalityTraitsJson")
    @Mapping(target = "agentStatus", source = "input.agentStatus")
    @Mapping(target = "uid", ignore = true)
    @Mapping(target = "guid",
            expression = "java(com.boomi.aiagentregistry.util.GuidUtil.createAIAgentVersionGuid())")
    @Mapping(target = "agent", source = "existingAgent")
    @Mapping(
            target = "createdTime",
            expression = "java(com.boomi.aiagentregistry.util.AuditUtilImpl.getCurrentTime())"
    )
    @Mapping(
            target = "modifiedTime",
            expression = "java(com.boomi.aiagentregistry.util.AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "trustLevel", source = "input.trustLevel", defaultValue = "UNENDORSED")
    @Mapping(target = "isDeleted", constant = "false")
    AiAgentVersion toEntityFromAgentVersionCreate(AiAgentCreateInput input, AiAgent existingAgent);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, ignoreByDefault = true)
    @Mapping(target = "versionString", expression = "java(input.getAgentVersion())")
    @Mapping(target = "name", source = "input.agentName")
    @Mapping(target = "description", source = "input.agentDescription")
    @Mapping(target = "personalityTraits", source = "input.personalityTraitsJson")
    @Mapping(target = "agentStatus", source = "input.agentStatus")
    @Mapping(target = "region", source = "input.region")
    @Mapping(target = "purpose", source = "input.purpose")
    @Mapping(target = "trustLevel", source = "input.trustLevel")
    @Mapping(
            target = "modifiedTime",
            expression = "java(com.boomi.aiagentregistry.util.AuditUtilImpl.getCurrentTime())")
    AiAgentVersion toEntityFromAgentVersionUpdate(AiAgentUpdateInput input,
                                                  @MappingTarget AiAgentVersion existingAgentVersion);

    @Mapping(target = "id", source = "input.guid")
    @Mapping(target = "version", source = "input.versionString")
    @Mapping(target = "auditData.createdByUserId", source = "createdByUserId")
    @Mapping(target = "auditData.createdTime", source = "createdTime")
    @Mapping(target = "auditData.modifiedByUserId", source = "modifiedByUserId")
    @Mapping(target = "auditData.modifiedTime", source = "modifiedTime")
    @Mapping(target = "auditData.updatedByOrigin", source = "updatedByOrigin")
    @Mapping(target = "auditData.updatedAtProviderTime", source = "updatedAtProviderTime")
    com.boomi.graphql.server.schema.types.AiAgentVersion toAiAgentVersion(AiAgentVersion input);

    @Mapping(target = "id", source = "input.guid")
    @Mapping(target = "version", source = "input.versionString")
    @Mapping(target = "agentAliases", source = "aliases")
    @Mapping(target = "auditData.createdByUserId", source = "input.createdByUserId")
    @Mapping(target = "auditData.createdTime", source = "input.createdTime")
    @Mapping(target = "auditData.modifiedByUserId", source = "input.modifiedByUserId")
    @Mapping(target = "auditData.modifiedTime", source = "input.modifiedTime")
    @Mapping(target = "auditData.updatedByOrigin", source = "input.updatedByOrigin")
    @Mapping(target = "auditData.updatedAtProviderTime", source = "input.updatedAtProviderTime")
    com.boomi.graphql.server.schema.types.AiAgentVersion toAiAgentVersion(AiAgentVersion input,
                                                                          Set<AiAgentAlias> aliases);

    @BeanMapping(ignoreByDefault = true)

    //identity
    @Mapping(target = "guid", expression = "java(GuidUtil.createAIAgentVersionGuid())")
    @Mapping(target = "externalId", expression = "java(agent.getExternalId())")
    //relationships
    @Mapping(target = "agent", source = "agent")

    // Status fields
    @Mapping(target = "isDeleted", constant = "false")
    @Mapping(target = "createdInRegistry", constant = "false")

    // Audit fields
    @Mapping(target = "createdTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "updatedAtProviderTime",
            expression = "java(GeneralUtil.tmStmpFromInst(versionSummary.updatedAt()))")
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")

    //other fields
    @Mapping(target = "name", expression = "java(versionSummary.agentName())")
    @Mapping(target = "versionString", expression = "java(versionSummary.agentVersion())")
    @Mapping(target = "trustLevel", constant = "UNENDORSED")
    AiAgentVersion toEntityFromAwsVersionSummary(
            AgentVersionSummary versionSummary,
            AiAgent agent,
            AiAgentProviderAccount aiAgentRegistryAccount);

    @BeanMapping(ignoreByDefault = true)

    // Audit fields
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "updatedAtProviderTime",
            expression = "java(GeneralUtil.tmStmpFromInst(awsVersion.updatedAt()))")
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")
    @Mapping(target = "createdByOrigin", constant = "PROVIDER")

    // Status fields
    @Mapping(target = "agentStatus", expression = "java(awsVersion.agentStatusAsString())")

    // Content fields
    @Mapping(target = "versionString", expression = "java(awsVersion.version())")
    @Mapping(target = "name", expression = "java(awsVersion.agentName())")
    @Mapping(target = "description", expression = "java(awsVersion.description())")
    void toEntityFromAwsAgentVersion(
            @MappingTarget AiAgentVersion existingVersion,
            AgentVersion awsVersion,
            AiAgentProviderAccount aiAgentRegistryAccount);

    @BeanMapping(ignoreByDefault = true)
    //identity
    @Mapping(target = "guid", source = "guid")
    @Mapping(target = "externalId", expression = "java(agent.getExternalId())")
    //relationships
    @Mapping(target = "agent", source = "agent")
    @Mapping(target = "isDeleted", constant = "false")
    @Mapping(target = "createdInRegistry", constant = "false")

    // Audit fields
    @Mapping(target = "createdTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")
    @Mapping(target = "createdByOrigin", constant = "PROVIDER")
    @Mapping(target = "updatedAtProviderTime",
            expression = "java(GeneralUtil.tmStmpFromString(versionSummary.getLastUpdatedOn()))")
    @Mapping(target = "agentStatus", expression = "java(versionSummary.getAgentStatus())")
    @Mapping(target = "name", expression = "java(versionSummary.getName())")
    @Mapping(target = "purpose", expression = "java(versionSummary.getObjective())")
    @Mapping(target = "trustLevel", constant = "UNENDORSED")
    AiAgentVersion toEntityFromGardenVersionSummary(AgentSummary versionSummary, String guid,
                                                    AiAgent agent, AiAgentProviderAccount providerAccount);

    // Audit fields
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "createdTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "updatedAtProviderTime", source = "updatedAtProviderTime")
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")
    @Mapping(target = "createdByOrigin", constant = "PROVIDER")

    @Mapping(target = "agentStatus", expression = "java(versionSummary.getAgentStatus())")
    @Mapping(target = "name", expression = "java(versionSummary.getName())")
    @Mapping(target = "purpose", expression = "java(versionSummary.getObjective())")
    void toUpdatedEntityFromGardenAgentVersion(
            @MappingTarget AiAgentVersion existingVersion,
            AgentSummary versionSummary,
            Timestamp updatedAtProviderTime);

    default AgentVersion fromAwsAgent(Agent agent) {
        return AgentVersion.builder()
                .agentId(agent.agentId())
                .agentName(agent.agentName())
                .description(agent.description())
                .agentStatus(agent.agentStatusAsString())
                .updatedAt(agent.updatedAt())
                .instruction(agent.instruction())
                .version(AGENT_VERSION_STATUS_DRAFT)
                .agentStatus(agent.agentStatus())
                .updatedAt(agent.updatedAt())
                .foundationModel(agent.foundationModel())
                .guardrailConfiguration(agent.guardrailConfiguration())
                .build();
    }

    AiAgentVersionEnableAuditLogChangeSetV1 toChangeSetV1(AiAgentVersionEnableInput input, String externalId);

    default Object toAuditLogChangeSet(AiAgentVersionEnableInput input, String externalId, int version) {
        if (input == null) {
            return null;
        }

        return switch (version) {
            case 1 -> toChangeSetV1(input, externalId);
            default -> null;
        };
    }
}



