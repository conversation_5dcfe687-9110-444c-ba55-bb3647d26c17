// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTask;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GeneralUtil;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.gardenagents.model.Task;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentTaskStatus;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> Satpute
 */
@Mapper(uses = { AiAgentTask.class, GeneralUtil.class },
        imports = { AiAgentTaskStatus.class, AiAgentProviderType.class, AuditUtilImpl.class, GuidUtil.class,
                GeneralUtil.class })
public interface AiAgentTaskMapper {

    AiAgentTaskMapper AI_AGENT_TASK_MAPPER = Mappers.getMapper(AiAgentTaskMapper.class);

    @Mapping(target = "id", source = "guid")
    @Mapping(target = "version",
            expression = "java(GeneralUtil.combineVersion(aiAgentTaskEntity.getVersionString(), aiAgentTaskEntity.getVersionInt()))")
    com.boomi.graphql.server.schema.types.AiAgentTask toAIAgentTaskDto(AiAgentTask aiAgentTaskEntity);


    @Mapping(target = "guid", source = "aiAgentTaskGuid", defaultExpression = "java(GuidUtil.createAIAgentTaskGuid())")
    @Mapping(target = "aiAgentProviderAccount", source = "aiAgentProviderAccount")
    @Mapping(target = "idpAccountId", source = "aiAgentProviderAccount.idpAccountId")
    @Mapping(target = "externalId", source = "aiAgentTaskGuid")
    @Mapping(target = "uid", ignore = true)

    @Mapping(target = "createdTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")

    @Mapping(target = "name", expression = "java(inputTask.getName())")
    AiAgentTask toEntityFromGardenTask(Task inputTask, AiAgentProviderAccount aiAgentProviderAccount,
            String aiAgentTaskGuid);
}
