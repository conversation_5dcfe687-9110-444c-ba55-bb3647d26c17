// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import org.mapstruct.Named;

@Named("Translators")
public class Translators {

    private Translators() {}

    @Named("mapVersionToString")
    public static String mapVersionToString(String version) {
        if (version == null) {
            return null;
        }
        try {
            Integer.parseInt(version);
            // If it's a valid number, return null for versionString
            return null;
        } catch (NumberFormatException e) {
            // If it's not a number, return the string value
            return version;
        }
    }

    @Named("mapVersionToInt")
    public static Integer mapVersionToInt(String version) {
        if (version == null) {
            return null;
        }
        try {
            // If it's a valid number, return the integer value
            return Integer.parseInt(version);
        } catch (NumberFormatException e) {
            // If it's not a number, return null for versionInt
            return null;
        }
    }
}
