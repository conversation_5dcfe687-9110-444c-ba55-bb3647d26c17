// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.service.sync.SyncContextAccessor;
import com.boomi.aiagentregistry.service.sync.boomigarden.GardenSyncContextAccessor;
import com.boomi.aiagentregistry.service.sync.model.SyncAudit;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(uses = {SyncUserAuditMapper.class}, imports = { AuditUtilImpl.class, GuidUtil .class})
public interface SyncUserAuditMapper {

    SyncUserAuditMapper SYNC_USER_AUDIT_MAPPER = org.mapstruct.factory.Mappers.getMapper(SyncUserAuditMapper.class);

    @Mapping(target = "guid",
            expression = "java(com.boomi.aiagentregistry.util.GuidUtil.createSyncUserAuditGuidAsUUID())")
    @Mapping(target = "syncStatus", constant = "IN_QUEUE")
    @Mapping(target = "syncStartDate", expression = "java(AuditUtilImpl.getCurrentTime())")
    SyncUserAudit toSyncUserAuditEntityStart(SyncAudit syncAudit);

    @Mapping(target = "guid",
            expression = "java(com.boomi.aiagentregistry.util.GuidUtil.createSyncUserAuditGuidAsUUID())")
    @Mapping(target = "syncEndDate", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "syncStatus", source = "newSyncStatus")
    @Mapping(target = "syncNumberOfEntitiesAdded", source = "counts.added")
    @Mapping(target = "syncNumberOfEntitiesUpdated", source = "counts.updated")
    @Mapping(target = "syncNumberOfEntitiesRemoved", source = "counts.deleted")
    @Mapping(target = "content", source = "syncDetails")
    SyncUserAudit toSyncUserAuditEntityComplete(SyncUserAudit existingSyncUserAudit,
            AiRegistryEntitySyncStatus newSyncStatus,
            SyncContextAccessor.EntityCounts counts,
            String syncDetails);

    @Mapping(target = "guid",
            expression = "java(com.boomi.aiagentregistry.util.GuidUtil.createSyncUserAuditGuidAsUUID())")
    @Mapping(target = "syncEndDate", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "syncStatus", source = "newSyncStatus")
    @Mapping(target = "syncNumberOfEntitiesAdded", source = "counts.added")
    @Mapping(target = "syncNumberOfEntitiesUpdated", source = "counts.updated")
    @Mapping(target = "syncNumberOfEntitiesRemoved", source = "counts.deleted")
    @Mapping(target = "content", source = "syncDetails")
    SyncUserAudit toSyncUserAuditEntityComplete(SyncUserAudit existingSyncUserAudit,
            AiRegistryEntitySyncStatus newSyncStatus, GardenSyncContextAccessor.EntityCounts counts,
            String syncDetails);

    @Mapping(target = "guid",
            expression = "java(com.boomi.aiagentregistry.util.GuidUtil.createSyncUserAuditGuidAsUUID())")
    @Mapping(target = "syncEndDate", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "content", source = "error")
    SyncUserAudit toSyncUserAuditEntityFailure(SyncUserAudit existingSyncUserAudit, String error);
}

