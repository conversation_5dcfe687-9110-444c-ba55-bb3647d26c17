// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AiAgentAlias;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GeneralUtil;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.graphql.server.schema.types.AiAgentRegistryTrustLevel;
import com.boomi.gardenagents.model.AgentSummary;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import software.amazon.awssdk.services.bedrockagent.model.AgentAlias;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(
        uses = {AiAgentAlias.class},
        imports = {AuditUtilImpl.class, GuidUtil.class, GeneralUtil.class, AiAgentRegistryTrustLevel.class})
public interface AiAgentAliasMapper {
    AiAgentAliasMapper ALIAS_MAPPER = org.mapstruct.factory.Mappers.getMapper(AiAgentAliasMapper.class);

    @BeanMapping(ignoreByDefault = true)

    // Content fields
    @Mapping(target = "name", expression = "java(awsAlias.agentAliasName())")
    @Mapping(target = "description", expression = "java(awsAlias.description())")
    @Mapping(target = "agentVersion", source = "version")

    // Audit fields
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "updatedAtProviderTime",
            expression = "java(GeneralUtil.tmStmpFromInst(awsAlias.updatedAt()))")
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")
    // Status fields
    @Mapping(target = "isDeleted", constant = "false")
    void toEntityFromAwsAgentAliasUpdate(
            @MappingTarget AiAgentAlias existingAlias,
            AgentAlias awsAlias,
            AiAgentVersion version);


    @BeanMapping(ignoreByDefault = true)

    // Identity fields
    @Mapping(target = "guid", expression = "java(GuidUtil.createAIAgentAliasGuid())")
    @Mapping(target = "externalId", expression = "java(awsAlias.agentAliasId())")

    // Content fields
    @Mapping(target = "name", expression = "java(awsAlias.agentAliasName())")
    @Mapping(target = "description", expression = "java(awsAlias.description())")

    // Audit fields
    @Mapping(target = "createdTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "updatedAtProviderTime",
            expression = "java(GeneralUtil.tmStmpFromInst(awsAlias.updatedAt()))")
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")

    // Relationships
    @Mapping(target = "agentVersion", source = "version")

    // Status fields
    @Mapping(target = "isDeleted", constant = "false")
    @Mapping(target = "trustLevel", constant = "UNENDORSED")
    AiAgentAlias toEntityFromAwsAgentAliasCreate(
            AgentAlias awsAlias,
            AiAgentVersion version,
            AiAgentProviderAccount account
    );

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "guid", expression = "java(GuidUtil.createAIAgentAliasGuid())")
    @Mapping(target = "externalId", expression = "java(version.getExternalId())")
    @Mapping(target = "name", expression = "java(aliasSummary.getName())")
    @Mapping(target = "createdTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")
    @Mapping(target = "agentVersion", source = "version")
    @Mapping(target = "isDeleted", constant = "false")
    AiAgentAlias toEntityFromGardenAgentAliasCreate(
            AgentSummary aliasSummary,
            AiAgentVersion version,
            AiAgentProviderAccount account
    );

    @Mapping(target = "auditData.createdByUserId", source = "createdByUserId")
    @Mapping(target = "auditData.createdTime", source = "createdTime")
    @Mapping(target = "auditData.modifiedByUserId", source = "modifiedByUserId")
    @Mapping(target = "auditData.modifiedTime", source = "modifiedTime")
    @Mapping(target = "auditData.updatedByOrigin", source = "updatedByOrigin")
    @Mapping(target = "auditData.updatedAtProviderTime", source = "updatedAtProviderTime")
    @Mapping(target = "id", source = "guid")
    com.boomi.graphql.server.schema.types.AiAgentAlias toAiAgentAlias(AiAgentAlias aiAgentAlias);

    List<com.boomi.graphql.server.schema.types.AiAgentAlias>
    toAiAgentAliasList(List<AiAgentAlias> aiAgentAliases);
}


