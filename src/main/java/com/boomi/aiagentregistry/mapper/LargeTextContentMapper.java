// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AiAgentLargeTextContent;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GuidUtil;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */

@Mapper(uses = {LargeTextContentMapper.class}, imports = {GuidUtil.class, AuditUtilImpl.class})

public interface LargeTextContentMapper {

    LargeTextContentMapper LRG_TXT_MAPPER = Mappers.getMapper(LargeTextContentMapper.class);


    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "relatedEntityUid", source = "entityId")
    @Mapping(target = "relatedEntityType", source = "entityType")
    @Mapping(target = "content", source = "content")
    AiAgentLargeTextContent createLargeTextContent(Integer entityId, String entityType, String content);

}
