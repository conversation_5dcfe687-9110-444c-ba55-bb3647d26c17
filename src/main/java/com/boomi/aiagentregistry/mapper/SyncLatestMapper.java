// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AgentEntitySyncLatest;
import com.boomi.aiagentregistry.entity.SyncUserAudit;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 */

@Mapper(uses = {SyncLatestMapper.class})

public interface SyncLatestMapper {

    SyncLatestMapper SYNC_LATEST_MAPPER = org.mapstruct.factory.Mappers.getMapper(SyncLatestMapper.class);

    default AgentEntitySyncLatest updateExistingSync(AgentEntitySyncLatest existingSync, AgentEntitySyncLatest newSync){
        if (newSync == null) {
            return existingSync;
        }
        existingSync.setSyncStatus(newSync.getSyncStatus());
        existingSync.setSyncStartDate(newSync.getSyncStartDate());
        existingSync.setSyncEndDate(newSync.getSyncEndDate());
        return existingSync;
    }

    @Mapping(target = "syncedEntityUid", source = "entityUid")
    @Mapping(target = "syncedEntityType", source = "entityType")
    @Mapping(target = "uid", ignore = true)
    AgentEntitySyncLatest toSyncLatest(SyncUserAudit syncAudit);
}

