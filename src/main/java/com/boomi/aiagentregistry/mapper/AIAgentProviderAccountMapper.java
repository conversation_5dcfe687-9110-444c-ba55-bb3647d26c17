// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AgentEntitySyncLatest;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.graphql.server.schema.types.AiAgent;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountCreateInput;
import com.boomi.graphql.server.schema.types.AiRegistryEntityAuditData;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncData;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus;

import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ValueMapping;
import org.mapstruct.ValueMappings;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

@Mapper(uses = {AiAgentProviderAccount.class},
        imports = {AiRegistryEntitySyncStatus.class})
public interface AIAgentProviderAccountMapper {

    AIAgentProviderAccountMapper PROVIDER_MAPPER = Mappers.getMapper(AIAgentProviderAccountMapper.class);

    @InheritInverseConfiguration
    @Mapping(target = "providerMetadata", source = "metadataJson")
    AiAgentProviderAccount toAiAgentProviderAccountEntity(AiAgentProviderAccountCreateInput input);

    @InheritInverseConfiguration
    @Mapping(target = "metadataJson", source = "providerMetadata")
    @Mapping(target = "id", source = "guid")
    @Mapping(target = "idpAccountId", source = "idpAccountId")
    @Mapping(source = "latestSync", target = "syncData")
    @Mapping(target = "auditData.createdByUserId", source = "createdByUserId")
    @Mapping(target = "auditData.createdTime", source = "createdTime")
    @Mapping(target = "auditData.modifiedByUserId", source = "modifiedByUserId")
    @Mapping(target = "auditData.modifiedTime", source = "modifiedTime")
    com.boomi.graphql.server.schema.types.AiAgentProviderAccount toAiAgentProviderAccount(
            AiAgentProviderAccount aiAgentProviderEntity);

    @InheritInverseConfiguration
    @Mapping(target = "metadataJson", source = "aiAgentProviderEntity.providerMetadata")
    @Mapping(target = "id", source = "aiAgentProviderEntity.guid")
    @Mapping(target = "idpAccountId", source = "aiAgentProviderEntity.idpAccountId")
    @Mapping( target = "syncData", source = "aiAgentProviderEntity.latestSync")
    @Mapping(target = "agents", source = "aiAgents")
    com.boomi.graphql.server.schema.types.AiAgentProviderAccount toAiAgentProviderAccountWithAgents(
            AiAgentProviderAccount aiAgentProviderEntity, List<AiAgent> aiAgents);

    @InheritInverseConfiguration
    @Mapping(target = "metadataJson", source = "providerMetadata")
    @Mapping(target = "id", source = "guid")
    @Mapping(target = "idpAccountId", source = "idpAccountId")
    @Mapping(source = "latestSync", target = "syncData")
    List<com.boomi.graphql.server.schema.types.AiAgentProviderAccount> toAIAgentProviderList(
            List<AiAgentProviderAccount> entities, @Context Map<String, Integer> guidToAgentCount);

    @Mapping(target = "lastSyncStatus", expression =
            "java(AiRegistryEntitySyncStatus.fromString(agentEntitySyncLatest.getSyncStatus().toString()))")
    @Mapping(target = "lastSyncStartDate", source = "syncStartDate")
    @Mapping(target = "lastSyncEndDate", source = "syncEndDate")
    @ValueMappings({@ValueMapping(source = "PARTIALLY_COMPLETED", target = "COMPLETED_WITH_ERROR")})
    AiRegistryEntitySyncData agentEntitySyncLatestToAiRegistryEntitySyncData(
            AgentEntitySyncLatest agentEntitySyncLatest);

   AiRegistryEntityAuditData providerAccountAuditData(AiAgentProviderAccount providerAccount);

    @AfterMapping
    default void setAgentCounts(
            List<AiAgentProviderAccount> source,
            @MappingTarget List<com.boomi.graphql.server.schema.types.AiAgentProviderAccount> aiAgentProviderAccounts,
            @Context Map<String, Integer> guidToAgentCount) {
        if (aiAgentProviderAccounts != null && guidToAgentCount != null) {
            aiAgentProviderAccounts.forEach(account -> {
                String guid = account.getId();
                Integer agentCount = guidToAgentCount.getOrDefault(guid, 0);
                account.setNumberOfAgents(agentCount);
            });
        }
    }
}
