// Copyright (c) 2024 Boomi, LP

package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AiAgentGuardrail;
import com.boomi.aiagentregistry.entity.AiAgentLlm;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GeneralUtil;
import com.boomi.aiagentregistry.util.GuidUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import software.amazon.awssdk.services.bedrock.model.FoundationModelDetails;

@Mapper(uses = {AiAgentGuardrail.class, GeneralUtil.class},
        imports = {AuditUtilImpl.class, GuidUtil.class})
public interface AiAgentLlmMapper {
    AiAgentLlmMapper LLM_MAPPER = org.mapstruct.factory.Mappers.getMapper(AiAgentLlmMapper.class);


    @BeanMapping(ignoreByDefault = true)
    //identity
    @Mapping(target = "guid", expression = "java(GuidUtil.createLlmGuid())")
    @Mapping(target = "externalId", expression = "java(awsModel.modelId())")
    //audit
    @Mapping(target = "versionString", expression = "java(extractVersion(awsModel.modelId()))")

    //relationships
    @Mapping(target = "providerAccount", source = "account")
    @Mapping(target = "idpAccountId", expression = "java(account.getIdpAccountId())")

    //other properties including name, desc
    @Mapping(target = "name", expression = "java(awsModel.modelName())")
    @Mapping(target = "description", expression = "java(awsModel.toString())")
    AiAgentLlm toEntityFrmAwsModel(FoundationModelDetails awsModel, AiAgentProviderAccount account);

    default com.boomi.graphql.server.schema.types.AiAgentLlm toAIAgentLlmDto(AiAgentLlm entity) {
        com.boomi.graphql.server.schema.types.AiAgentLlm aiAgentLlmDto = new
                com.boomi.graphql.server.schema.types.AiAgentLlm();
        aiAgentLlmDto.setVersion(GeneralUtil.combineVersion(entity.getVersionString(), entity.getVersionInt()));
        aiAgentLlmDto.setName(entity.getName());
        aiAgentLlmDto.setDescription(entity.getDescription());
        aiAgentLlmDto.setId(entity.getGuid());
        aiAgentLlmDto.setExternalId(entity.getExternalId());
        return aiAgentLlmDto;
    }

    default String extractVersion(String modelIdentifier) {
        if (modelIdentifier == null || !modelIdentifier.contains(":")) {
            return StringUtils.EMPTY;
        }
        return modelIdentifier.substring(modelIdentifier.lastIndexOf(':') + 1);
    }

}
