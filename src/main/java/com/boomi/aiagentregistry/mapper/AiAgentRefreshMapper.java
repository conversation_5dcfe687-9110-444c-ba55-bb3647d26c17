// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.ViewRefreshTracker;
import com.boomi.graphql.server.schema.types.AiAgentListingRefreshTracker;

import org.mapstruct.Mapper;

@Mapper(uses = { ViewRefreshTracker.class})
public interface AiAgentRefreshMapper {
    AiAgentRefreshMapper AI_AGENT_REFRESH_MAPPER = org.mapstruct.factory.Mappers.getMapper(AiAgentRefreshMapper.class);

    AiAgentListingRefreshTracker toAiAgentListingRefreshTrackerType(ViewRefreshTracker input);

}
