// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.graphql.server.schema.types.AiAgentFilterLookup;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Set;

@Mapper(uses = { AiAgentFilterDropdownMapper.class })
public interface AiAgentFilterDropdownMapper {

    AiAgentFilterDropdownMapper DROP_DOWN_FILTER_MAPPER = Mappers.getMapper(AiAgentFilterDropdownMapper.class);

    @Mapping(target = "guid", source = "guid")
    @Mapping(target = "name", source = "name")
    com.boomi.graphql.server.schema.types.AiAgentFilterLookup toGraphQLTypeFilter(
            com.boomi.aiagentregistry.model.AiAgentFilterDropdown filter);

    // Method to convert the entire list
    List<com.boomi.graphql.server.schema.types.AiAgentFilterLookup> toGraphQLTypeList(
            List<com.boomi.aiagentregistry.model.AiAgentFilterDropdown> filters);


    Set<AiAgentFilterLookup> toGraphQLTypeSet(
            Set<com.boomi.aiagentregistry.model.AiAgentFilterDropdown> filters);
}
