// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AiAgentTag;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(uses = { AiAgentTag.class })
public interface AiAgentTagMapper {
    AiAgentTagMapper AI_AGENT_TAG_MAPPER = org.mapstruct.factory.Mappers.getMapper(AiAgentTagMapper.class);

    @Mapping(target = "id", source = "guid")
    com.boomi.graphql.server.schema.types.AiAgentTag toAIAgentTag(AiAgentTag entity);

    List<com.boomi.graphql.server.schema.types.AiAgentTag> toAIAgentTagList(List<AiAgentTag> entity);
}
