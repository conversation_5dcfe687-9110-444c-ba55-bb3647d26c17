// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.mapper;

import software.amazon.awssdk.services.bedrockagent.model.AgentActionGroup;
import software.amazon.awssdk.services.bedrockagent.model.KnowledgeBase;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.entity.AiAgentToolResource;
import com.boomi.aiagentregistry.exception.SyncException;
import com.boomi.aiagentregistry.model.AiAgentToolAuditLogChangeSetV1;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GeneralUtil;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.gardenagents.model.ToolDetail;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentToolCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentToolResourceInput;
import com.boomi.graphql.server.schema.types.AiAgentToolStatus;
import com.boomi.graphql.server.schema.types.AiAgentToolType;
import com.fasterxml.jackson.core.JsonProcessingException;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.sql.Timestamp;

/**
 * <AUTHOR> Wang.
 */
@Mapper(uses = {AiAgentTool.class, GeneralUtil.class, Translators.class},
        imports = {GuidUtil.class, AuditUtilImpl.class, AiAgentToolStatus.class, AiAgentProviderType.class,
                GeneralUtil.class})
public interface AiAgentToolMapper {
    String FAILED_TO_PARSE_OBJECT_TO_JSON = "Failed to parse object to json";

    AiAgentToolMapper AI_AGENT_TOOL_MAPPER = Mappers.getMapper(AiAgentToolMapper.class);

    @Mapping(target = "versionString", source = "input.version",
            qualifiedByName = {"mapVersionToString"})
    @Mapping(target = "versionInt", source = "input.version", qualifiedByName = {"mapVersionToInt"})
    @Mapping(target = "guid", expression = "java(GuidUtil.createAIAgentToolGuid())")
    @Mapping(target = "aiAgentProviderAccount", source = "aiAgentProviderAccount")
    @Mapping(target = "idpAccountId", source = "aiAgentProviderAccount.idpAccountId")
    @Mapping(target = "uid", ignore = true)
    AiAgentTool toAIAgentToolEntityFromToolCreateInput(AiAgentToolCreateInput input,
                                                       AiAgentProviderAccount aiAgentProviderAccount);

    @Mapping(target = "updatedAtProviderTime", source = "updatedAtProviderTime")
    @Mapping(target = "createdTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "guid", expression = "java(GuidUtil.createAIAgentToolGuid())")
    @Mapping(target = "aiAgentProviderAccount", source = "aiAgentProviderAccount")
    @Mapping(target = "idpAccountId", source = "aiAgentProviderAccount.idpAccountId")
    @Mapping(target = "externalId", source = "gardenTool.id")
    @Mapping(target = "name", source = "gardenTool.name")
    @Mapping(target = "description", source = "gardenTool.description")
    @Mapping(target = "toolProvider", constant = "BOOMI")
    @Mapping(target = "toolType", source = "toolType")
    @Mapping(target = "status", source = "gardenTool.toolStatus")
    @Mapping(target = "toolJson", source = "gardenTool.toolJson")
    @Mapping(target = "uid", ignore = true)
    AiAgentTool toEntityFromGardenTool(ToolDetail gardenTool,
            AiAgentToolType toolType,
            Timestamp updatedAtProviderTime,
            AiAgentProviderAccount aiAgentProviderAccount);

    @BeanMapping(ignoreByDefault = true)
    //status
    @Mapping(target = "status", source = "gardenTool.toolStatus")
    //audit
    @Mapping(target = "updatedAtProviderTime", source = "updatedAtProviderTime")
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "toolProvider", constant = "BOOMI")

    //other info
    @Mapping(target = "name", source = "gardenTool.name")
    @Mapping(target = "description", source = "gardenTool.description")
    @Mapping(target = "toolJson", source = "gardenTool.toolJson")
    void toUpdatedEntityFromGardenTool(@MappingTarget AiAgentTool existingTool,
            ToolDetail gardenTool,
            Timestamp updatedAtProviderTime);

    @Mapping(target = "tool", source = "existingTool")
    @Mapping(target = "guid", source = "guid")
    @Mapping(target = "externalId", source = "guid")
    @Mapping(target = "name", source = "input.name")
    @Mapping(target = "description", source = "input.description")
    @Mapping(target = "resourceType", source = "input.resourceType")
    @Mapping(target = "toolResourceJson", source = "input.json")
    @Mapping(target = "status", source = "input.status")
    @Mapping(target = "uid", ignore = true)
    AiAgentToolResource toAiAgentToolResourceEntity(
            AiAgentToolResourceInput input,
            AiAgentTool existingTool,
            String guid);

    @Mapping(target = "version", expression = "java(GeneralUtil.combineVersion(aiAgentToolEntity.getVersionString(), "
            + "aiAgentToolEntity.getVersionInt()))")
    @Mapping(target = "id", source = "guid")
    @Mapping(target = "provider", source = "toolProvider")
    @Mapping(target = "type", source = "toolType")
    @Mapping(target = "json", source = "toolJson")
    @Mapping(target = "auditData.createdByUserId", source = "createdByUserId")
    @Mapping(target = "auditData.createdTime", source = "createdTime")
    @Mapping(target = "auditData.modifiedByUserId", source = "modifiedByUserId")
    @Mapping(target = "auditData.modifiedTime", source = "modifiedTime")
    @Mapping(target = "auditData.updatedByOrigin", source = "updatedByOrigin")
    @Mapping(target = "auditData.updatedAtProviderTime", source = "updatedAtProviderTime")
    com.boomi.graphql.server.schema.types.AiAgentTool toAIAgentToolDto(AiAgentTool aiAgentToolEntity);

    @Mapping(target = "version", expression = "java(GeneralUtil.combineVersion(aiAgentToolEntity.getVersionString(), "
            + "aiAgentToolEntity.getVersionInt()))")
    @Mapping(target = "id", source = "aiAgentToolEntity.guid")
    @Mapping(target = "provider", source = "aiAgentToolEntity.toolProvider")
    @Mapping(target = "type", source = "aiAgentToolEntity.toolType")
    @Mapping(target = "json", source = "aiAgentToolEntity.toolJson")
    @Mapping(target = "auditData.createdByUserId", source = "aiAgentToolEntity.createdByUserId")
    @Mapping(target = "auditData.createdTime", source = "aiAgentToolEntity.createdTime")
    @Mapping(target = "auditData.modifiedByUserId", source = "aiAgentToolEntity.modifiedByUserId")
    @Mapping(target = "auditData.modifiedTime", source = "aiAgentToolEntity.modifiedTime")
    @Mapping(target = "auditData.updatedByOrigin", source = "aiAgentToolEntity.updatedByOrigin")
    @Mapping(target = "auditData.updatedAtProviderTime", source = "aiAgentToolEntity.updatedAtProviderTime")
    @Mapping(target = "associationStatus", source = "associationStatus")
    com.boomi.graphql.server.schema.types.AiAgentTool toAIAgentToolDto(AiAgentTool aiAgentToolEntity,
            String associationStatus);

    @Mapping(target = "id", source = "guid")
    @Mapping(target = "json", source = "toolResourceJson")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "description", source = "description")
    @Mapping(target = "resourceType", source = "resourceType")
    @Mapping(target = "status", source = "status")
    com.boomi.graphql.server.schema.types.AiAgentToolResource toAiAgentToolResourceDto
            (AiAgentToolResource toolResource);

    AiAgentToolAuditLogChangeSetV1 toChangeSetV1(AiAgentToolCreateInput input);

    default Object toAuditLogChangeSet(AiAgentToolCreateInput input, int version) {
        if (input == null) {
            return null;
        }

        return switch (version) {
            case 1 -> toChangeSetV1(input);
            default -> null;
        };
    }

    @BeanMapping(ignoreByDefault = true)
    //identity
    @Mapping(target = "guid", expression = "java(GuidUtil.createAIAgentToolGuid())")
    @Mapping(target = "externalId", expression = "java(awsActionGroup.actionGroupId())")
    @Mapping(target = "idpAccountId", source = "aiAgentProviderAccount.idpAccountId")
    //status
    @Mapping(target = "status", expression = "java(awsActionGroup.actionGroupState().toString())")
    //audit
    @Mapping(target = "updatedAtProviderTime",
            expression = "java(GeneralUtil.tmStmpFromInst(awsActionGroup.updatedAt()))")
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")
    @Mapping(target = "createdTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "toolProvider", constant = "AWS_BEDROCK")
    //relationships
    @Mapping(target = "aiAgentProviderAccount", source = "aiAgentProviderAccount")
    //other info
    @Mapping(target = "name", expression = "java(awsActionGroup.actionGroupName())")
    @Mapping(target = "description", expression = "java(awsActionGroup.description())")
    @Mapping(target = "toolType", constant = "AWS_ACTION_GROUP")
    @Mapping(target = "toolJson", source = "awsActionGroup", qualifiedByName = "parseActionGroup")
    AiAgentTool fromAwsActionGroupCreate(AgentActionGroup awsActionGroup,
            AiAgentProviderAccount aiAgentProviderAccount);

    @BeanMapping(ignoreByDefault = true)
    //status
    @Mapping(target = "status", expression = "java(awsActionGroup.actionGroupState().toString())")
    //audit
    @Mapping(target = "updatedAtProviderTime",
            expression = "java(GeneralUtil.tmStmpFromInst(awsActionGroup.updatedAt()))")
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "toolProvider", constant = "AWS_BEDROCK")

    //other info
    @Mapping(target = "name", expression = "java(awsActionGroup.actionGroupName())")
    @Mapping(target = "description", expression = "java(awsActionGroup.description())")
    @Mapping(target = "toolJson", source = "awsActionGroup", qualifiedByName = "parseActionGroup")
    void fromAwsActionGroupUpdate(@MappingTarget AiAgentTool existingTool, AgentActionGroup awsActionGroup);


    @BeanMapping(ignoreByDefault = true)
    //status
    @Mapping(target = "status", expression = "java(kb.status().toString())")
    @Mapping(target = "aiAgentProviderAccount", source = "account")
    //audit
    @Mapping(target = "updatedAtProviderTime", expression = "java(GeneralUtil.tmStmpFromInst(kb.updatedAt()))")
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "toolProvider", constant = "AWS_BEDROCK")
    //other info
    @Mapping(target = "description", expression = "java(kb.description())")
    @Mapping(target = "name", expression = "java(kb.name())")
    @Mapping(target = "toolJson", source = "kb", qualifiedByName = "parseKnowledgebase")
    void fromKnowledgeBaseUpdate(@MappingTarget AiAgentTool existingTool, KnowledgeBase kb, AiAgentProviderAccount account);

    @BeanMapping(ignoreByDefault = true)
    //identity
    @Mapping(target = "guid", expression = "java(GuidUtil.createAIAgentToolGuid())")
    @Mapping(target = "externalId", expression = "java(kb.knowledgeBaseId())")
    @Mapping(target = "idpAccountId", source = "providerAccount.idpAccountId")
    //relationship
    @Mapping(target = "aiAgentProviderAccount", source = "providerAccount")
    @Mapping(target = "toolType", constant = "AWS_KNOWLEDGE_BASE")

    //audit
    @Mapping(target = "status", expression = "java(kb.status().toString())")
    @Mapping(target = "updatedAtProviderTime", expression = "java(GeneralUtil.tmStmpFromInst(kb.updatedAt()))")
    @Mapping(target = "updatedByOrigin", constant = "PROVIDER")
    @Mapping(target = "createdTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "modifiedTime", expression = "java(AuditUtilImpl.getCurrentTime())")
    @Mapping(target = "toolProvider", constant = "AWS_BEDROCK")

    //other fields
    @Mapping(target = "description", expression = "java(kb.description())")
    @Mapping(target = "name", expression = "java(kb.name())")
    @Mapping(target = "toolJson", source = "kb", qualifiedByName = "parseKnowledgebase")
    AiAgentTool fromKnowledgeBaseCreate(KnowledgeBase kb, AiAgentProviderAccount providerAccount);

    @Named("parseKnowledgebase")
    default String parseKnowledgebase(KnowledgeBase knowledgeBase) {
        if (knowledgeBase == null) {
            return null;
        }
        return deserialize(knowledgeBase.toBuilder());
    }

    @Named("parseActionGroup")
    default String parseActionGroup(AgentActionGroup actionGroup) {
        if (actionGroup == null) {
            return null;
        }
        return deserialize(actionGroup.toBuilder());
    }

    private String deserialize(Object obj) {
        try {
            return GeneralUtil.OBJECT_WRITER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new SyncException(FAILED_TO_PARSE_OBJECT_TO_JSON, e);
        }
    }
}
