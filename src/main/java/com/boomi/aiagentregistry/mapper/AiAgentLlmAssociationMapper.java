// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import com.boomi.aiagentregistry.entity.AiAgentLlm;
import com.boomi.aiagentregistry.entity.AiAgentLlmAssociation;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */

@Mapper(uses = {AiAgentLlmAssociationMapper.class}, imports = {GuidUtil.class, AuditUtilImpl.class})

public interface AiAgentLlmAssociationMapper {

    AiAgentLlmAssociationMapper LLM_ASSOC_MAPPER = Mappers.getMapper(AiAgentLlmAssociationMapper.class);

    default AiAgentLlmAssociation associateWithVersion(AiAgentVersion entity, AiAgentLlm llm) {
        AiAgentLlmAssociation association = new AiAgentLlmAssociation();
        association.setGuid(GuidUtil.createLlmAssociationGuid());
        association.setLlm(llm);
        association.setRelatedEntityUid(entity.getUid());
        association.setRelatedEntityType(AiRegistryEntityType.VERSION);
        return association;
    }

}
