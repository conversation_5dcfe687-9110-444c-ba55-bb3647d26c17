// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = { AiAgentStatusCountMapper.class })
public interface AiAgentStatusCountMapper {

    AiAgentStatusCountMapper AGENT_STATUS_COUNT_MAPPER = Mappers.getMapper(AiAgentStatusCountMapper.class);

    @Mapping(target = "status", source = "agentStatus")
    @Mapping(target = "count", source = "count")
    com.boomi.graphql.server.schema.types.AiAgentStatusCount toGraphQLType(
            com.boomi.aiagentregistry.model.AgentStatusCount entityStatusCount);

    // Method to convert the entire list
    List<com.boomi.graphql.server.schema.types.AiAgentStatusCount> toGraphQLTypeList(
            List<com.boomi.aiagentregistry.model.AgentStatusCount> entityStatusCounts);
}