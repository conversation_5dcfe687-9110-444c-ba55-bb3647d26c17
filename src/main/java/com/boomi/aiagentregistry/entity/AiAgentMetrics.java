// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Set;

@Entity
@Table(name = "ai_agent_metrics")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class AiAgentMetrics {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer uid;

    @Column(name = "obtained_at")
    private LocalDateTime obtainedAt;

    @Column(columnDefinition = "jsonb")
    private String request;

    @Column(columnDefinition = "jsonb")
    private String performance;

    @Column(name = "resource_utilization", columnDefinition = "jsonb")
    private String resourceUtilization;

    @Column(name = "error_rate", columnDefinition = "jsonb")
    private String errorRate;

    @Column(columnDefinition = "jsonb")
    private String cost;

    @OneToMany(mappedBy = "metrics")
    private Set<AiAgentMetricsAssociation> metricsAssociations;
}
