// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.entity;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import com.boomi.aiagentregistry.mapper.UUIDConverter;
import com.boomi.graphql.server.schema.types.AiAgentOriginType;
import com.boomi.graphql.server.schema.types.AiAgentRegistryTrustLevel;

import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.Filter;
import org.hibernate.annotations.Type;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Set;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.IS_DELETED_FILTER_CONDITION;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.IS_DELETED_FILTER_NAME;

@EqualsAndHashCode(callSuper = true, exclude = {"agent", "aliases"})
@ToString(exclude = {"agent", "aliases"})
@Entity
@Table(name = "ai_agent_version")
@Data
@SuperBuilder
@NoArgsConstructor
@Filter(name = IS_DELETED_FILTER_NAME, condition = IS_DELETED_FILTER_CONDITION)
public class AiAgentVersion extends Auditable implements ProviderFields, Serializable {
    @Serial
    private static final long serialVersionUID = -8998685531174497751L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer uid;

    @Convert(converter = UUIDConverter.class)
    @Column(nullable = false, unique = true)
    private String guid;

    @Column(name = "external_id", nullable = false, length = 2048)
    private String externalId;

    @ManyToOne
    @JoinColumn(name = "agent_uid")
    private AiAgent agent;

    @Column(name = "version_string", length = 100)
    private String versionString;

    @Column(name = "version_int")
    private Integer versionInt;

    @Column(nullable = false, length = 1000)
    private String name;

    @Column(length = 2000)
    private String description;

    @Column(length = 256)
    private String region;

    @Type(JsonType.class)
    @ColumnTransformer(write = "?::jsonb")
    @Column(columnDefinition = "jsonb")
    private String purpose;

    @Type(JsonType.class)
    @ColumnTransformer(write = "?::jsonb")
    @Column(name = "personality_traits", columnDefinition = "jsonb")
    private String personalityTraits;


    @Column(name = "created_in_registry")
    private Boolean createdInRegistry;

    @Column(name = "agent_status", length = 100)
    private String agentStatus;

    @Column(name = "updated_by_origin", length = 100)
    @Enumerated(EnumType.STRING)
    private AiAgentOriginType updatedByOrigin;

    @Column(name = "is_deleted")
    private Boolean isDeleted;

    @Column(name = "trust_level", length = 50)
    @Enumerated(EnumType.STRING)
    private AiAgentRegistryTrustLevel trustLevel;

    @Column(name = "created_by_origin", length = 100)
    @Enumerated(EnumType.STRING)
    private AiAgentOriginType createdByOrigin;

    @OneToMany(mappedBy = "agentVersion", fetch = FetchType.LAZY)
    private Set<AiAgentAlias> aliases;

    @Getter(AccessLevel.NONE)
    @Column(name = "updated_at_provider_time")
    private Timestamp updatedAtProviderTime;

    @Override
    public Timestamp getUpdatedAtProviderTime() {
        return updatedAtProviderTime;
    }

}
