// Copyright (c) 2025 Boom<PERSON>, LP.
package com.boomi.aiagentregistry.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.boomi.aiagentregistry.mapper.UUIDConverter;
import com.boomi.graphql.server.schema.types.AiAgentRegistryTrustLevel;

import org.hibernate.annotations.Immutable;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;

@Entity
@Table(name = "v_agent_listing")
@Immutable
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentListingView implements Serializable {

    @Serial
    private static final long serialVersionUID = 7184947130035522177L;

    @EmbeddedId
    private AgentListingViewId id;

    @Column(name = "agent_id")
    private Integer agentId;

    @Convert(converter = UUIDConverter.class)
    @Column(name = "agent_guid")
    private String agentGuid;

    @Column(name = "agent_external_id")
    private String agentExternalId;

    @Convert(converter = UUIDConverter.class)
    @Column(name = "provider_guid")
    private String providerAccountGuId;

    @Column(name = "idp_account_id", length = 100)
    private String idpAccountId;

    @Column(name = "provider_account_uid")
    private Integer providerAccountUid;

    @Column(name = "provider_type")
    private String providerType;

    @Column(name = "provider_account_name")
    private String providerAccountName;

    @Convert(converter = UUIDConverter.class)
    @Column(name = "version_guid")
    private String versionGuid;

    @Column(name = "version_name")
    private String versionName;

    @Column(name = "version")
    private String version;

    @Column(name = "version_external_id")
    private String versionExternalId;

    @Column(name = "description")
    private String description;

    @Column(name = "agent_status")
    private String agentStatus;

    @Column(name = "trust_level", length = 50)
    @Enumerated(EnumType.STRING)
    private AiAgentRegistryTrustLevel trustLevel;

    @Convert(converter = UUIDConverter.class)
    @Column(name = "alias_guid")
    private String aliasGuid;

    @Column(name = "alias_name")
    private String aliasName;

    @Column(name = "alias_external_id")
    private String aliasExternalId;

    @Column(name = "modified_time")
    private Timestamp modifiedTime;

    @Column(name = "updated_at_provider_time")
    private Timestamp updatedAtProviderTime;

    @Column(name = "has_alias")
    private Boolean hasAlias;

    @Column(name = "version_is_deleted")
    private Boolean versionIsDeleted;

    @Column(name = "alias_is_deleted")
    private Boolean aliasIsDeleted;

    @Column(name = "agent_is_deleted")
    private Boolean agentIsDeleted;
}
