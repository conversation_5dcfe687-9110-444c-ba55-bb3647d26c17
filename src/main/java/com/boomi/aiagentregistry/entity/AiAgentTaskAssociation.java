// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import com.boomi.aiagentregistry.mapper.UUIDConverter;
import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(exclude = {"task"}, callSuper = false)
@Entity
@Table(name = "ai_agent_task_association")
@Data
@ToString(exclude = {"task"})
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class AiAgentTaskAssociation extends BaseAssociation implements Serializable {

    @Serial
    private static final long serialVersionUID = -4436194873882298643L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer uid;

    @Convert(converter = UUIDConverter.class)
    @Column(nullable = false, unique = true)
    private String guid;

    @ManyToOne
    @JoinColumn(name = "task_uid")
    private AiAgentTask task;
}
