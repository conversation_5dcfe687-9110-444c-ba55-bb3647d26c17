// Copyright (c) 2025 Boom<PERSON>, LP.
package com.boomi.aiagentregistry.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Embeddable
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentListingViewId implements Serializable {

    @Serial
    private static final long serialVersionUID = 7184947130035522178L;

    @Column(name = "version_id")
    private Integer versionId;

    @Column(name = "alias_id")
    private Integer aliasId;
}
