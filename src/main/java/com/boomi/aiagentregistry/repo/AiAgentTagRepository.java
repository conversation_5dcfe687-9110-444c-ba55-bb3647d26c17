// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.repo;

import jakarta.persistence.LockModeType;
import jakarta.persistence.QueryHint;
import com.boomi.aiagentregistry.entity.AiAgentTag;
import com.boomi.aiagentregistry.model.AiAgentFilterDropdown;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Repository
public interface AiAgentTagRepository extends JpaRepository<AiAgentTag, Integer> {

    // New methods using materialized view
    @Query(value = "SELECT tag_key FROM mv_ai_agent_tags_by_idp WHERE idp_account_id = :idpAccountId"
            , nativeQuery = true)
    List<String> findByIdpAccountIdFromMaterializedView(
            @Param("idpAccountId") String idpAccountId);

    @Query("SELECT t FROM AiAgentTag t WHERE t.idpAccountId = :idpAccountId AND t.key IN :tagKeys")
    List<AiAgentTag> findByIdpAccountIdAndTagKeyIn(
            @Param("idpAccountId") String idpAccountId,
            @Param("tagKeys") Collection<String> tagKeys);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @QueryHints({@QueryHint(name = "jakarta.persistence.lock.timeout", value = "3000")})
    @Query("SELECT t FROM AiAgentTag t WHERE t.idpAccountId = :idpAccountId AND t.key IN :tagKeys")
    List<AiAgentTag> findByIdpAccountIdAndTagKeyInWithLock(
            @Param("idpAccountId") String idpAccountId,
            @Param("tagKeys") Collection<String> tagKeys);

    List<AiAgentTag> findAiAgentTagsByUidIn(@Param("uids")List<Integer> uids);

    @Query("SELECT DISTINCT NEW com.boomi.aiagentregistry.model.AiAgentFilterDropdown(t.guid, t.key) " +
            "FROM AiAgentTag t WHERE t.idpAccountId = :idpAccountId")
    Set<com.boomi.aiagentregistry.model.AiAgentFilterDropdown> getTags(@Param("idpAccountId") String idpAccountId);


    // Get tag IDs from VERSION type
    @Query("SELECT DISTINCT ata.tagUid FROM AiAgentTagAssociation ata " +
            "JOIN AiAgentVersion av ON ata.relatedEntityUid = av.uid " +
            "JOIN AiAgent a ON av.agent.uid = a.uid " +
            "WHERE ata.relatedEntityType = 'VERSION' " +
            "AND a.aiAgentProviderAccount.guid = :providerAccountGuId")
    Set<Long> getTagIdsFromVersions(@Param("providerAccountGuId") String providerAccountGuId);
    // Get tag IDs from ALIAS type
    @Query("SELECT DISTINCT ata.tagUid FROM AiAgentTagAssociation ata " +
            "JOIN AiAgentAlias aa ON ata.relatedEntityUid = aa.uid " +
            "JOIN AiAgentVersion av ON aa.agentVersion.uid = av.uid " +
            "JOIN AiAgent a ON av.agent.uid = a.uid " +
            "WHERE ata.relatedEntityType = 'ALIAS' " +
            "AND a.aiAgentProviderAccount.guid = :providerAccountGuId")
    Set<Long> getTagIdsFromAliases(@Param("providerAccountGuId") String providerAccountGuId);
    // Get tags with guid by IDs
    @Query("SELECT NEW com.boomi.aiagentregistry.model.AiAgentFilterDropdown(t.guid, t.key) " +
            "FROM AiAgentTag t WHERE t.uid IN :tagIds AND t.idpAccountId = :idpAccountId")
    Set<AiAgentFilterDropdown> getTagsByIds(@Param("tagIds") Set<Long> tagIds,
            @Param("idpAccountId") String idpAccountId);


    @Query("SELECT ta.relatedEntityUid, t FROM AiAgentTag t " +
            "JOIN AiAgentTagAssociation ta ON t.uid = ta.tagUid " +
            "WHERE ta.relatedEntityUid IN :relatedEntityUids " +
            "AND ta.relatedEntityType = :entityType")
    List<Object[]> findTagsWithAssociations(@Param("relatedEntityUids") Collection<Integer> relatedEntityUids,
            @Param("entityType") AiRegistryEntityType entityType);
}
