// Copyright (c) 2025 Boomi, LP

package com.boomi.aiagentregistry.repo;

import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.model.SyncUserAuditProjection;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface SyncUserAuditRepository extends JpaRepository<SyncUserAudit, UUID> {

    @Query("SELECT sua.syncStatus as syncStatus, sua.content as content FROM SyncUserAudit sua "
            + "JOIN AgentEntitySyncLatest aesl " + "ON sua.entityUid = aesl.syncedEntityUid "
            + "AND sua.entityType = aesl.syncedEntityType " + "AND sua.syncEndDate = aesl.syncEndDate "
            + "WHERE aesl.syncedEntityUid = :entityUid " + "AND aesl.syncedEntityType = :entityType")
    Optional<SyncUserAuditProjection> findLatestSyncAuditForEntity(@Param("entityUid") Integer entityUid,
            @Param("entityType") AiRegistryEntityType entityType);
}

