// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.repo;

import com.boomi.aiagentregistry.entity.AiAgentLlm;
import com.boomi.aiagentregistry.entity.AiAgentLlmAssociation;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */

@Repository
public interface AiAgentLlmAssociationRepository extends JpaRepository<AiAgentLlmAssociation, Integer> {
    Optional<AiAgentLlmAssociation> findByGuid(String guid);

    Set<AiAgentLlmAssociation> findByRelatedEntityUidAndRelatedEntityType(
            Integer uid,
            AiRegistryEntityType aiRegistryEntityType);


    @Query("SELECT l.uid FROM AiAgentLlm l " +
           "LEFT JOIN AiAgentLlmAssociation a ON l.uid = a.llm.uid " +
           "WHERE l.uid IN :llmIds " +
           "GROUP BY l.uid " +
           "HAVING COUNT(a) = 0")
    Set<Integer> findLlmIdsWithNoAssociations(@Param("llmIds") Set<Integer> llmIds);

    @Modifying
    @Transactional
    @Query(nativeQuery = true, value =
            "DELETE FROM ai_agent_llm_association " +
            "WHERE llm_uid IN (:llmIds) " +
            "AND related_entity_uid = :relatedEntityUid " +
            "AND related_entity_type = :relatedEntityType")
    void deleteByLlmUidInAndRelatedEntityUidAndRelatedEntityType(
            @Param("llmIds") Set<Integer> llmIds,
            @Param("relatedEntityUid") Integer relatedEntityUid,
            @Param("relatedEntityType") String relatedEntityType
    );


    Set<AiAgentLlmAssociation> findAllByLlm(AiAgentLlm aiAgentLlm);

    void deleteAllByRelatedEntityUidAndRelatedEntityType(Integer uid, AiRegistryEntityType aiRegistryEntityType);


    @Query("SELECT new com.boomi.aiagentregistry.model.LlmInfo(l.name, l.guid) " +
            "FROM AiAgentLlm l " +
            "JOIN AiAgentLlmAssociation ala ON l.uid = ala.llm.uid " +
            "WHERE ala.relatedEntityUid = :entityId " +
            "AND ala.relatedEntityType = 'VERSION'")
    List<com.boomi.aiagentregistry.model.LlmInfo> findLlmInfoByEntityIdAndType(
            @Param("entityId") Integer entityId);


    @Query("SELECT la.relatedEntityUid as entityId, l.guid as guid, l.name as name " +
            "FROM AiAgentLlm l " +
            "JOIN AiAgentLlmAssociation la ON l.uid = la.llm.uid " +
            "WHERE la.relatedEntityUid IN :entityIds")
    List<Object[]> findLlmInfosByEntityIds(@Param("entityIds") Collection<Integer> entityIds);

}
