// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.repo;

import com.boomi.aiagentregistry.entity.AiAgentListing;
import com.boomi.aiagentregistry.entity.AiAgentLlm;
import com.boomi.aiagentregistry.entity.AiAgentTag;
import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.entity.VersionAliasIdKey;
import com.boomi.graphql.server.schema.types.AiAgentRegistryTrustLevel;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface AiAgentListingRepository extends JpaRepository<AiAgentListing, Integer> {

    Page<AiAgentListing> findAll(Specification spec, Pageable page);

    List<AiAgentListing> findAll(Specification spec);

    @Query("SELECT distinct(a.id.aliasId) FROM AiAgentListing a WHERE a.aliasGuid = :guid")
    Optional<Integer> findAliasUidByGuid(@Param("guid") String guid);

    @Query("SELECT distinct(a.id.versionId) FROM AiAgentListing a WHERE a.versionGuid = :guid")
    Optional<Integer> findVersionUidByGuid(@Param("guid") String guid);

    @Query("SELECT DISTINCT a.trustLevel "
            + "FROM AiAgentListing a WHERE a.idpAccountId = :idpAccountId")
    List<AiAgentRegistryTrustLevel> getTrustLevel(@Param("idpAccountId") String idpAccountId);

    void deleteById(VersionAliasIdKey uid);

    // Batch fetch methods
    @Query("SELECT l.aliasGuid as guid, l.id.aliasId as uid FROM AiAgentListing l WHERE l.aliasGuid IN :guids")
    List<Object[]> findAliasUidsByGuids(@Param("guids") Collection<String> guids);

    @Query("SELECT l.versionGuid as guid, l.id.versionId as uid FROM AiAgentListing l WHERE l.versionGuid IN :guids")
    List<Object[]> findVersionUidsByGuids(@Param("guids") Collection<String> guids);

}
