// Copyright (c) 2025 Boomi, LP.
package com.boomi.aiagentregistry.repo;

import com.boomi.aiagentregistry.entity.AgentListingView;
import com.boomi.aiagentregistry.entity.AgentListingViewId;
import com.boomi.graphql.server.schema.types.AiAgentRegistryTrustLevel;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface AgentListingViewRepository extends JpaRepository<AgentListingView, AgentListingViewId>,
        JpaSpecificationExecutor<AgentListingView> {

    Page<AgentListingView> findAll(Specification spec, Pageable page);

    List<AgentListingView> findAll(Specification spec);

    @Query(value = "SELECT DISTINCT trust_level FROM v_agent_listing WHERE idp_account_id = :idpAccountId",
            nativeQuery = true)
    List<AiAgentRegistryTrustLevel> getTrustLevel(@Param("idpAccountId") String idpAccountId);

    // Batch fetch methods
    @Query("SELECT l.aliasGuid as guid, l.id.aliasId as uid FROM AgentListingView l WHERE l.aliasGuid IN :guids")
    List<Object[]> findAliasUidsByGuids(@Param("guids") Collection<String> guids);

    @Query("SELECT l.versionGuid as guid, l.id.versionId as uid FROM AgentListingView l WHERE l.versionGuid IN :guids")
    List<Object[]> findVersionUidsByGuids(@Param("guids") Collection<String> guids);

}
