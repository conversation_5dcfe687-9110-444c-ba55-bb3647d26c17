// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.repo;

import jakarta.transaction.Transactional;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.model.AiAgentProviderCount;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface AiAgentProviderAccountRepository
        extends JpaRepository<AiAgentProviderAccount, Integer>, JpaSpecificationExecutor<AiAgentProviderAccount> {

    Optional<AiAgentProviderAccount> findByGuid(String guid);

    List<AiAgentProviderAccount> findByGuidInAndIdpAccountId(List<String> guids, String idpAccountId);

    @Query("SELECT COUNT(guid) " +
           "FROM AiAgentProviderAccount " +
           "WHERE idpAccountId = :idpAccountId")
    int findProviderAccountsCountByIdpAccountId(@Param("idpAccountId") String idpAccountId);

    @Query("SELECT providerType as providerType, COUNT(uid) as count " +
            "FROM AiAgentProviderAccount " +
            "WHERE idpAccountId = :idpAccountId " +
            "GROUP BY providerType")
    List<AiAgentProviderCount> countAiAgentProviderAccountsGroupByProviderType(
            @Param("idpAccountId") String idpAccountId);

    default Specification<AiAgentProviderAccount> hasProviderType(AiAgentProviderType inputProviderType) {
        return (entityRoot, query, providerType) -> {
            if (inputProviderType == null) {
                return null;
            }
            return providerType.equal(entityRoot.get("providerType"), inputProviderType);
        };
    }

    default Specification<AiAgentProviderAccount> hasAccountName(String inputAccountName) {
        return (entityRoot, query, accountName) -> {
            if ((StringUtils.isEmpty(inputAccountName))) {
                return null;
            }
            return accountName.equal(entityRoot.get("providerAccountName"), inputAccountName);
        };
    }

    default Specification<AiAgentProviderAccount> hasProviderTypeAndAccountName(AiAgentProviderType providerType,
            String accountName) {
        return Specification.where(hasProviderType(providerType)).and(hasAccountName(accountName));
    }

    default Specification<AiAgentProviderAccount> idpAccountId(String inputAccountName) {
        return (entityRoot, criteriaQuery, criteriaBuilder) ->
                criteriaBuilder.equal(entityRoot.get("idpAccountId"), inputAccountName);
    }

    Boolean existsByIdpAccountIdAndProviderTypeAndExternalProviderAccountIdAndIsDeletedFalse(
            String idpAccountId,
            AiAgentProviderType providerType,
            String externalProviderAccountId);

    Boolean existsByAuthSchemaAndExternalProviderAccountIdAndRegionAndIsDeletedFalse(
            AiProviderAuthSchema providerAuthSchema, String externalProviderAccountId, String region);

    @Query("SELECT a.idpAccountId FROM AiAgentProviderAccount a "
            + "WHERE a.externalProviderAccountId = :externalProviderAccountId "
            + "AND a.authSchema = :authSchema "
            + "AND a.isDeleted = false")
    List<String> findIdpAccountIdsByExternalProviderAccountIdAndAuthSchemaAndIsDeletedFalse(
            @Param("externalProviderAccountId") String externalProviderAccountId,
            @Param("authSchema") AiProviderAuthSchema authSchema);

    Boolean existsByIdpAccountIdAndProviderTypeAndProviderAccountNameAndIsDeletedFalse(
            String idpAccountId,
            AiAgentProviderType providerType,
            String providerAccountName);

    Boolean existsByIdpAccountIdAndProviderAccountNameAndIsDeletedFalse(
            String idpAccountId,
            String providerAccountName);

    List<AiAgentProviderAccount> findAllByIsDeletedFalseAndProviderAccountStatusNotIn(
            Set<AiAgentProviderAccountStatus> status
                                                                                     );

    Boolean existsByIdpAccountIdAndProviderTypeAndExternalProviderAccountIdAndRegionAndIsDeletedFalse
            (String idpAccountId, AiAgentProviderType providerType, String externalProviderAccountId, String region);

    Optional<AiAgentProviderAccount> findFirstByExternalProviderAccountIdAndAuthSchemaAndIsDeletedFalse(
            String externalProviderAccountId, AiProviderAuthSchema authSchema);

    boolean existsByExternalProviderAccountIdAndUidNotAndAuthSchemaAndIsDeletedFalse(
            String externalProviderAccountId, int uid, AiProviderAuthSchema authSchema);

    boolean existsByExternalProviderAccountIdAndAuthSchemaAndProviderAccountStatusInAndIsDeletedFalse(
        String externalProviderAccountId, AiProviderAuthSchema authSchema,
        Set<AiAgentProviderAccountStatus> providerAccountStatus
    );

    Optional<AiAgentProviderAccount> findFirstByExternalProviderAccountIdAndAuthSchemaAndProviderAccountStatusInAndIsDeletedFalse(
        String externalProviderAccountId,
        AiProviderAuthSchema authSchema,
        Set<AiAgentProviderAccountStatus> providerAccountStatus
    );


    Optional<AiAgentProviderAccount>
findByIdpAccountIdAndAuthSchemaAndExternalProviderAccountIdAndRegionAndIsDeletedFalse
     (String idpAccountId, AiProviderAuthSchema authSchema, String externalProviderAccountId, String region);

    @Modifying
    @Transactional
    @Query(nativeQuery = true, value =
            "DELETE FROM ai_agent_provider_account WHERE uid = :providerAccountId")
    int deleteProviderAccount(@Param("providerAccountId") int providerAccountId);

    @Modifying
    @Query(nativeQuery = true, value = "delete from ai_agent_large_text_content where ("
            + " related_entity_uid in (select uid from ai_agent_task where provider_account_uid = :uid) and "
            + " related_entity_type = 'TASK')"
            + "  or ( related_entity_uid in (select uid from ai_agent_version where agent_uid in "
            + " (select uid from ai_agent where provider_account_uid =:uid)) and related_entity_type = " + "'VERSION')")
    void deleteLargeTextContentForProviderAccountDependents(@Param("uid") Integer uid);

    @Modifying
    @Query(nativeQuery = true, value = "delete from ai_agent_large_text_content where "
            + " related_entity_uid =:uid and related_entity_type=" + "'PROVIDER_ACCOUNT'")
    void deleteLargeTextContentForProviderAccount(@Param("uid") Integer uid);

    @Modifying
    @Query(nativeQuery = true, value =
            " delete  from ai_agent_tag_association where "
                    + "    (related_entity_uid in (select uid from ai_agent_alias where agent_version_uid in "
                    + " (select uid from ai_agent_version where agent_uid in "
                    + "       (select uid from ai_agent  where provider_account_uid =:uid))"
                    + "  ) and related_entity_type='ALIAS'"
                    + "    or"
                    + "    (related_entity_uid in (select uid from ai_agent_version where agent_uid in "
                    + "        (select uid from ai_agent where provider_account_uid =:uid "
                    + "      )) and related_entity_type = 'VERSION'"
                    + "    ))"
    )
    void deleteTagAssociationForProviderAccountDependents(@Param("uid") Integer uid);

    Optional<AiAgentProviderAccount> findByGuidAndIsDeletedFalseAndProviderAccountStatusNotIn(
            String id,
            Set<AiAgentProviderAccountStatus> status);

    @Query("SELECT DISTINCT a.providerType "
            + "FROM AiAgentProviderAccount a WHERE a.isDeleted = false "
            + "AND a.idpAccountId = :idpAccountId")
    List<AiAgentProviderType> getProviderTypes(@Param("idpAccountId") String idpAccountId);

    @Query("SELECT DISTINCT NEW com.boomi.aiagentregistry.model.AiAgentFilterDropdown"
            + "(pa.guid, pa.providerAccountName) " +
            "FROM AiAgentProviderAccount pa " +
            "WHERE pa.isDeleted = FALSE and pa.idpAccountId =:idpAccountId")
    List<com.boomi.aiagentregistry.model.AiAgentFilterDropdown> getAccounts(@Param("idpAccountId") String idpAccountId);

    @Query("SELECT COUNT(account.guid) " +
            "FROM AiAgentProviderAccount account " +
            "WHERE account.guid IN (:providerAccountIds) AND account.idpAccountId = :idpAccountId")
    int getProviderAccountsCountByAccountGuidsAndIdpAccountId(
            @Param("providerAccountIds") Set<String> providerAccountIds,
            @Param("idpAccountId") String idpAccountId);
}
