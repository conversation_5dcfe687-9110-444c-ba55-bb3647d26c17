// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.repo;

import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.model.AiAgentProviderCount;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface AiAgentRepository extends JpaRepository<AiAgent, Integer> {
    Optional<AiAgent> findByGuid(String guid);

    Optional<AiAgent> findByAiAgentProviderAccountAndExternalId(AiAgentProviderAccount account, String externalId);

    Optional<List<AiAgent>> findByAiAgentProviderAccountAndIsDeleted(AiAgentProviderAccount account, boolean isDeleted);

    Page<AiAgent> findAllByAiAgentProviderAccountGuidIn(List<String> aiAgentProviderAccountIds, Pageable pageable);

    @Query(value = """
            WITH agent_stats AS (
                SELECT
                    a.provider_account_uid,
                    -- Count agents without versions
                    COUNT(CASE WHEN av.uid IS NULL THEN 1 END) as agents_without_versions_count,
                    -- Count versions without aliases
                    COUNT(CASE WHEN av.uid IS NOT NULL AND aa.uid IS NULL THEN av.uid END) as versions_without_aliases_count,
                    -- Count total aliases
                    COUNT(aa.uid) as total_aliases_count
                FROM ai_agent a
                         JOIN ai_agent_version av ON a.uid = av.agent_uid AND av.is_deleted = false
                         LEFT JOIN ai_agent_alias aa ON av.uid = aa.agent_version_uid AND aa.is_deleted = false
                WHERE a.provider_account_uid in :providerAccountUids
                    AND a.is_deleted = false
                GROUP BY a.provider_account_uid
            )
            SELECT
                provider_account_uid,
                total_aliases_count +
                versions_without_aliases_count +
                agents_without_versions_count as total_elements
            FROM agent_stats
                """,nativeQuery = true)
    List<Object[]> countAgentsByProviderAccountUids(@Param("providerAccountUids") List<Integer> providerAccountUids);

    @Query(value = """
            WITH agent_stats AS (
                SELECT
                    ac.provider_type,
                    -- Count agents without versions
                    COUNT(CASE WHEN av.uid IS NULL THEN 1 END) as agents_without_versions_count,
                    -- Count versions without aliases
                    COUNT(CASE WHEN av.uid IS NOT NULL AND aa.uid IS NULL THEN av.uid END) as versions_without_aliases_count,
                    -- Count total aliases
                    COUNT(aa.uid) as total_aliases_count
                FROM ai_agent a
                         JOIN ai_agent_provider_account ac ON a.provider_account_uid = ac.uid
                         JOIN ai_agent_version av ON a.uid = av.agent_uid AND av.is_deleted = false
                         LEFT JOIN ai_agent_alias aa ON av.uid = aa.agent_version_uid AND aa.is_deleted = false
                WHERE a.is_deleted = false
                    AND ac.idp_account_id = :idpAccountId
                    AND ac.is_deleted = false
                GROUP BY ac.provider_type
            )
            SELECT
                provider_type as providerType,
                (total_aliases_count +
                versions_without_aliases_count +
                agents_without_versions_count) as count
            FROM agent_stats
            """, nativeQuery = true)
    List<AiAgentProviderCount> countAiAgentsGroupByProviderType(@Param("idpAccountId") String idpAccountId);
}
