// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.repo;

import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.entity.AiAgentToolAssociation;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */

@Repository
public interface AiAgentToolAssociationRepository extends JpaRepository<AiAgentToolAssociation, Integer> {
    Optional<AiAgentToolAssociation> findByGuid(String guid);


    Set<AiAgentToolAssociation> findByRelatedEntityUidAndRelatedEntityTypeAndTool(
            Integer relatedEntityUid,
            AiRegistryEntityType relatedEntityType,
            AiAgentTool tool
    );


    Set<AiAgentToolAssociation> findByRelatedEntityUidAndRelatedEntityType(Integer relatedEntityUid,
                                                                           AiRegistryEntityType relatedEntityType);

    @Query("SELECT t.uid FROM AiAgentTool t " +
           "LEFT JOIN AiAgentToolAssociation a ON t.uid = a.tool.uid " +
           "WHERE t.uid IN :ids " +
           "GROUP BY t.uid " +
           "HAVING COUNT(a) = 0")
    Set<Integer> findToolIdsWithNoAssociations(
            @Param("ids") Set<Integer> ids);

    @Transactional
    void deleteByToolUidIn(Set<Integer> toolIds);

    @Modifying
    @Transactional
    @Query(nativeQuery = true, value =
            "DELETE FROM ai_agent_tool_association " +
            "WHERE tool_uid IN (:toolIds) " +
            "AND related_entity_uid = :relatedEntityUid " +
            "AND related_entity_type = :relatedEntityType ")
    void deleteByToolUidInAndRelatedEntityUidAndRelatedEntityType(
            @Param("toolIds") Set<Integer> toolIds,
            @Param("relatedEntityUid") Integer relatedEntityUid,
            @Param("relatedEntityType") String relatedEntityType
    );

    Set<AiAgentToolAssociation> findAllByTool(AiAgentTool aiAgentTool);

    Set<AiAgentToolAssociation> findAllByToolAndRelatedEntityUidAndRelatedEntityType
            (AiAgentTool aiAgentTool, Integer relatedEntityUid,
            AiRegistryEntityType relatedEntityType);

    @Transactional
    void deleteAllByRelatedEntityTypeAndRelatedEntityUid(AiRegistryEntityType relatedEntityType,
            Integer relatedEntityUid);

    @Transactional
    void deleteAllByRelatedEntityTypeAndRelatedEntityUidIn(AiRegistryEntityType relatedEntityType,
            Collection<Integer> relatedEntityUids);

    @Query("SELECT t.guid " +
            "FROM AiAgentTool t " +
            "JOIN AiAgentToolAssociation ata ON t.uid = ata.tool.uid " +
            "WHERE ata.relatedEntityUid = :entityId " +
            "AND ata.relatedEntityType = 'VERSION'")
    List<String> findToolNamesByEntityId(@Param("entityId") Integer entityId);

    @Query(value = "SELECT ata.related_entity_uid, t.guid  " +
            "FROM ai_agent_tool t " +
            "JOIN ai_agent_tool_association ata ON t.uid = ata.tool_uid " +
            "WHERE ata.related_entity_uid IN :entityIds " +
            "AND ata.related_entity_type = 'VERSION'",
           nativeQuery = true)
    List<Object[]> findToolGuidsByEntityIds(@Param("entityIds") Collection<Integer> entityIds);
}
