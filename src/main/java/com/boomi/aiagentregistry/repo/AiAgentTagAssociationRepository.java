// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.repo;

import jakarta.persistence.LockModeType;
import jakarta.persistence.QueryHint;
import com.boomi.aiagentregistry.entity.AiAgentTag;
import com.boomi.aiagentregistry.entity.AiAgentTagAssociation;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AiAgentTagAssociationRepository extends JpaRepository<AiAgentTagAssociation, Integer> {

    List<AiAgentTagAssociation> findByRelatedEntityUid(Integer relativeEntityId);

    @Query("SELECT a FROM AiAgentTagAssociation a " +
            "WHERE a.relatedEntityType = :relatedEntityType " +
            "AND a.relatedEntityUid = :relatedEntityUid")
    List<AiAgentTagAssociation> findByRelatedEntityTypeAndRelatedEntityId(
            @Param("relatedEntityType") AiRegistryEntityType relatedEntityType,
            @Param("relatedEntityUid") Integer relatedEntityUid);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @QueryHints({ @QueryHint(name = "jakarta.persistence.lock.timeout", value = "3000") })
    @Query("SELECT a FROM AiAgentTagAssociation a " +
            "WHERE a.relatedEntityType = :relatedEntityType " +
            "AND a.relatedEntityUid = :relatedEntityUid")
    List<AiAgentTagAssociation> findByRelatedEntityTypeAndRelatedEntityIdWithLock(
            @Param("relatedEntityType") AiRegistryEntityType relatedEntityType,
            @Param("relatedEntityUid") Integer relatedEntityUid);

    @Query("select aat from AiAgentTag aat left join AiAgentTagAssociation aata "
            + "on aat.uid = aata.tagUid "
            + "where aata.relatedEntityType = :relatedEntityType "
            + "and  aata.relatedEntityUid = :relatedEntityUid")
    List<AiAgentTag> findAiAgentTagFromAiAgentTagAssociationByRelatedEntityTypeAndRelatedEntityId(
            @Param("relatedEntityType") AiRegistryEntityType relatedEntityType,
            @Param("relatedEntityUid") Integer relatedEntityUid);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @QueryHints({ @QueryHint(name = "jakarta.persistence.lock.timeout", value = "3000") })
    @Query("select aat from AiAgentTag aat left join AiAgentTagAssociation aata "
            + "on aat.uid = aata.tagUid "
            + "where aata.relatedEntityType = :relatedEntityType "
            + "and  aata.relatedEntityUid = :relatedEntityUid")
    List<AiAgentTag> findAiAgentTagFromAiAgentTagAssociationByRelatedEntityTypeAndRelatedEntityIdWithLock(
            @Param("relatedEntityType") AiRegistryEntityType relatedEntityType,
            @Param("relatedEntityUid") Integer relatedEntityUid);

    void deleteAllByRelatedEntityUidAndRelatedEntityType(Integer uid, AiRegistryEntityType aiRegistryEntityType);
}
