// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import software.amazon.awssdk.services.secretsmanager.model.CreateSecretResponse;
import software.amazon.awssdk.services.secretsmanager.model.DeleteSecretResponse;
import software.amazon.awssdk.services.secretsmanager.model.SecretsManagerResponse;
import software.amazon.awssdk.services.secretsmanager.model.UpdateSecretResponse;

import org.springframework.stereotype.Service;


@Service
@Slf4j
public class SecretsManageLocalServiceImpl implements SecretsManagerService {

    @Override
    public SecretsManagerResponse createSecret(String secretName, String secretValue) {
        return CreateSecretResponse.builder()
                .arn("arn:aws:secretsmanager:us-east-1:************:secret:" + secretName)
                .build();
    }

    @Override
    public SecretsManagerResponse deleteSecret(String secretName) {

        return DeleteSecretResponse.builder().
                name(secretName).build();
    }

    @Override
    public Flux<String> getSecret(String secretName) {
           //  "userName": "<EMAIL>",
           //  "accountId": "boomi-internal",
           //  "apiToken": "us-east-1",
           //  "jwt": "TestJwt"
        return Flux.just("""
                {
                    "awsAccessKeyId": "test-access-key",
                    "awsSecretAccessKey": "test-secret-key",
                    "awsRegion": "us-east-1"
                }"
                """);
    }

    @Override
    public UpdateSecretResponse updateSecret(String secretName, String secretValue) {
        return UpdateSecretResponse.builder()
                .arn("arn:aws:secretsmanager:us-east-1:************:secret:" + secretName)
                .build();
    }

    @Override
    public int isProviderSecretExists(String secretName) {
        // if secret exist 1
        // if secret not exist 0
        // if any exception occurred -1
        return 1;
    }
}
