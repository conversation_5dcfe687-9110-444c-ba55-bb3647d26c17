// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.auth;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import com.boomi.aiagentregistry.exception.AiAgentRegistryException;
import com.boomi.aiagentregistry.service.sync.boomigarden.GardenServiceProperties;
import com.boomi.aiagentregistry.servlet.ApiService;
import com.boomi.aiagentregistry.servlet.ReqBasicAuthStrategy;
import com.boomi.aiagentregistry.servlet.RequestBuilder;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.AUTH_JWT_GENERATE_PATH;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FAILED_TO_FETCH_JWT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.GARDEN_ACCOUNT_HAS_NO_ACCESS_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INVALID_CREDENTIALS_KEYS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INVALID_CREDENTIALS_TYPE;
import static com.boomi.aiagentregistry.constant.ErrorMessages.JSON_ERROR;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.KEY_HEADER_ACCEPT;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.KEY_HEADER_ACCOUNTID;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.KEY_HEADER_USERID;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.KEY_SUCCESS;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.VALUE_HEADER_ACCEPT;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.VALUE_HEADER_USERID;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenService.AGENT_LISTS_RESPONSE_FOR_GARDEN;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenService.AUTHORIZATION_HEADER;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenService.BEARER_HEADER;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
@Setter
@Getter
@Accessors(prefix = "_")
public class BoomiApiTokenAuthorizationParserStrategy implements AuthorizationParserStrategy {

    public static final String BOOMI_TOKEN = "BOOMI_TOKEN.";

    private ObjectMapper _objectMapper;

    @Value("${boomi.services.aiagentregistry.garden.jwtUrl}")
    private String _gardenJwtUrl;

    @Value("${boomi.services.aiagentregistry.garden.apiUrl}")
    private String _gardenApiUrl;

    private final ApiService _apiService;

    private final GardenServiceProperties _properties;

    public BoomiApiTokenAuthorizationParserStrategy(ObjectMapper objectMapper,
            ApiService apiService, GardenServiceProperties properties) {
        _apiService = apiService;
        _objectMapper = objectMapper;
        _properties = properties;
    }

    @Override
    public Mono<String> fetchAccountId(JsonNode connectionNode) {
        return Mono.fromCallable(() -> _objectMapper.treeToValue(connectionNode, BoomiApiTokenCredentials.class))
                .flatMap(this::loadAdditionalProperties)
                .flatMap(credentials -> {
                    BoomiApiTokenCredentials boomiApiTokenCredentials = (BoomiApiTokenCredentials) credentials;
                    // will check for is accountId has access to AI Agent Garden API
                    return hasGardenAccess(boomiApiTokenCredentials)
                            .filter(hasAccess -> hasAccess)
                            .map(hasAccess -> boomiApiTokenCredentials.getAccountId())
                            .defaultIfEmpty(StringUtils.EMPTY);
                }).onErrorResume(error -> {
                    if (error instanceof JsonProcessingException) {
                        log.error(INVALID_CREDENTIALS_KEYS, error);
                        return Mono.error(error);
                    } else {
                        log.error(FAILED_TO_FETCH_JWT, error);
                        return Mono.error(error);
                    }
                });
    }

    @Override
    public Mono<String> getMaskedCredentials(String credentials) {
        try {
            BoomiApiTokenCredentials boomiApiTokenCredentials =
                    _objectMapper.readValue(credentials, BoomiApiTokenCredentials.class);
            BoomiApiTokenCredentials maskedBoomiApiTokenCredentials = new BoomiApiTokenCredentials();
            maskedBoomiApiTokenCredentials.setUserName(boomiApiTokenCredentials.getUserName());
            return Mono.just(_objectMapper.writeValueAsString(maskedBoomiApiTokenCredentials));
        } catch (Exception e) {
            log.warn("Error masking credentials", e);
            return Mono.just(StringUtils.EMPTY);
        }
    }

    @Override
    public AiProviderAuthSchema getProviderAuthSchema() {
        return AiProviderAuthSchema.BOOMIAPITOKEN;
    }

    @Override
    public Credentials parseCredentials(JsonNode credentialsNode) throws JsonProcessingException {
        return _objectMapper.treeToValue(credentialsNode, BoomiApiTokenCredentials.class);
    }

    @Override
    public boolean validateCredentialFormat(JsonNode credentialsNode) throws JsonProcessingException {
        try {
            Map<String, String> expectedKeys = Map.of(
                    "userName", "User Id",
                    "apiToken", "API Token Password",
                    "accountId", "AWS Region"
            );

            List<String> validationErrors = expectedKeys.keySet()
                    .stream()
                    .map(key -> {
                        JsonNode valueNode = credentialsNode.get(key);
                        if (valueNode == null || valueNode.isNull() || valueNode.asText().isEmpty()) {
                            return expectedKeys.get(key);
                        }
                        if ((!valueNode.isTextual()) || valueNode.asText().isEmpty()) {
                            return expectedKeys.get(key) + "Value cannot be empty";
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .toList();

            if (!validationErrors.isEmpty()) {
                String keys = String.join(", ", validationErrors);
                log.error(INVALID_CREDENTIALS_KEYS, keys);
                throw new IllegalArgumentException(INVALID_CREDENTIALS_KEYS);
            }
            return true;
        } catch (Exception e) {
            log.error(INVALID_CREDENTIALS_KEYS, e);
            return false;
        }
    }

    @Override
    public Mono<Credentials> loadAdditionalProperties(Credentials credentials) {
        if (!(credentials instanceof BoomiApiTokenCredentials)) {
            return Mono.error(new IllegalArgumentException(INVALID_CREDENTIALS_TYPE));
        }

        BoomiApiTokenCredentials boomiApiTokenCredentials = (BoomiApiTokenCredentials) credentials;

        RequestBuilder requestBuilder = RequestBuilder.builder()
                .baseUrl(_gardenJwtUrl)
                .path(AUTH_JWT_GENERATE_PATH + boomiApiTokenCredentials.getAccountId())
                .authStrategy(new ReqBasicAuthStrategy(
                        BOOMI_TOKEN + boomiApiTokenCredentials.getUserName(),
                        boomiApiTokenCredentials.getApiToken()
                ))
                .build();

        return _apiService.executeGet(requestBuilder, String.class)
                .next()
                .map(response -> {
                    boomiApiTokenCredentials.setJwt(response);
                    return (Credentials) boomiApiTokenCredentials;
                })
                .onErrorMap(error -> {
                    log.error(FAILED_TO_FETCH_JWT, error);
                    return new RuntimeException(FAILED_TO_FETCH_JWT + error.getMessage());
                });
    }

    @Override
    public Optional<List<AiAgentRegistryErrorCode>> verifyImmutableFieldsAreNotChanged(
            String existingCredentialsString, String newCredentialsString) {
        try {
            BoomiApiTokenCredentials existingCredentials = _objectMapper.readValue(
                    existingCredentialsString, BoomiApiTokenCredentials.class);
            BoomiApiTokenCredentials newCredentials = _objectMapper.readValue(
                           newCredentialsString, BoomiApiTokenCredentials.class);

           boolean accountIdMatched = StringUtils.equals(existingCredentials.getAccountId(),
                   newCredentials.getAccountId());

           if (!accountIdMatched) {
              return Optional
                      .of(Collections.singletonList(AiAgentRegistryErrorCode.GARDEN_ACCOUNT_ID_IS_CHANGED));
           }
        } catch (JsonProcessingException error) {
            log.warn(JSON_ERROR, error);
            throw new AiAgentRegistryException(JSON_ERROR, error);
        }

        return Optional.empty();
    }

    private Mono<Boolean> hasGardenAccess(BoomiApiTokenCredentials boomiApiTokenCredentials) {
        log.info("Validating garden access for account: {}", boomiApiTokenCredentials.getAccountId());
        return _apiService.executeGet(getAgentListRequestBuilder(boomiApiTokenCredentials), LinkedHashMap.class)
                .next()
                .flatMap(response -> {
                    log.info(AGENT_LISTS_RESPONSE_FOR_GARDEN, boomiApiTokenCredentials.getAccountId());
                    Boolean success = (Boolean) response.get(KEY_SUCCESS);
                    if (success == null || !success) {
                        log.warn(String.format(GARDEN_ACCOUNT_HAS_NO_ACCESS_ERROR,
                                boomiApiTokenCredentials.getAccountId()));
                        return Mono.just(false);
                    }
                    return Mono.just(true);
                })
                .defaultIfEmpty(false)
                .onErrorResume(e -> {
                    log.error(String.format(GARDEN_ACCOUNT_HAS_NO_ACCESS_ERROR,
                            boomiApiTokenCredentials.getAccountId()), e);
                    return Mono.just(false);
                });
    }

    private RequestBuilder getAgentListRequestBuilder(BoomiApiTokenCredentials boomiApiTokenCredentials) {

        log.info("Constructing request builder for validating connection.");

        Map<String, String> requestHeaders = new HashMap<>();
        requestHeaders.put(KEY_HEADER_ACCEPT, VALUE_HEADER_ACCEPT);
        requestHeaders.put(KEY_HEADER_USERID, VALUE_HEADER_USERID);
        requestHeaders.put(KEY_HEADER_ACCOUNTID, boomiApiTokenCredentials.getAccountId());
        requestHeaders.put(AUTHORIZATION_HEADER,
                BEARER_HEADER.concat(boomiApiTokenCredentials.getJwt()));

        return RequestBuilder.builder().headers(requestHeaders)
                .baseUrl(_gardenApiUrl)
                .path(_properties.getAgentsPath())
                .body(new HashMap<>())
                .build();
    }
}
