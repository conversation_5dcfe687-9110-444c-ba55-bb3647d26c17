// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.auth;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import software.amazon.awssdk.services.bedrockagent.BedrockAgentAsyncClient;
import software.amazon.awssdk.services.bedrockagent.model.BedrockAgentException;
import software.amazon.awssdk.services.bedrockagent.model.ListAgentsRequest;
import software.amazon.awssdk.services.bedrockagent.model.ListAgentsResponse;
import software.amazon.awssdk.services.sts.model.GetCallerIdentityRequest;
import software.amazon.awssdk.services.sts.model.GetCallerIdentityResponse;
import com.boomi.aiagentregistry.servlet.AwsClient;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.MAX_RECORD_CONNECTION_TEST;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_CREATING_AWS_CREDENTIALS_OR_CLIENT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INVALID_CREDENTIALS_KEYS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.UNEXPECTED_ERROR_DURING_CONNECTION_VALIDATION;
import static com.boomi.aiagentregistry.util.ValidationUtil.verifyRegionIsNotChanged;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class AwsAuthorizationParserStrategy implements AuthorizationParserStrategy {

    private final ObjectMapper _objectMapper;
    private final AwsClient _awsClient;

    public AwsAuthorizationParserStrategy(ObjectMapper objectMapper, AwsClient awsClient) {
        _objectMapper = objectMapper;
        _awsClient = awsClient;
    }

    @Override
    public AiProviderAuthSchema getProviderAuthSchema() {
        return AiProviderAuthSchema.AWS;
    }

    @Override
    public Credentials parseCredentials(JsonNode credentialsNode) throws JsonProcessingException {
        return _objectMapper.treeToValue(credentialsNode, AwsCredentials.class);
    }

    @Override
    public boolean validateCredentialFormat(JsonNode credentialsNode) throws JsonProcessingException {
        try {
            Map<String, String> expectedKeys = Map.of(
                    "awsAccessKeyId", "AWS Access Key ID",
                    "awsSecretAccessKey", "AWS Secret Access Key",
                    "awsRegion", "AWS Region"
            );
            List<String> validationErrors = expectedKeys.keySet()
                    .stream()
                    .map(key -> {
                        JsonNode valueNode = credentialsNode.get(key);
                        if (valueNode == null || valueNode.isNull() || valueNode.asText().isEmpty()) {
                            return expectedKeys.get(key);
                        }
                        if ((!valueNode.isTextual()) || valueNode.asText().isEmpty()) {
                            return expectedKeys.get(key) + "Value cannot be empty";
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .toList();
            if (!validationErrors.isEmpty()) {
                String keys = String.join(", ", validationErrors);
                log.error(INVALID_CREDENTIALS_KEYS, keys);
                throw new IllegalArgumentException(INVALID_CREDENTIALS_KEYS);
            }
            return true;
        } catch (Exception e) {
            log.error(INVALID_CREDENTIALS_KEYS, e);
            return false;
        }
    }

    @Override
    public Mono<Credentials> loadAdditionalProperties(Credentials credentials) {
        return Mono.just(credentials);
    }

    @Override
    public Mono<String> fetchAccountId(JsonNode credentialsNode) {
        return Mono.fromCallable(() -> _objectMapper.convertValue(credentialsNode, AwsCredentials.class)).map(
                _awsClient::createStsAsyncClient).flatMap(stsAsyncClient -> Mono.fromFuture(
                () -> stsAsyncClient.getCallerIdentity(GetCallerIdentityRequest.builder().build())).map(
                GetCallerIdentityResponse::account).onErrorResume(error -> {
            log.error(UNEXPECTED_ERROR_DURING_CONNECTION_VALIDATION, error);
            return Mono.just(null);
        }).doFinally(signalType -> {
            if (stsAsyncClient != null) {
                stsAsyncClient.close();
            }
        })).onErrorResume(error -> {
            log.error(ERROR_CREATING_AWS_CREDENTIALS_OR_CLIENT, error);
            return Mono.just(null);
        });
    }

    @Override
    public boolean requiresRegionCheck() {
        return true;
    }

    @Override
    public boolean validateConnection(JsonNode credentialsNode) {
        AwsCredentials awsCredentials = _objectMapper.convertValue(credentialsNode, AwsCredentials.class);
        BedrockAgentAsyncClient bedrockAgentClient = _awsClient.createBedrockAgentAsyncClient(awsCredentials);
        return validateBedrockConnection(bedrockAgentClient);
    }

    @Override
    public Optional<List<AiAgentRegistryErrorCode>> verifyImmutableFieldsAreNotChanged(
            String existingCredentialsString, String newCredentialsString) {
        return verifyRegionIsNotChanged(existingCredentialsString, newCredentialsString, _objectMapper);
    }

    @Override
    public boolean checkIsOneExternalAccountWithOneIdpAccountOneAuthType() {
        return false;
    }

    private boolean validateBedrockConnection(BedrockAgentAsyncClient bedrockAgentClient) {
        try (bedrockAgentClient) {
            ListAgentsRequest listAgentsRequest = ListAgentsRequest.builder()
                    .maxResults(MAX_RECORD_CONNECTION_TEST)
                    .build();

            // Execute the request synchronously using get()
            ListAgentsResponse response = bedrockAgentClient.listAgents(listAgentsRequest).get();
            return response.sdkHttpResponse().isSuccessful();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error(UNEXPECTED_ERROR_DURING_CONNECTION_VALIDATION, e);
            return false;
        } catch (ExecutionException e) {
            if (e.getCause() instanceof BedrockAgentException) {
                log.error(INVALID_CREDENTIALS_KEYS, e.getCause());
            } else {
                log.error(UNEXPECTED_ERROR_DURING_CONNECTION_VALIDATION, e);
            }
            return false;
        }
    }
}
