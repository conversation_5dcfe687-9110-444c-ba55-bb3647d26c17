// Copyright (c) 2024 Boomi, LP

package com.boomi.aiagentregistry.service.auth;

import reactor.core.publisher.Mono;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.fasterxml.jackson.core.JsonProcessingException;

import java.util.List;
import java.util.Optional;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_CREATING_AUTHORIZATION;

/**
 * <AUTHOR>
 */

public class AuthorizationParsingServiceLocalImpl implements AuthorizationParsingService {
    private final AuthorizationParserStrategyFactory _strategyFactory;

    public AuthorizationParsingServiceLocalImpl(AuthorizationParserStrategyFactory strategyFactory) {
        _strategyFactory = strategyFactory;
    }

    @Override
    public Mono<Credentials> createAuthorization(String json, AiProviderAuthSchema authSchema) {

        return Mono.defer(() -> {
            AuthorizationParserStrategy strategy = _strategyFactory.getStrategy(authSchema);

            return Mono.fromCallable(() -> parseCredentials(json, strategy))
                    .onErrorMap(JsonProcessingException.class, e ->
                            new RuntimeException(ERROR_CREATING_AUTHORIZATION, e))
                    .flatMap(Mono::just);
        });
    }

    @Override
    public boolean validateCredentialFormat(String json, AiProviderAuthSchema authSchema) {
        return true;
    }

    @Override
    public Mono<String> fetchAccountId(String json, AiProviderAuthSchema authSchema) {

        return Mono.just("test-provider-account-id");
    }

    @Override
    public boolean validateConnection(String credentials, AiProviderAuthSchema authSchema) {
        return true;
    }

    @Override
    public Optional<List<AiAgentRegistryErrorCode>> verifyImmutableFieldsAreNotChanged(
            String existingCredentialsString, String newCredentialsString,
            AiProviderAuthSchema authSchema) {
        return Optional.empty();
    }

    @Override
    public Mono<String> getMaskedCredentials(String credentials,
            AiProviderAuthSchema authSchema) {
        AuthorizationParserStrategy strategy = _strategyFactory.getStrategy(authSchema);
        return strategy.getMaskedCredentials(credentials);
    }
}
