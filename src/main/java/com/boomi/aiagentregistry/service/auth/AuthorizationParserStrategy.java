// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.auth;

import reactor.core.publisher.Mono;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */

public interface AuthorizationParserStrategy {
    AiProviderAuthSchema getProviderAuthSchema();

    Credentials parseCredentials(JsonNode credentialsNode) throws JsonProcessingException;

    Mono<Credentials> loadAdditionalProperties(Credentials credentials);

    boolean validateCredentialFormat(JsonNode credentialsNode) throws JsonProcessingException;

    Mono<String> fetchAccountId(JsonNode credentialsNode);

    default boolean validateConnection(JsonNode credentialsNode) {
        return true;
    }

    default boolean requiresRegionCheck() {
        return false;
    }

    Optional<List<AiAgentRegistryErrorCode>> verifyImmutableFieldsAreNotChanged(
            String existingCredentialsString, String newCredentialsString);
    default Mono<String> getMaskedCredentials(String credentials) {
        return Mono.just(StringUtils.EMPTY);
    }

    default  boolean isDoCheckIfExternalAccountLinkedToAnotherProviderAccount() {
        return false;
    }

    default Credentials refreshTempAccessTokenIfExpired(Credentials existingCredentials, int refreshWindowInMinutes) {
        return null;
    }

    default boolean checkIsOneExternalAccountWithOneIdpAccountOneAuthType() {
        return true;
    }
}

