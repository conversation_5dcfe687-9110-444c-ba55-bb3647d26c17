// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.service.auth;

import com.boomi.aiagentregistry.constant.ErrorMessages;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Component
public class AuthorizationParserStrategyFactory {
    private final Map<AiProviderAuthSchema, AuthorizationParserStrategy> _strategies;


    public AuthorizationParserStrategyFactory(List<AuthorizationParserStrategy> strategyList) {
        _strategies = strategyList.stream()
                .collect(Collectors.toMap(AuthorizationParserStrategy::getProviderAuthSchema, Function.identity()));
    }

    public AuthorizationParserStrategy getStrategy(AiProviderAuthSchema schemaType) {
        AuthorizationParserStrategy strategy = _strategies.get(schemaType);
        if (strategy == null) {
            throw new IllegalArgumentException(ErrorMessages.format(ErrorMessages.UNSUPPORTED_AUTH_SCHEMA, schemaType));
        }
        return strategy;
    }
}
