// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.auth;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@Accessors(prefix = "_")
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class BoomiApiTokenCredentials implements Credentials {

    @JsonProperty("apiToken")
    private String _apiToken;
    @JsonProperty("jwt")
    private String _jwt;
    @JsonProperty("accountId")
    private String _accountId;
    @JsonProperty("userName")
    private String _userName;
}
