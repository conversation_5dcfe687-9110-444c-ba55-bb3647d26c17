// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.auth;

import reactor.core.publisher.Mono;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;
import java.util.Optional;

import static com.boomi.aiagentregistry.constant.ErrorMessages.UNSUPPORTED_AUTH_SCHEMA;

/**
 * <AUTHOR>
 */

public interface AuthorizationParsingService {

    Mono<Credentials> createAuthorization(String json, AiProviderAuthSchema authSchema);

    boolean validateCredentialFormat(String json, AiProviderAuthSchema authSchema);

    Mono<String> fetchAccountId(String json, AiProviderAuthSchema authSchema);

    default Credentials parseCredentials(String json, AuthorizationParserStrategy strategy)
            throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode credentialsNode = objectMapper.readTree(json);
        if (credentialsNode == null || credentialsNode.isNull()) {
            throw new IllegalArgumentException(UNSUPPORTED_AUTH_SCHEMA);
        }
        return strategy.parseCredentials(credentialsNode);
    }

    default boolean requiresRegionCheck(AiProviderAuthSchema authSchema) {
        return false;
    }

    boolean validateConnection(String credentialsJson, AiProviderAuthSchema authSchema);

    default Credentials getRefreshedTempAccessTokenIfExpired(Credentials existingCredentials,
            AiProviderAuthSchema authSchema, int refreshWindowInMinutes) {
        return null;
    }

    Optional<List<AiAgentRegistryErrorCode>> verifyImmutableFieldsAreNotChanged(
            String existingCredentialsString, String newCredentialsString, AiProviderAuthSchema authSchema);

    Mono<String> getMaskedCredentials(String credentials,
            AiProviderAuthSchema authSchema);

   default boolean isDoCheckIfExternalAccountLinkedToAnotherProviderAccount(AiProviderAuthSchema authSchema) {
       return false;
   }

    default boolean checkIsOneExternalAccountWithOneIdpAccountOneAuthType(AiProviderAuthSchema authSchema) {
        return true;
    }
}
