// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.auth;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@Accessors(prefix = "_")
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class AwsCredentials implements Credentials {
    @JsonProperty("awsAccessKeyId")
    private String _awsAccessKeyId;
    @JsonProperty("awsRegion")
    private String _awsRegion;
    @JsonProperty("awsSecretAccessKey")
    private String _awsSecretAccessKey;
    @JsonProperty("sessionToken")
    private String _sessionToken;
    @JsonProperty("externalId")
    private String _externalId;
    @JsonProperty("expirationEpochMilli")
    private long _expirationEpochMilli;
    @JsonProperty("awsAccountId")
    private String _awsAccountId;
}
