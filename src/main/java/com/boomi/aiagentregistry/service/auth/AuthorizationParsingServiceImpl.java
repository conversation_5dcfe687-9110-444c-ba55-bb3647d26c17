// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.auth;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;
import java.util.Optional;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_CREATING_AUTHORIZATION;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_LOADING_ADDITIONAL_PROPERTIES;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_VALIDATING_CONNECTION;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INVALID_CREDENTIALS_KEYS;

/**
 * <AUTHOR>
 */

@Slf4j
public class AuthorizationParsingServiceImpl implements AuthorizationParsingService {
    private final ObjectMapper _objectMapper;
    private final AuthorizationParserStrategyFactory _strategyFactory;

    public AuthorizationParsingServiceImpl(
            ObjectMapper objectMapper,
            AuthorizationParserStrategyFactory strategyFactory) {
        _objectMapper = objectMapper;
        _strategyFactory = strategyFactory;
    }


    @Override
    public Mono<Credentials> createAuthorization(String json, AiProviderAuthSchema authSchema) {
        return Mono.defer(() -> {
            AuthorizationParserStrategy strategy = _strategyFactory.getStrategy(authSchema);

            return Mono.fromCallable(() -> parseCredentials(json, strategy))
                    .onErrorMap(JsonProcessingException.class, e -> {
                        log.error(ERROR_CREATING_AUTHORIZATION, e);
                        return new RuntimeException(ERROR_CREATING_AUTHORIZATION, e);
                    })
                    .flatMap(credentials ->
                            loadAdditionalProperties(credentials, strategy)
                                    .onErrorMap(error -> {
                                        log.error(ERROR_LOADING_ADDITIONAL_PROPERTIES, error);
                                        return error;
                                    })
                    );
        });
    }


    private static Mono<Credentials> loadAdditionalProperties(Credentials credentials,
                                                              AuthorizationParserStrategy strategy) {
        return strategy.loadAdditionalProperties(credentials);
    }


    @Override
    public boolean validateCredentialFormat(String json, AiProviderAuthSchema authSchema) {

        try {
            AuthorizationParserStrategy strategy = _strategyFactory.getStrategy(authSchema);
            JsonNode credentialsNode = _objectMapper.readTree(json);
            if (credentialsNode == null || credentialsNode.isNull()) {
                log.warn(INVALID_CREDENTIALS_KEYS);
                return false;

            }
            return strategy.validateCredentialFormat(credentialsNode);
        } catch (JsonProcessingException e) {
            log.warn(INVALID_CREDENTIALS_KEYS, e);
            return false;
        }
    }

    @Override
    public Mono<String> fetchAccountId(String json, AiProviderAuthSchema authSchema) {
        return Mono.fromCallable(() -> _strategyFactory.getStrategy(authSchema)).flatMap(
                strategy -> Mono.fromCallable(() -> _objectMapper.readTree(json)).flatMap(credentialsNode -> {
                    if (credentialsNode == null || credentialsNode.isNull()) {
                        log.warn(INVALID_CREDENTIALS_KEYS);
                        return Mono.just("INVALID_CREDENTIALS_KEYS");
                    }
                    return strategy.fetchAccountId(credentialsNode);
                })).onErrorResume(error -> {
            if (error instanceof JsonProcessingException) {
                log.warn(INVALID_CREDENTIALS_KEYS, error);
                return Mono.just("Invalid JSON format");
            } else {
                log.error(ERROR_VALIDATING_CONNECTION, error);
                return Mono.error(error);
            }
        });
    }

    @Override
    public boolean requiresRegionCheck(AiProviderAuthSchema authSchema) {
        return _strategyFactory.getStrategy(authSchema).requiresRegionCheck();
    }

    @Override
    public boolean checkIsOneExternalAccountWithOneIdpAccountOneAuthType(AiProviderAuthSchema authSchema) {
        return _strategyFactory.getStrategy(authSchema).checkIsOneExternalAccountWithOneIdpAccountOneAuthType();
    }

    @Override
    public boolean validateConnection(String credentialsJson, AiProviderAuthSchema authSchema) {
        try {
            AuthorizationParserStrategy strategy = _strategyFactory.getStrategy(authSchema);
            JsonNode credentialsNode = _objectMapper.readTree(credentialsJson);

            if (credentialsNode == null || credentialsNode.isNull()) {
                log.warn(INVALID_CREDENTIALS_KEYS);
                return false;
            }

            return strategy.validateConnection(credentialsNode);
        } catch (JsonProcessingException e) {
            log.warn(INVALID_CREDENTIALS_KEYS, e);
            return false;
        } catch (Exception e) {
            log.error(ERROR_VALIDATING_CONNECTION, e);
            return false;
        }
    }

    @Override
    public Optional<List<AiAgentRegistryErrorCode>> verifyImmutableFieldsAreNotChanged(
            String existingCredentialsString, String newCredentialsString, AiProviderAuthSchema authSchema) {
        return _strategyFactory.getStrategy(authSchema).verifyImmutableFieldsAreNotChanged(existingCredentialsString,
                newCredentialsString);
    }

    @Override
    public Mono<String> getMaskedCredentials(String credentials,
        AiProviderAuthSchema authSchema) {
    AuthorizationParserStrategy strategy = _strategyFactory.getStrategy(authSchema);
    return strategy.getMaskedCredentials(credentials);
    }

    @Override
    public boolean isDoCheckIfExternalAccountLinkedToAnotherProviderAccount(AiProviderAuthSchema authSchema) {
        if (authSchema == null) {
            return false;
        }
        return _strategyFactory.getStrategy(authSchema).isDoCheckIfExternalAccountLinkedToAnotherProviderAccount();
    }

    @Override
    public Credentials getRefreshedTempAccessTokenIfExpired(Credentials existingCredentials,
            AiProviderAuthSchema authSchema, int refreshWindowInMinutes) {
        AuthorizationParserStrategy strategy = _strategyFactory.getStrategy(authSchema);
        return strategy.refreshTempAccessTokenIfExpired(existingCredentials, refreshWindowInMinutes);
    }
}
