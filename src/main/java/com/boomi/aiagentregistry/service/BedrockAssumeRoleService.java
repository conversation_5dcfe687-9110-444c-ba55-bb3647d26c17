// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service;

import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.model.AssumeRoleRequest;
import software.amazon.awssdk.services.sts.model.AssumeRoleResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.HYPHEN;

@Service
@Slf4j
public class BedrockAssumeRoleService {

    private final StsClient _stsClient;
    @Value("${boomi.services.aiagentregistry.customer.aws.role.assume.session_duration_seconds}")
    private int _assumeRoleDurationInSeconds;


    private final String _customerPolicyName;
    // The IAM role name created in the customer's AWS account to be assumed by the Boomi Agent Control Tower
    private final String _customerRoleName;

    private final String _bedrockCustomerPolicyName;
    // The IAM role name created in the customer's AWS account to be assumed by the Boomi Agent Control Tower - for
    // executing Bedrock APIs
    private final String _bedrockCustomerRoleName;
    private final String _oamCustomerPolicyName;
    // The IAM role name created in the customer's AWS account to be assumed by the Boomi Agent Control Tower - for
    // creating OAM link
    private final String _oamCustomerRoleName;
    
    // The complete Role ARN from CloudFormation output
    // Format: arn:aws:iam::{customerAccountId}:role/Boomi-Agent-Control-Tower-bedrock-customer-role
    private static final String ROLE_SESSION_NAME = "BedrockAccessSession";

    @Autowired
    public BedrockAssumeRoleService(
            @Value("${boomi.services.aiagentregistry.customer.aws.role.prefix}") String customerRoleNamePrefix,
            @Value("${boomi.services.aiagentregistry.customer.aws.role.policy.prefix}")
            String customerRolePolicyNamePrefix,
            @Value("${boomi.services.aiagentregistry.customer.aws.bedrock.role.policy.prefix}")
            String bedrockCustomerRolePolicyNamePrefix,
            @Value("${boomi.services.aiagentregistry.customer.aws.bedrock.role.prefix}")
            String bedrockCustomerRoleNamePrefix,
            @Value("${boomi.services.aiagentregistry.customer.aws.oam.role.policy.prefix}")
            String oamCustomerRolePolicyNamePrefix,
            @Value("${boomi.services.aiagentregistry.customer.aws.oam.role.prefix}")
            String oamCustomerRoleNamePrefix,
            @Value("${com.boomi.aiagentregistry.environment}") String environment) {
        _stsClient = StsClient.builder()
            .region(Region.AWS_GLOBAL)
            .build();

        _customerPolicyName = customerRolePolicyNamePrefix + HYPHEN + environment;
        _customerRoleName = customerRoleNamePrefix + HYPHEN + environment;
        _bedrockCustomerPolicyName = bedrockCustomerRolePolicyNamePrefix + HYPHEN + environment;
        _bedrockCustomerRoleName = bedrockCustomerRoleNamePrefix + HYPHEN + environment;
        _oamCustomerPolicyName = oamCustomerRolePolicyNamePrefix + HYPHEN + environment;
        _oamCustomerRoleName = oamCustomerRoleNamePrefix + HYPHEN + environment;
        log.info("customerPolicyName is {} and customerRoleName {}", _customerPolicyName,  _customerRoleName);
        log.info("_bedrockCustomerPolicyName is {} and _bedrockCustomerRoleName {}", _bedrockCustomerPolicyName,
                _bedrockCustomerRoleName);
        log.info("_oamCustomerPolicyName is {} and _oamCustomerRoleName {}", _oamCustomerPolicyName,
                _oamCustomerRoleName);
    }

    public AssumeRoleResponse  getAssumeRoleResponse(String awsAccountId, String awsRegion,
                String externalId) {
        // Construct the role ARN for the IAM role in the customer's AWS account
        String customerAccountRoleArn = String.format("arn:aws:iam::%s:role/%s",
                awsAccountId, _customerRoleName);
        log.info("Assuming role {} for aws account {} and region {}", customerAccountRoleArn,
                awsAccountId, awsRegion);
        AssumeRoleRequest assumeRoleRequest = AssumeRoleRequest.builder()
               .roleArn(customerAccountRoleArn)
               .roleSessionName(ROLE_SESSION_NAME)
               .durationSeconds(_assumeRoleDurationInSeconds)
               .externalId(externalId)
               .build();

       return _stsClient.assumeRole(assumeRoleRequest);
    }

    @PreDestroy
    public void cleanup() {
        if (_stsClient != null) {
            _stsClient.close();
        }
    }

    public String getCustomerPolicyName() {
        return _customerPolicyName;
    }

    public String getCustomerRoleName() {
        return _customerRoleName;
    }

    public String getBedrockCustomerPolicyName() {
        return _bedrockCustomerPolicyName;
    }

    public String getBedrockCustomerRoleName() {
        return _bedrockCustomerRoleName;
    }

    public String getOamCustomerPolicyName() {
        return _oamCustomerPolicyName;
    }

    public String getOamCustomerRoleName() {
        return _oamCustomerRoleName;
    }
}
