// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.graphql.server.schema.types.AiAgentTask;
import com.boomi.graphql.server.schema.types.AiAgentTool;

import java.util.List;
import java.util.concurrent.CompletionStage;

/**
 * <AUTHOR>
 */
public interface AiAgentTaskToolService {

    CompletionStage<List<AiAgentTool>> getAiAgentTaskTools(AiAgentTask aiAgentTask,
            DataFetchingEnvironment dfe);

    CompletionStage<List<String>> getInstructions(AiAgentTask aiAgentTask,
            DataFetchingEnvironment dfe);
}
