// Copyright (c) 2024 Boom<PERSON>, LP.
package com.boomi.aiagentregistry.service;

import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.repo.AiAgentRepository;

import org.springframework.stereotype.Component;


@Component("AGENT")
public class AiAgentManager implements ReferenceEntityManager {

    private final AiAgentRepository _aiAgentRepository;

    public AiAgentManager(AiAgentRepository aiAgentRepository) {
        _aiAgentRepository = aiAgentRepository;
    }

    @Override
    public AiAgent getAgent(String entityId) {
        return _aiAgentRepository.findByGuid(entityId).orElse(null);
    }

    @Override
    public Integer getRelatedEntityUid(String entityId) {
        AiAgent agent = _aiAgentRepository.findByGuid(entityId).orElse(null);
        assert agent != null;
        return agent.getUid();
    }
}
