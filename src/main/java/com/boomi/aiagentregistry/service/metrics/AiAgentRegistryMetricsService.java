// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import graphql.schema.DataFetchingEnvironment;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.util.UserUtil;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentRegistryAgentMetric;
import com.boomi.graphql.server.schema.types.AiAgentRegistryAggregateMetrics;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiAgentRegistryGraphMetrics;
import com.boomi.graphql.server.schema.types.AiAgentRegistryMetricsErrorCode;
import com.boomi.graphql.server.schema.types.AiAgentRegistryMetricsInput;
import com.boomi.graphql.server.servlet.ErrorUtil;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.io.UncheckedIOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AiAgentRegistryMetricsService {

    private final TimestreamService _timestreamService;
    private final AiAgentProviderAccountRepository _aiAgentProviderAccountRepository;
    private final UserUtil _userUtil;

    @Value("${boomi.services.timestream.database_name}")
    private String _databaseName;
    @Value("${boomi.services.timestream.agent_metrics_table_name}")
    private String _agentMetricsTableName;
    @Value("${boomi.services.timestream.bedrock_metrics_table_name}")
    private String _bedrockMetricsTableName;

    private String _agentMetricsTable;
    private String _bedrockMetricsTable;

    public static final String AGENT_EXTERNAL_ID_FIELD = "agentExternalId";
    public static final String ALIAS_EXTERNAL_ID_FIELD = "aliasExternalId";
    public static final String PROVIDER_ACCOUNT_GUID_FIELD = "providerAccountGuId";
    public static final String PROVIDER_TYPE_FIELD = "providerType";
    public static final String AI_AGENT_LISTING_FIELD = "aiAgentListing";
    private static final int ZERO_COUNT = 0;

    private static final String WHERE_TIME = """
            time >= '%s' AND time < '%s'
    """;

    private static final String WHERE_PROVIDER_ACCOUNT_ID_IN = """
                AND ProviderAccountId IN (%s)
    """;

    private static final String WHERE_IDP_ACCOUNT_ID_EQ = """
                AND AccountId = '%s'
    """;

    private static final String WHERE_PROVIDER_TYPE_IN = """
                AND ProviderType IN (%s)
    """;

    private static final String WHERE_EXT_MODEL_ID_IN = """
                AND ExtModelId IN (%s)
    """;

    // Combined query templates
    private static final String COMBINED_UNION_QUERY = """
            combined_query AS (
                SELECT * FROM final_bedrock_query
                UNION ALL
                SELECT * FROM final_unified_providers_query
            )
    """;

    private static final String COMBINED_BEDROCK_ONLY_QUERY = """
        combined_query AS (
            SELECT * FROM final_bedrock_query
        )
    """;

    private static final String COMBINED_UNIFIED_ONLY_QUERY = """
        combined_query AS (
            SELECT * FROM final_unified_providers_query
        )
    """;

    private static final String BIN_DURATION = "<BIN_DURATION>";
    private static final String AI_AGENT_REGISTRY_METRICS_TABLE_PLACEHOLDER = "<AI_AGENT_REGISTRY_METRICS_TABLE>";
    private static final String BEDROCK_TABLE_PLACEHOLDER = "<BEDROCK_TABLE>";
    private static final String COMBINED_QUERY_PLACEHOLDER = "<COMBINED_QUERY>";
    private static final String FINAL_UNIFIED_PROVIDERS_QUERY = "<FINAL_UNIFIED_PROVIDERS_QUERY>";
    private static final String FINAL_BEDROCK_QUERY_PLACEHOLDER = "<FINAL_BEDROCK_QUERY>";
    private static final String WHERE_CLAUSE_FILTERS_PLACEHOLDER = "<WHERE_CLAUSE_FILTERS>";
    private static final String PROVIDER_TYPE_FILTER_PLACEHOLDER = "<PROVIDER_TYPE_FILTER>";
    private static final String INVALID_TIME = "End time {} is not less than start time {}";
    private static final String COMMA_SEPARATOR = ", ";
    private static final String SINGLE_QUOTE = "'";
    private static final String INVOCATIONS_METRICS_SQL = "sql.templates/invocations_metrics_query_template.sql";
    private static final String GRAPH_METRICS_SQL = "sql.templates/graph_metrics_query_template.sql";
    private static final String AGGREGATE_METRICS_SQL = "sql.templates/aggregate_metrics_query_template.sql";
    private static final String INVOCATIONS_METRICS_BEDROCK_SQL =
            "sql.templates/invocations_metrics_bedrock_query_template.sql";
    private static final String INVOCATIONS_METRICS_UNIFIED_SQL =
            "sql.templates/invocations_metrics_unified_providers_query_template.sql";
    private static final String GRAPH_METRICS_BEDROCK_SQL =
            "sql.templates/graph_metrics_bedrock_query_template.sql";
    private static final String GRAPH_METRICS_UNIFIED_SQL =
            "sql.templates/graph_metrics_unified_providers_query_template.sql";
    private static final String AGGREGATE_METRICS_BEDROCK_SQL =
            "sql.templates/aggregate_metrics_bedrock_query_template.sql";
    private static final String AGGREGATE_METRICS_UNIFIED_SQL =
            "sql.templates/aggregate_metrics_unified_providers_query_template.sql";
    private static final int SINGLE_INPUT = 1;

    private static final EnumSet<AiAgentProviderType> EXCLUDED_PROVIDER_TYPES =
            EnumSet.of(AiAgentProviderType.CUSTOM, AiAgentProviderType.AWS_BEDROCK);

    // NON_BEDROCK Provider Types
    private static final EnumSet<AiAgentProviderType> UNIFIED_PROVIDER_TYPES =
            EnumSet.complementOf(EXCLUDED_PROVIDER_TYPES);

    @PostConstruct
    public void init() {

        _agentMetricsTable = String.format("""
                   "%s"."%s"
           """, _databaseName, _agentMetricsTableName);

        _bedrockMetricsTable = String.format("""
                   "%s"."%s"
           """, _databaseName, _bedrockMetricsTableName);
    }

    public CompletableFuture<List<AiAgentRegistryGraphMetrics>> getGraphMetrics(AiAgentRegistryMetricsInput input,
            DataFetchingEnvironment dfe) {

        if (!validateInput(input.getRequestTsStart(), input.getRequestTsEnd(), dfe)) {
            return CompletableFuture.completedFuture(null);
        }

        String accountFilter;

        if (isEmptyProviderIdsInput(input, dfe)) {
            if (ErrorUtil.hasErrors(dfe)) {
                return CompletableFuture.completedFuture(null);
            }
            accountFilter = constructIdpAccountIdFilter(dfe);
        } else {
            Set<String> providerAccountIds = validateProviderAccountIds(input, dfe);
            if (providerAccountIds.isEmpty()) {
                return CompletableFuture.completedFuture(null);
            }
            accountFilter = constructProviderAccountIdsFilter(providerAccountIds);
        }

        String graphMetricsSql = constructRegistryMetricsSqlQuery(input, accountFilter,GRAPH_METRICS_SQL,
                GRAPH_METRICS_BEDROCK_SQL, GRAPH_METRICS_UNIFIED_SQL, true);

        if (graphMetricsSql.isEmpty()) {
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<List<AiAgentRegistryGraphMetrics>> completion = new CompletableFuture<>();

        _timestreamService.execute(graphMetricsSql,
                (rowValues, columnInfos) -> _timestreamService.mapTimestreamRowToType(rowValues, columnInfos, dfe,
                        completion, AiAgentRegistryGraphMetrics.class, false),
                (aiAgentRegistryGraphMetrics, executionException) -> _timestreamService.processTimestreamCompletionList(
                        graphMetricsSql, aiAgentRegistryGraphMetrics, executionException, dfe, completion));

        return completion;
    }

    public CompletionStage<AiAgentRegistryAggregateMetrics> getAggregateMetrics(
            @NotNull AiAgentRegistryMetricsInput input, DataFetchingEnvironment dfe) {

        if (!validateInput(input.getRequestTsStart(), input.getRequestTsEnd(), dfe)) {
           return CompletableFuture.completedFuture(null);
        }

        String accountFilter;

        if (isEmptyProviderIdsInput(input, dfe)) {
            if (ErrorUtil.hasErrors(dfe)) {
                return CompletableFuture.completedFuture(null);
            }
            accountFilter = constructIdpAccountIdFilter(dfe);
        } else {
            Set<String> providerAccountIds = validateProviderAccountIds(input, dfe);
            if (providerAccountIds.isEmpty()) {
                return CompletableFuture.completedFuture(null);
            }
            accountFilter = constructProviderAccountIdsFilter(providerAccountIds);
        }

        String aggregateMetricsSql = constructRegistryMetricsSqlQuery(input, accountFilter,AGGREGATE_METRICS_SQL,
                AGGREGATE_METRICS_BEDROCK_SQL, AGGREGATE_METRICS_UNIFIED_SQL, false);

        if (aggregateMetricsSql.isEmpty()) {
           return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<AiAgentRegistryAggregateMetrics> completion = new CompletableFuture<>();

        _timestreamService.execute(aggregateMetricsSql,
               (rowValues, columnInfos) ->
                       _timestreamService.mapTimestreamRowToType(rowValues, columnInfos, dfe,
                               completion, AiAgentRegistryAggregateMetrics.class, false),
               (aiAgentRegistryGraphMetrics, executionException) ->
                       _timestreamService.processTimestreamCompletionSingle(aggregateMetricsSql,
                               aiAgentRegistryGraphMetrics, executionException, dfe, completion));

        return completion;
    }

    private static boolean validateInput(Date startTs, Date endTs, DataFetchingEnvironment dfe) {
        long endTime = endTs.getTime();
        long startTime = startTs.getTime();
        if (endTime <= startTime) {
            log.info(INVALID_TIME, endTs, startTs);
            ErrorUtil
                    .addError(dfe, AiAgentRegistryMetricsErrorCode.START_TS_INPUT_NOT_LESS_THAN_END_TS, endTs, startTs);
            return false;
        }

        return true;
    }

    private @NotNull String getWhereClause(AiAgentRegistryMetricsInput input, String accountFilter) {

        String modelFilter = constructModelIdFilter(input);

        String timeFilter = constructTimeFilter(input);

        // Combine filters into where clause
        return (timeFilter + accountFilter + modelFilter);
    }

    private boolean isEmptyProviderIdsInput(AiAgentRegistryMetricsInput input, DataFetchingEnvironment dfe) {
        List<String> inputIds = input.getProviderAccountIds();
        if (inputIds != null && !inputIds.isEmpty()) {
            return false;
        }

        int providerAccountsCountByIdpAccountId = _aiAgentProviderAccountRepository
                .findProviderAccountsCountByIdpAccountId(_userUtil.getAccountId(dfe));

        if (providerAccountsCountByIdpAccountId == ZERO_COUNT) {
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.NO_PROVIDER_ACCOUNTS_FOUND);
        }

        return true;
    }

    private Set<String> validateProviderAccountIds(AiAgentRegistryMetricsInput input, DataFetchingEnvironment dfe) {
        List<String> inputIds = input.getProviderAccountIds();

        Set<String> validIds = new HashSet<>(inputIds.size());
        for (String id : inputIds) {
            if (!isValidUUID(id, dfe)) {
                return Collections.emptySet();
            }
            validIds.add(id);
        }

        String idpAccountId = _userUtil.getAccountId(dfe);
        boolean allExist = _aiAgentProviderAccountRepository
                .getProviderAccountsCountByAccountGuidsAndIdpAccountId(validIds, idpAccountId) == validIds.size();

        if (!allExist) {
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_ID_NOT_FOUND);
            return Collections.emptySet();
        }

        return validIds;
    }

    private static boolean isValidUUID(String id, DataFetchingEnvironment dfe) {
        try {
            UUID.fromString(id);
            return true;
        } catch (IllegalArgumentException e) {
            log.error("Invalid Provider Account ID: {}", id, e);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.INVALID_PROVIDER_ACCOUNT_ID, id);
            return false;
        }
    }

    private static Set<String> validateStringArrayInput(List<String> stringArrayInput) {
        return Optional.ofNullable(stringArrayInput)
                .map(set -> set.stream().map(Object::toString).collect(Collectors.toSet()))
                .filter(ids -> !(ids.size() == SINGLE_INPUT && ids.contains(StringUtils.EMPTY)))
                .orElse(Collections.emptySet());
    }

    private static String constructProviderAccountIdsFilter(Set<String> providerAccountIds) {
        return String.format(WHERE_PROVIDER_ACCOUNT_ID_IN, getCommaSeparatedString(providerAccountIds));
    }

    private String constructIdpAccountIdFilter(DataFetchingEnvironment dfe) {
        return String.format(WHERE_IDP_ACCOUNT_ID_EQ, _userUtil.getAccountId(dfe));
    }

    private static String constructModelIdFilter(AiAgentRegistryMetricsInput input) {
        Set<String> modelIds = validateStringArrayInput(input.getModelIds());
        return (modelIds == null || modelIds.isEmpty())
                ? StringUtils.EMPTY
                : String.format(WHERE_EXT_MODEL_ID_IN, getCommaSeparatedString(modelIds));
    }

    private static String constructProviderTypeFilter(Set<AiAgentProviderType> providerTypes) {
        String joinedProviderTypes = providerTypes.stream()
                .map(aiAgentProviderType -> SINGLE_QUOTE + aiAgentProviderType.name() + SINGLE_QUOTE)
                .collect(Collectors.joining(COMMA_SEPARATOR));
        return String.format(WHERE_PROVIDER_TYPE_IN, joinedProviderTypes);
    }

    private static EnumSet<AiAgentProviderType> validateProviderTypesInput(AiAgentRegistryMetricsInput input) {
        return Optional.ofNullable(input.getProviderTypes())
                .filter(types -> !types.isEmpty())
                .map(EnumSet::copyOf)
                .orElseGet(() -> EnumSet.noneOf(AiAgentProviderType.class));
    }

    private String constructTimeFilter(AiAgentRegistryMetricsInput input) {
        return String.format(WHERE_TIME,
                _timestreamService.formatTimestreamTimeFilter(input.getRequestTsStart()),
                _timestreamService.formatTimestreamTimeFilter(input.getRequestTsEnd()));
    }

    private static String getCommaSeparatedString(Set<String> ids) {
        return ids.stream()
                .map(id -> SINGLE_QUOTE + id + SINGLE_QUOTE)
                .collect(Collectors.joining(COMMA_SEPARATOR));
    }

    public CompletionStage<List<AiAgentRegistryAgentMetric>> getInvocationsMetrics(
            @NotNull AiAgentRegistryMetricsInput input, DataFetchingEnvironment dfe) {

        if (!validateInput(input.getRequestTsStart(), input.getRequestTsEnd(), dfe)) {
            return CompletableFuture.completedFuture(null);
        }

        String accountFilter;

        if (isEmptyProviderIdsInput(input, dfe)) {
            if (ErrorUtil.hasErrors(dfe)) {
                return CompletableFuture.completedFuture(null);
            }
            accountFilter = constructIdpAccountIdFilter(dfe);
        } else {
            Set<String> providerAccountIds = validateProviderAccountIds(input, dfe);
            if (providerAccountIds.isEmpty()) {
                return CompletableFuture.completedFuture(null);
            }
            accountFilter = constructProviderAccountIdsFilter(providerAccountIds);
        }

        String invocationsMetricsSql = constructRegistryMetricsSqlQuery(input, accountFilter,
                INVOCATIONS_METRICS_SQL, INVOCATIONS_METRICS_BEDROCK_SQL, INVOCATIONS_METRICS_UNIFIED_SQL, false);

        if (invocationsMetricsSql.isEmpty()) {
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<List<AiAgentRegistryAgentMetric>> completion = new CompletableFuture<>();

        _timestreamService.execute(invocationsMetricsSql,
                (rowValues, columnInfos) ->
                        _timestreamService.mapTimestreamRowToType(rowValues, columnInfos, dfe,
                                completion, AiAgentRegistryAgentMetric.class, true),
                (aiAgentInvocationsMetrics, executionException) ->
                        _timestreamService.processTimestreamCompletionList(invocationsMetricsSql,
                        aiAgentInvocationsMetrics, executionException, dfe, completion));

        return completion;
    }

    public String constructRegistryMetricsSqlQuery(
            AiAgentRegistryMetricsInput input,
            String accountFilter,
            String mainQueryTemplate,
            String bedrockQueryTemplate,
            String unifiedProvidersQueryTemplate,
            boolean isGraphMetrics) {

        String whereClauseFilters = getWhereClause(input, accountFilter);
        EnumSet<AiAgentProviderType> inputProviderTypes = validateProviderTypesInput(input);

        boolean hasBedrock = inputProviderTypes.isEmpty() ||
                inputProviderTypes.contains(AiAgentProviderType.AWS_BEDROCK);
        EnumSet<AiAgentProviderType> matchedUnifiedProviders = inputProviderTypes.isEmpty()
                ? EnumSet.copyOf(UNIFIED_PROVIDER_TYPES)
                : inputProviderTypes.stream()
                        .filter(UNIFIED_PROVIDER_TYPES::contains)
                        .collect(Collectors.toCollection(() -> EnumSet.noneOf(AiAgentProviderType.class)));
        boolean hasUnifiedProvider = !matchedUnifiedProviders.isEmpty();

        if (!hasBedrock && !hasUnifiedProvider) {
            log.warn("No valid provider types found to construct sql query for input: {} and accountFilter: {}",
                    input, accountFilter);
            throw new IllegalArgumentException("No valid provider types found");
        }

        String unifiedProviderTypesFilter = hasUnifiedProvider
                ? constructProviderTypeFilter(matchedUnifiedProviders)
                : StringUtils.EMPTY;

        String binDuration = isGraphMetrics
                ? TimestreamIntervalCalculator
                .calculateTimestreamInterval(input.getRequestTsStart(), input.getRequestTsEnd())
                : null;

        if (binDuration != null) {
            log.info("Bin duration is {}", binDuration);
        }

        String bedrockSql = buildSql(loadSql(bedrockQueryTemplate), _bedrockMetricsTable, whereClauseFilters,
                StringUtils.EMPTY, binDuration);
        String unifiedSql = buildSql(loadSql(unifiedProvidersQueryTemplate), _agentMetricsTable, whereClauseFilters,
                unifiedProviderTypesFilter, binDuration);

        String nonUnionQuery = hasBedrock ? COMBINED_BEDROCK_ONLY_QUERY : COMBINED_UNIFIED_ONLY_QUERY;
        String combinedQuery = hasBedrock && hasUnifiedProvider ? COMBINED_UNION_QUERY : nonUnionQuery;

        return loadSql(mainQueryTemplate)
                .replace(FINAL_BEDROCK_QUERY_PLACEHOLDER, hasBedrock ? bedrockSql : StringUtils.EMPTY)
                .replace(FINAL_UNIFIED_PROVIDERS_QUERY, hasUnifiedProvider ? unifiedSql : StringUtils.EMPTY)
                .replace(COMBINED_QUERY_PLACEHOLDER, combinedQuery);
    }

    private static String buildSql(String template, String tableName, String whereClause, String providerTypeFilter,
            String binDuration) {
        String sql = template
                .replace(AI_AGENT_REGISTRY_METRICS_TABLE_PLACEHOLDER, tableName)
                .replace(BEDROCK_TABLE_PLACEHOLDER, tableName)
                .replace(WHERE_CLAUSE_FILTERS_PLACEHOLDER, whereClause)
                .replace(PROVIDER_TYPE_FILTER_PLACEHOLDER, providerTypeFilter);
        return binDuration != null ? sql.replace(BIN_DURATION, binDuration) : sql;
    }

    private static String loadSql(String path) {
        try (InputStream is = AiAgentRegistryMetricsService.class.getClassLoader().getResourceAsStream(path)) {
            if (is == null) {
                throw new IllegalArgumentException("SQL file not found: " + path);
            }
            return new String(is.readAllBytes(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new UncheckedIOException("Failed to load SQL file: " + path, e);
        }
    }
}
