// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import software.amazon.awssdk.auth.credentials.AwsSessionCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.oam.OamClient;

import org.springframework.stereotype.Service;

// created this class to help mock in integration tests
@Service
public class OamClientHelperService {

    public OamClient createOamClient(Region region) {
            return OamClient.builder()
                .region(region)
                .build();
    }

    public OamClient createOamClient(Region region, AwsSessionCredentials sessionCredentials) {
        return OamClient
                .builder()
                .region(region)
                .credentialsProvider(StaticCredentialsProvider.create(sessionCredentials))
                .build();
    }
}
