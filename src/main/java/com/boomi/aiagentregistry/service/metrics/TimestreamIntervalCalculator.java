// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import java.time.Duration;
import java.time.Instant;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * Utility class to calculate appropriate time intervals for Timestream bin function.
 * Determines bucket sizes based on the time range to maintain optimal number of data points (60-180 buckets).
 * <p>
 * Time Range -> Bucket Size (Target Buckets):
 * ≤ 30 min -> 5 min (7 buckets)
 * ≤ 1 hour -> 10 min (7 buckets)
 * ≤ 3 hours -> 30 min (7 buckets)
 * ≤ 6 hours -> 1 hour (7 buckets)
 * ≤ 12 hours -> 2 hours (7 buckets)
 * ≤ 24 hours -> 4 hours (7 buckets)
 * ≤ 7 days -> 1 day (8 buckets)
 * ≤ 30 days -> 1 week (5 buckets)
 * ≤ 3 months -> 2 weeks (7 buckets)
 * > 3 months -> 1 month (7 buckets)
 */

/**
 * Calculates the appropriate interval for AWS Timestream bin function based on the time range
 */
public class TimestreamIntervalCalculator {

    // Timestream interval strings
    private static final String FIVE_MINUTES_INTERVAL = "5m";
    private static final String TEN_MINUTES_INTERVAL = "10m";
    private static final String THIRTY_MINUTES_INTERVAL = "30m";
    private static final String ONE_HOUR_INTERVAL = "1h";
    private static final String TWO_HOURS_INTERVAL = "2h";
    private static final String FOUR_HOURS_INTERVAL = "4h";
    private static final String ONE_DAY_INTERVAL = "1d";
    private static final String ONE_WEEK_INTERVAL = "7d";
    private static final String TWO_WEEKS_INTERVAL = "14d";
    private static final String ONE_MONTH_INTERVAL = "30d";

    // Time range thresholds in seconds
    // 30 * 60
    private static final int THIRTY_MINUTES = 1800;
    // 3 * 60 * 60
    private static final int THREE_HOURS_SECONDS = 10800;
    // 60 * 60
    private static final int ONE_HOUR_SECONDS = 3600;
    // 6 * 60 * 60
    private static final int SIX_HOURS_SECONDS = 21600;
    // 12 * 60 * 60
    private static final int TWELVE_HOURS_SECONDS = 43200;
    // 24 * 60 * 60
    private static final int ONE_DAY_SECONDS = 86400;
    // 7 * 24 * 60 * 60
    private static final int SEVEN_DAYS_SECONDS = 604800;
    // 30 * 24 * 60 * 60
    private static final int THIRTY_DAYS_SECONDS = 2592000;
    // 90 * 24 * 60 * 60
    private static final int THREE_MONTHS_SECONDS = 7776000;
    private static final String UTC = "UTC";

    // Private constructor to prevent instantiation
    private TimestreamIntervalCalculator() {
    }

    /**
     * @param startTime Start time of the query range
     * @param endTime   End time of the query range
     * @return Interval string in Timestream format (e.g., "30s", "1m", "1h", "1d")
     */
    public static String calculateTimestreamInterval(Date startTime, Date endTime) {
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(startTime);
        startCal.setTimeZone(TimeZone.getTimeZone(UTC));

        Calendar endCal = Calendar.getInstance();
        endCal.setTime(endTime);
        endCal.setTimeZone(TimeZone.getTimeZone(UTC));

        // Convert to Instant for duration calculation
        Instant startInstant = startCal.toInstant();
        Instant endInstant = endCal.toInstant();
        Duration timeRange = Duration.between(startInstant, endInstant).abs();
        long timeRangeSeconds = timeRange.toSeconds();
        int seconds = (int) timeRangeSeconds;

        // Default case for zero duration
        if (seconds == 0) {
            return FIVE_MINUTES_INTERVAL;
        }
        // 30 minutes: 5-minute buckets (7 buckets)
        else if (seconds <= THIRTY_MINUTES) {
            return FIVE_MINUTES_INTERVAL;
        }
        // 1 hour: 10-minute buckets (7 buckets)
        else if (seconds <= ONE_HOUR_SECONDS) {
            return TEN_MINUTES_INTERVAL;
        }
        // 3 hours: 30-minute buckets (7 buckets)
        else if (seconds <= THREE_HOURS_SECONDS) {
            return THIRTY_MINUTES_INTERVAL;
        }
        // 6 hours: 1-hour buckets (7 buckets)
        else if (seconds <= SIX_HOURS_SECONDS) {
            return ONE_HOUR_INTERVAL;
        }
        // 12 hours: 2-hour buckets (7 buckets)
        else if (seconds <= TWELVE_HOURS_SECONDS) {
            return TWO_HOURS_INTERVAL;
        }
        // 24 hours: 4-hour buckets (7 buckets)
        else if (seconds <= ONE_DAY_SECONDS) {
            return FOUR_HOURS_INTERVAL;
        }
        // 7 days: 1-day buckets (8 buckets)
        else if (seconds <= SEVEN_DAYS_SECONDS) {
            return ONE_DAY_INTERVAL;
        }
        // 30 days: 1-week buckets (5 buckets)
        else if (seconds <= THIRTY_DAYS_SECONDS) {
            return ONE_WEEK_INTERVAL;
        }
        // 3 months: 2-week buckets (7 buckets)
        else if (seconds <= THREE_MONTHS_SECONDS) {
            return TWO_WEEKS_INTERVAL;
        }
        else {
            // 6 months or more: 1-month buckets
            return ONE_MONTH_INTERVAL;
        }
    }
}
