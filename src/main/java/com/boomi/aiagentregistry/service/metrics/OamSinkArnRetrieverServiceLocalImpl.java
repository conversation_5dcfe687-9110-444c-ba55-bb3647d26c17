// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import software.amazon.awssdk.regions.Region;
import com.boomi.graphql.server.schema.types.AWSRegion;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

public class OamSinkArnRetrieverServiceLocalImpl implements OamSinkArnRetrieverService {

    private static final Map<String, String> REGION_TO_SINK_ARN_MAP = Map.of(Region.US_EAST_1.id(),
            "arn:aws:oam:us-east-1:066499543631:sink/b1d64f36-6af9-4899-b478-8d5b43f8caec", Region.US_WEST_2.id(),
            "arn:aws:oam:us-west-2:066499543631:sink/c28e10c7-3253-4f99-a4cc-773407397c9b");

    // Ignore "Mutable members should not be stored or returned directly" because the stored reference
    // is unmodifiable
    @SuppressWarnings("java:S2384")
    @Override
    public Map<String, String> getRegionToSinkArnUnmodifiableMap() {
        return REGION_TO_SINK_ARN_MAP;
    }

    @Override
    public List<AWSRegion> getAwsRegionUnmodifiableList() {
        return Stream.of(
            new AWSRegion(Region.US_EAST_1.id(), Region.US_EAST_1.id()),
            new AWSRegion(Region.US_WEST_2.id(), Region.US_WEST_2.id())
        ).toList();
    }

    @Override
    public Set<String> getUnmodifiableRegionsSet() {
        return Set.of(Region.US_EAST_1.id(), Region.US_WEST_2.id());
    }

    @Override
    public String getSinkArn(String region) {
        return REGION_TO_SINK_ARN_MAP.get(region);
    }
}
