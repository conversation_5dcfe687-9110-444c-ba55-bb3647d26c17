// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import com.boomi.graphql.server.schema.types.AWSRegion;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface OamSinkArnRetrieverService {

    Map<String, String> getRegionToSinkArnUnmodifiableMap();

    List<AWSRegion> getAwsRegionUnmodifiableList();

    Set<String> getUnmodifiableRegionsSet();

    String getSinkArn(String region);
}
