// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.ec2.Ec2Client;
import software.amazon.awssdk.services.ec2.model.DescribeRegionsResponse;
import software.amazon.awssdk.services.oam.OamClient;
import software.amazon.awssdk.services.oam.model.ListSinksItem;
import software.amazon.awssdk.services.oam.model.ListSinksRequest;
import software.amazon.awssdk.services.oam.model.ListSinksResponse;
import software.amazon.awssdk.services.oam.model.OamException;

import com.boomi.graphql.server.schema.types.AWSRegion;

import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static software.amazon.awssdk.regions.Region.US_EAST_1;

@Slf4j
public class OamSinkArnRetrieverServiceImpl implements OamSinkArnRetrieverService {

    private final Map<String, String> _regionToSinkArnUnmodifiableMap;
    private final List<AWSRegion> _regionToLabelUnmodifiableMap;
    private final Set<String> _regions;

    private static final Set<String> EXCLUDED_REGIONS = Set.of(
            // Global Regions
            "aws-global", "aws-cn-global", "aws-us-gov-global", "aws-iso-global", "aws-iso-b-global",

            // GovCloud Regions
            "us-gov-east-1", "us-gov-west-1",

            // ISO Regions
            "us-isob-east-1", "us-iso-west-1", "us-iso-east-1", "us-isof-east-1", "us-isof-south-1", "eu-isoe-west-1",

            // China Regions
            "cn-north-1", "cn-northwest-1");


    public OamSinkArnRetrieverServiceImpl(String primaryRegion, boolean isMetricStreamAvailableInAllRegions) {
        log.info("primary region {}, isMetricStreamAvailableInAllRegions {} ", primaryRegion,
                isMetricStreamAvailableInAllRegions);
        _regionToSinkArnUnmodifiableMap = extractRegionToSinkIdMap(primaryRegion, isMetricStreamAvailableInAllRegions);
        _regionToLabelUnmodifiableMap = getRegionToLabelMap();
        _regions = _regionToLabelUnmodifiableMap
                .stream()
                .map(AWSRegion::getCode)
                .collect(Collectors.toUnmodifiableSet());
        log.info("regionToSinkArnUnmodifiableMap {}", _regionToSinkArnUnmodifiableMap);
        log.info("regionToLabelUnmodifiableMap {}", _regionToLabelUnmodifiableMap);
    }

    // Ignore "Mutable members should not be stored or returned directly" because the stored reference
    // is unmodifiable
    @SuppressWarnings("java:S2384")
    @Override
    public Map<String, String> getRegionToSinkArnUnmodifiableMap() {
        return _regionToSinkArnUnmodifiableMap;
    }

    // Ignore "Mutable members should not be stored or returned directly" because the stored reference
    // is unmodifiable
    @SuppressWarnings("java:S2384")
    @Override
    public List<AWSRegion> getAwsRegionUnmodifiableList() {
        return _regionToLabelUnmodifiableMap;
    }

    // Ignore "Mutable members should not be stored or returned directly" because the stored reference
    // is unmodifiable
    @SuppressWarnings("java:S2384")
    @Override
    public Set<String> getUnmodifiableRegionsSet() {
        return _regions;
    }

    @Override
    public String getSinkArn(String region) {
            return _regionToSinkArnUnmodifiableMap.get(region);
    }


    /**
     * Extracts sink IDs from monitoring account sink ARNs for all applicable regions
     *
     * @return Map of AWS region to sink ID where sinks exist
     */
    private Map<String, String> extractRegionToSinkIdMap(String primaryRegion,
            boolean isMetricStreamAvailableInAllRegions) {
        if (!isMetricStreamAvailableInAllRegions) {
            log.warn("Metrics stream is not available in all regions!!!");
            return extractRegionToSinkIdMapInUsEast1();
        }
        Map<String, String> regionToSinkArnMap = new HashMap<>();
        try (Ec2Client ec2Client =
                Ec2Client
                        .builder()
                        .region(Region.of(primaryRegion))
                        .build()) {

            // Makes an API call to AWS to get the current, live list of regions
            DescribeRegionsResponse liveRegionsResponse = ec2Client.describeRegions();

            for (software.amazon.awssdk.services.ec2.model.Region liveRegion : liveRegionsResponse.regions()) {
                String regionName = liveRegion.regionName();

                if (EXCLUDED_REGIONS.contains(regionName)) {
                    log.info("Skipping excluded liveRegion: {}", regionName);
                    continue;
                }
                populateSinkArnForRegion(regionName, regionToSinkArnMap);
            }

            return doAfterSinkArnsArePopulated(regionToSinkArnMap);
        } catch (Exception e) {
            log.error("Failed to list OAM sinks across regions", e);
            throw new com.boomi.aiagentregistry.exception.OamException("Failed to list OAM sinks across regions", e);
        }
    }

    private static Map<String, String> extractRegionToSinkIdMapInUsEast1() {
        Map<String, String> regionToSinkArnMap = new HashMap<>(1);
        populateSinkArnForRegion(US_EAST_1.id(), regionToSinkArnMap);
        return doAfterSinkArnsArePopulated(regionToSinkArnMap);
    }

    private static @NotNull Map<String, String> doAfterSinkArnsArePopulated(Map<String, String> regionToSinkArnMap) {
        if (regionToSinkArnMap.isEmpty()) {
            throw new com.boomi.aiagentregistry.exception.OamException("No monitoring account "
                    + "sinks found in any checked region");
        } else {
            log.info("Found following sinkArns {}", regionToSinkArnMap);
            return Collections.unmodifiableMap(regionToSinkArnMap);
        }
    }

    private static void populateSinkArnForRegion(String regionName, Map<String, String> regionToSinkArnMap) {
        try (OamClient regionalOamClient = OamClient
                .builder()
                .region(Region.of(regionName))
                .build()) {
            ListSinksResponse response = regionalOamClient.listSinks(ListSinksRequest.builder().build());

            // A liveRegion might have no sinks - only add if sink exists
            if (CollectionUtils.isNotEmpty(response.items())) {
                ListSinksItem sink = response.items().get(0);
                // e.g sink ARN:
                // arn:aws:oam:us-east-1:************:sink/abcd1234-a123-456a-a12b-a123b4cd5678
                String sinkArn = sink.arn();
                regionToSinkArnMap.put(regionName, sinkArn);
                log.info("Found sink ARN: {} in liveRegion: {}", sinkArn, regionName);
            } else {
                log.info("No sink found in liveRegion: {}", regionName);
            }
        } catch (OamException e) {
            log.warn("Error listing OAM sinks in liveRegion {}", regionName, e);
        } catch (Exception e) {
            log.warn("Failed to check sinks in liveRegion {}", regionName, e);
        }
    }

    private List<AWSRegion> getRegionToLabelMap() {
        return _regionToSinkArnUnmodifiableMap
                .keySet()
                .stream()
                .map(regionName -> new AWSRegion(regionName, regionName))
                .toList();
    }
}
