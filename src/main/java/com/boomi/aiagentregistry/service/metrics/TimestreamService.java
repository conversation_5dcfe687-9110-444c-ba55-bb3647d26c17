// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import graphql.schema.DataFetchingEnvironment;
import jakarta.annotation.Nullable;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.timestreamquery.TimestreamQueryAsyncClient;
import software.amazon.awssdk.services.timestreamquery.model.CancelQueryRequest;
import software.amazon.awssdk.services.timestreamquery.model.ColumnInfo;
import software.amazon.awssdk.services.timestreamquery.model.Datum;
import software.amazon.awssdk.services.timestreamquery.model.QueryRequest;
import software.amazon.awssdk.services.timestreamquery.model.Row;
import com.boomi.graphql.server.schema.types.AiAgentRegistryMetricsErrorCode;
import com.boomi.graphql.server.servlet.ErrorUtil;
import com.boomi.metering.timestream.query.querier.RowLimitExceededException;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.boomi.aiagentregistry.service.metrics.AiAgentRegistryMetricsService.AGENT_EXTERNAL_ID_FIELD;
import static com.boomi.aiagentregistry.service.metrics.AiAgentRegistryMetricsService.ALIAS_EXTERNAL_ID_FIELD;
import static com.boomi.aiagentregistry.service.metrics.AiAgentRegistryMetricsService.AI_AGENT_LISTING_FIELD;
import static com.boomi.aiagentregistry.service.metrics.AiAgentRegistryMetricsService.PROVIDER_ACCOUNT_GUID_FIELD;
import static com.boomi.aiagentregistry.service.metrics.AiAgentRegistryMetricsService.PROVIDER_TYPE_FIELD;
@Service
@Slf4j
@RequiredArgsConstructor
public class TimestreamService {

   @Value("${boomi.services.timestream.max_rows}")
    private int _maxRows;
    private final TimestreamQueryAsyncClient _timestreamAsyncClient;
    private final ObjectMapper _objectMapper;

    private static final DateTimeFormatter BIN_VALUE_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.nnnnnnnnn")
                    .withZone(ZoneOffset.UTC);

    private static final DateTimeFormatter TIMESTREAM_TIME_FILTER_FORMATTER =
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    private static final String QUERY_EXECUTION_FAILED = "Failed to execute timestream query {}";
    private static final String TIMESTREAM_ROW_CONVERSION_FAILED = "Failed to convert row values to {}";
    private static final String BUCKET_TS_COLUMN_NAME = "bucketTs";

    private static final String FAILED_TO_CANCEL_QUERY = "Failed to cancel paginated query.  query-id={}";
    private static final String CANCELLED_QUERY = "Cancelled paginated query.  query-id={}";

    @PreDestroy
    public void cleanup() {
        try {
            _timestreamAsyncClient.close();
        } catch (Exception e) {
            log.error("Failed to close timestream client", e);
        }
    }

    public String formatTimestreamTimeFilter(Date date) {
        return TIMESTREAM_TIME_FILTER_FORMATTER.format(date.toInstant().atZone(ZoneOffset.UTC));
    }


    public <T> void processTimestreamCompletionList(String sql,
            List<T> results,
            ExecutionException executionException, DataFetchingEnvironment dfe,
            CompletableFuture<List<T>> completion) {
        if (executionException != null) {
            log.error(QUERY_EXECUTION_FAILED, sql, executionException);
            ErrorUtil.addError(dfe, AiAgentRegistryMetricsErrorCode.QUERY_EXECUTION_FAILED);
            completion.complete(null);
            return;
        }
        completion.complete(results);
    }

    public <T> void processTimestreamCompletionSingle(String sql,
            List<T> results,
            ExecutionException executionException, DataFetchingEnvironment dfe,
            CompletableFuture<T> completion) {
        if (executionException != null) {
            log.error(QUERY_EXECUTION_FAILED, sql, executionException);
            ErrorUtil.addError(dfe, AiAgentRegistryMetricsErrorCode.QUERY_EXECUTION_FAILED);
            completion.complete(null);
            return;
        }

        if (!results.isEmpty()) {
            completion.complete(results.get(0));
        } else {
            completion.complete(null);
        }
    }

    public  <T> @Nullable T mapTimestreamRowToType(List<Object> rowValues,
            List<ColumnInfo> columnInfos, DataFetchingEnvironment dfe,
            CompletableFuture<?> completion, Class<T> targetType, boolean invocationsMetrics) {
        Map<String, Object> propertyMap = new HashMap<>();
        // Zip column names with values and create a map
        for (int index = 0; index < columnInfos.size(); index++) {
            if (index < rowValues.size()) {
                String columnName = columnInfos.get(index).name();
                Object value = rowValues.get(index);
                if (columnName.equals(BUCKET_TS_COLUMN_NAME)) {
                    value = parseToDate(value.toString());
                }
                propertyMap.put(columnName, value);
            }
        }

        if (invocationsMetrics) {
            propertyMap = convertToAgentMetricMap(propertyMap);
        }

        try {
            return _objectMapper.convertValue(propertyMap, targetType);
        } catch (IllegalArgumentException e) {
            log.error(TIMESTREAM_ROW_CONVERSION_FAILED, targetType.getSimpleName(), e);
            ErrorUtil.addError(dfe, AiAgentRegistryMetricsErrorCode.METRIC_DATA_MAPPING_FAILED);
            completion.complete(null);
            return null;
        }
    }

    private static Map<String, Object> convertToAgentMetricMap(Map<String, Object> propertyMap) {
        // Extract metadata fields
        Map<String, Object> metadataMap = Stream.of(
                        AGENT_EXTERNAL_ID_FIELD,
                        ALIAS_EXTERNAL_ID_FIELD,
                        PROVIDER_ACCOUNT_GUID_FIELD,
                        PROVIDER_TYPE_FIELD
                ).filter(key -> propertyMap.get(key) != null)
                .collect(Collectors.toMap(
                        Function.identity(),
                        propertyMap::get
                ));

        // Create a new map that matches AiAgentRegistryAgentMetric structure
        Map<String, Object> agentMetricMap = new HashMap<>(propertyMap);
        agentMetricMap.put(AI_AGENT_LISTING_FIELD, metadataMap);

        // Remove the metadata keys from the top-level to avoid duplicate fields
        agentMetricMap.keySet().removeAll(metadataMap.keySet());

        return agentMetricMap;
    }

    private static LocalDateTime parseToLocalDateTimeInUtc(String utcTimestamp) {
        return LocalDateTime.parse(utcTimestamp, BIN_VALUE_FORMATTER);
    }

    // e.g. 2025-02-26 00:12:00.*********
    private static Date parseToDate(String utcTimestamp) {
        LocalDateTime localDateTimeInUtc = parseToLocalDateTimeInUtc(utcTimestamp);
        return Date.from(localDateTimeInUtc.atZone(ZoneOffset.UTC).toInstant());
    }

    public  <R> void execute(String sql, BiFunction<List<Object>, List<ColumnInfo>, R> rowTransformer,
            BiConsumer<List<R>, ExecutionException> resultHandler) {
        queryWithPagination(sql, null, rowTransformer, resultHandler, new ArrayList<>());
    }

    private <R> void queryWithPagination(String sql, String paginationToken,
            BiFunction<List<Object>, List<ColumnInfo>, R> rowTransformer,
            BiConsumer<List<R>, ExecutionException> resultHandler, List<R> results) {
        Instant before = Instant.now();
        Map<String, String> context = MDC.getCopyOfContextMap();
        final QueryRequest.Builder builder = QueryRequest.builder();
        builder.maxRows(_maxRows);
        _timestreamAsyncClient
                .query(builder
                        .queryString(sql)
                        .nextToken(paginationToken)
                        .build())
                .whenComplete((queryResponse, exception) -> {
                    if (context != null) {
                        MDC.setContextMap(context);
                    }
                    Instant after = Instant.now();
                    if (exception != null) {
                        resultHandler.accept(null,
                                (exception instanceof ExecutionException)
                                        ? (ExecutionException) exception : new ExecutionException(exception));
                    } else {
                        try {
                            List<Row> rows = queryResponse.rows();
                            List<ColumnInfo> columnInfo = queryResponse.columnInfo();
                            log.info(generateLogMessage(rows.size(), results.size(),
                                    Duration.between(before, after).toMillis(), queryResponse.queryId(),
                                    paginationToken, queryResponse.nextToken(), sql));
                            for (Row row : rows) {
                                if (results.size() > _maxRows) {
                                    RowLimitExceededException rowLimitExceededException = new RowLimitExceededException(
                                            queryResponse.queryId(), _maxRows);
                                    log.warn(rowLimitExceededException.getMessage());
                                    cancelQuery(queryResponse.queryId());
                                    resultHandler.accept(results, new ExecutionException(rowLimitExceededException));
                                    return;
                                }
                                List<Datum> datum = row.data();
                                List<Object> rowValues = new ArrayList<>(datum.size());
                                for (Datum item : datum) {
                                    Object value = getValue(item);
                                    rowValues.add(value);
                                }
                                R transformedRow = rowTransformer.apply(rowValues, columnInfo);
                                results.add(transformedRow);
                            }
                            if (queryResponse.nextToken() == null) {
                                resultHandler.accept(results, null);
                            } else {
                                queryWithPagination(sql, queryResponse.nextToken(), rowTransformer,
                                        resultHandler, results);
                            }
                        } catch (RuntimeException e) {
                            resultHandler.accept(null, new ExecutionException(e));
                        }
                    }
                    MDC.clear();
                });
    }

    private void cancelQuery(String queryId) {
        _timestreamAsyncClient.cancelQuery(CancelQueryRequest.builder().queryId(queryId).build()).whenComplete(
                (cancelQueryResponse, throwable) -> {
                    if (throwable != null) {
                        log.error(FAILED_TO_CANCEL_QUERY, queryId, throwable);
                    } else {
                        log.info(CANCELLED_QUERY, queryId);
                    }
                });
    }

    private static String generateLogMessage(int numRows, int resultSize, long requestDuration, String queryId,
            String paginationToken, String nextPaginationToken, String sql) {
        StringBuilder sb = new StringBuilder("Retrieved ").append(numRows).append(" rows in ").append(requestDuration)
                .append(" ms for query id '").append(queryId).append("'; current result size is ").append(resultSize)
                .append(".");
        if (paginationToken != null) {
            sb.append(" Used pagination token '").append(paginationToken).append("'.");
        }
        if (nextPaginationToken != null) {
            sb.append(" Next pagination token is '").append(nextPaginationToken).append("'.");
        }
        sb.append(" Executed SQL: ").append(sql);
        return sb.toString();
    }

    private static Object getValue(Datum datum) {
        if (datum.hasArrayValue()) {
            return datum.arrayValue();
        }
        if (datum.hasTimeSeriesValue()) {
            return datum.timeSeriesValue();
        }

        return datum.scalarValue();
    }
}
