// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.util.GuidUtil;

@Slf4j
public class OamLinkServiceMockImpl implements OamLinkService {

    private static final String MOCK_DELETE_OAM_LINK = "Mock delete OAM link invoked";
    private static final String MOCK_CREATE_OAM_LINK = "Mock create OAM link invoked";

    @Override
    public void deleteOamLink(String linkArn, String providerAccountId, String customerAwsAccountId,
            String customerRegion, String externalId) {
        log.warn(MOCK_DELETE_OAM_LINK);
    }

    @Override
    public String createOamLink(String providerAccountId, String customerAwsAccountId, String customerRegion,
            String externalId) {
        log.warn(MOCK_CREATE_OAM_LINK);
        return GuidUtil.createGuid();
    }
}
