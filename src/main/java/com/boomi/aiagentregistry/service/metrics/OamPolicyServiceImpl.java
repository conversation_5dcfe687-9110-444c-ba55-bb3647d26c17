// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.oam.OamClient;
import software.amazon.awssdk.services.oam.model.GetSinkPolicyRequest;
import software.amazon.awssdk.services.oam.model.GetSinkPolicyResponse;
import software.amazon.awssdk.services.oam.model.PutSinkPolicyRequest;
import com.boomi.aiagentregistry.exception.OamException;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.AWS_ACCOUNT_ID_PATTERN;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.COMMA;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.DOUBLE_QUOTED_STRING;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.ESCAPED_DOUBLE_QUOTE;

@Slf4j
// CloudWatch Observability Access Manager to update sink policy of monitoring account  by adding/removing
// source (aka customer) account Id
public class OamPolicyServiceImpl implements OamPolicyService {

    private static final String FOUND_INVALID_AWS_ACCOUNT_IDS = "Found invalid AWS account IDs: {}";
    private final NamedParameterJdbcTemplate _namedParameterJdbcTemplate;
    private final Map<Region, OamClient> _oamClientsMapByRegion;
    private final PlatformTransactionManager _transactionManager;
    private final String _quotedMonitoringAccountId;
    private final Map<Region, String> _sinkArnsMapByRegion;

    private static final String ACQUIRING_LOCK =
            "Acquiring lock to update OAM sink policy for customerAwsAccountId {} and region {} isAddSourceAccount {}";
    private static final String UPDATING_OAM_SINK_POLICY =
            "Updating OAM sink policy for customerAwsAccountId {} and region {}";
    private static final String SINK_ARNS_MAP_BY_REGION = "_sinkArnsMapByRegion {}";
    private static final String OAM_CLIENTS_KEYS_BY_REGION = "_oamClientsMapByRegion {}";
    private static final String STATUSES_PARAM = "statusesToInclude";
    private static final String REGION_PARAM = "region";
    private static final String AUTH_SCHEMA_PARAM = "authSchema";
    private static final String NO_SOURCE_ACCOUNTS_FOUND =
            "No active accounts found for region {} when onboarding/offboarding customer {}. Use "
                    + "monitoring account {} as zero state";
    private static final String SUCCESS_UPDATED_OAM_SINK = "Success Updated OAM sink policy for region {}";
    private static final String AWS_ARRAY_TEMPLATE =
            "\"AWS\": [%s]";
    private static final String AWS_SINGLE_VALUE_PATTERN =
            "\"AWS\"\\s*:\\s*\"[^\"]*\"";
    private static final String AWS_ARRAY_PATTERN = "\"AWS\"\\s*:\\s*\\[\\s*\"[^\"]*\"(?:\\s*,\\s*\"[^\"]*\")*\\s*]";

    private static final String SELECT_WITH_LOCK_SQL =
            """ 
                SELECT external_provider_account_id 
                FROM ai_agent_provider_account 
                WHERE region = :region 
                AND auth_schema = :authSchema
                AND provider_account_status IN (:statusesToInclude) 
                AND is_deleted = false 
                FOR UPDATE
            """;
    private static final Pattern SINGLE_VALUE_PATTERN = Pattern.compile(AWS_SINGLE_VALUE_PATTERN);
    private static final Pattern ARRAY_PATTERN = Pattern.compile(AWS_ARRAY_PATTERN);

    public OamPolicyServiceImpl(
           OamSinkArnRetrieverService oamSinkArnRetrieverService,
           JdbcTemplate jdbcTemplate,
           PlatformTransactionManager transactionManager,
           OamClientHelperService oamClientHelperService,
           @Value("${boomi.services.aiagentregistry.monitoring.account.id}")
           String monitoringAccountId) {
        _namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
        _transactionManager = transactionManager;
        _quotedMonitoringAccountId = String.format(DOUBLE_QUOTED_STRING, monitoringAccountId);

        // Initialize OAM clients for each region
        _oamClientsMapByRegion = new ConcurrentHashMap<>();

        // Convert string region keys to Region enum and create clients
        _sinkArnsMapByRegion = new ConcurrentHashMap<>();
        oamSinkArnRetrieverService.getRegionToSinkArnUnmodifiableMap().forEach((regionStr, arn) -> {
            Region region = Region.of(regionStr);
            OamClient oamClient = oamClientHelperService.createOamClient(region);
            _oamClientsMapByRegion.put(region, oamClient);
            _sinkArnsMapByRegion.put(region, arn);
        });

        log.info(SINK_ARNS_MAP_BY_REGION, _sinkArnsMapByRegion);
        log.info(OAM_CLIENTS_KEYS_BY_REGION, _oamClientsMapByRegion.keySet());
    }

    @Override
    public void updateOamSinkPolicy(String customerAwsAccountId, String customerRegion, boolean isAddSourceAccount) {
        log.info(ACQUIRING_LOCK, customerAwsAccountId, customerRegion, isAddSourceAccount);
        TransactionStatus transactionStatus = null;
        try {
            // Start manual transaction for better control over lock management and performance
            transactionStatus = _transactionManager.getTransaction(new DefaultTransactionDefinition());

            List<AiAgentProviderAccountStatus> statusesToInclude = Arrays.stream(AiAgentProviderAccountStatus.values())
                .filter(status -> status != AiAgentProviderAccountStatus.DISABLED)
                .toList();

            // Get all active accounts with lock
            Set<String> activeAccounts = getActiveAccountsWithLock(customerAwsAccountId, customerRegion,
                    isAddSourceAccount, statusesToInclude);

            Region region = Region.of(customerRegion);
            OamClient oamClient = _oamClientsMapByRegion.get(region);
            updateOamSinkPolicy(activeAccounts, customerAwsAccountId, region, oamClient);

            // Commit transaction
            _transactionManager.commit(transactionStatus);

        } catch (Exception exception) {
            // Rollback transaction on any error
            if (transactionStatus != null) {
                _transactionManager.rollback(transactionStatus);
            }
            throw new OamException("Failed to update provider account transactionStatus", exception);
        }
    }

    private Set<String> getActiveAccountsWithLock(String customerAwsAccountId, String customerRegion,
            boolean isAddSourceAccount, List<AiAgentProviderAccountStatus> statusesToInclude) {
        MapSqlParameterSource parameters = new MapSqlParameterSource();
        parameters.addValue(STATUSES_PARAM, statusesToInclude.stream().map(Enum::name).toList());
        parameters.addValue(REGION_PARAM, customerRegion);
        parameters.addValue(AUTH_SCHEMA_PARAM, AiProviderAuthSchema.AWS_ASSUME_ROLE.name());


        Set<String> activeSourceAccountIds = new HashSet<>(_namedParameterJdbcTemplate.queryForList(
                SELECT_WITH_LOCK_SQL, parameters, String.class));

        if (isAddSourceAccount) {
            activeSourceAccountIds.add(customerAwsAccountId);
            log.info("Adding source account {} to active accounts list", customerAwsAccountId);
        } else {
            activeSourceAccountIds.remove(customerAwsAccountId);
            log.info("Removing source account {} from active accounts list", customerAwsAccountId);
        }

        return filterValidAwsAccountIds(activeSourceAccountIds);
    }

    // to do - this should be a bean method for sprint to do the retry
    @Retryable(
        retryFor = { OamException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public void updateOamSinkPolicy(Set<String> activeAccounts, String customerAwsAccountId, Region region,
            OamClient oamClient) {
        log.info(UPDATING_OAM_SINK_POLICY, customerAwsAccountId, region);
        try {
            String sinkArn = _sinkArnsMapByRegion.get(region);
            if (sinkArn == null) {
                throw new OamException("No sink ARN configured for region: " + region);
            }

            // Get current sink policyString
            GetSinkPolicyResponse currentPolicy = oamClient.getSinkPolicy(
                GetSinkPolicyRequest.builder()
                    .sinkIdentifier(sinkArn)
                    .build()
            );

            // Refer to src/test/resources/aws_oam_sink_policy.json for example policy string
            String policyString = currentPolicy.policy();

            if (activeAccounts.isEmpty()) {
                log.warn(NO_SOURCE_ACCOUNTS_FOUND, customerAwsAccountId, region, _quotedMonitoringAccountId);
            }
            String sourceAccountIds = activeAccounts.isEmpty()
                    ? _quotedMonitoringAccountId
                    : activeAccounts.stream()
                            // .map(account -> "\"arn:aws:iam::" + account + ":root\"")
                               .map(account -> ESCAPED_DOUBLE_QUOTE + account + ESCAPED_DOUBLE_QUOTE)
                            .collect(Collectors.joining(COMMA));

            log.info("quoted sourceAccountIds to be added to OAM policy {}", sourceAccountIds);
            log.info("existing policyString is {}", policyString);

            // Determine if the current policy uses array or single value format
            Matcher singleValueMatcher = SINGLE_VALUE_PATTERN.matcher(policyString);
            Matcher arrayMatcher = ARRAY_PATTERN.matcher(policyString);

            String updatedPolicy;
            if (singleValueMatcher.find()) {
                log.info("singleValueMatcher found");
                updatedPolicy = policyString.replaceAll(
                        AWS_SINGLE_VALUE_PATTERN,
                        String.format(AWS_ARRAY_TEMPLATE, sourceAccountIds));
            }
            else if (arrayMatcher.find()) {
                log.info("Array ValueMatcher found");
                updatedPolicy = policyString.replaceAll(
                       AWS_ARRAY_PATTERN,
                       String.format(AWS_ARRAY_TEMPLATE, sourceAccountIds)
                   );
            }
            else {
                throw new OamException("Unable to determine AWS principal format in policy");
            }


            log.info("updatedPolicy is {}", updatedPolicy);

             // Update sink policyString
             oamClient.putSinkPolicy(PutSinkPolicyRequest.builder()
                 .sinkIdentifier(sinkArn)
                 .policy(updatedPolicy)
                 .build());

             log.info(SUCCESS_UPDATED_OAM_SINK, region);

        } catch (Exception exception) {
            log.error("Failed to update OAM sink policy in region {}", region, exception);
            throw new OamException("Failed to update OAM sink policy in region " + region, exception);
        }
    }

    public Set<String> filterValidAwsAccountIds(Set<String> activeAccounts) {
        if (activeAccounts == null || activeAccounts.isEmpty()) {
            return Collections.emptySet();
        }

        Set<String> validAccounts = new HashSet<>();
        Set<String> invalidAccounts = new HashSet<>();

        activeAccounts.forEach(accountId -> {
            if (AWS_ACCOUNT_ID_PATTERN.matcher(accountId).matches()) {
                validAccounts.add(accountId);
            } else {
                invalidAccounts.add(accountId);
            }
        });

        if (!invalidAccounts.isEmpty()) {
            log.warn(FOUND_INVALID_AWS_ACCOUNT_IDS, invalidAccounts);
        }

        return validAccounts;
    }
}
