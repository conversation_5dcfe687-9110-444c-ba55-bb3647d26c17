// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.auth.credentials.AwsSessionCredentials;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.oam.OamClient;
import software.amazon.awssdk.services.oam.model.CreateLinkRequest;
import software.amazon.awssdk.services.oam.model.CreateLinkResponse;
import software.amazon.awssdk.services.oam.model.LinkConfiguration;
import software.amazon.awssdk.services.oam.model.MetricConfiguration;
import software.amazon.awssdk.services.oam.model.ResourceType;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.model.AssumeRoleRequest;
import software.amazon.awssdk.services.sts.model.AssumeRoleResponse;
import software.amazon.awssdk.services.sts.model.Credentials;
import com.boomi.aiagentregistry.exception.OamException;
import com.boomi.aiagentregistry.service.BedrockAssumeRoleService;

import org.jetbrains.annotations.NotNull;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.CF_PARAM_ACT_OAM_CUSTOMER_ROLE_NAME;

@Slf4j
public class OamLinkServiceImpl implements OamLinkService {

    private final OamSinkArnRetrieverService _oamSinkArnRetrieverService;
    private final String _assumedRoleArn;
    private final StsClient _stsClient;
    private final OamClientHelperService _oamClientHelperService;

    private static final String ERROR_IN_OAM_LINK_OPERATION = "Error  in OAM link operation for providerAccount ";
    private static final String CREATE_OAM_LINK = "OAM link isAdd {} in source account using assumed role {}";
    private static final String SUCCESSFULLY_ASSUMED_ROLE = "Successfully assumed role {} for oam client";
    private static final String SINK_ARN = "sink is {}";
    private static final String OAM_LINK_ARN = "oamLinkArn {}";
    private static final String SOURCE_ACCOUNT_ID = "%SOURCE_ACCOUNT_ID%";
    // Role to assume in the source account
    private static final String ASSUMED_ROLE_ARN = "arn:aws:iam::%SOURCE_ACCOUNT_ID%:role/%ACT_OAM_CUSTOMER_ROLE_NAME%";
    private static final String AGENT_METRICS_FILTER = "Namespace IN ('AWS/Bedrock/Agents')";
    private static final boolean IS_ERROR = true;
    private static final String LOG_LINE = "providerAccountId=%s, customerAwsAccountId=%s, customerRegion=%s";
    public static final String LABEL_TEMPLATE = "${AWS::AccountId}";
    private static final String OAM_LINK_CREATED_SUCCESSFULLY = "OAM Link created successfully. Link ARN: {}";
    private final String _assumeRoleSessionName;

    public OamLinkServiceImpl(BedrockAssumeRoleService bedrockAssumeRoleService,
            OamSinkArnRetrieverService oamSinkArnRetrieverService,
            StsClient stsClient,
            OamClientHelperService oamClientHelperService,
            String assumeRoleSessionName
            ) {
        _assumedRoleArn = ASSUMED_ROLE_ARN.replace(CF_PARAM_ACT_OAM_CUSTOMER_ROLE_NAME,
                bedrockAssumeRoleService.getOamCustomerRoleName());
        _oamSinkArnRetrieverService = oamSinkArnRetrieverService;
        _stsClient = stsClient;
        _oamClientHelperService = oamClientHelperService;
        _assumeRoleSessionName = assumeRoleSessionName;
        log.info("_assumeRoleSessionName {}", _assumeRoleSessionName);
    }

    @Override
    public void deleteOamLink(String linkArn, String providerAccountId, String customerAwsAccountId,
            String customerRegion, String externalId) {
        try {
            doOamLinkOperation(providerAccountId, customerAwsAccountId, customerRegion,
                    externalId, false, linkArn);
        } catch (Exception exception) {
            // allow the rest of the code to delete to continue
            log.error("Error in deleting OAM link for customerAwsAccountId {}, customerRegion {} and "
                    + "providerAccountId {}", customerAwsAccountId, customerRegion, providerAccountId, exception);
        }
    }

    @Override
    public String createOamLink(String providerAccountId, String customerAwsAccountId, String customerRegion,
            String externalId) {
        String oamLinkArn = doOamLinkOperation(providerAccountId, customerAwsAccountId, customerRegion, externalId,
                true, null);
        log.info(OAM_LINK_ARN, oamLinkArn);
        return oamLinkArn;
    }

    private String doOamLinkOperation(String providerAccountId, String customerAwsAccountId, String customerRegion,
            String externalId, boolean isAdd, String linkArnToBeDeleted) {
        printLog(!IS_ERROR, providerAccountId, customerAwsAccountId, customerRegion);

        // Assume the role
        String assumedRoleWithAwsAccountId = _assumedRoleArn.replace(SOURCE_ACCOUNT_ID, customerAwsAccountId);
        log.info(CREATE_OAM_LINK, isAdd, assumedRoleWithAwsAccountId);
        AssumeRoleRequest assumeRoleRequest = AssumeRoleRequest
                .builder()
                .roleArn(assumedRoleWithAwsAccountId)
                .roleSessionName(_assumeRoleSessionName)
                .externalId(externalId)
                .build();

        AssumeRoleResponse assumeRoleResponse = _stsClient.assumeRole(assumeRoleRequest);
        log.info(SUCCESSFULLY_ASSUMED_ROLE, assumedRoleWithAwsAccountId);
        Credentials tempCredentials = assumeRoleResponse.credentials();

        // Create credentials provider with temporary credentials
        AwsSessionCredentials sessionCredentials = AwsSessionCredentials
                .create(tempCredentials.accessKeyId(), tempCredentials.secretAccessKey(),
                        tempCredentials.sessionToken());

        try (OamClient oamClient = _oamClientHelperService.createOamClient(Region.of(customerRegion),
                sessionCredentials)) {
            if (isAdd) {
                CreateLinkResponse response = createOamLink(customerRegion, oamClient);
                return response.arn();
            } else {
                oamClient.deleteLink(request -> request.identifier(linkArnToBeDeleted));
                log.info("Successfully deleted OAM link: {}", linkArnToBeDeleted);
                return linkArnToBeDeleted;
            }
        } catch (Exception e) {
            printLog(IS_ERROR, providerAccountId, customerAwsAccountId, customerRegion);
            log.error(e.getMessage(), e);
            throw new OamException(ERROR_IN_OAM_LINK_OPERATION + providerAccountId, e);
        }
    }

    private @NotNull CreateLinkResponse createOamLink(String customerRegion, OamClient oamClient) {
        // Create MetricConfiguration
        MetricConfiguration metricConfig = MetricConfiguration
                .builder()
                .filter(AGENT_METRICS_FILTER)
                .build();

        // Create LinkConfiguration
        LinkConfiguration linkConfig = LinkConfiguration
                .builder()
                .metricConfiguration(metricConfig)
                .build();

        // Create the OAM link
        String sinkArn = _oamSinkArnRetrieverService.getSinkArn(customerRegion);
        log.info(SINK_ARN, sinkArn);
        CreateLinkRequest createLinkRequest = CreateLinkRequest
                .builder()
                .labelTemplate(LABEL_TEMPLATE)
                .resourceTypes(ResourceType.AWS_CLOUD_WATCH_METRIC)
                .sinkIdentifier(sinkArn)
                .linkConfiguration(linkConfig)
                .build();

        CreateLinkResponse response = oamClient.createLink(createLinkRequest);
        log.info(OAM_LINK_CREATED_SUCCESSFULLY, response.arn());
        return response;
    }

    private void printLog(
            boolean isError,
            String providerAccountId,
            String customerAwsAccountId,
            String customerRegion) {
        String logLine = String.format(LOG_LINE, providerAccountId, customerAwsAccountId, customerRegion);
        if (isError) {
            log.error(logLine);
        } else {
            log.info(logLine);
        }
    }
}

