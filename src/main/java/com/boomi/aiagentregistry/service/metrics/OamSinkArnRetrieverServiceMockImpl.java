// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.constant.AWSBedrockEnabledRegionConstant;
import com.boomi.graphql.server.schema.types.AWSRegion;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class OamSinkArnRetrieverServiceMockImpl implements OamSinkArnRetrieverService {

    private final Map<String, String> _regionToSinkArnUnmodifiableMap;
    private final List<AWSRegion> _regionToLabelUnmodifiableMap;
    private final Set<String> _regions;

    private static final String MOCK_SINK_ARN_FORMAT = "arn:aws:oam:%s:066499543631:"
            + "sink/c28e10c7-3253-4f99-a4cc-773407397c9b";

    public OamSinkArnRetrieverServiceMockImpl() {
        Map<String, String> allRegions = AWSBedrockEnabledRegionConstant.getAllRegions();
        _regionToLabelUnmodifiableMap = allRegions.entrySet()
           .stream()
           .map(entry -> new AWSRegion(entry.getKey(), entry.getValue()))
           .toList();
        _regions = allRegions.keySet();
        _regionToSinkArnUnmodifiableMap = _regions
            .stream()
            .collect(Collectors
                .collectingAndThen(
                    Collectors.toMap(
                         // key
                        region -> region,
                         // value
                        region -> String.format(MOCK_SINK_ARN_FORMAT, region),
                        // Merge function in case of duplicates
                        (existing, replacement) -> existing,
                        HashMap::new),
                    // Make the map unmodifiable
                    Collections::unmodifiableMap));
    }

    // Ignore "Mutable members should not be stored or returned directly" because the stored reference
    // is unmodifiable
    @SuppressWarnings("java:S2384")
    @Override
    public Map<String, String> getRegionToSinkArnUnmodifiableMap() {
        return _regionToSinkArnUnmodifiableMap;
    }

    // Ignore "Mutable members should not be stored or returned directly" because the stored reference
    // is unmodifiable
    @SuppressWarnings("java:S2384")
    @Override
    public List<AWSRegion> getAwsRegionUnmodifiableList() {
       return _regionToLabelUnmodifiableMap;
    }

    // Ignore "Mutable members should not be stored or returned directly" because the stored reference
    // is unmodifiable
    @SuppressWarnings("java:S2384")
    @Override
    public Set<String> getUnmodifiableRegionsSet() {
        return _regions;
    }

    @Override
    public String getSinkArn(String region) {
        log.warn("Returning mock sink arn {}", _regionToSinkArnUnmodifiableMap.get(region));
        return _regionToSinkArnUnmodifiableMap.get(region);
    }
}
