// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import lombok.extern.slf4j.Slf4j;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Component
@Slf4j
public class CloudformationFileService {

    private final String _completeTemplate;
    private final String _oamPolicyOnlyTemplate;
    // Has assumed role for doing OAM linking programmatically
    private final String _completeTemplateV2;

    public CloudformationFileService() throws IOException {
        Resource resource1 = new ClassPathResource("cloudformation/customer-account-provision.yaml");
        Resource resource2 = new ClassPathResource("cloudformation/customer-account-provision-oam-only.yaml");
        Resource resource3 = new ClassPathResource("cloudformation/customer-account-provision.v2.yaml");

        try {
            _completeTemplate = new String(resource1.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
            _oamPolicyOnlyTemplate = new String(resource2.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
            _completeTemplateV2 = new String(resource3.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
            log.info("Successfully loaded CloudFormation templates");
        } catch (IOException e) {
            log.error("Failed to load CloudFormation templates", e);
            throw e;
        }
    }

    public String getCompleteTemplate() {
        return _completeTemplate;
    }

    public String getOamPolicyOnlyTemplate() {
        return _oamPolicyOnlyTemplate;
    }

    public String getCompleteTemplateV2() {
        return _completeTemplateV2;
    }
}
