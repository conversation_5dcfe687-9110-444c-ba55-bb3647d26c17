// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OamPolicyServiceMockImpl implements OamPolicyService {

    private static final String MOCK_UPDATE_OAM_SINK_POLICY = "Mock update OAM sink invoked";

    @Override
    public void updateOamSinkPolicy(String customerAwsAccountId, String customerRegion, boolean isAddSourceAccount) {
        log.warn(MOCK_UPDATE_OAM_SINK_POLICY);
    }
}
