// Copyright (c) 2024 Boom<PERSON>, LP.
package com.boomi.aiagentregistry.service;

import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentAlias;
import com.boomi.aiagentregistry.repo.AiAgentAliasRepository;

import org.springframework.stereotype.Component;


@Component("ALIAS")
public class AiAgentAliasManager implements ReferenceEntityManager {

    private final AiAgentAliasRepository _aiAgentAliasRepository;

    public AiAgentAliasManager(AiAgentAliasRepository aiAgentAliasRepository) {
        _aiAgentAliasRepository = aiAgentAliasRepository;
    }

    @Override
    public AiAgent getAgent(String entityId) {
        AiAgentAlias alias = _aiAgentAliasRepository.findByGuid(entityId).orElse(null);
        if (alias != null) {
            return alias.getAgentVersion().getAgent();
        }
        return null;
    }

    @Override
    public Integer getRelatedEntityUid(String entityId) {
        AiAgentAlias alias = _aiAgentAliasRepository.findByGuid(entityId).orElse(null);
        assert alias != null;
        return alias.getUid();
    }
}
