// Copyright (c) 2025 Boom<PERSON>, LP
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.graphql.server.schema.types.AiAgentListingRefreshTracker;

import java.util.concurrent.CompletableFuture;

public interface IAiAgentRefreshTrackerService {

    CompletableFuture<AiAgentListingRefreshTracker> getRefreshTracker(DataFetchingEnvironment dfe);
}
