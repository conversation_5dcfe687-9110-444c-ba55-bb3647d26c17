// Copyright (c) 2025 Boomi, LP.
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.graphql.server.schema.types.AiAgentListingAllFiltersResponse;
import com.boomi.graphql.server.schema.types.AiAgentListingFilterQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryResponse;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

public interface IAiAgentListingService {

    CompletableFuture<AiAgentsListingQueryResponse> getAiAgentListings(
            AiAgentsListingQueryInput input, DataFetchingEnvironment dfe);

    CompletionStage<AiAgentListingAllFiltersResponse> allAiAgentListingFilters(AiAgentListingFilterQueryInput input,
            DataFetchingEnvironment dfe);
}
