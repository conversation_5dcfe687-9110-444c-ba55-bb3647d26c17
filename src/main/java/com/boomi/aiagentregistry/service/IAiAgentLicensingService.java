// Copyright (c) 2025 Boomi, LP.
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.graphql.server.schema.types.AiAgentRegistryLicense;
import com.boomi.graphql.server.schema.types.AiRegistryLicenseLimitInput;
import com.boomi.graphql.server.schema.types.IdpAccountTierLimitInput;
import com.boomi.graphql.server.schema.types.AiAgentRegistryLicenseIdpAccountAssociation;

import java.util.concurrent.CompletionStage;

public interface IAiAgentLicensingService {

    CompletionStage<AiAgentRegistryLicense> updateAiRegistryLicenseLimit(
            AiRegistryLicenseLimitInput input, DataFetchingEnvironment dfe);

    CompletionStage<AiAgentRegistryLicenseIdpAccountAssociation> updateIdpAccountTierLimit(
            IdpAccountTierLimitInput input, DataFetchingEnvironment dfe);

    AiAgentRegistryLicenseIdpAccountAssociation getLicenseLimit(String idpAccountId, DataFetchingEnvironment dfe);
}
