// Copyright (c) 2024 Boomi, LP.
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.aop.IdpAccountFilter;
import com.boomi.aiagentregistry.aop.IsDeletedFilter;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentAlias;
import com.boomi.aiagentregistry.entity.AiAgentListing;
import com.boomi.aiagentregistry.entity.AiAgentLlm;
import com.boomi.aiagentregistry.entity.AiAgentLlmAssociation;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTag;
import com.boomi.aiagentregistry.entity.AiAgentTagAssociation;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.entity.VersionAliasIdKey;
import com.boomi.aiagentregistry.repo.AiAgentAliasRepository;
import com.boomi.aiagentregistry.repo.AiAgentListingRepository;
import com.boomi.aiagentregistry.repo.AiAgentLlmAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentLlmRepository;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.repo.AiAgentRepository;
import com.boomi.aiagentregistry.repo.AiAgentTagAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentTagRepository;
import com.boomi.aiagentregistry.repo.AiAgentVersionRepository;
import com.boomi.aiagentregistry.service.provider.externallink.ExternalLinkParams;
import com.boomi.aiagentregistry.service.provider.externallink.ExternalLinkProviderServiceFactory;
import com.boomi.aiagentregistry.util.AuditUtil;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.UserUtil;
import com.boomi.aiagentregistry.util.ValidationUtil;
import com.boomi.graphql.server.schema.types.AiAgentCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentLlmInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiAgentRegistryTrustLevel;
import com.boomi.graphql.server.schema.types.AiAgentTagInput;
import com.boomi.graphql.server.schema.types.AiAgentUpdateInput;
import com.boomi.graphql.server.schema.types.AiAgentsQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentsQueryResponse;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.boomi.graphql.server.servlet.ErrorUtil;
import com.boomi.util.StringUtil;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ADD_LLM_TO_AGENT_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ADD_TAG_TO_AGENT_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.AI_AGENT_CREATE_INPUT_NULL_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.AI_AGENT_NAME_ALREADY_EXIST_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.AI_AGENT_NOT_FOUND_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_AGENT_VERSION_NOT_FOUND;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_AI_AGENT_BY_ALIAS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_AI_AGENT_BY_VERSION;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_ON_AI_AGENT_DELETE;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INVALID_AI_AGENT_NAME_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INVALID_LLM_INPUT_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INVALID_TAG_INPUT_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INVALID_TRUST_LEVEL_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.NON_CUSTOM_PROVIDER_AGENT_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.PROVIDER_ACCOUNT_NOT_FOUND_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.VERSION_ID_REQUIRED_ERROR;
import static com.boomi.aiagentregistry.mapper.AiAgentAliasMapper.ALIAS_MAPPER;
import static com.boomi.aiagentregistry.mapper.AiAgentMapper.AI_AGENT_MAPPER;
import static com.boomi.aiagentregistry.mapper.AiAgentVersionMapper.AI_AGENT_VERSION_MAPPER;
import static com.boomi.aiagentregistry.util.ValidationUtil.isInputFieldPresent;
import static com.boomi.graphql.server.schema.types.AiAgentCreateInput.PROP_AGENT_NAME;
import static com.boomi.graphql.server.schema.types.AiAgentCreateInput.PROP_LLMS;
import static com.boomi.graphql.server.schema.types.AiAgentCreateInput.PROP_TAGS;
import static com.boomi.graphql.server.schema.types.AiAgentCreateInput.PROP_TRUST_LEVEL;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.AI_AGENT_BY_ALIAS_ERROR;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.AI_AGENT_BY_VERSION_ERROR;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.AI_AGENT_CREATE_ERROR;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.AI_AGENT_CREATE_INPUT_NULL;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.AI_AGENT_NAME_ALREADY_EXIST;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.AI_AGENT_NAME_REQUIRED;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.AI_AGENT_NOT_FOUND;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.AI_AGENT_VERSION_NOT_FOUND;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_LLM_INPUT;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_TAG_INPUT;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_TRUST_LEVEL;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.NON_CUSTOM_PROVIDER_AGENT;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PAGE_INDEX_INVALID;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PAGE_SIZE_INVALID;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.VERSION_ID_REQUIRED;
import static com.boomi.graphql.server.servlet.ErrorUtil.GeneralErrorCode.SYSTEM_ERROR;

@Slf4j
@Service
public class AiAgentServiceImpl implements IAiAgentService {

    private final AiAgentRepository _aiAgentRepository;
    private final AiAgentVersionRepository _aiAgentVersionRepository;
    private final AiAgentAliasRepository _aiAgentAliasRepository;
    private final AiAgentProviderAccountRepository _aiAgentProviderAccountRepository;
    private final ExternalLinkProviderServiceFactory _externalLinkProviderServiceFactory;
    private final UserUtil _userUtil;
    private final AiAgentTagRepository _aiAgentTagRepository;
    private final AiAgentTagAssociationRepository _aiAgentTagAssociationRepository;
    private final AiAgentLlmRepository _aiAgentLlmRepository;
    private final AiAgentLlmAssociationRepository _aiAgentLlmAssociationRepository;
    private final AuditUtil _auditUtil;

    private final AiAgentListingRepository _aiAgentListingRepository;

    private static final String EXCEPTION_EXTERNAL_LINK =
            "Exception in setting {} external link using params {}";

    public AiAgentServiceImpl(AiAgentRepository aiAgentRepository,
                              AiAgentVersionRepository aiAgentVersionRepository,
                              AiAgentAliasRepository aiAgentAliasRepository,
                              AiAgentProviderAccountRepository aiAgentProviderAccountRepository,
                              ExternalLinkProviderServiceFactory externalLinkProviderServiceFactory,
            UserUtil userUtil, AiAgentTagRepository aiAgentTagRepository,
            AiAgentTagAssociationRepository aiAgentTagAssociationRepository,
            AiAgentLlmRepository aiAgentLlmRepository,
            AiAgentLlmAssociationRepository aiAgentLlmAssociationRepository, AuditUtil auditUtil,
            AiAgentListingRepository aiAgentListingRepository) {
        _aiAgentRepository = aiAgentRepository;
        _aiAgentVersionRepository = aiAgentVersionRepository;
        _aiAgentAliasRepository = aiAgentAliasRepository;
        _aiAgentProviderAccountRepository = aiAgentProviderAccountRepository;
        _externalLinkProviderServiceFactory = externalLinkProviderServiceFactory;
        _userUtil = userUtil;
        _aiAgentTagRepository = aiAgentTagRepository;
        _aiAgentTagAssociationRepository = aiAgentTagAssociationRepository;
        _aiAgentLlmRepository = aiAgentLlmRepository;
        _aiAgentLlmAssociationRepository = aiAgentLlmAssociationRepository;
        _auditUtil = auditUtil;
        _aiAgentListingRepository = aiAgentListingRepository;
    }

    @Transactional
    @Override
    @IdpAccountFilter
    @IsDeletedFilter
    public CompletableFuture<AiAgentsQueryResponse> getAiAgents(AiAgentsQueryInput input,
            DataFetchingEnvironment dfe) {
        CompletableFuture<AiAgentsQueryResponse> completion = new CompletableFuture<>();
        AiAgentsQueryResponse aiAgentsQueryResponse = new AiAgentsQueryResponse();
        try {
            List<String> providerAccountIds = input.getProviderAccountIds();
            if (validateInput(input, dfe)) {
                log.info("Fetching AI Agent for registry accounts: {}", providerAccountIds);
                Pageable pageable = PageRequest.of(input.getPageIndex(), input.getPageSize());
                Page<AiAgent> aiAgentsPage = (providerAccountIds == null || providerAccountIds.isEmpty())
                        ? _aiAgentRepository.findAll(pageable)
                        : _aiAgentRepository.findAllByAiAgentProviderAccountGuidIn(providerAccountIds, pageable);

                List<com.boomi.graphql.server.schema.types.AiAgent> aiAgents = aiAgentsPage.getContent().stream()
                        .map(dbAiAgent -> {
                            Optional<Set<AiAgentVersion>> versions = getAllAgentVersionsForAgentUid(dbAiAgent.getUid());
                            return AI_AGENT_MAPPER.toAIAgent(dbAiAgent,
                                    versions.orElse(Collections.emptySet())
                                            .stream()
                                            .map(AI_AGENT_VERSION_MAPPER::toAiAgentVersion)
                                            .collect(Collectors.toSet()));
                        })
                        .toList();

                aiAgentsQueryResponse.setAiAgents(aiAgents);
                aiAgentsQueryResponse.setNumberOfResults(aiAgentsPage.getTotalElements());
                aiAgentsQueryResponse.setCurrentPageSize(aiAgentsPage.getSize());
                completion.complete(aiAgentsQueryResponse);
                return completion;
            }
            completion.complete(null);
        } catch (Exception e) {
            log.error(AiAgentRegistryErrorCode.FETCH_AI_AGENT_ERROR.getDetail(), e);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.FETCH_AI_AGENT_ERROR);
            completion.complete(null);
        }
        return completion;
    }

    @Override
    @Transactional
    public CompletionStage<com.boomi.graphql.server.schema.types.AiAgent> createAIAgent(AiAgentCreateInput input,
            DataFetchingEnvironment dfe) {
        // AI Agent create implementation to handle ONLY the Custom Type Provider (aka. Internal Registry Provider)
        CompletableFuture<com.boomi.graphql.server.schema.types.AiAgent> completion = new CompletableFuture<>();
        try {
            // Validate and Retrieve Provider Account Entity
            AiAgentProviderAccount providerAccount = validateAndGetProviderAccount(input.getProviderAccountId(), dfe);
            if (providerAccount == null) {
                log.warn(PROVIDER_ACCOUNT_NOT_FOUND_ERROR, input.getProviderAccountId());
                ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_NOT_FOUND,
                        input.getProviderAccountId());
                completion.complete(null);
                return completion;
            }

            // validate inputs
            validateCreateAIAgentInput(input, providerAccount, dfe);
            if (ErrorUtil.hasErrors(dfe)) {
                completion.complete(null);
                return completion;
            }

            // Create agent
            AiAgent aiAgent = createAgent(input, providerAccount);
            // create version
            AiAgentVersion agentVersion = createAgentVersion(input, aiAgent, dfe);
            // create tag if tag section exist
            if (isInputFieldPresent(dfe, PROP_TAGS)) {
                createTagAndAssociateWithCurrentVersion(input.getTags(), agentVersion, dfe);
            }
            // create model if model field exist for current version
            if (isInputFieldPresent(dfe, PROP_LLMS)) {
                createModelAndAssociateWithCurrentVersion(input.getLlms(), providerAccount, agentVersion, dfe);
            }

            //populate entry in AiAgentListing table for immediate query.
            AiAgentListing aiAgentListing = populateAiAgentListing
                    (aiAgent,agentVersion,aiAgent.getAiAgentProviderAccount());
            _aiAgentListingRepository.save(aiAgentListing);

            return getAiAgent(completion, aiAgent);
        } catch (Exception exception) {
            ErrorUtil.addError(dfe, AI_AGENT_CREATE_ERROR);
            log.error(AI_AGENT_CREATE_ERROR.getDetail(), exception);
            completion.complete(null);
        }
        return completion;
    }

    @Override
    @Transactional
    @IdpAccountFilter
    @IsDeletedFilter
    public CompletionStage<com.boomi.graphql.server.schema.types.AiAgent> updateAIAgent(AiAgentUpdateInput input,
            DataFetchingEnvironment dfe) {
        log.info("Started AiAgentServiceImpl -> updateAIAgent");
        // AI Agent update implementation to handle ONLY the Custom Type Provider (aka. Internal Registry Provider)
        CompletableFuture<com.boomi.graphql.server.schema.types.AiAgent> completion = new CompletableFuture<>();
        try {
            // Agent must already exist
            AiAgent existingAiAgent = getAgent(input.getId()).orElse(null);
            if (existingAiAgent == null) {
                ErrorUtil.addError(dfe, AI_AGENT_NOT_FOUND);
                log.warn(AI_AGENT_NOT_FOUND.getDetail(), dfe);
                completion.complete(null);
                return completion;
            }

            // validate tags and model/llm
            validateUpdateAiAgentInput(input, existingAiAgent, dfe);
            if (ErrorUtil.hasErrors(dfe)) {
                completion.complete(null);
                return completion;
            }

            // update agent for modified time
            updateAgent(existingAiAgent);

            // get existing agent version entity which need to be updated (versionString can be null)
            AiAgentVersion existingAiAgentVersion = getAgentVersion(input.getAgentVersion(), existingAiAgent.getUid());
            // if version not exist create new
            AiAgentVersion agentVersion = null;
            if (existingAiAgentVersion == null) {
                AiAgentCreateInput createInput = AI_AGENT_MAPPER.toAiAgentCreateInput(input);
                agentVersion = createAgentVersion(createInput, existingAiAgent, dfe);
            } else {
                agentVersion = updateAgentVersion(input, existingAiAgentVersion, dfe);
                if (ErrorUtil.hasErrors(dfe)) {
                    completion.complete(null);
                    return completion;
                }
            }

            createTagAndAssociateWithCurrentVersion(input.getTags(), agentVersion, dfe);

            createModelAndAssociateWithCurrentVersion(
                    input.getLlms(), existingAiAgent.getAiAgentProviderAccount(), agentVersion, dfe);

            //populate entry in AiAgentListing table for immediate query.
            AiAgentListing aiAgentListing = populateAiAgentListing
                    (existingAiAgent,agentVersion,existingAiAgent.getAiAgentProviderAccount());
            _aiAgentListingRepository.save(aiAgentListing);

            log.info("Ended AiAgentServiceImpl -> updateAIAgent");
            return getAiAgent(completion, existingAiAgent);
        } catch (Exception exception) {
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.AI_AGENT_UPDATE_ERROR);
            log.error(AiAgentRegistryErrorCode.AI_AGENT_UPDATE_ERROR.getDetail(), exception);
            completion.complete(null);
        }
        return completion;
    }

    private void updateAgent(AiAgent existingAiAgent) {
        // currently we have only this field to update.
        existingAiAgent.setModifiedTime(AuditUtilImpl.getCurrentTime());
        _aiAgentRepository.save(existingAiAgent);
    }

    @Transactional
    @IsDeletedFilter
    @Override
    public CompletionStage<com.boomi.graphql.server.schema.types.AiAgent> getAiAgentByVersionId(
            @NotNull String aiAgentVersionId, DataFetchingEnvironment dfe) {
        try {
            String idpAccountId = _userUtil.getAccountId(dfe);
            AiAgentVersion dbAiAgentVersion = _aiAgentVersionRepository
                    .findByGuidAndAgentIdpAccountIdAndAgentIsDeleted(aiAgentVersionId, idpAccountId, false)
                    .orElse(null);
            if (dbAiAgentVersion == null) {
                ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.AI_AGENT_VERSION_NOT_FOUND);
                log.warn(AiAgentRegistryErrorCode.AI_AGENT_VERSION_NOT_FOUND.getDetail(), dfe);
                return CompletableFuture.completedFuture(null);
            }
            return getAgentCompletableFutureFromAgentVersion(dbAiAgentVersion, null, dfe);
        } catch (Exception e) {
            ErrorUtil.addError(dfe, AI_AGENT_BY_VERSION_ERROR, aiAgentVersionId);
            log.error(ERROR_AI_AGENT_BY_VERSION, aiAgentVersionId, e.getMessage());
            return CompletableFuture.completedFuture(null);
        }

    }

    @Override
    @IsDeletedFilter
    @Transactional
    public CompletionStage<com.boomi.graphql.server.schema.types.AiAgent> getAiAgentByAliasId(String aiAgentAliasId,
            DataFetchingEnvironment dfe) {
        try {
            String idpAccountId = _userUtil.getAccountId(dfe);
            final AiAgentAlias agentAlias = _aiAgentAliasRepository
                    .findByGuidAndVersionIsDeletedAndAgentIdpAccountIdAndAgentIsDeleted(
                            aiAgentAliasId, idpAccountId, false)
                    .orElse(null);
            if (agentAlias == null) {
                ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.AI_AGENT_ALIAS_NOT_FOUND);
                log.warn(AiAgentRegistryErrorCode.AI_AGENT_ALIAS_NOT_FOUND.getDetail(), dfe);
                return CompletableFuture.completedFuture(null);
            }
            AiAgentVersion dbAiAgentVersion = agentAlias.getAgentVersion();
            return getAgentCompletableFutureFromAgentVersion(dbAiAgentVersion, agentAlias, dfe);
        } catch (Exception e) {
            ErrorUtil.addError(dfe, AI_AGENT_BY_ALIAS_ERROR, aiAgentAliasId);
            log.error(ERROR_AI_AGENT_BY_ALIAS, aiAgentAliasId, e.getMessage());
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    @Transactional
    @IsDeletedFilter
    @IdpAccountFilter
    public CompletionStage<Boolean> deleteAIAgent(String id, DataFetchingEnvironment dfe) {

        try {
            AiAgent aiAgent = getAgent(id).orElse(null);
            if (aiAgent == null) {
                ErrorUtil.addError(dfe, AI_AGENT_NOT_FOUND);
                log.warn(AI_AGENT_NOT_FOUND_ERROR, id);
                return CompletableFuture.completedFuture(false);
            } else if (!aiAgent.getAiAgentProviderAccount().getProviderType().name()
                    .equalsIgnoreCase(AiAgentProviderType.CUSTOM.name())) {
                ErrorUtil.addError(dfe, NON_CUSTOM_PROVIDER_AGENT);
                log.warn(NON_CUSTOM_PROVIDER_AGENT_ERROR);
                return CompletableFuture.completedFuture(false);
            }
            deleteAgent(aiAgent, dfe);
            return CompletableFuture.completedFuture(true);
        } catch (Exception e) {
            ErrorUtil.addError(dfe, SYSTEM_ERROR);
            log.error(ERROR_ON_AI_AGENT_DELETE, id, e.getMessage(), e);
            return CompletableFuture.completedFuture(false);
        }
    }

    private static com.boomi.graphql.server.schema.types.AiAgent getAiAgentGraphql(AiAgent aiAgent,
            Set<AiAgentVersion> allAgentVersionsOptional) {
        return AI_AGENT_MAPPER.toAIAgent(aiAgent, allAgentVersionsOptional
                .stream()
                .map(AI_AGENT_VERSION_MAPPER::toAiAgentVersion)
                .collect(Collectors.toSet()));
    }

    private static boolean validateInput(AiAgentsQueryInput input,
                                         DataFetchingEnvironment dfe) {
        return ValidationUtil.validatePagination(input.getPageIndex(), input.getPageSize(), dfe
                , PAGE_INDEX_INVALID, PAGE_SIZE_INVALID);
    }

    private AiAgentProviderAccount validateAndGetProviderAccount(String providerAccountId,
                                                                 DataFetchingEnvironment dfe) {
        // Validate provider account input id
        if (!ValidationUtil.validateProviderAccountInputId(providerAccountId, dfe)) {
            return null;
        }

        // Retrieve Provider Account Entity
        AiAgentProviderAccount providerAccount = ValidationUtil.getProviderAccount(providerAccountId,
                _aiAgentProviderAccountRepository, dfe);
        if (providerAccount == null) {
            return null;
        }
        return providerAccount;
    }

    private Optional<AiAgent> getAgent(String agentId) {
        return _aiAgentRepository.findByGuid(agentId);
    }

    private AiAgentVersion getAgentVersion(String version, Integer aiAgentId) {
        return _aiAgentVersionRepository.findByAgentUidAndVersionString(aiAgentId, version)
                .orElse(null);
    }

    private AiAgentVersion createAgentVersion(AiAgentCreateInput input, AiAgent existingAgent,
            DataFetchingEnvironment dfe) {
        AiAgentVersion newAiAgentVersion = AI_AGENT_VERSION_MAPPER.toEntityFromAgentVersionCreate(input, existingAgent);
        // Use Agent Version’s GUID for its external id (aka. fake external id, due to the Custom Type provider.)
        newAiAgentVersion.setExternalId(newAiAgentVersion.getGuid());
        newAiAgentVersion.setCreatedByUserId(_userUtil.getUserName(dfe));
        newAiAgentVersion.setModifiedByUserId(_userUtil.getUserName(dfe));
        return _aiAgentVersionRepository.save(newAiAgentVersion);
    }

    private AiAgentVersion updateAgentVersion(AiAgentUpdateInput input, AiAgentVersion existingAgentVersion,
            DataFetchingEnvironment dfe) {
        AiAgentVersion updatedAiAgentVersion =
                AI_AGENT_VERSION_MAPPER.toEntityFromAgentVersionUpdate(input, existingAgentVersion);
        updatedAiAgentVersion.setModifiedByUserId(_userUtil.getUserName(dfe));
        return _aiAgentVersionRepository.save(updatedAiAgentVersion);
    }

    private AiAgent createAgent(AiAgentCreateInput input, AiAgentProviderAccount aiAgentProviderAccount) {
        AiAgent newAiAgent = AI_AGENT_MAPPER.toEntityFromAgentCreateInput(input, aiAgentProviderAccount);
        // Use Agent’s GUID for its external id (aka. fake external id, due to the Custom Type provider.)
        newAiAgent.setExternalId(newAiAgent.getGuid());
        newAiAgent = _aiAgentRepository.save(newAiAgent);
        return newAiAgent;
    }

    private Optional<Set<AiAgentVersion>> getAllAgentVersionsForAgentUid(Integer aiAgentId) {
        return _aiAgentVersionRepository.findByAgentUid(aiAgentId);
    }

    private @NotNull CompletableFuture<com.boomi.graphql.server.schema.types.AiAgent>
    getAgentCompletableFutureFromAgentVersion(AiAgentVersion dbAiAgentVersion,
                                              AiAgentAlias dbAiAgentAlias,
                                              DataFetchingEnvironment dfe) {
        com.boomi.graphql.server.schema.types.AiAgentVersion aiAgentVersion;
        com.boomi.graphql.server.schema.types.AiAgentAlias agentAlias = null;
        if (dbAiAgentAlias != null) {
            agentAlias = ALIAS_MAPPER
                    .toAiAgentAlias(dbAiAgentAlias);
            Set<com.boomi.graphql.server.schema.types.AiAgentAlias> agentAliasSet = Set.of(agentAlias);
            aiAgentVersion = AI_AGENT_VERSION_MAPPER.toAiAgentVersion(dbAiAgentVersion,
                    agentAliasSet);
        } else {
            aiAgentVersion = AI_AGENT_VERSION_MAPPER.toAiAgentVersion(dbAiAgentVersion);
        }

        AiAgent dbAiAgent = dbAiAgentVersion.getAgent();
        com.boomi.graphql.server.schema.types.AiAgent aiAgent = AI_AGENT_MAPPER.toAIAgent(dbAiAgent,
                Collections.singleton(aiAgentVersion));
        populateExternalLinks(dbAiAgent, dbAiAgentVersion, dbAiAgentAlias, aiAgent, aiAgentVersion, agentAlias, dfe);

        return CompletableFuture.completedFuture(aiAgent);
    }

    private void populateExternalLinks(
            AiAgent dbAiAgent,
            AiAgentVersion dbAiAgentVersion,
            AiAgentAlias dbAiAgentAlias,
            com.boomi.graphql.server.schema.types.AiAgent aiAgent,
            com.boomi.graphql.server.schema.types.AiAgentVersion aiAgentVersion,
            com.boomi.graphql.server.schema.types.AiAgentAlias agentAlias,
            DataFetchingEnvironment dfe) {
        AiAgentProviderAccount dbProviderAccount = dbAiAgent.getAiAgentProviderAccount();
        AiAgentProviderType providerType = dbProviderAccount.getProviderType();

        ExternalLinkParams.ExternalLinkParamsBuilder externalLinkParamsBuilder = ExternalLinkParams.builder()
                .agentExternalId(dbAiAgent.getExternalId())
                .agentVersion(dbAiAgentVersion.getVersionString())
                .platformAccountId(_userUtil.getAccountId(dfe))
                .providerRegion(dbProviderAccount.getRegion());

        if (dbAiAgentAlias != null) {
            externalLinkParamsBuilder.agentAliasExternalId(dbAiAgentAlias.getExternalId());
        }
        ExternalLinkParams externalLinkParams = externalLinkParamsBuilder.build();

        populateAgentExternalLink(aiAgent, providerType, externalLinkParams);
        populateAgentVersionExternalLink(aiAgentVersion, providerType, externalLinkParams);
        populateAgentAliasExternalLink(agentAlias, providerType, externalLinkParams);
    }

    private void populateAgentExternalLink(
            com.boomi.graphql.server.schema.types.AiAgent aiAgent,
            AiAgentProviderType providerType,
            ExternalLinkParams externalLinkParams) {
        try {
            String agentExternalLink = _externalLinkProviderServiceFactory
                    .getStrategy(providerType)
                    .getAgentExternalLink(externalLinkParams);
            aiAgent.setExternalLink(agentExternalLink);
        } catch (Exception exception) {
            log.warn(EXCEPTION_EXTERNAL_LINK, AiRegistryEntityType.AGENT, externalLinkParams, exception);
        }
    }

    private void populateAgentVersionExternalLink(com.boomi.graphql.server.schema.types.AiAgentVersion aiAgentVersion,
            AiAgentProviderType providerType, ExternalLinkParams externalLinkParams) {
        try {
            String agentVersionExternalLink = _externalLinkProviderServiceFactory
                    .getStrategy(providerType)
                    .getAgentVersionExternalLink(externalLinkParams);
            aiAgentVersion.setExternalLink(agentVersionExternalLink);
        } catch (Exception exception) {
            log.warn(EXCEPTION_EXTERNAL_LINK, AiRegistryEntityType.VERSION, externalLinkParams, exception);
        }
    }

    private void populateAgentAliasExternalLink(com.boomi.graphql.server.schema.types.AiAgentAlias agentAlias,
            AiAgentProviderType providerType, ExternalLinkParams externalLinkParams) {
        if (agentAlias != null) {
            try {
                String agentAliasExternalLink = _externalLinkProviderServiceFactory
                        .getStrategy(providerType)
                        .getAgentAliasExternalLink(externalLinkParams);
                agentAlias.setExternalLink(agentAliasExternalLink);
            } catch (Exception exception) {
                log.warn(EXCEPTION_EXTERNAL_LINK, AiRegistryEntityType.ALIAS, externalLinkParams, exception);

            }
        }
    }

    @NotNull
    private CompletionStage<com.boomi.graphql.server.schema.types.AiAgent> getAiAgent(
            CompletableFuture<com.boomi.graphql.server.schema.types.AiAgent> completion, AiAgent aiAgent) {

        Optional<Set<AiAgentVersion>> allAgentVersionsOptional = getAllAgentVersionsForAgentUid(aiAgent.getUid());

        if (allAgentVersionsOptional.isPresent()) {
            com.boomi.graphql.server.schema.types.AiAgent aiAgentGraphql = getAiAgentGraphql(aiAgent,
                    allAgentVersionsOptional.get());
            completion.complete(aiAgentGraphql);
            return completion;
        }
        completion.complete(null);
        return completion;
    }

    private void validateCreateAIAgentInput(AiAgentCreateInput input, AiAgentProviderAccount providerAccount,
            DataFetchingEnvironment dfe) {
        log.info("Validating AiAgent Create input");
        if (input == null) {
            ErrorUtil.addError(dfe, AI_AGENT_CREATE_INPUT_NULL);
            log.warn(AI_AGENT_CREATE_INPUT_NULL_ERROR);
            return;
        }

        validateAgentName(input.getAgentName(), providerAccount.getUid(), dfe);
        validateTagInput(input.getTags(), dfe);
        validateLlmInput(input.getLlms(), dfe);
        validateTrustLevel(input.getTrustLevel(), dfe);
    }

    private void validateAgentName(String agentName, Integer providerAccountUid, DataFetchingEnvironment dfe) {
        if (isInputFieldPresent(dfe, PROP_AGENT_NAME)) {
            if (StringUtil.isBlank(agentName)) {
                ErrorUtil.addError(dfe, AI_AGENT_NAME_REQUIRED);
                log.warn(INVALID_AI_AGENT_NAME_ERROR);
            } else {
                // check agent name already exist for  provider account uid
                if (_aiAgentVersionRepository
                        .existsByNameAndAiAgentProviderAccountUidAndAgentIsDeletedAndVersionIsDeleted
                                (agentName, providerAccountUid, false)) {
                    ErrorUtil.addError(dfe, AI_AGENT_NAME_ALREADY_EXIST);
                    log.warn(AI_AGENT_NAME_ALREADY_EXIST_ERROR, agentName);
                }
            }
        }
    }

    private void validateLlmInput(List<AiAgentLlmInput> llms, DataFetchingEnvironment dfe) {
        if (isInputFieldPresent(dfe, PROP_LLMS)
                && (llms.stream().anyMatch(llm -> StringUtil.isBlank(llm.getName())))) {
            ErrorUtil.addError(dfe, INVALID_LLM_INPUT);
            log.warn(INVALID_LLM_INPUT_ERROR);
        }
    }

    private void validateTrustLevel(AiAgentRegistryTrustLevel trustLevel, DataFetchingEnvironment dfe) {
        if (isInputFieldPresent(dfe, PROP_TRUST_LEVEL)
                && trustLevel == null) {
            ErrorUtil.addError(dfe, INVALID_TRUST_LEVEL);
            log.warn(INVALID_TRUST_LEVEL_ERROR);
        }
    }

    private void validateTagInput(List<AiAgentTagInput> tags, DataFetchingEnvironment dfe) {
        if (isInputFieldPresent(dfe, PROP_TAGS)
                && (tags.stream().anyMatch(tag -> StringUtil.isBlank(tag.getKey())))) {
            ErrorUtil.addError(dfe, INVALID_TAG_INPUT);
            log.warn(INVALID_TAG_INPUT_ERROR);
        }
    }

    private void createTagAndAssociateWithCurrentVersion(List<AiAgentTagInput> tags, AiAgentVersion agentVersion,
            DataFetchingEnvironment dfe) {
        log.info("Creating non existing tag and associating into current version");
        try {
            // get the idp account from dfe
            String idpAccountId = _userUtil.getAccountId(dfe);

            // Early null/empty check for tags
            if (tags == null || tags.isEmpty()) {
                // Just delete existing associations since no tags are provided
                _aiAgentTagAssociationRepository.deleteAllByRelatedEntityUidAndRelatedEntityType(
                        agentVersion.getUid(),
                        AiRegistryEntityType.VERSION
                );
                return;
            }

            // check any tag name already exist in db for idpAccountId
            List<String> tagKeys = tags.stream().map(AiAgentTagInput::getKey).collect(Collectors.toList());
            List<AiAgentTag> existingTags = _aiAgentTagRepository.findByIdpAccountIdAndTagKeyIn(idpAccountId, tagKeys);

            // construct tag entity object from AiAgentTagInput excluding tag key exist in existingTags
            List<AiAgentTag> newTags = tags.stream()
                    .filter(tag -> existingTags.stream()
                            .noneMatch(existingTag -> existingTag.getKey().equals(tag.getKey())))
                    .map(tag -> populateAiAgentTagEntity(idpAccountId, tag.getKey(), tag.getValue()))
                    .collect(Collectors.toList());

            // save new tags
            if (!newTags.isEmpty()) {
                _aiAgentTagRepository.saveAll(newTags);
            }

            // remove all the entry from tagAssociation table for current version uid and type VERSION
            _aiAgentTagAssociationRepository.deleteAllByRelatedEntityUidAndRelatedEntityType(agentVersion.getUid(),
                    AiRegistryEntityType.VERSION);

            // Filter existing tags to only include those that are in the input tags list
            List<AiAgentTag> relevantExistingTags = existingTags.stream()
                    .filter(existingTag -> tags.stream()
                            .anyMatch(inputTag -> inputTag.getKey().equals(existingTag.getKey())))
                    .toList();

            // update tag association db with existingTags and new Tags uid with current version uid
            List<AiAgentTag> allTags =
                    Stream.concat(relevantExistingTags.stream(), newTags.stream()).toList();
            if (!allTags.isEmpty()) {
                List<Integer> allTagsUid = allTags.stream().map(AiAgentTag::getUid).toList();
                _aiAgentTagAssociationRepository.saveAll(
                        allTagsUid
                                .stream()
                                .map(tagUid -> populateAiAgentTagAssociationEntity(agentVersion.getUid(), tagUid))
                                .collect(Collectors.toList()));
            }
        } catch (Exception e) {
            log.warn(ADD_TAG_TO_AGENT_ERROR, e.getMessage(), e);
        }
    }

    private void createModelAndAssociateWithCurrentVersion(List<AiAgentLlmInput> llms,
            AiAgentProviderAccount providerAccount, AiAgentVersion agentVersion, DataFetchingEnvironment dfe) {
        log.info("Creating non existing model and associating into current version");
        try {

            if (llms == null || llms.isEmpty()) {
                // Just delete existing associations since no LLMs are provided
                _aiAgentLlmAssociationRepository.deleteAllByRelatedEntityUidAndRelatedEntityType(
                        agentVersion.getUid(),
                        AiRegistryEntityType.VERSION
                );
                return;
            }

            // check llm names from input llms exist for providerAccountId
            List<String> llmNames = llms.stream().map(AiAgentLlmInput::getName).collect(Collectors.toList());
            List<AiAgentLlm> existingLlms =
                    _aiAgentLlmRepository.findByProviderAccountUidAndNameIn(providerAccount.getUid(), llmNames);

            // construct llm entity object from AiAgentLlmInput excluding llm name exist in existingLlms
            List<AiAgentLlm> newLlms = llms.stream()
                    .filter(llm -> existingLlms.stream()
                            .noneMatch(existingLlm -> existingLlm.getName().equals(llm.getName())))
                    .map(llm -> populateAiAgentLlmEntity(providerAccount, llm.getName(), llm.getDescription(),
                            _userUtil.getAccountId(dfe)))
                    .collect(Collectors.toList());
            // save new llms
            if (!newLlms.isEmpty()) {
                _aiAgentLlmRepository.saveAll(newLlms);
            }

            // remove all the entry from llmAssociation table for current version uid and type VERSION
            _aiAgentLlmAssociationRepository.deleteAllByRelatedEntityUidAndRelatedEntityType(agentVersion.getUid(),
                    AiRegistryEntityType.VERSION);

            // Filter existing LLMs to only include those that are in the input LLMs list
            List<AiAgentLlm> relevantExistingLlms = existingLlms.stream()
                    .filter(existingLlm -> llms.stream()
                            .anyMatch(inputLlm -> inputLlm.getName().equals(existingLlm.getName())))
                    .toList();

            // update llm association db with existingLlms and new Llms uid with current version uid
            List<AiAgentLlm> allLlms = Stream.concat(relevantExistingLlms.stream(), newLlms.stream()).toList();

            if (!allLlms.isEmpty()) {
                _aiAgentLlmAssociationRepository.saveAll(
                        allLlms.stream().map(llm -> populateAiAgentLlmAssociationEntity(agentVersion.getUid(), llm))
                                .collect(Collectors.toList()));
            }
        } catch (Exception e) {
            log.warn(ADD_LLM_TO_AGENT_ERROR, e.getMessage(), e);
        }
    }

    private static AiAgentLlmAssociation populateAiAgentLlmAssociationEntity(Integer uid, AiAgentLlm llm) {
        return AiAgentLlmAssociation.builder()
                .guid(GuidUtil.createLlmAssociationGuid())
                .llm(llm)
                .relatedEntityUid(uid)
                .relatedEntityType(AiRegistryEntityType.VERSION)
                .build();
    }

    private static AiAgentLlm populateAiAgentLlmEntity(AiAgentProviderAccount providerAccount, String name,
            String description, String idpAccountId) {

        String guid = GuidUtil.createLlmGuid();
        return AiAgentLlm.builder()
                .guid(guid)
                .externalId(guid)
                .providerAccount(providerAccount)
                .idpAccountId(idpAccountId)
                .name(name)
                .description(description)
                .build();
    }

    private static AiAgentTagAssociation populateAiAgentTagAssociationEntity(Integer versionUid, Integer tagUid) {
        return AiAgentTagAssociation.builder()
                .guid(GuidUtil.createAIAgentTagAssociationGuid())
                .tagUid(tagUid)
                .relatedEntityUid(versionUid)
                .relatedEntityType(AiRegistryEntityType.VERSION)
                .build();
    }

    private static AiAgentTag populateAiAgentTagEntity(String idpAccountId, String key, String value) {
        return AiAgentTag.builder()
                .guid(GuidUtil.createAIAgentTagGuid())
                .idpAccountId(idpAccountId)
                .key(key)
                .value(value)
                .build();
    }

    private void validateUpdateAiAgentInput(AiAgentUpdateInput input, AiAgent existingAiAgent,
            DataFetchingEnvironment dfe) {

        AiAgentVersion existingVersion = null;
        if (StringUtil.isBlank(input.getVersionId())) {
            ErrorUtil.addError(dfe, VERSION_ID_REQUIRED);
            log.warn(VERSION_ID_REQUIRED_ERROR);
            return;
        } else {
            existingVersion =
                    _aiAgentVersionRepository
                            .findAiAgentVersionByGuidAndIsDeletedAndAgent_Uid(input.getVersionId(), false,
                                    existingAiAgent.getUid())
                            .orElse(null);
            if (existingVersion == null) {
                ErrorUtil.addError(dfe, AI_AGENT_VERSION_NOT_FOUND);
                log.warn(ERROR_AGENT_VERSION_NOT_FOUND, input.getVersionId());
                return;
            }
        }

        // check if updated agent name already exists or not
        if (isInputFieldPresent(dfe, PROP_AGENT_NAME)) {
            log.info("Validating AiAgent name input");
            if (StringUtil.isBlank(input.getAgentName())) {
                ErrorUtil.addError(dfe, AI_AGENT_NAME_REQUIRED);
                log.warn(INVALID_AI_AGENT_NAME_ERROR);
            } else {
                log.info("Verify if an agent with this name already exists for the current provider account");

                if (!existingVersion.getName().equalsIgnoreCase(input.getAgentName())) {
                    // get the providerAccount from existingAiAgent
                    AiAgentProviderAccount providerAccount = existingAiAgent.getAiAgentProviderAccount();

                    // get all the existing agent for providerAccount
                    List<AiAgent> existingAgents =
                            _aiAgentRepository.findByAiAgentProviderAccountAndIsDeleted(providerAccount, false)
                                    .orElse(null);

                    // check is any version from existingAgents has same name as input.getAgentName()
                    if (existingAgents != null && existingAgents.stream()
                            .flatMap(aiAgent -> aiAgent.getVersions().stream())
                            .anyMatch(aiAgentVersion -> !aiAgentVersion.getIsDeleted()
                                    && aiAgentVersion.getName().equalsIgnoreCase(input.getAgentName()))) {
                        ErrorUtil.addError(dfe, AI_AGENT_NAME_ALREADY_EXIST);
                        log.warn(AI_AGENT_NAME_ALREADY_EXIST_ERROR, input.getAgentName());
                    }
                }
            }
        }

        // validate updated tags input
        validateTagInput(input.getTags(), dfe);
        // validate updated model/llm input
        validateLlmInput(input.getLlms(), dfe);
        // validate truest level
        validateTrustLevel(input.getTrustLevel(), dfe);
    }

    // we are deleting single agent, thinking we are not have much data used normal delete call over Async call.
    public void deleteAgent(AiAgent aiAgent, DataFetchingEnvironment dfe) {
        log.info("Deleting Agent and its related as associations with ID: {}", aiAgent.getGuid());

        // update is_deleted flag for aliases associated with each version of the agent
        log.info("Updating is_deleted flag for aliases associated with each version of the agent");
        _aiAgentAliasRepository.saveAll(
                aiAgent.getVersions().stream()
                        .flatMap(version -> version.getAliases().stream())
                        .peek(alias -> {
                            alias.setIsDeleted(true);
                            _auditUtil.setUpdatedAudit(alias, dfe);
                        })
                        .toList());

        // update is_deleted flag for all the version associated with agent
        log.info("Updating is_deleted flag for all the versions associated with agent");
        _aiAgentVersionRepository.saveAll(
                aiAgent.getVersions().stream()
                        .peek(version -> {
                            version.setIsDeleted(true);
                            _auditUtil.setUpdatedAudit(version, dfe);
                            // check with  Sandesh

                            //delete AiAgentListing table entry for immediate query.
                            _aiAgentListingRepository.deleteById(new VersionAliasIdKey(0,version.getUid()));

                        })
                        .toList());

        // update is_deleted flag for the agent
        log.info("Updating is_deleted flag for agent");
        aiAgent.setIsDeleted(true);
        aiAgent.setModifiedTime(AuditUtilImpl.getCurrentTime());
        _aiAgentRepository.save(aiAgent);
    }


    private static AiAgentListing populateAiAgentListing
            (AiAgent aiAgent, AiAgentVersion agentVersion, AiAgentProviderAccount providerAccount) {
        AiAgentListing listing = new AiAgentListing();

        // Set Agent related fields
        listing.setAgentId(aiAgent.getUid());
        listing.setAgentGuid(aiAgent.getGuid());
        listing.setAgentExternalId(aiAgent.getExternalId());
        listing.setAgentIsDeleted(aiAgent.getIsDeleted());

        // Set Version related fields
        listing.setVersionGuid(agentVersion.getGuid());
        listing.setVersionName(agentVersion.getName());
        listing.setVersion(agentVersion.getVersionString());
        listing.setDescription(agentVersion.getDescription());
        listing.setVersionExternalId(agentVersion.getExternalId());
        listing.setVersionIsDeleted(agentVersion.getIsDeleted());

        // Set Provider Account related fields
        listing.setProviderAccountUid(providerAccount.getUid());
        listing.setProviderType(providerAccount.getProviderType().toString());
        listing.setProviderAccountGuId(providerAccount.getGuid());
        listing.setIdpAccountId(providerAccount.getIdpAccountId());
        listing.setProviderAccountName(providerAccount.getProviderAccountName());

        // Set Status
        listing.setAgentStatus(agentVersion.getAgentStatus());

        // Set Trust Level (if applicable)
        listing.setTrustLevel(agentVersion.getTrustLevel().name());

        // Set timestamps
        listing.setModifiedTime(agentVersion.getModifiedTime());
        listing.setUpdatedAtProviderTime(agentVersion.getUpdatedAtProviderTime());

        // Set Alias related fields (since custom providers, no alias exists)
        listing.setHasAlias(false);
        listing.setAliasIsDeleted(false);
        VersionAliasIdKey id = new VersionAliasIdKey(0,agentVersion.getUid());
        listing.setId(id);
        listing.setAliasGuid(null);
        listing.setAliasName(null);
        listing.setAliasExternalId(null);

        return listing;
    }

}
