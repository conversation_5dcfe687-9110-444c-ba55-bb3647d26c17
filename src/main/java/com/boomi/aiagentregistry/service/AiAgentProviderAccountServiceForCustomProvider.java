// Copyright (c) 2024 Boomi, LP

package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.util.AiAgentProviderAccountCommonServiceUtil;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccount;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountUpdateInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.servlet.ErrorUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_ON_ADDING_PROVIDER_DETAILS_FOR_CUSTOM;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_DUPLICATE_NAME_CONSTRAINT;
import static com.boomi.graphql.server.servlet.ErrorUtil.GeneralErrorCode.SYSTEM_ERROR;

@Service("aiAgentProviderAccountServiceForCustomProvider")
@Slf4j
public class AiAgentProviderAccountServiceForCustomProvider implements AiAgentProviderAccountService{

    @Autowired
    AiAgentProviderAccountCommonServiceUtil _aiAgentProviderAccountCommonServiceUtil;

    @Override
    public AiAgentProviderType getProviderType() {
        return AiAgentProviderType.CUSTOM;
    }

    @Override
    @Transactional
    public CompletionStage<AiAgentProviderAccount> createAiAgentProviderAccount(AiAgentProviderAccountCreateInput input,
            DataFetchingEnvironment dfe) {
        log.info("Creating provider account for provider type {}", input.getProviderType().name());

        CompletableFuture<com.boomi.graphql.server.schema.types.AiAgentProviderAccount> completion =
                new CompletableFuture<>();
        try {

            String idpAccountId = _aiAgentProviderAccountCommonServiceUtil.getIdpAccountId(dfe);
            validateAiAgentProviderAccountCreateInputForCustom(input, idpAccountId, dfe);

            if(ErrorUtil.hasErrors(dfe)){
                completion.complete(null);
                return completion;
            }

            // for custom provider both external and ipd account are same.
            return _aiAgentProviderAccountCommonServiceUtil
                    .createProviderAccount(input , completion,idpAccountId, idpAccountId, dfe);


        } catch (Exception dae) {
            ErrorUtil.addError(dfe, SYSTEM_ERROR);
            log.error(ERROR_ON_ADDING_PROVIDER_DETAILS_FOR_CUSTOM, dae);
            completion.complete(null);
            return completion;
        }
    }

    @Override
    @Transactional
    public CompletionStage<AiAgentProviderAccount> updateAiAgentProviderAccount(AiAgentProviderAccountUpdateInput input,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {
        log.info("Updating Boomi Provider");

        return validateAndUpdateAiAgentProviderAccount(input, existingProviderAccount, dfe);
    }

    private CompletionStage<AiAgentProviderAccount> validateAndUpdateAiAgentProviderAccount(
            AiAgentProviderAccountUpdateInput input,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {

        CompletableFuture<com.boomi.graphql.server.schema.types.AiAgentProviderAccount> completion =
                new CompletableFuture<>();

        validateAiAgentProviderAccountUpdateInputForCustom(input, existingProviderAccount, dfe);

        if(ErrorUtil.hasErrors(dfe)){
            completion.complete(null);
            return completion;
        }

        return _aiAgentProviderAccountCommonServiceUtil
                .updateAiAgentProviderAccount(input, existingProviderAccount, completion, dfe);

    }

    private void validateAiAgentProviderAccountCreateInputForCustom(AiAgentProviderAccountCreateInput input,
            String idpAccountId, DataFetchingEnvironment dfe) {
        _aiAgentProviderAccountCommonServiceUtil.validateIdpAccountId(dfe);
        _aiAgentProviderAccountCommonServiceUtil
                .validateProviderAccountName(input.getProviderAccountName(), null, dfe);
        if (_aiAgentProviderAccountCommonServiceUtil
                .isProviderAccountExists(input.getProviderAccountName(), idpAccountId)) {
            log.warn(PROVIDER_ACCOUNT_DUPLICATE_NAME_CONSTRAINT.name());
            ErrorUtil.addError(dfe, PROVIDER_ACCOUNT_DUPLICATE_NAME_CONSTRAINT);
        }
        _aiAgentProviderAccountCommonServiceUtil.validateProvideAccountDescription(
                input.getProviderAccountDescription(), dfe);

        if (input.getProviderAccountName() != null
                && _aiAgentProviderAccountCommonServiceUtil.isProviderAccountExistsForProviderTypeAndIdpAccountId(
                        input.getProviderAccountName(), idpAccountId, input.getProviderType())) {
            log.warn(PROVIDER_ACCOUNT_DUPLICATE_NAME_CONSTRAINT.name());
            ErrorUtil.addError(dfe, PROVIDER_ACCOUNT_DUPLICATE_NAME_CONSTRAINT);
        }
    }

    private void validateAiAgentProviderAccountUpdateInputForCustom(AiAgentProviderAccountUpdateInput input,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {
        _aiAgentProviderAccountCommonServiceUtil.validateIdpAccountId(dfe);
        _aiAgentProviderAccountCommonServiceUtil.validateProviderAccountName(input.getProviderAccountName(),
                existingProviderAccount, dfe);
        _aiAgentProviderAccountCommonServiceUtil
                .validateProvideAccountDescription(input.getProviderAccountDescription(), dfe);

    }
}
