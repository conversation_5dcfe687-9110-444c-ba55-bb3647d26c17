// Copyright (c) 2024 Boomi, LP.

package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.constant.ActionEnum;
import com.boomi.aiagentregistry.constant.ActionResultEnum;
import com.boomi.aiagentregistry.entity.AgentEntitySyncLatest;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentGuardrailAssociation;
import com.boomi.aiagentregistry.entity.AiAgentLargeTextContent;
import com.boomi.aiagentregistry.entity.AiAgentLlmAssociation;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTagAssociation;
import com.boomi.aiagentregistry.entity.AiAgentTaskAssociation;
import com.boomi.aiagentregistry.entity.AiAgentToolAssociation;
import com.boomi.aiagentregistry.mapper.AiAgentLlmMapper;
import com.boomi.aiagentregistry.mapper.AiAgentSyncLatestMapper;
import com.boomi.aiagentregistry.mapper.AiAgentTaskMapper;
import com.boomi.aiagentregistry.mapper.AiAgentToolMapper;
import com.boomi.aiagentregistry.model.AuditLogEntry;
import com.boomi.aiagentregistry.repo.AiAgentAliasRepository;
import com.boomi.aiagentregistry.repo.AiAgentGuardrailAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentLargeTextContentRepository;
import com.boomi.aiagentregistry.repo.AiAgentLatestSyncRepository;
import com.boomi.aiagentregistry.repo.AiAgentLlmAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.repo.AiAgentTagAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentTagRepository;
import com.boomi.aiagentregistry.repo.AiAgentTaskAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentVersionRepository;
import com.boomi.aiagentregistry.service.agent.action.AiAgentActionResponse;
import com.boomi.aiagentregistry.service.agent.action.AiAgentActionServiceFactory;
import com.boomi.aiagentregistry.util.AuditUtil;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.UserUtil;
import com.boomi.aiagentregistry.util.ValidationUtil;
import com.boomi.graphql.server.schema.types.AiAgentAlias;
import com.boomi.graphql.server.schema.types.AiAgentGuardrail;
import com.boomi.graphql.server.schema.types.AiAgentLlm;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentRegistryTrustLevel;
import com.boomi.graphql.server.schema.types.AiAgentTag;
import com.boomi.graphql.server.schema.types.AiAgentTask;
import com.boomi.graphql.server.schema.types.AiAgentTool;
import com.boomi.graphql.server.schema.types.AiAgentTrustLevelQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentTrustLevelQueryResponse;
import com.boomi.graphql.server.schema.types.AiAgentVersion;
import com.boomi.graphql.server.schema.types.AiAgentVersionEnableInput;
import com.boomi.graphql.server.schema.types.AiAgentVersionTrustLevelAddInput;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncData;
import com.boomi.graphql.server.servlet.ErrorUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collections;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.CORRELATION_PROPERTY_NAME;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_AGENT_VERSION_NOT_FOUND;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_ALIAS_NOT_FOUND_FOR_VERSION;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_DISABLING_AGENT_VERSION;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_ENABLING_AGENT_VERSION;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FAILED_TO_ADD_TRUST_LEVEL_ERROR;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INFO_ALIAS_FOUND_FOR_VERSION;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INFO_NO_SYNC_DATA_FOR_VERSION;
import static com.boomi.aiagentregistry.mapper.AiAgentAliasMapper.ALIAS_MAPPER;
import static com.boomi.aiagentregistry.mapper.AiAgentGuardrailMapper.AI_AGENT_GUARDRAIL_MAPPER;
import static com.boomi.aiagentregistry.mapper.AiAgentTagMapper.AI_AGENT_TAG_MAPPER;
import static com.boomi.aiagentregistry.mapper.AiAgentVersionMapper.AI_AGENT_VERSION_MAPPER;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.AI_AGENT_VERSION_NOT_FOUND;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.AI_AGENT_VERSION_TAG_ERROR;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.FAILED_TO_ADD_TRUST_LEVEL;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.FAILED_TO_DISABLE_AGENT_VERSION;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.FAILED_TO_ENABLE_AGENT_VERSION;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.VERSION;
import static com.boomi.graphql.server.servlet.ErrorUtil.GeneralErrorCode.SYSTEM_ERROR;

@Service
@Slf4j
public class AiAgentVersionServiceImpl implements AiAgentVersionService {
    private final AiAgentProviderAccountRepository _aiAgentProviderAccountRepository;
    private final AiAgentGuardrailAssociationRepository _aiAgentGuardrailAssociationRepository;
    private final AiAgentVersionRepository _aiAgentVersionRepository;
    private final AiAgentToolAssociationRepository _aiAgentToolAssociationRepository;
    private final AiAgentAliasRepository _aiAgentAliasRepository;
    private final AiAgentTaskAssociationRepository _aiAgentTaskAssociationRepository;
    private final AiAgentTagAssociationRepository _aiAgentTagAssociationRepository;
    private final AiAgentTagRepository _aiAgentTagRepository;
    private final AiAgentLlmAssociationRepository _aiAgentLlmAssociationRepository;
    private final AiAgentLargeTextContentRepository _largeTxtRepo;
    private final AiAgentLatestSyncRepository _aiAgentLatestSyncRepository;
    private final AiAgentActionServiceFactory _aiAgentActionServiceFactory;
    private final UserUtil _userUtil;
    private final AuditUtil _auditUtil;
    private final AuditLogService _auditLogService;
    private final ObjectMapper _objectMapper;

    private static final int AI_AGENT_VERSION_AUDIT_LOG_CHANGE_SET_VERSION = 1;

    public AiAgentVersionServiceImpl(AiAgentGuardrailAssociationRepository aiAgentGuardrailAssociationRepository,
            AiAgentVersionRepository aiAgentVersionRepository,
            AiAgentToolAssociationRepository aiAgentToolAssociationRepository,
            AiAgentAliasRepository aiAgentAliasRepository,
            AiAgentTaskAssociationRepository aiAgentTaskAssociationRepository,
            AiAgentTagAssociationRepository aiAgentTagAssociationRepository, AiAgentTagRepository aiAgentTagRepository,
            AiAgentLlmAssociationRepository aiAgentLlmAssociationRepository,
            AiAgentLargeTextContentRepository largeTxtRepo, AiAgentLatestSyncRepository aiAgentLatestSyncRepository,
            AiAgentActionServiceFactory aiAgentActionServiceFactory,
            AiAgentProviderAccountRepository aiAgentProviderAccountRepository, UserUtil userUtil, AuditUtil auditUtil,
            AuditLogService auditLogService, ObjectMapper objectMapper) {
        _aiAgentGuardrailAssociationRepository = aiAgentGuardrailAssociationRepository;
        _aiAgentVersionRepository = aiAgentVersionRepository;
        _aiAgentToolAssociationRepository = aiAgentToolAssociationRepository;
        _aiAgentAliasRepository = aiAgentAliasRepository;
        _aiAgentTaskAssociationRepository = aiAgentTaskAssociationRepository;
        _aiAgentTagAssociationRepository = aiAgentTagAssociationRepository;
        _aiAgentTagRepository = aiAgentTagRepository;
        _aiAgentLlmAssociationRepository = aiAgentLlmAssociationRepository;
        _largeTxtRepo = largeTxtRepo;
        _aiAgentLatestSyncRepository = aiAgentLatestSyncRepository;
        _aiAgentActionServiceFactory = aiAgentActionServiceFactory;
        _aiAgentProviderAccountRepository = aiAgentProviderAccountRepository;
        _userUtil = userUtil;
        _auditUtil = auditUtil;
        _auditLogService = auditLogService;
        _objectMapper = objectMapper;
    }

    @Override
    public CompletionStage<List<AiAgentGuardrail>> getAiAgentGuardrails(AiAgentVersion aiAgentVersion,
                                                                        DataFetchingEnvironment dfe) {

        final com.boomi.aiagentregistry.entity.AiAgentVersion agentVersion =
                    findAiAgentVersionByGuid(aiAgentVersion.getId());

            if (agentVersion == null) {
                return CompletableFuture.completedFuture(Collections.emptyList());
            } else {
                Set<AiAgentGuardrailAssociation> aiAgentGuardrailAssociations =
                        _aiAgentGuardrailAssociationRepository
                                .findByRelatedEntityUidAndRelatedEntityType(agentVersion.getUid(), VERSION);

                List<com.boomi.aiagentregistry.entity.AiAgentGuardrail> aiAgentGuardrails =
                        _aiAgentGuardrailAssociationRepository
                                .findAiAgentGuardrailByRelatedEntityGuidAndRelatedEntityType
                                        (agentVersion.getUid(), VERSION).orElse(null);

                if (aiAgentGuardrailAssociations == null
                        || aiAgentGuardrailAssociations.isEmpty()
                        || aiAgentGuardrails == null
                        || aiAgentGuardrails.isEmpty()) {
                    return CompletableFuture.completedFuture(Collections.emptyList());
                }
                Map<Integer, String> aiAgentGuardrailUidAndAssociationStatusMap = new HashMap<>();

                aiAgentGuardrailAssociations.stream()
                        .filter(aiAgentGuardrailAssociation ->
                                aiAgentGuardrailAssociation != null
                                        && aiAgentGuardrailAssociation.getGuardrail() != null)
                        .forEach(aiAgentGuardrailAssociation ->
                                aiAgentGuardrailUidAndAssociationStatusMap.put(
                                        aiAgentGuardrailAssociation.getGuardrail().getUid(),
                                        aiAgentGuardrailAssociation.getAssociationStatus()));

                final List<AiAgentGuardrail> agentGuardrails = aiAgentGuardrails.stream()
                        .map(aiAgentGuardrail -> AI_AGENT_GUARDRAIL_MAPPER.toAiAgentGuardrail(
                                aiAgentGuardrail,
                                aiAgentGuardrailUidAndAssociationStatusMap.getOrDefault(aiAgentGuardrail.getUid(),
                                        null))).collect(
                                Collectors.toList());

                return CompletableFuture.completedFuture(agentGuardrails);
            }
    }

    @Override
    public CompletionStage<List<AiAgentTool>> getAiAgentTools(AiAgentVersion aiAgentVersion,
                                                              DataFetchingEnvironment dfe) {

        // get Agent Version Entity
        final com.boomi.aiagentregistry.entity.AiAgentVersion aiAgentVersionFromDB =
                findAiAgentVersionByGuid(aiAgentVersion.getId());

        if (aiAgentVersionFromDB == null) {
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

        Integer agentVersionEntityUid = aiAgentVersionFromDB.getUid();
        // Get all tool associations for this agent version
        Set<AiAgentToolAssociation> toolAssociations =
                _aiAgentToolAssociationRepository.findByRelatedEntityUidAndRelatedEntityType(agentVersionEntityUid,
                        VERSION);

        if (toolAssociations == null || toolAssociations.isEmpty()) {
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

        // Get tool and convert tool entity to graphql dto
        List<AiAgentTool> agentToolDtoList = toolAssociations.stream().map(
                toolAssociation -> AiAgentToolMapper.AI_AGENT_TOOL_MAPPER.toAIAgentToolDto(
                        toolAssociation.getTool(),
                        toolAssociation.getAssociationStatus())).collect(Collectors.toList());

        return CompletableFuture.completedFuture(agentToolDtoList);
    }

    @Override
    public CompletionStage<List<AiAgentAlias>> getAiAgentAliases(AiAgentVersion aiAgentVersion,
                                                                 DataFetchingEnvironment dfe) {
        final com.boomi.aiagentregistry.entity.AiAgentVersion agentVersion =
                findAiAgentVersionByGuid(aiAgentVersion.getId());

        if (agentVersion == null) {
            log.warn(ERROR_AGENT_VERSION_NOT_FOUND, aiAgentVersion.getId());
            return CompletableFuture.completedFuture(Collections.emptyList());
        } else {
            final List<com.boomi.aiagentregistry.entity.AiAgentAlias> aiAgentAliasEntities =
                    _aiAgentAliasRepository.findByAgentVersionAndIsDeletedFalse(agentVersion).orElse(
                            Collections.emptyList());
            if (aiAgentAliasEntities.isEmpty()) {
                log.info(ERROR_ALIAS_NOT_FOUND_FOR_VERSION, aiAgentVersion.getId());
                return CompletableFuture.completedFuture(Collections.emptyList());
            } else {
                log.info(INFO_ALIAS_FOUND_FOR_VERSION, aiAgentAliasEntities.size());
                return CompletableFuture.completedFuture(ALIAS_MAPPER.toAiAgentAliasList(aiAgentAliasEntities));
            }
        }
    }

    @Transactional
    @Override
    public CompletionStage<AiAgentVersion> aiAgentVersionTrustLevelAdd(
            AiAgentVersionTrustLevelAddInput input, DataFetchingEnvironment dfe) {

        try {
            String idpAccountId = _userUtil.getAccountId(dfe);
            final com.boomi.aiagentregistry.entity.AiAgentVersion agentVersion =
                    _aiAgentVersionRepository.findByGuidAndAgentIdpAccountId(input.getAgentVersionId(), idpAccountId)
                                             .orElse(null);

            if (agentVersion == null) {
                ErrorUtil.addError(dfe, AI_AGENT_VERSION_NOT_FOUND);
                log.warn(ERROR_AGENT_VERSION_NOT_FOUND, input.getAgentVersionId());
                return CompletableFuture.completedFuture(null);
            }
            agentVersion.setTrustLevel(input.getTrustLevel());
            agentVersion.setModifiedTime(AuditUtilImpl.getCurrentTime());
            final com.boomi.aiagentregistry.entity.AiAgentVersion updatedVersion =
                    _aiAgentVersionRepository.save(agentVersion);

            return CompletableFuture.completedFuture(AI_AGENT_VERSION_MAPPER.toAiAgentVersion(updatedVersion));
        } catch (Exception e) {
            ErrorUtil.addError(dfe, FAILED_TO_ADD_TRUST_LEVEL, input.getAgentVersionId());
            log.warn(FAILED_TO_ADD_TRUST_LEVEL_ERROR, input.getAgentVersionId(), e.getMessage());
            return CompletableFuture.completedFuture(null);
        }
    }

    public CompletionStage<List<AiAgentTask>> getAiAgentTasks(AiAgentVersion aiAgentVersion,
                                                              DataFetchingEnvironment dfe) {
        // get Agent Version Entity
        String agentVersionGuid = aiAgentVersion.getId();
        com.boomi.aiagentregistry.entity.AiAgentVersion aiAgentVersionFromDB =
                findAiAgentVersionByGuid(agentVersionGuid);
        if (aiAgentVersionFromDB == null) {
            return CompletableFuture.completedFuture(Collections.emptyList());
        }
        Integer agentVersionEntityUid = aiAgentVersionFromDB.getUid();
        // Get all task associations for this agent version
        List<AiAgentTaskAssociation> taskAssociations =
                _aiAgentTaskAssociationRepository.findByRelatedEntityUidAndRelatedEntityType(agentVersionEntityUid,
                        VERSION);

        if (taskAssociations == null) {
            return CompletableFuture.completedFuture(Collections.emptyList());
        }
        // Get task and convert task entity to graphql dto
        List<AiAgentTask> agentTaskDtoList = taskAssociations.stream().map(AiAgentTaskAssociation::getTask)
                .map(AiAgentTaskMapper.AI_AGENT_TASK_MAPPER::toAIAgentTaskDto).toList();

        return CompletableFuture.completedFuture(agentTaskDtoList);
    }

    @Override
    public CompletionStage<List<AiAgentTag>> getAiAgentTags(AiAgentVersion aiAgentVersion,
                                                            DataFetchingEnvironment dfe) {
        try {
            return CompletableFuture.completedFuture(Optional.ofNullable(aiAgentVersion)
                    .map(AiAgentVersion::getId)
                    .flatMap(_aiAgentVersionRepository::findByGuid)
                    .map(version -> getTagsForVersion(version.getUid()))
                    .orElse(Collections.emptyList()));
        } catch (Exception e) {
            String versionId = aiAgentVersion != null ? aiAgentVersion.getId() : null;
            log.warn(versionId, e, AI_AGENT_VERSION_TAG_ERROR);
            ErrorUtil.addError(dfe, AI_AGENT_VERSION_TAG_ERROR, versionId);
            return CompletableFuture.completedFuture(Collections.emptyList());
        }
    }

    @Override
    public CompletionStage<List<AiAgentLlm>> getAiAgentLlms(AiAgentVersion aiAgentVersion,
                                                            DataFetchingEnvironment dfe) {
        try {
            return CompletableFuture.completedFuture(Optional.ofNullable(aiAgentVersion)
                    .map(AiAgentVersion::getId)
                    .flatMap(_aiAgentVersionRepository::findByGuid)
                    .map(version -> _aiAgentLlmAssociationRepository
                            .findByRelatedEntityUidAndRelatedEntityType(version.getUid(), VERSION)
                            .stream()
                            .map(AiAgentLlmAssociation::getLlm)
                            .map(AiAgentLlmMapper.LLM_MAPPER::toAIAgentLlmDto)
                            .toList())
                    .orElse(Collections.emptyList()));
        } catch (Exception e) {
            String versionId = aiAgentVersion != null ? aiAgentVersion.getId() : null;
            log.warn(versionId, e, AI_AGENT_VERSION_TAG_ERROR);
            ErrorUtil.addError(dfe, AI_AGENT_VERSION_TAG_ERROR, versionId);
            return CompletableFuture.completedFuture(Collections.emptyList());
        }
    }

    @Override
    public CompletionStage<List<String>> getVersionInstructions(AiAgentVersion version, DataFetchingEnvironment dfe) {
        String versionId = version.getId();
        com.boomi.aiagentregistry.entity.AiAgentVersion versionFromDB = findAiAgentVersionByGuid(versionId);
        if (versionFromDB == null) {
            log.warn(ERROR_AGENT_VERSION_NOT_FOUND, versionId);
            ErrorUtil.addError(dfe, AI_AGENT_VERSION_NOT_FOUND);
            return CompletableFuture.completedFuture(null);
        }

        return getInstructionsContent(versionFromDB.getUid());
    }

    @Override
    public CompletionStage<AiRegistryEntitySyncData> syncData(AiAgentVersion aiAgentVersion,
                                                              DataFetchingEnvironment dfe) {
        String versionId = aiAgentVersion.getId();
        com.boomi.aiagentregistry.entity.AiAgentVersion versionFromDB = findAiAgentVersionByGuid(versionId);
        if (versionFromDB == null) {
            log.warn(ERROR_AGENT_VERSION_NOT_FOUND, versionId);
            ErrorUtil.addError(dfe, AI_AGENT_VERSION_NOT_FOUND);
            return CompletableFuture.completedFuture(null);
        } else {
            AgentEntitySyncLatest aiRegistryEntitySyncData =
                    _aiAgentLatestSyncRepository.findBySyncedEntityUidAndSyncedEntityType(versionFromDB.getUid(),
                            VERSION).orElse(null);

            if (aiRegistryEntitySyncData == null) {
                log.info(INFO_NO_SYNC_DATA_FOR_VERSION, versionFromDB.getGuid());
                return null;
            } else {
                final AiRegistryEntitySyncData latestSyncData =
                        AiAgentSyncLatestMapper.AI_AGENT_ALIAS_SYNC_LATEST_MAPPER.toAiRegistrySyncData(
                                aiRegistryEntitySyncData);
                return CompletableFuture.completedFuture(latestSyncData);
            }
        }
    }

    @Transactional
    @Override
    public CompletionStage<AiAgentVersion> aiAgentVersionEnable(AiAgentVersionEnableInput input,
            DataFetchingEnvironment dfe) {
        try {
            String idpAccountId = _userUtil.getAccountId(dfe);
            final com.boomi.aiagentregistry.entity.AiAgentVersion dbAiAgentVersion =
                    _aiAgentVersionRepository.findByGuidAndAgentIdpAccountId(input.getAgentVersionId(), idpAccountId)
                            .orElse(null);

            if (dbAiAgentVersion == null) {
                log.warn(ERROR_AGENT_VERSION_NOT_FOUND, input.getAgentVersionId());
                ErrorUtil.addError(dfe, AI_AGENT_VERSION_NOT_FOUND);
                return CompletableFuture.completedFuture(null);
            }

            AiAgent dbAiAgent = dbAiAgentVersion.getAgent();
            AiAgentProviderAccount dbProviderAccount = dbAiAgent.getAiAgentProviderAccount();
            AiAgentProviderType providerType = dbProviderAccount.getProviderType();

            if (input.isEnable()) {
                return _aiAgentActionServiceFactory.getStrategy(providerType).enableAiAgentVersion(
                        dbProviderAccount, dbAiAgentVersion.getExternalId()).map(response -> {
                    // Handle logic after getting external API call response
                    return handleAiAgentVersionActionResponse(input, dbAiAgentVersion, response, dfe);
                }).doOnError(error -> {
                    log.warn(ERROR_ENABLING_AGENT_VERSION, input.getAgentVersionId(), error);
                    ErrorUtil.addError(dfe, FAILED_TO_ENABLE_AGENT_VERSION, input.getAgentVersionId());
                }).toFuture();
            } else {
                return _aiAgentActionServiceFactory.getStrategy(providerType).disableAiAgentVersion(
                        dbProviderAccount, dbAiAgentVersion.getExternalId()).map(response -> {
                    // Handle logic after getting external API call response
                    return handleAiAgentVersionActionResponse(input, dbAiAgentVersion, response, dfe);
                }).doOnError(error -> {
                    log.warn(ERROR_DISABLING_AGENT_VERSION, input.getAgentVersionId(), error);
                    ErrorUtil.addError(dfe, FAILED_TO_DISABLE_AGENT_VERSION, input.getAgentVersionId());
                }).toFuture();
            }
        } catch (Exception e) {
            log.error(input.isEnable() ? ERROR_ENABLING_AGENT_VERSION : ERROR_DISABLING_AGENT_VERSION,
                    input.getAgentVersionId(), e);
            ErrorUtil.addError(dfe, input.isEnable() ? FAILED_TO_ENABLE_AGENT_VERSION : FAILED_TO_DISABLE_AGENT_VERSION,
                    input.getAgentVersionId());
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<AiAgentTrustLevelQueryResponse> getAiAgentTrustLevelMetrics(
            AiAgentTrustLevelQueryInput input, DataFetchingEnvironment dfe) {
        String providerAccountId = input.getProviderAccountId();
        try {
            if (providerAccountId != null && (
                    !ValidationUtil.validateProviderAccountInputId(providerAccountId, dfe) ||
                            ValidationUtil.getProviderAccount(providerAccountId, _aiAgentProviderAccountRepository,
                                    dfe) == null)) {
                return CompletableFuture.completedFuture(null);
            }

            Set<com.boomi.aiagentregistry.entity.AiAgentVersion> versions = _aiAgentVersionRepository
                    .getAiAgentVersions(_userUtil.getAccountId(dfe), providerAccountId)
                    .orElse(Collections.emptySet());

            Map<AiAgentRegistryTrustLevel, Integer> trustLevelCounts = new EnumMap<>(AiAgentRegistryTrustLevel.class);
            Arrays.stream(AiAgentRegistryTrustLevel.values())
                    .forEach(trustLevel -> trustLevelCounts.put(trustLevel, 0));

            // The following null check for trust level should be removed once default trust level value is set during
            // aiAgent creation.
            versions.forEach(version -> trustLevelCounts.merge(
                    version.getTrustLevel() != null ? version.getTrustLevel() : AiAgentRegistryTrustLevel.UNENDORSED,
                    1, Integer::sum));

            return CompletableFuture.completedFuture(new AiAgentTrustLevelQueryResponse(
                    versions.size(),
                    trustLevelCounts.get(AiAgentRegistryTrustLevel.ENDORSED),
                    trustLevelCounts.get(AiAgentRegistryTrustLevel.UNENDORSED),
                    trustLevelCounts.get(AiAgentRegistryTrustLevel.DEPRECATED)
            ));
        } catch (Exception dae) {
            ErrorUtil.addError(dfe, SYSTEM_ERROR);
            log.error("Error while fetching trust level details", dae);
        }
        return CompletableFuture.completedFuture(null);
    }

    private AiAgentVersion handleAiAgentVersionActionResponse(AiAgentVersionEnableInput input,
            com.boomi.aiagentregistry.entity.AiAgentVersion dbAiAgentVersion, AiAgentActionResponse response,
            DataFetchingEnvironment dfe) {
        AiAgentVersion aiAgentVersion = AI_AGENT_VERSION_MAPPER.toAiAgentVersion(dbAiAgentVersion);
        try {
            if (response.isSuccess()) {
                dbAiAgentVersion.setAgentStatus(response.getAiAgentStatus());
                _auditUtil.setUpdatedAudit(dbAiAgentVersion, dfe);
                _aiAgentVersionRepository.save(dbAiAgentVersion);
                aiAgentVersion = AI_AGENT_VERSION_MAPPER.toAiAgentVersion(dbAiAgentVersion);

                // Create audit log entry for agent version enable/disable action
                log.debug("Saving Audit Log for enabling or disabling an agent version");
                createAuditLogForAiAgentVersionAction(input, dbAiAgentVersion.getExternalId(),
                        dbAiAgentVersion.getGuid(), dfe);
            } else {
                log.warn("Agent version action's external API call returns error message: {}", response.getError());
                ErrorUtil.addError(dfe,
                        input.isEnable() ? FAILED_TO_ENABLE_AGENT_VERSION : FAILED_TO_DISABLE_AGENT_VERSION,
                        dbAiAgentVersion.getGuid());
            }
        } catch (Exception e) {
            log.error(input.isEnable() ? ERROR_ENABLING_AGENT_VERSION : ERROR_DISABLING_AGENT_VERSION,
                    dbAiAgentVersion.getGuid(), e);
            ErrorUtil.addError(dfe, input.isEnable() ? FAILED_TO_ENABLE_AGENT_VERSION : FAILED_TO_DISABLE_AGENT_VERSION,
                    dbAiAgentVersion.getGuid());
        }
        return aiAgentVersion;
    }

    private void createAuditLogForAiAgentVersionAction(AiAgentVersionEnableInput input, String externalId,
            String aiAgentVersionGuid, DataFetchingEnvironment dfe) throws JsonProcessingException {
        Object changeSet = AI_AGENT_VERSION_MAPPER.toAuditLogChangeSet(input, externalId,
                AI_AGENT_VERSION_AUDIT_LOG_CHANGE_SET_VERSION);
        AuditLogEntry auditLogEntry = AuditLogEntry.builder().entityName(VERSION).entityGuid(aiAgentVersionGuid).action(
                input.isEnable() ? ActionEnum.ENABLE : ActionEnum.DISABLE).userId(_userUtil.getUserName(dfe)).accountId(
                _userUtil.getAccountId(dfe)).entityVersion(AI_AGENT_VERSION_AUDIT_LOG_CHANGE_SET_VERSION).changeSet(
                _objectMapper.writeValueAsString(changeSet)).requestId(MDC.get(CORRELATION_PROPERTY_NAME)).actionResult(
                ActionResultEnum.SUCCESS).build();
        _auditLogService.logAction(auditLogEntry);
    }

    private CompletionStage<List<String>> getInstructionsContent(Integer versionUid) {
        List<AiAgentLargeTextContent> textContent = _largeTxtRepo.findListByRelatedEntityUidAndRelatedEntityType(
                versionUid, VERSION.name());

        return CompletableFuture.completedFuture(
                (textContent != null) ? textContent.stream().map(AiAgentLargeTextContent::getContent).toList()
                        : Collections.emptyList()
        );
    }


    private List<AiAgentTag> getTagsForVersion(Integer versionUid) {
        List<AiAgentTagAssociation> tagAssociations =
                _aiAgentTagAssociationRepository.findByRelatedEntityTypeAndRelatedEntityId(VERSION,
                        versionUid);
        if (tagAssociations.isEmpty()) {
            return Collections.emptyList();
        }
        List<Integer> tagUids = tagAssociations.stream()
                .map(AiAgentTagAssociation::getTagUid)
                .toList();
        return Optional.ofNullable(_aiAgentTagRepository.findAiAgentTagsByUidIn(tagUids))
                .map(AI_AGENT_TAG_MAPPER::toAIAgentTagList)
                .orElse(Collections.emptyList());
    }

    private com.boomi.aiagentregistry.entity.AiAgentVersion findAiAgentVersionByGuid(String guid) {
        return _aiAgentVersionRepository.findByGuid(guid).orElse(null);
    }
}
