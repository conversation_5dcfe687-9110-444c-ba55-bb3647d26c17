// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.entity.ViewRefreshTracker;
import com.boomi.aiagentregistry.mapper.AiAgentRefreshMapper;
import com.boomi.aiagentregistry.repo.ViewRefreshTrackerRepository;
import com.boomi.graphql.server.schema.types.AiAgentListingRefreshTracker;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.servlet.ErrorUtil;

import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public class AiAgentRefreshTrackerServiceImpl implements IAiAgentRefreshTrackerService{

    private final ViewRefreshTrackerRepository _viewRefreshTrackerRepository;

    private static final String VIEW_NAME = "v_agent_refresh";

    public AiAgentRefreshTrackerServiceImpl(ViewRefreshTrackerRepository viewRefreshTrackerRepository){
        _viewRefreshTrackerRepository = viewRefreshTrackerRepository;
    }

    @Override
    public CompletableFuture<AiAgentListingRefreshTracker> getRefreshTracker(DataFetchingEnvironment dfe) {
        CompletableFuture<AiAgentListingRefreshTracker> completion = new CompletableFuture<>();

        AiAgentListingRefreshTracker aiAgentListingRefreshTracker = new AiAgentListingRefreshTracker();

        try{
            ViewRefreshTracker viewRefreshTracker = _viewRefreshTrackerRepository.findByViewName(VIEW_NAME);

            if(viewRefreshTracker != null){
                aiAgentListingRefreshTracker = AiAgentRefreshMapper
                        .AI_AGENT_REFRESH_MAPPER.toAiAgentListingRefreshTrackerType(viewRefreshTracker);
            }
            completion.complete(aiAgentListingRefreshTracker);
        }catch (Exception e){
            log.error(AiAgentRegistryErrorCode.FETCH_AI_AGENT_REFRESH_TRACKER_ERROR.getDetail(), e);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.FETCH_AI_AGENT_REFRESH_TRACKER_ERROR);
            completion.complete(null);
        }

        return completion;
    }
}
