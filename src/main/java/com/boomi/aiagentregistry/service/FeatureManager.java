// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class FeatureManager {

    @Value("${boomi.services.aiagentregistry.use.bedrock.assume.role:true}")
    private boolean _doProvisioningWithAssumeRole;

    @Value("${boomi.services.aiagentregistry.allow.one.external.account.with.one.idp.and.one.auth.type:false}")
    private boolean _allowOneExternalAccountWithOneIdpAccountOneAuthType;
    @Value("${boomi.services.aiagentregistry.auto.create.oam.link:true}")
    private boolean _autoCreateOamLink;

    @PostConstruct
    public void init() {
        log.info("use.bedrock.assume.role: {}", _doProvisioningWithAssumeRole);
        log.info("allow.one.aws.account.with.one.idp.account: {}",
                _allowOneExternalAccountWithOneIdpAccountOneAuthType);
        log.info("_autoCreateOamLink: {}", _autoCreateOamLink);
    }

    public boolean isDoProvisioningWithAssumeRole() {
        return _doProvisioningWithAssumeRole;
    }

    public boolean isOneExternalAccountWithOneIdpAccountOneAuthType() {
        return _allowOneExternalAccountWithOneIdpAccountOneAuthType;
    }
    public boolean isAutoCreateOamLink() {
        return _autoCreateOamLink;
    }
}
