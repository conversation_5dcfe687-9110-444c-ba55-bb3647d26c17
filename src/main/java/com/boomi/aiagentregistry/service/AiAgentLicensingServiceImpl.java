// Copyright (c) 2025 Boomi, LP.
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.entity.AiAgentRegistryLicense;
import com.boomi.aiagentregistry.entity.AiAgentRegistryLicenseAssociation;
import com.boomi.aiagentregistry.mapper.AiAgentLicenseAssociationMapper;
import com.boomi.aiagentregistry.mapper.AiAgentLicenseMapper;
import com.boomi.aiagentregistry.repo.AiAgentRegistryLicenseAssociationRepository;
import com.boomi.aiagentregistry.repo.AiRegistryLicenseRepository;
import com.boomi.aiagentregistry.util.UserUtil;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiAgentRegistryLicenseIdpAccountAssociation;
import com.boomi.graphql.server.schema.types.AiRegistryLicenseLimitInput;
import com.boomi.graphql.server.schema.types.IdpAccountTierLimitInput;
import com.boomi.graphql.server.servlet.ErrorUtil;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

@Service
@Slf4j
public class AiAgentLicensingServiceImpl implements IAiAgentLicensingService{

    private static final String FREE_TIER_DEFAULT_VALUE = "0";

    private final AiRegistryLicenseRepository _aiRegistryLicenseRepository;
    private final AiAgentRegistryLicenseAssociationRepository _aiAgentRegistryLicenseAssociationRepository;
    private final UserUtil _userUtil;

    public AiAgentLicensingServiceImpl(AiRegistryLicenseRepository aiRegistryLicenseRepository,
            AiAgentRegistryLicenseAssociationRepository aiAgentRegistryLicenseAssociationRepository,
            UserUtil userUtil) {
        _aiRegistryLicenseRepository = aiRegistryLicenseRepository;
        _aiAgentRegistryLicenseAssociationRepository = aiAgentRegistryLicenseAssociationRepository;
        _userUtil = userUtil;
    }

    @Override
    @Transactional
    public CompletionStage<com.boomi.graphql.server.schema.types.AiAgentRegistryLicense> updateAiRegistryLicenseLimit(
            AiRegistryLicenseLimitInput input, DataFetchingEnvironment dfe) {

        log.info("AiAgentLicensingServiceImpl -> updateAiRegistryLicenseLimit started");

        CompletableFuture<com.boomi.graphql.server.schema.types.AiAgentRegistryLicense> completion
                = new CompletableFuture<>();

        try {
            AiAgentRegistryLicense
                    existingAiAgentRegistryLicense = _aiRegistryLicenseRepository.findByTierId(input.getTierId());

            if (existingAiAgentRegistryLicense == null) {
                log.error("License not found for Tier Id: {}", input.getTierId());
                ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.LICENSE_NOT_FOUND_ERROR);
                completion.complete(null);
                return completion;
            }

            if (input.getTierDefaultSoftLimit() != null) {
                existingAiAgentRegistryLicense.setTierDefaultSoftLimit(input.getTierDefaultSoftLimit());
            }

            AiAgentRegistryLicense updatedLicense = _aiRegistryLicenseRepository.save(existingAiAgentRegistryLicense);

            com.boomi.graphql.server.schema.types.AiAgentRegistryLicense mappedLicense =
                    AiAgentLicenseMapper.AI_AGENT_LICENSE_MAPPER.toAiAgentRegistryLicenseType(updatedLicense);

            log.info("Successfully updated license for Tier Id: {}", input.getTierId());
            completion.complete(mappedLicense);

        } catch (Exception e) {
            log.error(AiAgentRegistryErrorCode.UPDATE_LICENSE_LIMIT_ERROR.getDetail(), e);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.UPDATE_LICENSE_LIMIT_ERROR);
            completion.complete(null);
        }

        return completion;
    }


    @Override
    @Transactional
    public CompletionStage<AiAgentRegistryLicenseIdpAccountAssociation> updateIdpAccountTierLimit(
            IdpAccountTierLimitInput input, DataFetchingEnvironment dfe) {
        CompletableFuture<AiAgentRegistryLicenseIdpAccountAssociation> completion = new CompletableFuture<>();
        try {
            log.debug("Updating tier limit for IDP account: {}, tier: {}", input.getIdpAccountId(), input.getTierId());

            // Find existing association
            AiAgentRegistryLicenseAssociation existingAssociation =
                    _aiAgentRegistryLicenseAssociationRepository.findByIdpAccountId(input.getIdpAccountId());

            //Final Association
            AiAgentRegistryLicenseAssociation newOrUpdatedaiAgentRegistryLicenseAssociation;

            if (existingAssociation == null) {
                newOrUpdatedaiAgentRegistryLicenseAssociation = createNewAssociation(input);
            }else {
                // Update existing association
                newOrUpdatedaiAgentRegistryLicenseAssociation =
                        updateExistingAssociation(existingAssociation, input, true);
            }

            log.debug("Successfully updated tier limit for IDP account: {}", input.getIdpAccountId());


            AiAgentRegistryLicenseIdpAccountAssociation mappedAssociation =
                        AiAgentLicenseAssociationMapper.AI_AGENT_LICENSE_ASSOCIATION_MAPPER
                                .toAiAgentRegistryLicenseIdpAccountAssociationType
                                        (newOrUpdatedaiAgentRegistryLicenseAssociation,
                                                newOrUpdatedaiAgentRegistryLicenseAssociation
                                                        .getAiAgentRegistryLicense());

            completion.complete(mappedAssociation);

        } catch (Exception e) {
            log.error(AiAgentRegistryErrorCode.UPDATE_IDP_ACCOUNT_TIER_LIMIT_ERROR.getDetail(), e);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.UPDATE_IDP_ACCOUNT_TIER_LIMIT_ERROR);
            completion.complete(null);
        }

        return completion;
    }

    @Override
    public AiAgentRegistryLicenseIdpAccountAssociation getLicenseLimit(String idpAccountId,
            DataFetchingEnvironment dfe) {
        String tierValue = _userUtil.checkTier(dfe);
        /*if tierValue is null then it means user is of free tier. No tier value added to him yet.
        So, setting tierValue as 0.*/
        if (Objects.isNull(tierValue)) {
            tierValue = FREE_TIER_DEFAULT_VALUE;
        }
        Integer tierId = Integer.parseInt(tierValue);

        // Find existing association
        AiAgentRegistryLicenseAssociation existingAssociation =
                _aiAgentRegistryLicenseAssociationRepository.findByIdpAccountId(idpAccountId);

        //Final Association
        AiAgentRegistryLicenseAssociation updatedAssociation;

        IdpAccountTierLimitInput input = new IdpAccountTierLimitInput();
        input.setTierId(tierId);
        input.setIdpAccountId(idpAccountId);

        if (existingAssociation == null) {
            updatedAssociation =  createNewAssociation(input);
        }else {
            // Update existing association
            updatedAssociation = updateExistingAssociation(existingAssociation, input, false);
        }

        log.debug("Successfully updated tier limit for IDP account: {}", input.getIdpAccountId());


        return AiAgentLicenseAssociationMapper.AI_AGENT_LICENSE_ASSOCIATION_MAPPER
                        .toAiAgentRegistryLicenseIdpAccountAssociationType
                                (updatedAssociation, updatedAssociation.getAiAgentRegistryLicense());

    }

    private AiAgentRegistryLicenseAssociation createNewAssociation(IdpAccountTierLimitInput input) {
        AiAgentRegistryLicenseAssociation newAssociation = new AiAgentRegistryLicenseAssociation();
        final AiAgentRegistryLicense aiAgentRegistryLicense =
                _aiRegistryLicenseRepository.findByTierId(input.getTierId());
        newAssociation.setIdpAccountId(input.getIdpAccountId());
        newAssociation.setAiAgentRegistryLicense(aiAgentRegistryLicense);

        AiAgentRegistryLicenseAssociation savedAssociation =
                _aiAgentRegistryLicenseAssociationRepository.save(newAssociation);
        log.info("Created new association for IDP account: {}", input.getIdpAccountId());
        return savedAssociation;
    }

    private AiAgentRegistryLicenseAssociation updateExistingAssociation(
            AiAgentRegistryLicenseAssociation existingAiAgentRegistryLicenseAssociation,
            IdpAccountTierLimitInput input, boolean isAdminUpdate) {

        // First, handle the tierId null case
        if (input.getTierId() == null) {
            // If tierId is null, only process limit updates if they exist
            if (hasOverriddenLimits(existingAiAgentRegistryLicenseAssociation, input)) {
                // Update with new override values
                updateLimits(existingAiAgentRegistryLicenseAssociation, input);
                AiAgentRegistryLicenseAssociation savedAssociation =
                        _aiAgentRegistryLicenseAssociationRepository.save(existingAiAgentRegistryLicenseAssociation);
                log.info("Updated limits for IDP account: {} without tier change",
                        existingAiAgentRegistryLicenseAssociation.getIdpAccountId());
                return savedAssociation;
            }
            // If no limits to update, return the existing existingAiAgentRegistryLicenseAssociation unchanged
            log.debug("No updates needed for IDP account: {}, tierId is null and no limit changes",
                    existingAiAgentRegistryLicenseAssociation.getIdpAccountId());
            return existingAiAgentRegistryLicenseAssociation;
        }

        // If tierId is not null, proceed with the original logic
        Integer inputTierId = input.getTierId();
        AiAgentRegistryLicense aiAgentRegistryLicense = _aiRegistryLicenseRepository.findByTierId(inputTierId);

        if (aiAgentRegistryLicense == null) {
            throw new IllegalArgumentException(
                    String.format("No aiAgentRegistryLicense found for tier ID: %d", inputTierId));
        }

        Integer uid = aiAgentRegistryLicense.getUid();
        boolean isTierChanged = !uid.equals(
                existingAiAgentRegistryLicenseAssociation.getAiAgentRegistryLicense().getUid());

        if (isTierChanged) {
            // Update existingAiAgentRegistryLicenseAssociation with new aiAgentRegistryLicense and reset limits
            existingAiAgentRegistryLicenseAssociation.setAiAgentRegistryLicense(aiAgentRegistryLicense);

            // If tier changed, nullify  any existing override limits
            existingAiAgentRegistryLicenseAssociation.setTierSoftLimit(null);
            existingAiAgentRegistryLicenseAssociation.setSystemLimit(null);
            AiAgentRegistryLicenseAssociation savedAssociation =
                    _aiAgentRegistryLicenseAssociationRepository.save(existingAiAgentRegistryLicenseAssociation);
            log.info("Updated tier and reset limits for IDP account: {}",
                    existingAiAgentRegistryLicenseAssociation.getIdpAccountId());
            return savedAssociation;
        } else if (isAdminUpdate && hasOverriddenLimits(existingAiAgentRegistryLicenseAssociation, input)) {
            // Update with new override values
            updateLimits(existingAiAgentRegistryLicenseAssociation, input);
            AiAgentRegistryLicenseAssociation savedAssociation =
                    _aiAgentRegistryLicenseAssociationRepository.save(existingAiAgentRegistryLicenseAssociation);
            log.info("Updated limits for IDP account: {}", existingAiAgentRegistryLicenseAssociation.getIdpAccountId());
            return savedAssociation;
        }

        // If no changes needed, return the existing existingAiAgentRegistryLicenseAssociation
        log.debug("No updates needed for IDP account: {}", existingAiAgentRegistryLicenseAssociation.getIdpAccountId());
        return existingAiAgentRegistryLicenseAssociation;
    }

    private static boolean hasOverriddenLimits(AiAgentRegistryLicenseAssociation association,
            IdpAccountTierLimitInput input) {
        return !Objects.equals(association.getTierSoftLimit(), input.getSoftLimit());
    }

    private static void updateLimits(AiAgentRegistryLicenseAssociation association,
            IdpAccountTierLimitInput input) {
        if (input.getSoftLimit() != null) {
            association.setTierSoftLimit(input.getSoftLimit());
        }
    }

}
