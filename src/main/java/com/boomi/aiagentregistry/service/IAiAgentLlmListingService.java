// Copyright (c) 2025 Boomi, Inc.
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.graphql.server.schema.types.AiAgentLlmsQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentLlmsQueryResponse;

import java.util.concurrent.CompletionStage;

public interface IAiAgentLlmListingService {
    CompletionStage<AiAgentLlmsQueryResponse> getAiAgentLlms(AiAgentLlmsQueryInput input, DataFetchingEnvironment dfe);
}
