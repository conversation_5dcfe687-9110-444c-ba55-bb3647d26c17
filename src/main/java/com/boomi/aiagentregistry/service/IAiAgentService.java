// Copyright (c) 2024 Boom<PERSON>, LP.
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import jakarta.validation.constraints.NotNull;
import com.boomi.graphql.server.schema.types.AiAgent;
import com.boomi.graphql.server.schema.types.AiAgentCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentUpdateInput;
import com.boomi.graphql.server.schema.types.AiAgentsQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentsQueryResponse;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

public interface IAiAgentService {
    CompletableFuture<AiAgentsQueryResponse> getAiAgents(
            AiAgentsQueryInput input, DataFetchingEnvironment dfe);

    CompletionStage<AiAgent> createAIAgent(
            AiAgentCreateInput input,
            DataFetchingEnvironment dfe);

    CompletionStage<AiAgent> updateAIAgent(
            AiAgentUpdateInput input,
            DataFetchingEnvironment dfe);

    CompletionStage<AiAgent> getAiAgentByVersionId(@NotNull String aiAgentVersionId, DataFetchingEnvironment dfe);

    CompletionStage<AiAgent> getAiAgentByAliasId(@NotNull String aiAgentAliasId, DataFetchingEnvironment dfe);

    CompletionStage<Boolean> deleteAIAgent(String id, DataFetchingEnvironment dfe);
}
