// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service;

import reactor.core.publisher.Flux;
import software.amazon.awssdk.services.secretsmanager.model.SecretsManagerResponse;
import software.amazon.awssdk.services.secretsmanager.model.UpdateSecretResponse;

import org.springframework.stereotype.Service;


@Service
public interface SecretsManagerService {

    SecretsManagerResponse createSecret(String secretName, String secretValue);

    SecretsManagerResponse deleteSecret(String secretName);

    Flux<String> getSecret(String secretName);

    UpdateSecretResponse updateSecret(String secretName, String secretValue);

    int isProviderSecretExists(String secretName);
}
