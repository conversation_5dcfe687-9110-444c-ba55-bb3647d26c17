// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.provider.externallink;

import lombok.RequiredArgsConstructor;
import com.boomi.aiagentregistry.config.AppPatternConfig;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.AGENT_ALIAS_ID_PARAM;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.AGENT_ID_PARAM;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.AGENT_VERSION_PARAM;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.AGENT_VERSION_STATUS_DRAFT;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.REGION_PARAM;

@Component
@RequiredArgsConstructor
public class BedrockExternalLinkProviderService implements ExternalLinkProviderService {

    private final AppPatternConfig _appPatternConfig;

    @Override
    public AiAgentProviderType getAgentProviderType() {
        return AiAgentProviderType.AWS_BEDROCK;
    }

    @Override
    public String getAgentExternalLink(ExternalLinkParams externalLinkParams) {
        String agentExternalLinkPattern = _appPatternConfig.getExternalLinkPatternOrEmpty(
                AiRegistryEntityType.AGENT,
                AiAgentProviderType.AWS_BEDROCK);
        return agentExternalLinkPattern
            .replace(REGION_PARAM, externalLinkParams.getProviderRegion())
            .replace(AGENT_ID_PARAM, externalLinkParams.getAgentExternalId());
    }

    @Override
    public String getAgentVersionExternalLink(ExternalLinkParams externalLinkParams) {
        String agentVersion = externalLinkParams.getAgentVersion();
        if (StringUtils.equals(agentVersion, AGENT_VERSION_STATUS_DRAFT)) {
            return getAgentExternalLink(externalLinkParams);
        }

        String agentExternalLinkPattern = _appPatternConfig.getExternalLinkPatternOrEmpty(
                        AiRegistryEntityType.VERSION,
                        AiAgentProviderType.AWS_BEDROCK);
        return agentExternalLinkPattern
            .replace(REGION_PARAM, externalLinkParams.getProviderRegion())
            .replace(AGENT_ID_PARAM, externalLinkParams.getAgentExternalId())
            .replace(AGENT_VERSION_PARAM, agentVersion);
    }

    @Override
    public String getAgentAliasExternalLink(ExternalLinkParams externalLinkParams) {
        String agentExternalLinkPattern = _appPatternConfig.getExternalLinkPatternOrEmpty(
                                AiRegistryEntityType.ALIAS,
                                AiAgentProviderType.AWS_BEDROCK);
        return agentExternalLinkPattern
            .replace(REGION_PARAM, externalLinkParams.getProviderRegion())
            .replace(AGENT_ID_PARAM, externalLinkParams.getAgentExternalId())
            .replace(AGENT_ALIAS_ID_PARAM, externalLinkParams.getAgentAliasExternalId());
    }
}
