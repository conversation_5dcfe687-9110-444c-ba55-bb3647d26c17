// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.provider.externallink;

import com.boomi.aiagentregistry.constant.ErrorMessages;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class ExternalLinkProviderServiceFactory {

    private final Map<AiAgentProviderType, ExternalLinkProviderService> _strategies;

    public ExternalLinkProviderServiceFactory(List<ExternalLinkProviderService> strategyList) {
        _strategies = strategyList.stream().collect(
                Collectors.toMap(ExternalLinkProviderService::getAgentProviderType, Function.identity()));
    }

    public ExternalLinkProviderService getStrategy(AiAgentProviderType aiAgentProviderType) {
        ExternalLinkProviderService strategy = _strategies.get(aiAgentProviderType);
        if (strategy == null) {
            throw new IllegalArgumentException(ErrorMessages.
                    format(ErrorMessages.UNSUPPORTED_PROVIDER, aiAgentProviderType));
        }
        return strategy;
    }
}
