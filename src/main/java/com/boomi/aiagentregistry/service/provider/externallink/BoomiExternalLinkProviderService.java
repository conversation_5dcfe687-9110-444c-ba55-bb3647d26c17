// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.provider.externallink;

import lombok.RequiredArgsConstructor;
import com.boomi.aiagentregistry.config.AppPatternConfig;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;

import org.springframework.stereotype.Component;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.AGENT_ID_PARAM;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.BOOMI_ACCOUNT_ID_PARAM;

@Component
@RequiredArgsConstructor
public class BoomiExternalLinkProviderService implements ExternalLinkProviderService {

    private final AppPatternConfig _appPatternConfig;

    @Override
    public AiAgentProviderType getAgentProviderType() {
        return AiAgentProviderType.BOOMI;
    }

    @Override
    public String getAgentExternalLink(ExternalLinkParams externalLinkParams) {
        String agentExternalLinkPattern = _appPatternConfig.getExternalLinkPatternOrEmpty(
                AiRegistryEntityType.AGENT,
                AiAgentProviderType.BOOMI);
        return agentExternalLinkPattern
            .replace(BOOMI_ACCOUNT_ID_PARAM, externalLinkParams.getPlatformAccountId())
            .replace(AGENT_ID_PARAM, externalLinkParams.getAgentExternalId());
    }

    @Override
    public String getAgentVersionExternalLink(ExternalLinkParams externalLinkParams) {
        String agentExternalLinkPattern = _appPatternConfig.getExternalLinkPatternOrEmpty(
                        AiRegistryEntityType.VERSION,
                        AiAgentProviderType.BOOMI);
        return agentExternalLinkPattern
            .replace(BOOMI_ACCOUNT_ID_PARAM, externalLinkParams.getPlatformAccountId())
            .replace(AGENT_ID_PARAM, externalLinkParams.getAgentExternalId());
    }
}
