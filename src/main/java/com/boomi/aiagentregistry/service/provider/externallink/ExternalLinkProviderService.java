// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.provider.externallink;

import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.NOT_IMPLEMENTED;

public interface ExternalLinkProviderService {

    AiAgentProviderType getAgentProviderType();

    default String getAgentExternalLink(ExternalLinkParams externalLinkParams) {
        throw new IllegalArgumentException(NOT_IMPLEMENTED);
    }

    default String getAgentVersionExternalLink(ExternalLinkParams externalLinkParams) {
        throw new IllegalArgumentException(NOT_IMPLEMENTED);
    }

    default String getAgentAliasExternalLink(ExternalLinkParams externalLinkParams) {
        throw new IllegalArgumentException(NOT_IMPLEMENTED);
    }
}
