// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.provider.externallink;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString()
@Accessors(prefix = { "_" })
@Builder
public class ExternalLinkParams {
    private String _agentExternalId;
    private String _providerRegion;
    // the agent's version e.g. DRAFT, 1, etc
    private String _agentVersion;
    private String _agentAliasExternalId;
    private String _platformAccountId;
}
