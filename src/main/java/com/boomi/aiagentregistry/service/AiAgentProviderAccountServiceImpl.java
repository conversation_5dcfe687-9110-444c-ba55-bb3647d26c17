// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.secretsmanager.model.SecretsManagerResponse;
import com.boomi.aiagentregistry.aop.IdpAccountFilter;
import com.boomi.aiagentregistry.entity.AgentEntitySyncLatest;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.repo.AiAgentLatestSyncRepository;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.repo.AiAgentRepository;
import com.boomi.aiagentregistry.repo.SyncUserAuditRepository;
import com.boomi.aiagentregistry.service.auth.AuthorizationParsingService;
import com.boomi.aiagentregistry.util.AiAgentProviderAccountCommonServiceUtil;
import com.boomi.aiagentregistry.util.UserUtil;
import com.boomi.aiagentregistry.util.ValidationUtil;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountsQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountsQueryResponse;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.graphql.server.schema.types.AiRegistryEntityAuditData;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.boomi.graphql.server.servlet.ErrorUtil;
import com.boomi.graphql.server.servlet.ResolverUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.SECRET_FOUND;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.SECRET_NOT_FOUND;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_ACCOUNT_NOT_FOUND;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_FAILED_TO_DELETE_PROVIDER_ACCOUNT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_OCCURRED_WHILE_CHECKING_SECRET;
import static com.boomi.aiagentregistry.mapper.AIAgentProviderAccountMapper.PROVIDER_MAPPER;
import static com.boomi.aiagentregistry.util.AiAgentProviderAccountCommonServiceUtilImpl.ERROR_WHILE_SYNCING_PROVIDER_ACCOUNT;
import static com.boomi.aiagentregistry.util.ValidationUtil.getProviderAccount;
import static com.boomi.graphql.server.schema.types.AiAgentProviderAccount.PROP_AUDIT_DATA;
import static com.boomi.graphql.server.schema.types.AiAgentProviderAccount.PROP_NUMBER_OF_AGENTS;
import static com.boomi.graphql.server.schema.types.AiAgentProviderAccount.PROP_SYNC_DATA;
import static com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus.DISABLED;
import static com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus.DISCONNECTED;
import static com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus.PROVISIONING_STEP1_COMPLETE;
import static com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus.PROVISIONING_STEP2_ERROR;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.AUTHENTICATED_ACCOUNT_ID_MISSING;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.FAILED_TO_DELETE_PROVIDER_ACCOUNT;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.FETCH_AI_AGENT_PROVIDER_ERROR;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_EXTERNAL_PROVIDER_ACCOUNT_ID;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_PROVIDER_ACCOUNT_ID;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_NOT_FOUND;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_SYNC_DISABLED;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.SYNC_PROVIDER_ACCOUNT_ERROR;
import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.COMPLETED_WITH_ERROR;
import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.FAILED;

/*
 * This is the default implementation which will be used for operation which is not dependent on Provider Type
 * mainly Query.
 *
 * */
@Service("aiAgentProviderAccountServiceImpl")
@Slf4j
@Primary
public class AiAgentProviderAccountServiceImpl implements AiAgentProviderAccountService {
    @Override
    public AiAgentProviderType getProviderType() {
        return null;
    }

    private final AiAgentProviderAccountRepository _accountRepo;
    private final AiAgentRepository _aiAgentRepository;
    private final AiAgentLatestSyncRepository _latestSyncRepo;
    private final SecretsManagerService _secretsManagerService;
    private final UserUtil _userUtil;
    private final AuthorizationParsingService _authParseService;
    private final AsyncDeleteService _asyncDeleteService;
    private final SyncUserAuditRepository _syncUserAuditRepository;
    private final AiAgentProviderAccountCommonServiceUtil _aiAgentProviderAccountCommonServiceUtil;

    public static final String ACCOUNT_SYNC_DISABLED_FOR_ID = "Account sync disabled for id: {}";
    private static final String RECEIVED_NULL_MASKED_CREDENTIALS = "Received null masked credentials";
    private static final String WONT_DELETE_SECRET =
              "Won't delete secret {} because it is linked to other account(s). AccountId is {}";
      private static final String DELETING_PROVIDER_ACCOUNT = "Deleting provider account {} and its"
              + " related associations";

    public AiAgentProviderAccountServiceImpl(AiAgentProviderAccountRepository aiAgentProviderAccountRepository,
            AiAgentRepository aiAgentRepository, AiAgentLatestSyncRepository aiAgentLatestSyncRepository,
            SecretsManagerService secretsManagerService, UserUtil userUtil,
            AuthorizationParsingService authorizationParsingService, AsyncDeleteService asyncDeleteService,
            SyncUserAuditRepository syncUserAuditRepository,
            AiAgentProviderAccountCommonServiceUtil aiAgentProviderAccountCommonServiceUtil) {

        _accountRepo = aiAgentProviderAccountRepository;
        _aiAgentRepository = aiAgentRepository;
        _latestSyncRepo = aiAgentLatestSyncRepository;
        _secretsManagerService = secretsManagerService;
        _userUtil = userUtil;
        _authParseService = authorizationParsingService;
        _asyncDeleteService = asyncDeleteService;
        _syncUserAuditRepository = syncUserAuditRepository;
        _aiAgentProviderAccountCommonServiceUtil = aiAgentProviderAccountCommonServiceUtil;
    }

    @Override
    @Transactional
    @IdpAccountFilter
    public CompletionStage<AiRegistryEntitySyncStatus> syncAiAgentProviderAccount(String id,
            DataFetchingEnvironment dfe) {
        try {
            //make sure that the account is not blank and exists in db
            if (StringUtils.isBlank(id)) {
                ErrorUtil.addError(dfe, INVALID_PROVIDER_ACCOUNT_ID, id);
                return CompletableFuture.completedFuture(FAILED);
            }

            try {
                UUID.fromString(id);
            } catch (Exception e) {
                log.error(INVALID_EXTERNAL_PROVIDER_ACCOUNT_ID.getDetail(), e);
                ErrorUtil.addError(dfe, INVALID_PROVIDER_ACCOUNT_ID, id);
                return CompletableFuture.completedFuture(FAILED);
            }

            final AiAgentProviderAccount aiAgentProviderAccount =
                    _aiAgentProviderAccountCommonServiceUtil.getExistingProviderAccountForIdAndStatus(id,
                            Set.of(DISABLED, DISCONNECTED, PROVISIONING_STEP1_COMPLETE, PROVISIONING_STEP2_ERROR));

            if (aiAgentProviderAccount == null) {
                log.warn(ACCOUNT_SYNC_DISABLED_FOR_ID, id);
                ErrorUtil.addError(dfe, PROVIDER_ACCOUNT_SYNC_DISABLED, id);
                return CompletableFuture.completedFuture(FAILED);
            }

            populateLatestProviderAccountSyncData(List.of(aiAgentProviderAccount));

            return _aiAgentProviderAccountCommonServiceUtil.syncProviderAccount(id, dfe);
        } catch (Exception e) {
            log.warn(ERROR_WHILE_SYNCING_PROVIDER_ACCOUNT, id, e);
            ErrorUtil.addError(dfe, SYNC_PROVIDER_ACCOUNT_ERROR, id);
            return CompletableFuture.completedFuture(FAILED);
        }

    }

    @Override
    @Transactional
    public CompletableFuture<AiAgentProviderAccountsQueryResponse>
    getAiAgentProviders(AiAgentProviderAccountsQueryInput input, DataFetchingEnvironment dfe) {
        CompletableFuture<AiAgentProviderAccountsQueryResponse> completion =
                new CompletableFuture<>();
        AiAgentProviderAccountsQueryResponse aiAgentProvidersQueryResponse = new AiAgentProviderAccountsQueryResponse();
        try {
            String idpAccountId = _userUtil.getAccountId(dfe);
            if (validateInput(input, idpAccountId, dfe)) {
                log.info("Fetching AI Agent providers for account: {}", idpAccountId);

                Pageable pageable = PageRequest.of(input.getPageIndex(), input.getPageSize());
                Page<AiAgentProviderAccount> aiAgentProviderAccountPage;
                List<AiAgentProviderAccount> dbProviderAccounts;
                Specification<AiAgentProviderAccount> where = _accountRepo.idpAccountId(idpAccountId);
                if (isProviderTypePresent(input) && isAccountNamePresent(input)) {
                    where = where.and(_accountRepo
                            .hasProviderTypeAndAccountName(input.getAiAgentProviderType(), input.getAccountName()));
                } else if (isProviderTypePresent(input)) {
                    where = where.and(_accountRepo.hasProviderType(input.getAiAgentProviderType()));
                } else if (isAccountNamePresent(input)) {
                    where = where.and(_accountRepo.hasAccountName(input.getAccountName()));
                }

                aiAgentProviderAccountPage = _accountRepo.findAll(where, pageable);
                dbProviderAccounts = aiAgentProviderAccountPage.getContent();
                Map<String, Integer> guidToAgentCount = Collections.emptyMap();
                if (CollectionUtils.isNotEmpty(dbProviderAccounts)) {
                    if (ResolverUtil.isChildFieldPresent(dfe, PROP_SYNC_DATA)) {
                        populateLatestProviderAccountSyncData(dbProviderAccounts);
                    }
                    if (ResolverUtil.isChildFieldPresent(dfe, PROP_AUDIT_DATA)) {
                        populateProviderAccountAuditData(dbProviderAccounts);
                    }
                    if (ResolverUtil.isChildFieldPresent(dfe, PROP_NUMBER_OF_AGENTS)) {
                        guidToAgentCount = getAgentCount(dbProviderAccounts);
                    }
                }

                // Map to graphql type
                List<com.boomi.graphql.server.schema.types.AiAgentProviderAccount> resultProviderAccounts =
                        PROVIDER_MAPPER.toAIAgentProviderList(dbProviderAccounts, guidToAgentCount);
                aiAgentProvidersQueryResponse.setAiAgentProviderAccounts(resultProviderAccounts);
                aiAgentProvidersQueryResponse.setNumberOfResults(aiAgentProviderAccountPage.getTotalElements());
                aiAgentProvidersQueryResponse.setCurrentPageSize(pageable.getPageSize());
                completion.complete(aiAgentProvidersQueryResponse);
            } else {
                completion.complete(null);
            }
        } catch (Exception e) {
            log.error("Error while retrieving AI Agent Provider Account Details.", e);
            ErrorUtil.addError(dfe, FETCH_AI_AGENT_PROVIDER_ERROR);
            completion.complete(null);
        }
        return completion;
    }

    @Override
    @IdpAccountFilter
    @Transactional
    public CompletionStage<com.boomi.graphql.server.schema.types.AiAgentProviderAccount>
    getAiAgentProvider(String id, DataFetchingEnvironment dfe) {
        CompletableFuture<com.boomi.graphql.server.schema.types.AiAgentProviderAccount> completion =
                new CompletableFuture<>();
        try {
            UUID.fromString(id);
        } catch (IllegalArgumentException e) {
            log.warn("The provided provider account id is not in valid UUID format: {}", id, e);
            ErrorUtil.addError(dfe, INVALID_PROVIDER_ACCOUNT_ID, id);
            completion.complete(null);
            return completion;
        }
        final AiAgentProviderAccount providerAccount = StringUtils.isBlank(id)
                ? null
                : _accountRepo.findByGuid(id).orElse(null);

        if (providerAccount == null) {
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_NOT_FOUND, id);
            log.warn(ERROR_ACCOUNT_NOT_FOUND, id);
            completion.complete(null);
            return completion;
        }

        if (ResolverUtil.isChildFieldPresent(dfe, PROP_SYNC_DATA)) {
            populateLatestProviderAccountSyncData(List.of(providerAccount));
        }

        com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentProviderAccount =
                PROVIDER_MAPPER.toAiAgentProviderAccount(providerAccount);

        updateSyncErrorMessage(aiAgentProviderAccount, providerAccount.getUid());

        _secretsManagerService.getSecret(providerAccount.getCredentialsKey())
                .single()
                .flatMap(credentials -> _authParseService
                        .getMaskedCredentials(credentials, providerAccount.getAuthSchema()))
                .doOnSuccess(maskedCredentials -> {
                    if (StringUtils.isNotBlank(maskedCredentials)) {
                        aiAgentProviderAccount.setCredential(maskedCredentials);
                    } else {
                        log.warn(RECEIVED_NULL_MASKED_CREDENTIALS);
                    }
                }).doOnError(error ->
                        log.warn("Error processing credentials for account {}: ", providerAccount.getGuid(), error))
                .doFinally(signalType -> {
                    if (ResolverUtil.isChildFieldPresent(dfe, PROP_NUMBER_OF_AGENTS)) {
                        Map<String, Integer> guidToAgentCount = getAgentCount(List.of(providerAccount));
                        aiAgentProviderAccount.setNumberOfAgents(guidToAgentCount.get(providerAccount.getGuid()));
                    }
                    if (ResolverUtil.isChildFieldPresent(dfe, PROP_AUDIT_DATA)) {
                        List<AiRegistryEntityAuditData> auditDataList =
                                populateProviderAccountAuditData(List.of(providerAccount));
                        aiAgentProviderAccount.setAuditData(auditDataList.get(0));
                    }

                    completion.complete(aiAgentProviderAccount);
                }).subscribe();
        return completion;
    }

    private void updateSyncErrorMessage(
            com.boomi.graphql.server.schema.types.AiAgentProviderAccount aiAgentProviderAccount,
            int providerAccountUid) {
        _syncUserAuditRepository.findLatestSyncAuditForEntity(providerAccountUid, AiRegistryEntityType.PROVIDER_ACCOUNT)
                                .ifPresent(auditProjection -> {
                                    if (isErrorStatus(auditProjection.getSyncStatus())) {
                                        aiAgentProviderAccount.setSyncErrorMessage(auditProjection.getContent());
                                    }
                });
    }

    private boolean isErrorStatus(AiRegistryEntitySyncStatus status) {
        return status == FAILED || status == COMPLETED_WITH_ERROR;
    }

    @IdpAccountFilter
    @Transactional
    @Override
    public CompletionStage<Boolean> deleteProviderAccount(String providerAccountId, DataFetchingEnvironment dfe,
            Map<AiAgentProviderType, AiAgentProviderAccountService> getBeanMappedByAiAgentProviderType) {
        log.info("Deleting provider account with ID: {}", providerAccountId);
        try {
            //make sure that the account is not blank and exists in db
            if (StringUtils.isBlank(providerAccountId)) {
                ErrorUtil.addError(dfe, INVALID_PROVIDER_ACCOUNT_ID, providerAccountId);
                return CompletableFuture.completedFuture(false);
            }

            try {
                UUID.fromString(providerAccountId);
            } catch (Exception e) {
                log.error(INVALID_EXTERNAL_PROVIDER_ACCOUNT_ID.getDetail(), e);
                ErrorUtil.addError(dfe, INVALID_PROVIDER_ACCOUNT_ID, providerAccountId);
                return CompletableFuture.completedFuture(false);
            }

            AiAgentProviderAccount providerAccount = getProviderAccount(providerAccountId, _accountRepo, dfe);

            if (providerAccount == null) {
                log.info("Provider Account not found for guid {}", providerAccountId);
                ErrorUtil.addError(dfe, PROVIDER_ACCOUNT_NOT_FOUND);
                return CompletableFuture.completedFuture(false);
            }
            if (providerAccount.getProviderType() != null) {
                return getBeanMappedByAiAgentProviderType
                    .get(providerAccount.getProviderType())
                    .deleteProviderAccount(providerAccount, dfe)
                    .thenCompose(success -> {
                        if (success && !ErrorUtil.hasErrors(dfe)) {
                            CompletableFuture<Boolean> result = doDeleteCommon(dfe, providerAccount);
                            return result != null ? result :
                                    CompletableFuture.completedFuture(!ErrorUtil.hasErrors(dfe));
                        }
                        return CompletableFuture.completedFuture(false);
                    });
            } else {
                CompletableFuture<Boolean> result = doDeleteCommon(dfe, providerAccount);
                return result != null ? result : CompletableFuture.completedFuture(!ErrorUtil.hasErrors(dfe));
            }

        } catch (IllegalArgumentException illegalArgumentException) {
            log.warn("Invalid registry account ID UUID format: {}", providerAccountId, illegalArgumentException);
            ErrorUtil.addError(dfe, INVALID_PROVIDER_ACCOUNT_ID);
        } catch (Exception e) {
            log.error("Error deleting provider account with ID: {}", providerAccountId, e);
            ErrorUtil.addError(dfe, FAILED_TO_DELETE_PROVIDER_ACCOUNT);
            return CompletableFuture.completedFuture(false);
        }
        return CompletableFuture.completedFuture(true);
    }

    private @Nullable CompletableFuture<Boolean> doDeleteCommon(DataFetchingEnvironment dfe,
            AiAgentProviderAccount providerAccount) {
        boolean deleteSecret = true;
        boolean isDoCheckIfExternalAccountLinkedToAnotherProviderAccount =
        _authParseService.isDoCheckIfExternalAccountLinkedToAnotherProviderAccount(providerAccount.getAuthSchema());
        if (isDoCheckIfExternalAccountLinkedToAnotherProviderAccount) {
            boolean isExternalAccountLinkedToAnotherProviderAccount =
            _accountRepo.existsByExternalProviderAccountIdAndUidNotAndAuthSchemaAndIsDeletedFalse(
            providerAccount.getExternalProviderAccountId(), providerAccount.getUid(),
                    AiProviderAuthSchema.AWS_ASSUME_ROLE);
            if (isExternalAccountLinkedToAnotherProviderAccount) {
                deleteSecret = false;
            }
        }

        if (deleteSecret) {
            return deleteProviderAccountWithSecret(dfe, providerAccount);
        } else {
            log.info(WONT_DELETE_SECRET, providerAccount.getCredentialsKey(), providerAccount.getGuid());
            deleteProviderAccount(dfe, providerAccount);
        }
        return null;
    }

    private @Nullable CompletableFuture<Boolean> deleteProviderAccountWithSecret(DataFetchingEnvironment dfe,
            AiAgentProviderAccount providerAccount) {
        // we need to track secret exist/ not exist / error on checking secret exist so used int instead of boolean
        int isProviderSecretExists =
                _secretsManagerService.isProviderSecretExists(providerAccount.getCredentialsKey());
        log.info("Secret exists for provider account: {}", isProviderSecretExists);

        // if any exception occurred on checking secret exists or not
        // we should not allow to delete respective provider account
        if (isProviderSecretExists != SECRET_NOT_FOUND) {

            SecretsManagerResponse secretsManagerResponse = null;
            // call delete secret method only if secret exists.
            if (isProviderSecretExists == SECRET_FOUND) {
                log.info("Deleting provider secret");
                secretsManagerResponse = _secretsManagerService.deleteSecret(providerAccount.getCredentialsKey());
            }

            // if any error occurred on deleting secret
            // we should not allow to delete respective provider account
            if (isProviderSecretExists == SECRET_FOUND
                    && secretsManagerResponse == null) {
                log.warn(ERROR_FAILED_TO_DELETE_PROVIDER_ACCOUNT, providerAccount.getGuid());
                ErrorUtil.addError(dfe, FAILED_TO_DELETE_PROVIDER_ACCOUNT, providerAccount.getGuid());
                return CompletableFuture.completedFuture(false);
            }
            deleteProviderAccount(dfe, providerAccount);
        } else {
            log.warn(ERROR_OCCURRED_WHILE_CHECKING_SECRET);
            ErrorUtil.addError(dfe, FAILED_TO_DELETE_PROVIDER_ACCOUNT, providerAccount.getGuid());
            return CompletableFuture.completedFuture(false);
        }
        return null;
    }

    private void deleteProviderAccount(DataFetchingEnvironment dfe, AiAgentProviderAccount providerAccount) {
        log.info(DELETING_PROVIDER_ACCOUNT, providerAccount.getGuid());
        deleteProviderAccountAndRelatedAssociations(providerAccount, dfe);
    }

    @Override
    public boolean syncInProgress(AiAgentProviderAccount existingAccount) {
        return _aiAgentProviderAccountCommonServiceUtil.syncInProgress(existingAccount);
    }

    private void populateLatestProviderAccountSyncData(List<AiAgentProviderAccount> dbProviderAccounts) {
        List<Integer> providerAccountUids = dbProviderAccounts.stream().map(AiAgentProviderAccount::getUid).toList();
        List<AgentEntitySyncLatest> agentEntitySyncLatestList = _latestSyncRepo
                .findBySyncedEntityUidInAndSyncedEntityType(providerAccountUids, AiRegistryEntityType.PROVIDER_ACCOUNT);
        Map<Integer, AgentEntitySyncLatest> providerAccountUidToSyncLatestMap = agentEntitySyncLatestList
                .stream()
                .collect(Collectors.toMap(AgentEntitySyncLatest::getSyncedEntityUid, Function.identity()));
        dbProviderAccounts.forEach(dbAccount -> {
            AgentEntitySyncLatest syncLatest = providerAccountUidToSyncLatestMap.get(dbAccount.getUid());
            if (syncLatest != null) {
                dbAccount.setLatestSync(syncLatest);
            }
        });
    }

    private static List<AiRegistryEntityAuditData> populateProviderAccountAuditData(
            List<AiAgentProviderAccount> providerAccounts) {
        return providerAccounts
                .stream()
                .map(PROVIDER_MAPPER::providerAccountAuditData)
                .toList();
    }

    private static boolean validateInput(AiAgentProviderAccountsQueryInput input, String idpAccountId,
                                         DataFetchingEnvironment dfe) {
        if (StringUtils.isEmpty(idpAccountId)) {
            ErrorUtil.addError(dfe, AUTHENTICATED_ACCOUNT_ID_MISSING);
            return false;
        }
        return ValidationUtil.validatePagination(input.getPageIndex(), input.getPageSize(), dfe,
                AiAgentRegistryErrorCode.PAGE_INDEX_INVALID, AiAgentRegistryErrorCode.PAGE_SIZE_INVALID);
    }

    private Map<String, Integer> getAgentCount(List<AiAgentProviderAccount> aiAgentProviderAccounts) {
        Map<String, Integer> providerGuidToUidMap = aiAgentProviderAccounts.stream()
                .collect(Collectors.toMap(AiAgentProviderAccount::getGuid, AiAgentProviderAccount::getUid));


        // Map of UID and agent count
        List<Integer> providerAccountUids = providerGuidToUidMap.values().stream().toList();
        Map<Integer, Integer> providerAccountUuidToAgentCount =
                _aiAgentRepository.countAgentsByProviderAccountUids(providerAccountUids).stream()
                        .collect(Collectors.toMap(row -> (Integer) row[0], row -> ((Long) row[1]).intValue()));

        // Map of GUID to Agent Count
        return providerGuidToUidMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> providerAccountUuidToAgentCount.getOrDefault(entry.getValue(), 0)));
    }

    private static boolean isAccountNamePresent(AiAgentProviderAccountsQueryInput providerAccountQueryInput) {
        return StringUtils.isNotEmpty(providerAccountQueryInput.getAccountName());
    }

    private static boolean isProviderTypePresent(AiAgentProviderAccountsQueryInput providerAccountQueryInput) {
        return providerAccountQueryInput.getAiAgentProviderType() != null;
    }

     @Async
    public void deleteProviderAccountAndRelatedAssociations(AiAgentProviderAccount providerAccount,
            DataFetchingEnvironment dfe) {
        log.info("Deleting provider account and its related associations with ID: {}", providerAccount.getGuid());
        try {
            _asyncDeleteService.executeAsyncDelete(providerAccount.getUid());
        } catch (Exception e) {
            log.error("Error deleting provider account with ID: {}", providerAccount.getGuid(), e);
            ErrorUtil.addError(dfe, FAILED_TO_DELETE_PROVIDER_ACCOUNT);
        }
    }
}

