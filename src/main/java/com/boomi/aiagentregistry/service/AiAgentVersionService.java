// Copyright (c) 2024 Boom<PERSON>, LP.

package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.graphql.server.schema.types.AiAgentAlias;
import com.boomi.graphql.server.schema.types.AiAgentGuardrail;
import com.boomi.graphql.server.schema.types.AiAgentLlm;
import com.boomi.graphql.server.schema.types.AiAgentTag;
import com.boomi.graphql.server.schema.types.AiAgentTask;
import com.boomi.graphql.server.schema.types.AiAgentTool;
import com.boomi.graphql.server.schema.types.AiAgentVersion;
import com.boomi.graphql.server.schema.types.AiAgentVersionEnableInput;
import com.boomi.graphql.server.schema.types.AiAgentVersionTrustLevelAddInput;
import com.boomi.graphql.server.schema.types.AiAgentTrustLevelQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentTrustLevelQueryResponse;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncData;

import java.util.List;
import java.util.concurrent.CompletionStage;

public interface AiAgentVersionService {

    CompletionStage<List<AiAgentGuardrail>> getAiAgentGuardrails(AiAgentVersion aiAgentVersion,
            DataFetchingEnvironment dfe);

    CompletionStage<List<AiAgentTool>> getAiAgentTools(AiAgentVersion aiAgentVersion,
            DataFetchingEnvironment dfe);

    CompletionStage<List<AiAgentAlias>> getAiAgentAliases(AiAgentVersion aiAgentVersion, DataFetchingEnvironment dfe);

    CompletionStage<List<AiAgentTask>> getAiAgentTasks(AiAgentVersion aiAgentVersion, DataFetchingEnvironment dfe);

    CompletionStage<List<AiAgentTag>> getAiAgentTags(AiAgentVersion aiAgentVersion, DataFetchingEnvironment dfe);

    CompletionStage<List<AiAgentLlm>> getAiAgentLlms(AiAgentVersion aiAgentVersion, DataFetchingEnvironment dfe);

    CompletionStage<List<String>> getVersionInstructions(AiAgentVersion aiAgentVersion, DataFetchingEnvironment dfe);

    CompletionStage<AiAgentVersion> aiAgentVersionTrustLevelAdd(AiAgentVersionTrustLevelAddInput input,
            DataFetchingEnvironment dfe);

    CompletionStage<AiRegistryEntitySyncData> syncData(AiAgentVersion aiAgentVersion, DataFetchingEnvironment dfe);

    CompletionStage<AiAgentVersion> aiAgentVersionEnable(AiAgentVersionEnableInput input, DataFetchingEnvironment dfe);

    CompletionStage<AiAgentTrustLevelQueryResponse> getAiAgentTrustLevelMetrics(
            AiAgentTrustLevelQueryInput input, DataFetchingEnvironment dfe);
}
