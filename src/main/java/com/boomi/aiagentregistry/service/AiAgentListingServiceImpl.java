// Copyright (c) 2025 Boomi, LP.
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.aop.IdpAccountFilter;
import com.boomi.aiagentregistry.constant.ErrorMessages;
import com.boomi.aiagentregistry.constant.FieldConstant;
import com.boomi.aiagentregistry.entity.AgentListingView;
import com.boomi.aiagentregistry.entity.AiAgentListing;
import com.boomi.aiagentregistry.model.AgentStatusCount;
import com.boomi.aiagentregistry.repo.AgentListingViewRepository;
import com.boomi.aiagentregistry.repo.AiAgentListingRepository;
import com.boomi.aiagentregistry.repo.AiAgentLlmAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentLlmRepository;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.repo.AiAgentTagAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentTagRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolAssociationRepository;
import com.boomi.aiagentregistry.search.SearchByProviderTypeStrategy;
import com.boomi.aiagentregistry.search.SearchStrategy;
import com.boomi.aiagentregistry.util.SearchUtil;
import com.boomi.aiagentregistry.util.UserUtilImpl;
import com.boomi.graphql.server.schema.types.AiAgentFilterLookup;
import com.boomi.graphql.server.schema.types.AiAgentListingAllFiltersResponse;
import com.boomi.graphql.server.schema.types.AiAgentListingFilterQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiAgentRegistryTrustLevel;
import com.boomi.graphql.server.schema.types.AiAgentTag;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryResponse;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.boomi.graphql.server.schema.types.LlmInfo;
import com.boomi.graphql.server.servlet.ErrorUtil;
import com.boomi.graphql.server.servlet.ResolverUtil;

import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import static com.boomi.aiagentregistry.mapper.AgentListingViewMapper.AGENT_LISTING_VIEW_MAPPER;
import static com.boomi.aiagentregistry.mapper.AiAgentFilterDropdownMapper.DROP_DOWN_FILTER_MAPPER;
import static com.boomi.aiagentregistry.mapper.AiAgentListingMapper.AI_AGENT_LISTING_MAPPER;
import static com.boomi.aiagentregistry.mapper.AiAgentStatusCountMapper.AGENT_STATUS_COUNT_MAPPER;
import static com.boomi.aiagentregistry.mapper.AiAgentTagMapper.AI_AGENT_TAG_MAPPER;
import static java.util.stream.Collectors.toList;

@Slf4j
@Service
public class AiAgentListingServiceImpl implements IAiAgentListingService {

    private final SearchUtil _searchUtil;
    private final AiAgentProviderAccountRepository _aiAgentProviderAccountRepository;
    private final AiAgentTagRepository _aiAgentTagRepository;
    private final AiAgentLlmRepository _aiAgentLlmRepository;
    private final AiAgentListingRepository _aiAgentListingRepository;
    private final AgentListingViewRepository _agentListingViewRepository;
    private final AiAgentTagAssociationRepository _aiAgentTagAssociationRepository;

    private final AiAgentLlmAssociationRepository _aiAgentLlmAssociationRepository;
    private final AiAgentToolAssociationRepository _aiAgentToolAssociationRepository;

    private static final int DEFAULT_START_INDEX = 0;
    private static final int DEFAULT_END_INDEX = 10;
    private final UserUtilImpl userUtilImpl;

    private static final boolean IS_NEW_IMPLEMENTATION = true;

    public AiAgentListingServiceImpl(SearchUtil searchUtil,
            AiAgentProviderAccountRepository aiAgentProviderAccountRepository,
            AiAgentTagRepository aiAgentTagRepository, AiAgentLlmRepository aiAgentLlmRepository,
            AiAgentListingRepository aiAgentListingRepository, AgentListingViewRepository agentListingViewRepository,
            AiAgentTagAssociationRepository aiAgentTagAssociationRepository,
            AiAgentLlmAssociationRepository aiAgentLlmAssociationRepository,
            AiAgentToolAssociationRepository aiAgentToolAssociationRepository, UserUtilImpl userUtilImpl) {
        _searchUtil = searchUtil;
        _aiAgentProviderAccountRepository = aiAgentProviderAccountRepository;
        _aiAgentTagRepository = aiAgentTagRepository;
        _aiAgentLlmRepository = aiAgentLlmRepository;
        _aiAgentListingRepository = aiAgentListingRepository;
        _agentListingViewRepository = agentListingViewRepository;
        _aiAgentTagAssociationRepository = aiAgentTagAssociationRepository;
        _aiAgentLlmAssociationRepository = aiAgentLlmAssociationRepository;
        _aiAgentToolAssociationRepository = aiAgentToolAssociationRepository;
        this.userUtilImpl = userUtilImpl;
    }

    @Transactional
    @Override
    /**
     *  This will return required data for filter dropdowns. All tags ,llms,provider accounts and provider types
     */
    public CompletionStage<AiAgentListingAllFiltersResponse> allAiAgentListingFilters(
            AiAgentListingFilterQueryInput input, DataFetchingEnvironment dfe) {

        if(IS_NEW_IMPLEMENTATION){
            return getAiAgentListingAllFiltersUsingAiAgentListingView(input, dfe);
        }else{
            return getAiAgentListingAllFiltersUsingAiAgentListing(input,dfe);
        }
    }

    @Transactional(readOnly = true)
    @Override
    @IdpAccountFilter
    public CompletableFuture<AiAgentsListingQueryResponse> getAiAgentListings(AiAgentsListingQueryInput input,
            DataFetchingEnvironment dfe) {
        if(IS_NEW_IMPLEMENTATION){
            return getAiAgentsListingsUsingAgentListingView(input, dfe);
        }else{
            return getAiAgentsListingsUsingAgentListing(input, dfe);
        }
    }

    private CompletionStage<AiAgentListingAllFiltersResponse>
    getAiAgentListingAllFiltersUsingAiAgentListing(
            AiAgentListingFilterQueryInput input, DataFetchingEnvironment dfe) {

        CompletableFuture<AiAgentListingAllFiltersResponse> completion = new CompletableFuture<>();
        try {
            String idpAccountId = userUtilImpl.getAccountId(dfe);
            List<AiAgentProviderType> providerTypes = _aiAgentProviderAccountRepository.getProviderTypes(idpAccountId);
            List<AiAgentRegistryTrustLevel> trustLevels =
                    _aiAgentListingRepository.getTrustLevel(idpAccountId);
            List<AiAgentFilterLookup> accounts =
                    DROP_DOWN_FILTER_MAPPER.toGraphQLTypeList(_aiAgentProviderAccountRepository
                            .getAccounts(idpAccountId));
            String providerId = (input != null) ? input.getProviderAccountId() : null;

            Set<String> llms = getLlms(providerId,idpAccountId);

            Set<AiAgentFilterLookup> tags = DROP_DOWN_FILTER_MAPPER.toGraphQLTypeSet(
                    getTags(idpAccountId, providerId));

            AiAgentListingAllFiltersResponse response = new AiAgentListingAllFiltersResponse();
            // returning accounts if the request is not for a particular provider account
            if (providerId == null) {
                response.setAccount(accounts);
            }

            response.setProvider(providerTypes);
            response.setModel(llms.stream().toList());
            response.setTag(tags.stream().toList());
            response.setTrustLevel(trustLevels);
            completion.complete(response);
            return completion;
        } catch (Exception e) {
            log.error(AiAgentRegistryErrorCode.FETCH_AI_AGENT_FILTER_ERROR.getDetail(), e);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.FETCH_AI_AGENT_FILTER_ERROR);
            completion.complete(null);
            return completion;
        }
    }

    private @NotNull CompletableFuture<AiAgentListingAllFiltersResponse>
    getAiAgentListingAllFiltersUsingAiAgentListingView(
            AiAgentListingFilterQueryInput input, DataFetchingEnvironment dfe) {
        CompletableFuture<AiAgentListingAllFiltersResponse> completion = new CompletableFuture<>();
        try {
            String idpAccountId = userUtilImpl.getAccountId(dfe);
            List<AiAgentProviderType> providerTypes = _aiAgentProviderAccountRepository.getProviderTypes(idpAccountId);
            List<AiAgentRegistryTrustLevel> trustLevels =
                    _agentListingViewRepository.getTrustLevel(idpAccountId);
            List<AiAgentFilterLookup> accounts =
                    DROP_DOWN_FILTER_MAPPER.toGraphQLTypeList(_aiAgentProviderAccountRepository
                            .getAccounts(idpAccountId));
            String providerId = (input != null) ? input.getProviderAccountId() : null;

            Set<String> llms = getLlms(providerId,idpAccountId);

            Set<AiAgentFilterLookup> tags = DROP_DOWN_FILTER_MAPPER.toGraphQLTypeSet(
                    getTags(idpAccountId, providerId));

            AiAgentListingAllFiltersResponse response = new AiAgentListingAllFiltersResponse();
            // returning accounts if the request is not for a particular provider account
            if (providerId == null) {
                response.setAccount(accounts);
            }

            response.setProvider(providerTypes);
            response.setModel(llms.stream().toList());
            response.setTag(tags.stream().toList());
            response.setTrustLevel(trustLevels);
            completion.complete(response);
            return completion;
        } catch (Exception e) {
            log.error(AiAgentRegistryErrorCode.FETCH_AI_AGENT_FILTER_ERROR.getDetail(), e);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.FETCH_AI_AGENT_FILTER_ERROR);
            completion.complete(null);
            return completion;
        }
    }

    private Set<String> getLlms(String providerId, String idpAccountId) {
        return providerId != null ? _aiAgentLlmRepository.getLlmsByProviderAccountGuid(idpAccountId, providerId) :
                _aiAgentLlmRepository.getLlms(idpAccountId);
    }

    private CompletableFuture<AiAgentsListingQueryResponse> getAiAgentsListingsUsingAgentListing(
            AiAgentsListingQueryInput input, DataFetchingEnvironment dfe) {
        CompletableFuture<AiAgentsListingQueryResponse> completion = new CompletableFuture<>();
        if (_searchUtil.validateInput(input, dfe)) {
            try {
                long overallStartTime = System.currentTimeMillis();
                int offset = input.getOffset() == null ? DEFAULT_START_INDEX : input.getOffset();
                int limit = input.getLimit() == null ? DEFAULT_END_INDEX : input.getLimit();

                validatePagination(offset, limit, dfe);
                Pageable pageable = PageRequest.of(offset, limit);
                // Get all filter strategies. if no filters searchStrategies list wil be empty
                List<SearchStrategy> searchStrategies = _searchUtil.getFilterStrategies(input);
                log.info(" Number of filter searchStrategies: {} ", searchStrategies.size());
                String condition = "AND";
                if (input.getAgentListingFilter() != null && input.getAgentListingFilter().getCondition() != null) {
                    condition = input.getAgentListingFilter().getCondition().name();
                }

                Specification<AiAgentListing> filterSpec = _searchUtil.buildSpecification(searchStrategies,
                        condition, input, dfe);
                log.info(" filterSpec: {} ", filterSpec);
                Page<AiAgentListing> aiAgentsPage = _aiAgentListingRepository.findAll(filterSpec, pageable);
                log.info("aiAgentsPage: {}", aiAgentsPage);

                if (!aiAgentsPage.getContent().isEmpty()) {
                    log.info(" aiAgentsPage content is not empty");

                    AiAgentsListingQueryResponse response =
                            createFlattenedResponse(aiAgentsPage, dfe);
                    boolean statusCount = searchStrategies.stream()
                            .anyMatch(SearchByProviderTypeStrategy.class::isInstance);
                    if (statusCount) {
                        response.setAiAgentStatusCount(AGENT_STATUS_COUNT_MAPPER.toGraphQLTypeList(
                                getStatusCountsFlattened(_aiAgentListingRepository.findAll(filterSpec))));

                        log.info("AiAgentStatusCount {} ", response.getAiAgentStatusCount());
                    }
                    log.info("statusCount {}  and response {} ", statusCount, response);
                    long overallEndTime = System.currentTimeMillis();
                    long overallDuration = overallEndTime - overallStartTime;

                    // Log the execution time
                    log.info("Overall processing completed - Time taken: {} ms, Number of listings processed: {}",
                            overallDuration, response.getNumberOfResults());
                    completion.complete(response);
                    return completion;
                }

                log.info("No results found. Returning null");
                completion.complete(null);
                return completion;
            } catch (Exception e) {
                log.error(AiAgentRegistryErrorCode.FETCH_AI_AGENT_ERROR.getDetail(), e);
                ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.FETCH_AI_AGENT_ERROR);
                completion.complete(null);
            }
        } else {
            log.warn(ErrorMessages.INVALID_INPUT);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.INVALID_INPUT);
            completion.complete(null);
        }
        return completion;


    }

    private @NotNull CompletableFuture<AiAgentsListingQueryResponse> getAiAgentsListingsUsingAgentListingView(
            AiAgentsListingQueryInput input, DataFetchingEnvironment dfe) {
        CompletableFuture<AiAgentsListingQueryResponse> completion = new CompletableFuture<>();
        if (_searchUtil.validateInput(input, dfe)) {
            try {
                long overallStartTime = System.currentTimeMillis();
                int offset = input.getOffset() == null ? DEFAULT_START_INDEX : input.getOffset();
                int limit = input.getLimit() == null ? DEFAULT_END_INDEX : input.getLimit();

                validatePagination(offset, limit, dfe);
                Pageable pageable = PageRequest.of(offset, limit);
                // Get all filter strategies. if no filters searchStrategies list wil be empty
                List<SearchStrategy> searchStrategies = _searchUtil.getViewFilterStrategies(input);
                log.info(" Number of filter searchStrategies: {} ", searchStrategies.size());
                String condition = "AND";
                if (input.getAgentListingFilter() != null && input.getAgentListingFilter().getCondition() != null) {
                    condition = input.getAgentListingFilter().getCondition().name();
                }

                Specification<AgentListingView> filterSpec = _searchUtil.buildViewSpecification(searchStrategies,
                        condition, input, dfe);
                log.info(" filterSpec: {} ", filterSpec);
                Page<AgentListingView> aiAgentsPage = _agentListingViewRepository.findAll(filterSpec, pageable);
                log.info("aiAgentsPage: {}", aiAgentsPage);

                if (!aiAgentsPage.getContent().isEmpty()) {
                    log.info(" aiAgentsPage content is not empty");

                    AiAgentsListingQueryResponse response =
                            createResponse(aiAgentsPage, dfe);
                    boolean statusCount = searchStrategies.stream()
                            .anyMatch(SearchByProviderTypeStrategy.class::isInstance);
                    if (statusCount) {
                        response.setAiAgentStatusCount(AGENT_STATUS_COUNT_MAPPER.toGraphQLTypeList(
                                getStatusCounts(_agentListingViewRepository.findAll(filterSpec))));

                        log.info("AiAgentStatusCount {} ", response.getAiAgentStatusCount());
                    }
                    log.info("statusCount {}  and response {} ", statusCount, response);
                    long overallEndTime = System.currentTimeMillis();
                    long overallDuration = overallEndTime - overallStartTime;

                    // Log the execution time
                    log.info("Overall processing completed - Time taken: {} ms, Number of listings processed: {}",
                            overallDuration, response.getNumberOfResults());
                    completion.complete(response);
                    return completion;
                }

                log.info("No results found. Returning null");
                completion.complete(null);
                return completion;
            } catch (Exception e) {
                log.error(AiAgentRegistryErrorCode.FETCH_AI_AGENT_ERROR.getDetail(), e);
                ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.FETCH_AI_AGENT_ERROR);
                completion.complete(null);
            }
        } else {
            log.warn(ErrorMessages.INVALID_INPUT);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.INVALID_INPUT);
            completion.complete(null);
        }
        return completion;
    }

    private boolean validatePagination(int offset, int limit, DataFetchingEnvironment dfe) {
        // Validate minimum values
        if (offset < 0) {
            log.warn("Invalid offset value: {}. Offset must be non-negative.", offset);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.INVALID_OFFSET);
            return false;
        }

        if (limit <= 0) {
            log.warn("Invalid limit value: {}. Limit must be greater than zero.", limit);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.INVALID_LIMIT);
            return false;
        }

        return true;
    }

    private AiAgentsListingQueryResponse createResponse(Page<AgentListingView> aiAgentsPage,
            DataFetchingEnvironment dfe) {

        List<com.boomi.graphql.server.schema.types.AiAgentListing> aiAgentListings = aiAgentsPage.getContent()
                .stream()
                .map(AGENT_LISTING_VIEW_MAPPER::toAiAgentListing)
                //.map(listing -> setRequiredRelatedEntities(listing, dfe))
                .toList();

        // Replaced the individual db call with batch operation.
        long batchEntitiesStartTime = System.currentTimeMillis();
        aiAgentListings = setRequiredRelatedEntitiesBatch(aiAgentListings, dfe);
        long batchEntitiesEndTime = System.currentTimeMillis();
        long batchProcessingDuration = batchEntitiesEndTime - batchEntitiesStartTime;

        // Log the execution time
        log.info("Batch processing completed - Batch Processing Time taken: {} ms, Number of listings processed: {}",
                batchProcessingDuration, aiAgentListings.size());

        // create response
        AiAgentsListingQueryResponse aiAgentsListingQueryResponse = new AiAgentsListingQueryResponse();
        aiAgentsListingQueryResponse.setAiAgentListings(aiAgentListings);
        aiAgentsListingQueryResponse.setNumberOfResults(aiAgentsPage.getTotalElements());
        aiAgentsListingQueryResponse.setCurrentPageSize(aiAgentsPage.getSize());
        return aiAgentsListingQueryResponse;
    }

    private AiAgentsListingQueryResponse createFlattenedResponse(Page<AiAgentListing> aiAgentsPage,
            DataFetchingEnvironment dfe) {

        List<com.boomi.graphql.server.schema.types.AiAgentListing> aiAgentListings = aiAgentsPage.getContent()
                .stream()
                .map(AI_AGENT_LISTING_MAPPER::toAiAgentListing)
                //.map(listing -> setRequiredRelatedEntities(listing, dfe))
                .toList();

        // Replaced the individual db call with batch operation.
        long batchEntitiesStartTime = System.currentTimeMillis();
        aiAgentListings = setRequiredRelatedEntitiesBatch(aiAgentListings, dfe);
        long batchEntitiesEndTime = System.currentTimeMillis();
        long batchProcessingDuration = batchEntitiesEndTime - batchEntitiesStartTime;

        // Log the execution time
        log.info("Batch processing completed - Batch Processing Time taken: {} ms, Number of listings processed: {}",
                batchProcessingDuration, aiAgentListings.size());

        // create response
        AiAgentsListingQueryResponse aiAgentsListingQueryResponse = new AiAgentsListingQueryResponse();
        aiAgentsListingQueryResponse.setAiAgentListings(aiAgentListings);
        aiAgentsListingQueryResponse.setNumberOfResults(aiAgentsPage.getTotalElements());
        aiAgentsListingQueryResponse.setCurrentPageSize(aiAgentsPage.getSize());
        return aiAgentsListingQueryResponse;
    }

    public List<com.boomi.graphql.server.schema.types.AiAgentListing> setRequiredRelatedEntitiesBatch
            (List<com.boomi.graphql.server.schema.types.AiAgentListing> listings,
                    DataFetchingEnvironment dfe) {
        if (listings.isEmpty()) {
            return listings;
        }

        // Collect all unique GUIDs
        List<String> aliasGuids = listings.stream()
                .map(com.boomi.graphql.server.schema.types.AiAgentListing::getAliasId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<String> versionGuids = listings.stream()
                .map(com.boomi.graphql.server.schema.types.AiAgentListing::getVersionId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // Batch fetch all UIDs
        Map<String, Integer> aliasUidMap = aliasGuids.isEmpty() ?
                Collections.emptyMap() :
                convertToUidMap(_agentListingViewRepository.findAliasUidsByGuids(aliasGuids));

        Map<String, Integer> versionUidMap = versionGuids.isEmpty() ?
                Collections.emptyMap() :
                convertToUidMap(_agentListingViewRepository.findVersionUidsByGuids(versionGuids));

        // Get all unique UIDs for batch queries
        Set<Integer> validAliasUids = aliasUidMap.values().stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<Integer> validVersionUids = versionUidMap.values().stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // Prepare maps for related data
        Map<Integer, List<com.boomi.aiagentregistry.entity.AiAgentTag>> aliasTagsMap = Collections.emptyMap();
        Map<Integer, List<com.boomi.aiagentregistry.entity.AiAgentTag>> versionTagsMap = Collections.emptyMap();
        Map<Integer, List<LlmInfo>> llmsMap = Collections.emptyMap();
        Map<Integer, List<String>> toolsMap = Collections.emptyMap();

        if (ResolverUtil.isChildFieldPresent(dfe, FieldConstant.ALIASTAGS)) {
            aliasTagsMap = getAssociatedTagsBatch(validAliasUids, AiRegistryEntityType.ALIAS);
        }

        if (ResolverUtil.isChildFieldPresent(dfe, FieldConstant.VERSIONTAGS)) {
            versionTagsMap = getAssociatedTagsBatch(validVersionUids, AiRegistryEntityType.VERSION);
        }

        if (ResolverUtil.isChildFieldPresent(dfe, FieldConstant.LLMS)) {
            llmsMap = getAssociatedLlmsBatch(validVersionUids);
        }

        if (ResolverUtil.isChildFieldPresent(dfe, FieldConstant.TOOLIDS)) {
            toolsMap = getAssociatedToolsBatch(validVersionUids);
        }

        // Apply fetched data to listings
        for (com.boomi.graphql.server.schema.types.AiAgentListing listing : listings) {
            Integer aliasUid = aliasUidMap.get(listing.getAliasId());
            Integer versionUid = versionUidMap.get(listing.getVersionId());

            if (ResolverUtil.isChildFieldPresent(dfe, FieldConstant.ALIASTAGS) &&
                    aliasUid != null && !listing.isAliasIsDeleted()) {
                List<AiAgentTag> aliasTags = AI_AGENT_TAG_MAPPER.toAIAgentTagList(
                        aliasTagsMap.getOrDefault(aliasUid, Collections.emptyList()));
                listing.setAliasTags(aliasTags);
            }

            if (ResolverUtil.isChildFieldPresent(dfe, FieldConstant.VERSIONTAGS) && versionUid != null) {
                List<AiAgentTag> versionTags = AI_AGENT_TAG_MAPPER.toAIAgentTagList(
                        versionTagsMap.getOrDefault(versionUid, Collections.emptyList()));
                listing.setVersionTags(versionTags);
            }

            if (ResolverUtil.isChildFieldPresent(dfe, FieldConstant.LLMS)) {
                listing.setLlms(llmsMap.getOrDefault(versionUid, Collections.emptyList()));
            }

            if (ResolverUtil.isChildFieldPresent(dfe, FieldConstant.TOOLIDS)) {
                listing.setToolIds(toolsMap.getOrDefault(versionUid, Collections.emptyList()));
            }
        }

        return listings;
    }

    private Map<String, Integer> convertToUidMap(List<Object[]> results) {
        return results.stream()
                .collect(Collectors.toMap(
                        result -> (String) result[FieldConstant.FIELD_INDEX_ZERO],
                        result -> (Integer) result[FieldConstant.FIELD_INDEX_ONE],
                        (existing, replacement) -> existing
                ));
    }


    private Map<Integer, List<com.boomi.aiagentregistry.entity.AiAgentTag>>
    getAssociatedTagsBatch(Set<Integer> relatedEntityUids, AiRegistryEntityType entityType) {
        if (relatedEntityUids.isEmpty()) {
            return Collections.emptyMap();
        }

        // Execute the query
        List<Object[]> tagResults = _aiAgentTagRepository.findTagsWithAssociations(
                relatedEntityUids,
                entityType
        );

        // Group the results by relatedEntityUid
        return tagResults.stream()
                .collect(Collectors.groupingBy(
                        result -> (Integer) result[FieldConstant.FIELD_INDEX_ZERO],
                        Collectors.mapping(
                                result -> (com.boomi.aiagentregistry.entity.AiAgentTag)
                                        result[FieldConstant.FIELD_INDEX_ONE],
                                Collectors.toList()
                        )
                ));
    }


    // Helper method for batch processing
    private Map<Integer, List<LlmInfo>> getAssociatedLlmsBatch(Set<Integer> entityIds) {
        if (entityIds.isEmpty()) {
            return Collections.emptyMap();
        }

        List<Object[]> llmResults = _aiAgentLlmAssociationRepository.findLlmInfosByEntityIds(entityIds);

        return llmResults.stream()
                .collect(Collectors.groupingBy(
                        result -> (Integer) result[FieldConstant.FIELD_INDEX_ZERO],
                        Collectors.mapping(
                                result -> {
                                    LlmInfo llmInfo = new LlmInfo();
                                    llmInfo.setGuid((String) result[FieldConstant.FIELD_INDEX_ONE]);
                                    llmInfo.setName((String) result[FieldConstant.FIELD_INDEX_TWO]);
                                    return llmInfo;
                                },
                                Collectors.toList()
                        )
                ));
    }

    // Helper method for batch processing
    private Map<Integer, List<String>> getAssociatedToolsBatch(Set<Integer> entityIds) {
        if (entityIds.isEmpty()) {
            return Collections.emptyMap();
        }

        List<Object[]> toolResults = _aiAgentToolAssociationRepository.findToolGuidsByEntityIds(entityIds);

        return toolResults.stream()
                .collect(Collectors.groupingBy(
                        result -> (Integer) result[FieldConstant.FIELD_INDEX_ZERO],
                        Collectors.mapping(
                                result -> ( result[FieldConstant.FIELD_INDEX_ONE]).toString(),
                                Collectors.toList()
                        )
                ));
    }




    private List<String> getAssociatedTools(Integer entityId) {
        return _aiAgentToolAssociationRepository.findToolNamesByEntityId(entityId);
    }

    private List<LlmInfo> getAssociatedLlms(Integer entityId) {
        List<com.boomi.aiagentregistry.model.LlmInfo> llmInfos =
                _aiAgentLlmAssociationRepository.findLlmInfoByEntityIdAndType(entityId);

        List<LlmInfo> llmInfoList = new ArrayList<>();
        for (com.boomi.aiagentregistry.model.LlmInfo llm : llmInfos) {
            LlmInfo llmInfo = new LlmInfo();
            llmInfo.setGuid(llm.getGuid());
            llmInfo.setName(llm.getName());
            llmInfoList.add(llmInfo);
        }

        return llmInfoList;
    }

    private List<AiAgentTag> getAssociatedTags(Integer entityId, AiRegistryEntityType entityType) {
        return AI_AGENT_TAG_MAPPER.toAIAgentTagList(_aiAgentTagAssociationRepository
                .findAiAgentTagFromAiAgentTagAssociationByRelatedEntityTypeAndRelatedEntityId(entityType, entityId));
    }

    private Set<com.boomi.aiagentregistry.model.AiAgentFilterDropdown> getTags(String idpAccountId
            , String providerAccountGuId) {
        if (providerAccountGuId == null || providerAccountGuId.isEmpty()) {
            return _aiAgentTagRepository.getTags(idpAccountId);
        }
        // Get tag IDs from both VERSION and ALIAS associations
        Set<Long> versionTagIds = _aiAgentTagRepository.getTagIdsFromVersions(providerAccountGuId);
        Set<Long> aliasTagIds = _aiAgentTagRepository.getTagIdsFromAliases(providerAccountGuId);
        // Combine all tag IDs
        Set<Long> allTagIds = new HashSet<>();
        allTagIds.addAll(versionTagIds);
        allTagIds.addAll(aliasTagIds);
        // If no tags found, return empty set
        if (allTagIds.isEmpty()) {
            return Collections.emptySet();
        }
        // Get tags with names and guids for the collected IDs
        return _aiAgentTagRepository.getTagsByIds(allTagIds, idpAccountId);
    }

    public List<AgentStatusCount> getStatusCountsFlattened(List<AiAgentListing> aiAgentListings) {
        // Group by status and count occurrences, filtering out null status
        Map<String, Long> statusCountMap = new HashMap<>();
        for (AiAgentListing agent : aiAgentListings) {
            if (agent.getAgentStatus() != null) {
                statusCountMap.merge(agent.getAgentStatus(), 1L, Long::sum);
            }
        }

        // Convert the map to List<AgentStatusCount>
        return statusCountMap.entrySet().stream()
                .map(entry -> new AgentStatusCount(entry.getKey(), entry.getValue()))
                .collect(toList());
    }

    public List<AgentStatusCount> getStatusCounts(List<AgentListingView> aiAgentListings) {
        // Group by status and count occurrences, filtering out null status
        Map<String, Long> statusCountMap = new HashMap<>();
        for (AgentListingView agent : aiAgentListings) {
            if (agent.getAgentStatus() != null) {
                statusCountMap.merge(agent.getAgentStatus(), 1L, Long::sum);
            }
        }

        // Convert the map to List<AgentStatusCount>
        return statusCountMap.entrySet().stream()
                .map(entry -> new AgentStatusCount(entry.getKey(), entry.getValue()))
                .collect(toList());
    }
}
