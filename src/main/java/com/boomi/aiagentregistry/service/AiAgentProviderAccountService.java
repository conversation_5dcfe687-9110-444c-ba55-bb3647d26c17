// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import jakarta.validation.constraints.NotNull;
import com.boomi.graphql.server.schema.types.AiAgentCloudProviderAccountConfirmInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccount;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountUpdateInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountsQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountsQueryResponse;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.COMPLETED;

/*
 * @author: Subramanya Hegde
 * Description: Many of the methods here are default methods. The reason for this is that we have a common
 * ServiceImpl primarily used for queries and other operations that do not involve the Provider type as a criterion.
 * To avoid unnecessary implementations, the default implementation is used. Even custom ServiceImpls do not involve
 * many operations, so this default implementation is utilized to prevent the need for dummy implementations.
 * */

public interface AiAgentProviderAccountService {

    AiAgentProviderType getProviderType();

    default CompletionStage<AiAgentProviderAccount> createAiAgentProviderAccount(
            AiAgentProviderAccountCreateInput input,
            DataFetchingEnvironment dfe) {
        return CompletableFuture.completedFuture(null);
    }

    default CompletionStage<AiAgentProviderAccount> updateAiAgentProviderAccount(
            AiAgentProviderAccountUpdateInput input,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {
        return CompletableFuture.completedFuture(null);
    }

    default CompletionStage<AiRegistryEntitySyncStatus> syncAiAgentProviderAccount(String id,
            DataFetchingEnvironment dfe) {
        return CompletableFuture.completedFuture(COMPLETED);
    }

    default boolean syncInProgress(com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingAccount) {
        return false;
    }

    default CompletionStage<Boolean> deleteProviderAccount(String id, DataFetchingEnvironment dfe,
            Map<AiAgentProviderType, AiAgentProviderAccountService> getBeanMappedByAiAgentProviderType) {
        return CompletableFuture.completedFuture(true);
    }

    default CompletionStage<Boolean> deleteProviderAccount(
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount providerAccount, DataFetchingEnvironment dfe) {
        return CompletableFuture.completedFuture(true);
    }

    default CompletableFuture<AiAgentProviderAccountsQueryResponse> getAiAgentProviders(
            AiAgentProviderAccountsQueryInput input, DataFetchingEnvironment dfe) {
        return CompletableFuture.completedFuture(null);
    }

    default CompletionStage<AiAgentProviderAccount> getAiAgentProvider(String id, DataFetchingEnvironment dfe) {
        return CompletableFuture.completedFuture(null);
    }

    default CompletionStage<AiAgentProviderAccount> aiAgentCloudProviderAccountConfirm(
            @NotNull AiAgentCloudProviderAccountConfirmInput input,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {
        return CompletableFuture.completedFuture(null);
    }

    // return a list of CompletableFuture each of which contains the input provider account with the cloudformation
    // template populated
    default List<CompletableFuture<AiAgentProviderAccount>> populateOnboardingTemplate(
            @NotNull List<AiAgentProviderAccount> accounts, DataFetchingEnvironment dfe) {
        return  Collections.emptyList();
    }
}
