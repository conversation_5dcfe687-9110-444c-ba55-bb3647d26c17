// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import software.amazon.awssdk.services.bedrockagent.model.GuardrailConfiguration;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

@EqualsAndHashCode(callSuper = true)
@Setter
@Getter
@SuperBuilder
@Accessors(prefix = "_")
public class BedrockVersionSyncStats extends SyncStats {

    private Map<String, BedrockKbSyncStats> _kbStats;
    private Set<String> _toRetainActGrpIds;
    private Set<String> _toRetainKbIds;
    private Set<GuardrailConfiguration> _toRetainGuardrails;
    private Set<String> _toRetainLlmIds;
    private AtomicBoolean _failedToFetchActionGroups = new AtomicBoolean(false);
    private AtomicBoolean _failedToFetchKbs = new AtomicBoolean(false);

    BedrockVersionSyncStats() {
        super();
        _toRetainActGrpIds = ConcurrentHashMap.newKeySet();
        _toRetainKbIds = ConcurrentHashMap.newKeySet();
        _kbStats = new ConcurrentHashMap<>();
        _toRetainGuardrails = ConcurrentHashMap.newKeySet();
        _toRetainLlmIds = ConcurrentHashMap.newKeySet();
    }

    public BedrockKbSyncStats getOrCreateKbStats(String kbId) {
        BedrockKbSyncStats stats = _kbStats.get(kbId);
        if (stats == null) {
            stats = _kbStats.putIfAbsent(kbId, new BedrockKbSyncStats());
            if (stats == null) {
                stats = _kbStats.get(kbId);
            }
        }
        return stats;
    }
}
