// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.boomigarden;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.boomi.aiagentregistry.aop.ReactiveLogging;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.service.SecretsManagerService;
import com.boomi.aiagentregistry.service.auth.AuthorizationParsingService;
import com.boomi.aiagentregistry.service.auth.BoomiApiTokenCredentials;
import com.boomi.aiagentregistry.servlet.ApiService;
import com.boomi.aiagentregistry.servlet.RequestBuilder;
import com.boomi.gardenagents.model.AgentDetail;
import com.boomi.gardenagents.model.AgentInstallResponse;
import com.boomi.gardenagents.model.AgentSummary;
import com.boomi.gardenagents.model.AgentUninstallResponse;
import com.boomi.gardenagents.model.Guardrail;
import com.boomi.gardenagents.model.Task;
import com.boomi.gardenagents.model.TaskTool;
import com.boomi.gardenagents.model.ToolDetail;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_FETCHING_AGENTS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_FETCHING_AGENT_DETAILS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_INSTALLING_GARDEN_AGENT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_MAKING_REQUEST_TO_GARDEN;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_UNINSTALLING_GARDEN_AGENT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FAILED_TO_CREATE_AUTHORIZATION;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FAILED_TO_GET_TOOL;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FAILED_TO_LIST_AGENTS;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.KEY_CURRENT_PAGE;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.KEY_DATA;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.KEY_ERROR;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.KEY_HEADER_ACCEPT;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.KEY_HEADER_ACCOUNTID;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.KEY_HEADER_USERID;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.KEY_ITEMS;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.KEY_MESSAGE;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.KEY_SUCCESS;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.KEY_TOTAL_PAGES;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.VALUE_HEADER_ACCEPT;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.VALUE_HEADER_ACCOUNTID;
import static com.boomi.aiagentregistry.service.sync.boomigarden.GardenConstants.VALUE_HEADER_USERID;
import static com.boomi.gardenagents.model.AgentDetail.AGENT_GUARDRAILS;
import static com.boomi.gardenagents.model.AgentDetail.AGENT_ID;
import static com.boomi.gardenagents.model.AgentDetail.AGENT_PERSONALITY_TRAITS;
import static com.boomi.gardenagents.model.AgentDetail.AGENT_TASKS;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_CREATED_BY;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_CREATED_ON;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_ID;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_INSTALLED_ON;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_LAST_UPDATED_BY;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_LAST_UPDATED_ON;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_LAST_USED_ON;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_NAME;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_OBJECTIVE;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_PRE_INSTALLED;
import static com.boomi.gardenagents.model.AgentSummary.AGENT_SUMMARY_STATUS;
import static com.boomi.gardenagents.model.Guardrail.GUARDRAIL_ID;
import static com.boomi.gardenagents.model.Task.TASK_INSTRUCTIONS;
import static com.boomi.gardenagents.model.Task.TASK_NAME;
import static com.boomi.gardenagents.model.Task.TASK_TOOLS;
import static com.boomi.gardenagents.model.TaskTool.TOOL_SUMMARY_ID;
import static com.boomi.gardenagents.model.TaskTool.TOOL_SUMMARY_TYPE;
import static com.boomi.gardenagents.model.ToolDetail.TOOL_CREATED_BY;
import static com.boomi.gardenagents.model.ToolDetail.TOOL_CREATED_ON;
import static com.boomi.gardenagents.model.ToolDetail.TOOL_DESCRIPTION;
import static com.boomi.gardenagents.model.ToolDetail.TOOL_ID;
import static com.boomi.gardenagents.model.ToolDetail.TOOL_LAST_UPDATED_BY;
import static com.boomi.gardenagents.model.ToolDetail.TOOL_LAST_UPDATED_ON;
import static com.boomi.gardenagents.model.ToolDetail.TOOL_LAST_USED_ON;
import static com.boomi.gardenagents.model.ToolDetail.TOOL_NAME;
import static com.boomi.gardenagents.model.ToolDetail.TOOL_PROVIDER_ID;
import static com.boomi.gardenagents.model.ToolDetail.TOOL_PROVIDER_NAME;
import static com.boomi.gardenagents.model.ToolDetail.TOOL_STATUS;

@Service
@Slf4j
@ReactiveLogging
public class GardenService {

    @Value("${boomi.services.aiagentregistry.garden.apiUrl}")
    private String _gardenApiUrl;

    private final GardenServiceProperties _properties;
    private static final String KEY_QUERY_PARAM_PAGE = "1";
    public static final String AUTHORIZATION_HEADER = "Authorization";
    public static final String BEARER_HEADER = "Bearer ";
    public static final String AGENT_LISTS_RESPONSE_FOR_GARDEN = "Agent lists response for garden: {}";


    private final ApiService _apiService;
    private final SecretsManagerService _secretsManager;
    private final AuthorizationParsingService _authService;

    public GardenService(
            ApiService apiService,
            SecretsManagerService secretsManager,
            AuthorizationParsingService authService,
            GardenServiceProperties properties) {
        _apiService = apiService;
        _secretsManager = secretsManager;
        _authService = authService;
        _properties = properties;
    }

    public Flux<BoomiApiTokenCredentials> getGardenCredentials(AiAgentProviderAccount providerAccount) {
        return _secretsManager.getSecret(providerAccount.getCredentialsKey())
                .flatMap(secrete -> _authService.createAuthorization(secrete, providerAccount.getAuthSchema())
                        .map(BoomiApiTokenCredentials.class::cast)
                ).onErrorResume(
                        error -> {
                            log.error(FAILED_TO_CREATE_AUTHORIZATION.concat(providerAccount.getGuid()));
                            return Mono.error(error);
                        }
                );

    }

    public Flux<AgentSummary> listAgentsRecursively(AiAgentProviderAccount providerAccount) {
        return Flux.defer(() ->
                 listPaginatedAgents(providerAccount, 1)
                    .onErrorResume(error -> {
                        log.error(ERROR_FETCHING_AGENTS.concat(providerAccount.getGuid()), error);
                        return Mono.error(error);
                    })
        );
    }

    public Flux<AgentSummary> listPaginatedAgents(AiAgentProviderAccount providerAccount, Integer pageNumber) {

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put(KEY_QUERY_PARAM_PAGE, pageNumber.toString());

        return getRequestBuilder(providerAccount, _properties.getAgentsPath(), queryParams)
                .flatMap(requestBuilder -> _apiService
                        .executeGet(requestBuilder, LinkedHashMap.class)
                        .next())
                .flatMapMany(response -> {
                    log.info(AGENT_LISTS_RESPONSE_FOR_GARDEN, providerAccount.getGuid());
                    LinkedHashMap responseData = (LinkedHashMap) response.get(KEY_DATA);
                    Integer totalPages = (Integer) responseData.get(KEY_TOTAL_PAGES);
                    Integer currentPage = (Integer) responseData.get(KEY_CURRENT_PAGE);
                    List<LinkedHashMap> items = (List<LinkedHashMap>) responseData.get(KEY_ITEMS);

                    Flux<AgentSummary> currentPageFlux = Flux.fromIterable(items)
                            .map(item -> AgentSummary.builder()
                                    .id((String) item.get(AGENT_SUMMARY_ID))
                                    .name((String) item.get(AGENT_SUMMARY_NAME))
                                    .createdOn((String) item.get(AGENT_SUMMARY_CREATED_ON))
                                    .lastUpdatedOn((String) item.get(AGENT_SUMMARY_LAST_UPDATED_ON))
                                    .createdBy((String) item.get(AGENT_SUMMARY_CREATED_BY))
                                    .lastUpdatedBy((String) item.get(AGENT_SUMMARY_LAST_UPDATED_BY))
                                    .lastUsedOn((String) item.get(AGENT_SUMMARY_LAST_USED_ON))
                                    .installedOn((String) item.get(AGENT_SUMMARY_INSTALLED_ON))
                                    .preInstalled((Boolean) item.get(AGENT_SUMMARY_PRE_INSTALLED))
                                    .objective(convertResponseDataToJson(item.get(AGENT_SUMMARY_OBJECTIVE)))
                                    .agentStatus((String) item.get(AGENT_SUMMARY_STATUS))
                                    .build()
                            );

                    if (currentPage < totalPages) {
                        return currentPageFlux.concatWith(listPaginatedAgents(providerAccount, currentPage + 1));
                    }

                    return currentPageFlux;
                })
                .onErrorResume(error -> {
                    log.error(FAILED_TO_LIST_AGENTS.concat(providerAccount.getGuid()), error);
                    return Mono.error(error);
                });
    }

    public Flux<ToolDetail> getGardenToolByIdAndType(AiAgentProviderAccount prvAcct,
            String gardenToolId, String gardenToolType) {
        return getRequestBuilder(prvAcct,_properties.buildToolPath(gardenToolType, gardenToolId), null)
                .flatMap(requestBuilder -> _apiService
                        .executeGet(requestBuilder, LinkedHashMap.class)
                        .next())
                .flatMapMany(response -> {
                    LinkedHashMap responseData = (LinkedHashMap) response.get(KEY_DATA);
                    return Flux.just(ToolDetail.builder()
                            .id((String) responseData.get(TOOL_ID))
                            .createdOn((String) responseData.get(TOOL_CREATED_ON))
                            .lastUpdatedOn((String) responseData.get(TOOL_LAST_UPDATED_ON))
                            .createdBy((String) responseData.get(TOOL_CREATED_BY))
                            .lastUpdatedBy((String) responseData.get(TOOL_LAST_UPDATED_BY))
                            .lastUsedOn((String) responseData.get(TOOL_LAST_USED_ON))
                            .name((String) responseData.get(TOOL_NAME))
                            .description((String) responseData.get(TOOL_DESCRIPTION))
                            .providerName((String) responseData.get(TOOL_PROVIDER_NAME))
                            .providerId((String) responseData.get(TOOL_PROVIDER_ID))
                            .toolStatus((String) responseData.get(TOOL_STATUS))
                            .toolJson(convertResponseDataToJson(responseData))
                            .build());
                })
                .onErrorResume(error -> {
                    log.error(FAILED_TO_GET_TOOL.concat(prvAcct.getGuid()), gardenToolId, gardenToolType, error);
                    return Mono.error(error);
                });
    }

    public Flux<AgentDetail> getGardenAgentById(AiAgentProviderAccount providerAccount, String gardenAgentId) {
        return getRequestBuilder(providerAccount, _properties.buildAgentPath(gardenAgentId), null)
                .flatMap(requestBuilder -> _apiService
                        .executeGet(requestBuilder, LinkedHashMap.class)
                        .next())
                .flatMapMany(response -> {
                    if(response.get(KEY_DATA) == null) {
                        return Flux.empty();
                    }
                    LinkedHashMap<String, Object> responseData = (LinkedHashMap)
                            response.get(KEY_DATA);
                    return Flux.just(AgentDetail.builder()
                            .id(responseData.get(AGENT_ID) == null
                                    ? null
                                    : (String) responseData.get(AGENT_ID))
                            .personalityTraits(responseData.get(AGENT_PERSONALITY_TRAITS) == null
                                    ? null
                                    :convertResponseDataToJson(responseData
                                            .get(AGENT_PERSONALITY_TRAITS)))
                            .guardrails(responseData.get(AGENT_GUARDRAILS) == null
                                    ? null
                                    :getGuardrail((LinkedHashMap)responseData.get(AGENT_GUARDRAILS)))
                            .tasks(responseData.get(AGENT_TASKS) == null
                                    ? null
                                    :getTasks((List<LinkedHashMap<String, Object>>)responseData
                                    .get(AGENT_TASKS)))
                            .build());
                })
                .onErrorResume(error -> {
                    log.error(ERROR_FETCHING_AGENT_DETAILS, gardenAgentId, error);
                    return Mono.error(error);
                });
    }

    public Mono<AgentInstallResponse> installAgent(AiAgentProviderAccount providerAccount, String gardenAgentId) {
        return getRequestBuilder(providerAccount, _properties.buildAgentInstallPath(gardenAgentId), null)
                .flatMap(
                requestBuilder -> _apiService.executePost(requestBuilder, LinkedHashMap.class)
                        .next())
                .map(response -> {
            log.debug("Garden agent install response: {}.", response.toString());
            boolean success = (boolean) response.get(KEY_SUCCESS);
            String message = (String) response.get(KEY_MESSAGE);
            String error = (String) response.get(KEY_ERROR);

            String id = null;
            String agentStatus = null;

            if (response.containsKey(KEY_DATA)) {
                LinkedHashMap<String, Object> data = (LinkedHashMap<String, Object>) response.get(KEY_DATA);
                if (data != null) {
                    id = (String) data.get(AgentInstallResponse.AGENT_ID);
                    agentStatus = (String) data.get(AgentInstallResponse.AGENT_STATUS);
                }
            }

            return AgentInstallResponse.builder().success(success).message(message).error(error).agentId(id)
                    .agentStatus(agentStatus).build();
        }).onErrorMap(error -> {
            log.error(ERROR_INSTALLING_GARDEN_AGENT, gardenAgentId, error);
            return new RuntimeException(ERROR_INSTALLING_GARDEN_AGENT + error.getMessage());
        });
    }

    public Mono<AgentUninstallResponse> uninstallAgent(AiAgentProviderAccount providerAccount, String gardenAgentId) {
        return getRequestBuilder(providerAccount, _properties.buildAgentUninstallPath(gardenAgentId), null).flatMap(
                requestBuilder -> _apiService.executePost(requestBuilder, LinkedHashMap.class).next()).map(response -> {
            log.debug("Garden agent uninstall response: {}.", response.toString());
            boolean success = (boolean) response.get(KEY_SUCCESS);
            String message = (String) response.get(KEY_MESSAGE);
            String error = (String) response.get(KEY_ERROR);

            String id = null;
            String agentStatus = null;

            if (response.containsKey(KEY_DATA)) {
                LinkedHashMap<String, Object> data = (LinkedHashMap<String, Object>) response.get(KEY_DATA);
                if (data != null) {
                    id = (String) data.get(AgentUninstallResponse.AGENT_ID);
                    agentStatus = (String) data.get(AgentUninstallResponse.AGENT_STATUS);
                }
            }

            return AgentUninstallResponse.builder().success(success).message(message).error(error).agentId(id)
                    .agentStatus(agentStatus).build();
        }).onErrorMap(error -> {
            log.error(ERROR_UNINSTALLING_GARDEN_AGENT, gardenAgentId, error);
            return new RuntimeException(ERROR_UNINSTALLING_GARDEN_AGENT + error.getMessage());
        });
    }

    private List<TaskTool> getTools(List<LinkedHashMap<String, Object>> inputTools) {

        List<TaskTool> result = new ArrayList<>(inputTools.size());

        for (LinkedHashMap<String, Object> tool : inputTools) {
            if (tool != null) {
                result.add(TaskTool.builder()
                        .id((String) tool.get(TOOL_SUMMARY_ID))
                        .type((String) tool.get(TOOL_SUMMARY_TYPE))
                        .build());
            }
        }
        return result;
    }

    private List<Task> getTasks(List<LinkedHashMap<String, Object>> inputTasks) {
        if (inputTasks.isEmpty()) {
            return Collections.EMPTY_LIST;
        }

        return inputTasks.stream()
                .map(this::buildTask)
                .collect(Collectors.toList());

    }

    private Guardrail getGuardrail(LinkedHashMap input) {
        return Guardrail.builder()
                .id((String) input.get(GUARDRAIL_ID))
                .guardrailJson(convertResponseDataToJson(input))
                .build();

    }

    private Task buildTask(LinkedHashMap<String, Object> task) {
        List<LinkedHashMap<String, Object>> taskTools = (List<LinkedHashMap<String, Object>>) task.get(TASK_TOOLS);

        return Task.builder()
                .name((String) task.get(TASK_NAME))
                .instructions((task.get(TASK_INSTRUCTIONS) == null
                || ((ArrayList<String>)task.get(TASK_INSTRUCTIONS)).isEmpty())
                        ? null : (ArrayList<String>) task.get(TASK_INSTRUCTIONS))
                .tools(taskTools == null || taskTools.isEmpty()
                        ? null : getTools(taskTools))
                .build();
    }

    public String convertResponseDataToJson(Object response) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(response);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert response data to JSON", e);
            throw new RuntimeException("JSON conversion failed", e);
        } catch (ClassCastException e) {
            log.error("Invalid response data type", e);
            throw new RuntimeException("Invalid data type", e);
        }
    }

    public Mono<RequestBuilder> getRequestBuilder(AiAgentProviderAccount providerAccount,
            String inputPath, Map<String, String> queryParams) {
        return Mono.from(getGardenCredentials(providerAccount)
                .map(credentials -> {
                    Map<String, String> requestHeaders = new HashMap<>();
                    requestHeaders.put(KEY_HEADER_ACCEPT, VALUE_HEADER_ACCEPT);
                    requestHeaders.put(KEY_HEADER_USERID, VALUE_HEADER_USERID);
                    requestHeaders.put(KEY_HEADER_ACCOUNTID, VALUE_HEADER_ACCOUNTID);
                    requestHeaders.put(AUTHORIZATION_HEADER,
                            BEARER_HEADER.concat(credentials.getJwt()));

                    return RequestBuilder.builder().headers(requestHeaders)
                            .baseUrl(_gardenApiUrl)
                            .path(inputPath)
                            .queryParams(queryParams).body(new HashMap<>())
                            .build();
                }))
                .onErrorResume(error -> {
                    log.error(ERROR_MAKING_REQUEST_TO_GARDEN, inputPath, error);
                    return Mono.error(error);
                });
    }
}
