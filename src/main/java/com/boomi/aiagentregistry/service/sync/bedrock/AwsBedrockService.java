// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.bedrock;

import com.boomi.aiagentregistry.aop.ReactiveLogging;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.service.SecretsManagerService;
import com.boomi.aiagentregistry.service.auth.AuthorizationParsingService;
import com.boomi.aiagentregistry.service.auth.AwsCredentials;
import com.boomi.aiagentregistry.service.sync.model.BedrockSyncContext;
import com.boomi.aiagentregistry.servlet.AwsClient;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.MonoSink;
import software.amazon.awssdk.auth.credentials.AwsSessionCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.services.bedrock.BedrockAsyncClient;
import software.amazon.awssdk.services.bedrock.model.AccessDeniedException;
import software.amazon.awssdk.services.bedrock.model.FoundationModelDetails;
import software.amazon.awssdk.services.bedrock.model.GetFoundationModelRequest;
import software.amazon.awssdk.services.bedrock.model.GetGuardrailRequest;
import software.amazon.awssdk.services.bedrock.model.GetGuardrailResponse;
import software.amazon.awssdk.services.bedrock.model.GetInferenceProfileRequest;
import software.amazon.awssdk.services.bedrock.model.GetInferenceProfileResponse;
import software.amazon.awssdk.services.bedrock.model.ResourceNotFoundException;
import software.amazon.awssdk.services.bedrock.model.ValidationException;
import software.amazon.awssdk.services.bedrockagent.BedrockAgentAsyncClient;
import software.amazon.awssdk.services.bedrockagent.model.AgentActionGroup;
import software.amazon.awssdk.services.bedrockagent.model.DataSource;
import software.amazon.awssdk.services.bedrockagent.model.GetAgentActionGroupRequest;
import software.amazon.awssdk.services.bedrockagent.model.GetDataSourceRequest;
import software.amazon.awssdk.services.bedrockagent.model.GetKnowledgeBaseRequest;
import software.amazon.awssdk.services.bedrockagent.model.GuardrailConfiguration;
import software.amazon.awssdk.services.bedrockagent.model.KnowledgeBase;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ConcurrentHashMap;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_FETCHING_ACTION_GROUP_DETAILS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FAILED_TO_CREATE_AUTHORIZATION;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FAILED_TO_CREATE_BEDROCK_CLIENT;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@ReactiveLogging
public class AwsBedrockService {
    private static final String ERROR_FETCHING_KNOWLEDGE =
            "Error fetching details for knowledge base {}.";

    private static final String ERROR_FETCHING_DR_FOR_KB =
            "Error fetching data sources {} for knowledge base {}.";
    private static final String ERROR_FETCHING_GUARDRAIL = "Error while fetching guardrail details {}:{}";
    private static final String ERROR_FETCHING_LLM = "Error fetching Foundation Model {}";
    private static final String VALIDATION_ERROR_OCCURRED_REQUEST_ID = "Validation error occurred {}. Request ID: {}";
    private static final String UNDERSCORE = "_";
    private static final long CACHING_DURATION_IN_MINUTES = 1;
    public static final String RESOURCE_NOT_FOUND = "Resource not found or access denied for {} {}";
    private static final String ERROR_FETCHING_INFERENCE_PROFILE = "Error fetching Inference Profile {}";
    public static final String FETCHING_SECRETES_FOR_ACCOUNT = "Fetching secretes for account {}";
    public static final String FETCHED_SECRETES_FOR_ACCOUNT = "Fetched secretes for account {} - account id {}";
    private static final String SAVING_NEW_ACCESS_TOKEN_FOR_ACCOUNT =
            "Saving the new access token for provider account {}.";
    private static final String SAVED_NEW_ACCESS_TOKEN_FOR_ACCOUNT =
            "Successfully saved the new access token for " + "provider account {}.";
    private static final int REFRESH_ACCESS_TOKEN_WINDOW_IN_MINUTES_FOR_SYNC_JOB = 20;
    private final Map<String, Mono<AgentActionGroup>> actionGroupCache = new ConcurrentHashMap<>();
    private final Map<String, Mono<KnowledgeBase>> knowledgeBaseCache = new ConcurrentHashMap<>();
    private final Map<String, Mono<DataSource>> dataSourceCache = new ConcurrentHashMap<>();
    private final Map<String, Mono<GetGuardrailResponse>> guardrailCache = new ConcurrentHashMap<>();
    private final Map<String, Mono<FoundationModelDetails>> llmCache = new ConcurrentHashMap<>();
    private final Map<String, Mono<GetInferenceProfileResponse>> inferenceProfileCache = new ConcurrentHashMap<>();
    private final SecretsManagerService _secretsManager;
    private final AwsClient _awsClient;
    private final AuthorizationParsingService _authService;
    private ObjectMapper _objectMapper;

    public AwsBedrockService(SecretsManagerService secretsManager, AwsClient awsClient,
            AuthorizationParsingService authService, ObjectMapper objectMapper) {
        _secretsManager = secretsManager;
        _awsClient = awsClient;
        _authService = authService;
        _objectMapper = objectMapper;
    }

    public Flux<BedrockAgentAsyncClient> getBedrockAgentClient(AiAgentProviderAccount providerAccount) {
        log.info(FETCHING_SECRETES_FOR_ACCOUNT, providerAccount.getProviderAccountName());
        if (providerAccount.getAuthSchema() == AiProviderAuthSchema.AWS_ASSUME_ROLE) {
            return _secretsManager.getSecret(providerAccount.getCredentialsKey())
                                  .flatMap(secrete -> {
                                      log.info(FETCHED_SECRETES_FOR_ACCOUNT, providerAccount.getProviderAccountName(),
                                              providerAccount.getGuid());
                                      return _authService.createAuthorization(secrete, providerAccount.getAuthSchema())
                                                         .map(AwsCredentials.class::cast)
                                                         .map(credentialsWithExternalId -> validateAccessTokenExpire(
                                                                 credentialsWithExternalId, providerAccount,
                                                                 REFRESH_ACCESS_TOKEN_WINDOW_IN_MINUTES_FOR_SYNC_JOB))
                                                         .map(awsCredentials ->
                                                             _awsClient.createBedrockAgentAsyncClient(
                                                                 getCredentialsProvider(awsCredentials),
                                                                 providerAccount.getRegion()))
                                                         .onErrorResume(error -> {
                                                             log.error(FAILED_TO_CREATE_AUTHORIZATION,
                                                                     providerAccount.getGuid());
                                                             return Mono.error(error);
                                                         });
                                  })
                                  .onErrorResume(error -> {
                                      log.error(FAILED_TO_CREATE_BEDROCK_CLIENT, providerAccount.getGuid());
                                      return Mono.error(error);
                                  });
        } else {
            return _secretsManager.getSecret(providerAccount.getCredentialsKey())
                                  .flatMap(secrete -> {
                                      log.info(FETCHED_SECRETES_FOR_ACCOUNT, providerAccount.getProviderAccountName(),
                                              providerAccount.getGuid());
                                      return _authService.createAuthorization(secrete, providerAccount.getAuthSchema())
                                                         .map(AwsCredentials.class::cast)
                                                         .map(_awsClient::createBedrockAgentAsyncClient)
                                                         .onErrorResume(error -> {
                                                             log.error(FAILED_TO_CREATE_AUTHORIZATION,
                                                                     providerAccount.getGuid());
                                                             return Mono.error(error);
                                                         });
                                  })
                                  .onErrorResume(error -> {
                                      log.error(FAILED_TO_CREATE_BEDROCK_CLIENT, providerAccount.getGuid());
                                      return Mono.error(error);
                                  });
        }
    }

    public Flux<BedrockAsyncClient> getBedrockClient(AiAgentProviderAccount providerAccount) {
        log.info(FETCHING_SECRETES_FOR_ACCOUNT, providerAccount.getProviderAccountName());
        if (providerAccount.getAuthSchema() == AiProviderAuthSchema.AWS_ASSUME_ROLE) {
            return _secretsManager.getSecret(providerAccount.getCredentialsKey())
                                  .flatMap(secrete -> {
                                      log.info(FETCHED_SECRETES_FOR_ACCOUNT, providerAccount.getProviderAccountName(),
                                              providerAccount.getGuid());
                                      return _authService.createAuthorization(secrete, providerAccount.getAuthSchema())
                                                         .map(AwsCredentials.class::cast)
                                                         .map(credentialsWithExternalId -> validateAccessTokenExpire(
                                                                 credentialsWithExternalId, providerAccount,
                                                                 REFRESH_ACCESS_TOKEN_WINDOW_IN_MINUTES_FOR_SYNC_JOB))
                                                         .map(awsCredentials -> _awsClient.createBedrockAsyncClient(
                                                                 getCredentialsProvider(awsCredentials),
                                                                 providerAccount.getRegion()))
                                                         .onErrorResume(error -> {
                                                             log.error(FAILED_TO_CREATE_AUTHORIZATION,
                                                                     providerAccount.getGuid());
                                                             return Mono.error(error);
                                                         });
                                  })
                                  .onErrorResume(error -> {
                                      log.error(FAILED_TO_CREATE_BEDROCK_CLIENT, providerAccount.getGuid());
                                      return Mono.error(error);
                                  });
        } else {
            return _secretsManager.getSecret(providerAccount.getCredentialsKey())
                                  .flatMap(secrete -> {
                                      log.info(FETCHED_SECRETES_FOR_ACCOUNT, providerAccount.getProviderAccountName(),
                                              providerAccount.getGuid());
                                      return _authService.createAuthorization(secrete, providerAccount.getAuthSchema())
                                                         .map(AwsCredentials.class::cast)
                                                         .map(_awsClient::createBedrockAsyncClient)
                                                         .onErrorResume(error -> {
                                                             log.error(FAILED_TO_CREATE_AUTHORIZATION,
                                                                     providerAccount.getGuid());
                                                             return Mono.error(error);
                                                         });
                                  })
                                  .onErrorResume(error -> {
                                      log.error(FAILED_TO_CREATE_BEDROCK_CLIENT, providerAccount.getGuid());
                                      return Mono.error(error);
                                  });
        }

    }

    public Mono<AgentActionGroup> fetchActionGroupDetails(
            String agentId,
            String version,
            String actionGroupId,
            BedrockSyncContext context) {

        String cacheKey = context.getProviderAccount().getUid() + UNDERSCORE + actionGroupId;
        log.info("Fetching action group details for action group id: {}", actionGroupId);

        return actionGroupCache.computeIfAbsent(cacheKey, k ->
                Mono.defer(() -> {
                    GetAgentActionGroupRequest request = GetAgentActionGroupRequest.builder()
                            .agentId(agentId)
                            .agentVersion(version)
                            .actionGroupId(actionGroupId)
                            .build();

                    return Mono.<AgentActionGroup>create(sink -> {
                                context.getBedrockAgentClient()
                                        .getAgentActionGroup(request)
                                        .whenCompleteAsync((response, throwable) -> {
                                            if (throwable != null) {
                                                handleBedrockError(
                                                        actionGroupId,
                                                        "Action group",
                                                        ERROR_FETCHING_ACTION_GROUP_DETAILS,
                                                        sink,
                                                        throwable);
                                                return;
                                            }
                                            log.info("Fetched action group details for action group id: {}", actionGroupId);
                                            sink.success(response.agentActionGroup());
                                        });
                            })
                            .cache(Duration.ofMinutes(CACHING_DURATION_IN_MINUTES));
                }).share()
        );
    }

    public Mono<KnowledgeBase> fetchKnowledgeBaseDetails(
            String kbId,
            BedrockSyncContext context) {

        String cacheKey = context.getProviderAccount().getUid() + UNDERSCORE + kbId;
        log.info("Fetching knowledge base details for knowledge base id: {}", kbId);

        return knowledgeBaseCache.computeIfAbsent(cacheKey, k ->
                Mono.defer(() ->
                        Mono.<KnowledgeBase>create(sink -> {
                                    GetKnowledgeBaseRequest request = GetKnowledgeBaseRequest.builder()
                                            .knowledgeBaseId(kbId)
                                            .build();

                                    context.getBedrockAgentClient()
                                            .getKnowledgeBase(request)
                                            .whenCompleteAsync((response, throwable) -> {
                                                if (throwable != null) {
                                                    handleBedrockError(
                                                            kbId,
                                                            "Knowledge base",
                                                            String.format(ERROR_FETCHING_KNOWLEDGE, kbId),
                                                            sink,
                                                            throwable);
                                                    return;
                                                }
                                                log.info("Fetched knowledge base details for knowledge base id: {}", kbId);
                                                sink.success(response.knowledgeBase());
                                            });
                                })
                                .cache(Duration.ofMinutes(CACHING_DURATION_IN_MINUTES))
                ).share()
        );
    }


    public Mono<DataSource> fetchDataSourceDetails(
            AiAgentTool kb,
            String dataSourceId,
            BedrockSyncContext context) {

        String cacheKey = context.getProviderAccount().getUid() + UNDERSCORE + dataSourceId;

        log.info("Fetching data source details for data source id: {}", dataSourceId);

        return dataSourceCache.computeIfAbsent(cacheKey, k ->
                Mono.defer(() ->
                        Mono.<DataSource>create(sink -> {
                                    GetDataSourceRequest request = GetDataSourceRequest.builder()
                                            .knowledgeBaseId(kb.getExternalId())
                                            .dataSourceId(dataSourceId)
                                            .build();

                                    context.getBedrockAgentClient()
                                            .getDataSource(request)
                                            .whenCompleteAsync((response, throwable) -> {
                                                if (throwable != null) {
                                                    handleBedrockError(
                                                            dataSourceId,
                                                            "Data source",
                                                            String.format(
                                                                    ERROR_FETCHING_DR_FOR_KB,
                                                                    dataSourceId,
                                                                    kb.getUid()),
                                                            sink,
                                                            throwable);
                                                    return;
                                                }
                                                log.info("Fetched data source details for data source id: {}", dataSourceId);
                                                sink.success(response.dataSource());
                                            });
                                })
                                .cache(Duration.ofMinutes(CACHING_DURATION_IN_MINUTES))
                ).share()
        );
    }

    public Mono<GetGuardrailResponse> fetchGuardrailDetails(
            GuardrailConfiguration grConfig,
            BedrockSyncContext context) {
        log.info("Fetching guardrail details for {} version {}",
                grConfig.guardrailIdentifier(),
                grConfig.guardrailVersion());

        String cacheKey = context.getProviderAccount().getUid() + UNDERSCORE +
                          grConfig.guardrailIdentifier() + UNDERSCORE + grConfig.guardrailVersion();

        return guardrailCache.computeIfAbsent(cacheKey, k ->
                Mono.defer(() ->
                        Mono.<GetGuardrailResponse>create(sink -> {
                                    GetGuardrailRequest request = GetGuardrailRequest.builder()
                                            .guardrailIdentifier(grConfig.guardrailIdentifier())
                                            .guardrailVersion(grConfig.guardrailVersion())
                                            .build();

                                    context.getBedrockClient()
                                            .getGuardrail(request)
                                            .whenCompleteAsync((response, throwable) -> {
                                                if (throwable != null) {
                                                    String resourceId = grConfig.guardrailIdentifier();
                                                    String grVersion = grConfig.guardrailVersion();
                                                    handleBedrockError(
                                                            resourceId,
                                                            "Guardrail",
                                                            String.format(
                                                                    ERROR_FETCHING_GUARDRAIL,
                                                                    resourceId,
                                                                    grVersion),
                                                            sink,
                                                            throwable);
                                                    return;
                                                }
                                                log.info("Finished fetching guardrail details for {} version {}",
                                                        grConfig.guardrailIdentifier(),
                                                        grConfig.guardrailVersion());
                                                sink.success(response);
                                            });
                                })
                                .cache(Duration.ofMinutes(CACHING_DURATION_IN_MINUTES))
                ).share()
        );
    }


    public Mono<FoundationModelDetails> fetchLlmDetails(
            String llmId,
            BedrockSyncContext context) {

        String cacheKey = context.getProviderAccount().getUid() + UNDERSCORE + llmId;

        log.info("Fetching LLM details for {}", llmId);

        return llmCache.computeIfAbsent(cacheKey, k ->
                Mono.defer(() ->
                        Mono.<FoundationModelDetails>create(sink -> {
                                    GetFoundationModelRequest request = GetFoundationModelRequest.builder()
                                            .modelIdentifier(llmId)
                                            .build();

                                    context.getBedrockClient()
                                            .getFoundationModel(request)
                                            .whenCompleteAsync((response, throwable) -> {
                                                if (throwable != null) {
                                                    handleBedrockError(
                                                            llmId,
                                                            "LLM",
                                                            ERROR_FETCHING_LLM,
                                                            sink,
                                                            throwable);
                                                    return;
                                                }
                                                log.info("Finished fetching LLM details for {}", llmId);
                                                sink.success(response.modelDetails());
                                            });
                                })
                                .cache(Duration.ofMinutes(CACHING_DURATION_IN_MINUTES))
                ).share()
        );
    }

    public Mono<GetInferenceProfileResponse> fetchInferenceProfile(
            String inferenceProfileArn,
            BedrockSyncContext context) {

        String cacheKey = context.getProviderAccount().getUid() + UNDERSCORE + inferenceProfileArn;
        log.info("Fetching inference profile details for {}", inferenceProfileArn);

        return inferenceProfileCache.computeIfAbsent(cacheKey, k ->
                Mono.defer(() ->
                        Mono.<GetInferenceProfileResponse>create(sink -> {
                                    GetInferenceProfileRequest request = GetInferenceProfileRequest.builder()
                                            .inferenceProfileIdentifier(inferenceProfileArn)
                                            .build();

                                    context.getBedrockClient()
                                            .getInferenceProfile(request)
                                            .whenCompleteAsync((response, throwable) -> {
                                                if (throwable != null) {
                                                    handleBedrockError(
                                                            inferenceProfileArn,
                                                            "Inference profile",
                                                            String.format(
                                                                    ERROR_FETCHING_INFERENCE_PROFILE,
                                                                    inferenceProfileArn),
                                                            sink,
                                                            throwable);
                                                    return;
                                                }
                                                log.info("Finished fetching inference profile details for {}",
                                                        inferenceProfileArn);
                                                sink.success(response);
                                            });
                                })
                                .cache(Duration.ofMinutes(CACHING_DURATION_IN_MINUTES))
                ).share()
        );
    }


    private <T> void handleBedrockError(
            String resourceId,
            String resourceType,
            String errorMessage,
            MonoSink<T> sink,
            Throwable throwable) {

        if (throwable == null) {
            return;
        }

        Throwable cause = throwable instanceof CompletionException ?
                throwable.getCause() : throwable;

        if (cause instanceof AccessDeniedException ||
            cause instanceof software.amazon.awssdk.services.bedrockagent.model.AccessDeniedException ||
            cause instanceof ResourceNotFoundException ||
            cause instanceof software.amazon.awssdk.services.bedrockagent.model.ResourceNotFoundException) {
            log.warn(RESOURCE_NOT_FOUND, resourceType, resourceId);
            sink.error(cause);
            return;
        }

        if (cause instanceof ValidationException validationError) {
            log.warn(VALIDATION_ERROR_OCCURRED_REQUEST_ID, resourceId, validationError.requestId());
            sink.success(null);
            return;
        }

        log.error(errorMessage, throwable);
        sink.error(cause);
    }

    // suppress log JsonProcessingException message because it should not log secrets
    @SuppressWarnings("java:S1166")
    private AwsCredentials validateAccessTokenExpire(AwsCredentials existingCredentials,
            AiAgentProviderAccount providerAccount, int refreshWindowInMinutes) {
        AwsCredentials newCredentials = (AwsCredentials) _authService.getRefreshedTempAccessTokenIfExpired(
                existingCredentials, providerAccount.getAuthSchema(), refreshWindowInMinutes);

        if (newCredentials != null) {
            try {
                // Save the new secret to the Secret Manager
                log.info(SAVING_NEW_ACCESS_TOKEN_FOR_ACCOUNT, providerAccount.getGuid());
                String secret = _objectMapper.writeValueAsString(newCredentials);
                _secretsManager.updateSecret(providerAccount.getCredentialsKey(), secret);
            } catch (JsonProcessingException e) {
                log.error("JsonProcessingException in writing temp aws credentials");
                return newCredentials;
            }
            log.info(SAVED_NEW_ACCESS_TOKEN_FOR_ACCOUNT, providerAccount.getGuid());
            return newCredentials;
        } else {
            return existingCredentials;
        }
    }

    private static StaticCredentialsProvider getCredentialsProvider(AwsCredentials awsCredentials) {
        AwsSessionCredentials sessionCredentials = AwsSessionCredentials.create(awsCredentials.getAwsAccessKeyId(),
                awsCredentials.getAwsSecretAccessKey(), awsCredentials.getSessionToken());

        return StaticCredentialsProvider.create(sessionCredentials);
    }
}


