// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.bedrock;

import com.boomi.aiagentregistry.entity.AiAgentLlm;
import com.boomi.aiagentregistry.entity.AiAgentLlmAssociation;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.repo.AiAgentLlmAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentLlmRepository;
import com.boomi.aiagentregistry.service.sync.SyncContextAccessor;
import com.boomi.aiagentregistry.service.sync.SyncHistoryService;
import com.boomi.aiagentregistry.service.sync.model.BedrockSyncContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import software.amazon.awssdk.services.bedrock.model.ValidationException;

import java.util.ConcurrentModificationException;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.boomi.aiagentregistry.mapper.AiAgentLlmAssociationMapper.LLM_ASSOC_MAPPER;
import static com.boomi.aiagentregistry.mapper.AiAgentLlmMapper.LLM_MAPPER;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.VERSION;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class LlmSyncHandler extends BedrockSyncHandler {
    private static final String ERROR_PROCESSING_LLM = "Error processing LLM {}.";
    private static final String ERROR_DELETING_LLM_RAIL = "Error while deleting LLMS for version {}";
    private static final String LLM_CREATION_FAILED = "Error creating LLM";
    public static final String INFERENCE_PROFILE_PATTERN_ERROR = "Error validating inference profile pattern for: {}";
    private static final String FOUNDATION_MODEL_PREFIX = "foundation-model/";
    public static final String ERROR_EXTRACTING_FOUNDATION_MODEL = "Error extracting foundation model from ARN: {}";
    private static final String ARN = "arn:aws";
    private static final String ERROR_DELETING_LLM =
            "Error Deleting LLM that belongs to Version with Version ID: %s. \n The error message from amazon is: %s";
    private static final String SYNC_ERROR_PROCESSING_LLM =
            "Error processing LLM with ID: %s. \n The error message from amazon is: %s";
    private final AiAgentLlmRepository _llmRepo;
    private final AiAgentLlmAssociationRepository _llmAssociationRepo;

    public LlmSyncHandler(
            AwsBedrockService awsBedrockService,
            SyncContextAccessor ctxAccessor,
            SyncHistoryService syncHistoryService,
            AiAgentLlmRepository llmRepo,
            AiAgentLlmAssociationRepository llmAssociationRepo) {

        super(syncHistoryService, ctxAccessor, awsBedrockService);

        _llmRepo = llmRepo;
        _llmAssociationRepo = llmAssociationRepo;
    }

    public Mono<AiAgentLlm> processLlm(AiAgentVersion version, String llmId, BedrockSyncContext context) {
        if (llmId == null) {
            return Mono.empty();
        }

        String providerAccountName = context.getProviderAccount().getProviderAccountName();
        log.info("Processing LLM {} for version {} of account {}", llmId, version.getUid(), providerAccountName);

        if(isInferenceProfile(llmId)){
            log.info("Fetching LLM name from inference profile ARN {}", llmId);
            return _awsBedrockService.fetchInferenceProfile(llmId, context)
                    .flatMap(inferenceProfile -> {
                        try {
                            String modelArn = inferenceProfile.models().get(0).modelArn();
                            String llmName = extractFoundationModelName(modelArn, version, context);
                            log.info("Finished fetching LLM name from inference profile ARN {}", llmId);
                            return processLlmByLlmName(version, llmName, context);
                        } catch (Exception e) {
                            log.warn(ERROR_EXTRACTING_FOUNDATION_MODEL, llmId, e);
                            return Mono.error(e);
                        }
                    });
        }else{
            return processLlmByLlmName(version, llmId, context);
        }
    }

    private Mono<AiAgentLlm> processLlmByLlmName(AiAgentVersion version, String llmId, BedrockSyncContext context) {
        return findLlm(llmId, _ctxAccessor.getPrvAcct(context))
                .map(aiAgentLlm -> handleExistingLlm(version, aiAgentLlm)
                        .doOnSuccess(logOnSuccess(version, llmId, context))
                        .onErrorResume(error -> handleSyncError(version, llmId, context, error)))
                .orElseGet(() -> handleNewLlm(version, llmId, context)
                        .doOnSuccess(logOnSuccess(version, llmId, context))
                        .onErrorResume(error -> handleSyncError(version, llmId, context, error)));
    }

    private static  Consumer<AiAgentLlm> logOnSuccess(
            AiAgentVersion version,
            String llmId,
            BedrockSyncContext context) {
        return unused ->
                log.info("Finished processing LLM {} for version {} of account {}",
                        llmId,
                        version.getUid(),
                        context.getProviderAccount().getProviderAccountName());
    }

    private Mono<AiAgentLlm> handleSyncError(
            AiAgentVersion version,
            String llmId,
            BedrockSyncContext context,
            Throwable error) {
        log.error(ERROR_PROCESSING_LLM, llmId, error);
        String errorMessage = String.format(SYNC_ERROR_PROCESSING_LLM, llmId, error.getMessage());
        updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
        return Mono.empty();
    }


    private Mono<AiAgentLlm> handleNewLlm(AiAgentVersion version, String llmId, BedrockSyncContext context) {
        //if this is an id validation error that gets thrown by aws and is caught in aws service
        return _awsBedrockService.fetchLlmDetails(llmId, context)
                .switchIfEmpty(Mono.defer(() -> {
                    //if this is an id validation error that gets thrown by aws and is caught in aws service
                    return Mono.empty();
                }))
                .flatMap(llmDetails -> {
                    AiAgentProviderAccount prvAcct = _ctxAccessor.getPrvAcct(context);
                    AiAgentLlm newLlm = LLM_MAPPER.toEntityFrmAwsModel(llmDetails, prvAcct);
                    return saveLlmWithRetry(version, newLlm, context);

                })
                .onErrorResume(ValidationException.class, e -> {
                    String errorMessage = String.format(SYNC_ERROR_PROCESSING_LLM, llmId, e.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return Mono.empty();
                })
                .onErrorResume(error -> {
                    String errorMessage = String.format(SYNC_ERROR_PROCESSING_LLM, llmId, error.getMessage());
                    log.error(ERROR_PROCESSING_LLM, llmId, error);
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return Mono.empty();
                });
    }


    private Mono<AiAgentLlm> saveLlmWithRetry(
            AiAgentVersion version,
            AiAgentLlm llm,
            BedrockSyncContext context) {

        return Mono.fromCallable(() -> {
                    log.info("Saving llm {} to db", llm.getExternalId());
                    AiAgentLlm savedLlm = _llmRepo.save(llm);
                    log.info("Finished saving llm {} to db", llm.getExternalId());

                    associateLlmWithVersion(version, savedLlm);
                    updateAddStats(_ctxAccessor.getOrCrtVerStats(version, context));
                    return savedLlm;
                })
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(DataIntegrityViolationException.class, e ->
                        handleLlmCreationConflict(version, llm, context)
                );
    }

    private Mono<AiAgentLlm> handleLlmCreationConflict(
            AiAgentVersion version,
            AiAgentLlm llm,
            BedrockSyncContext context) {

        return findLlm(llm.getExternalId(), _ctxAccessor.getPrvAcct(context))
                .map(existing -> {
                    associateLlmWithVersion(version, existing);
                    return Mono.just(existing);
                }).orElseGet(() -> {
                    ConcurrentModificationException e = new ConcurrentModificationException(LLM_CREATION_FAILED);
                    String errorMessage = String.format(SYNC_ERROR_PROCESSING_LLM, llm.getExternalId(), e.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return Mono.empty();
                });

    }

    private Mono<AiAgentLlm> handleExistingLlm(
            AiAgentVersion version,
            AiAgentLlm existingLlm) {

        //if there is an association already for the version, don't create one
        if (associationDoesNotExist(version, existingLlm)) {
            associateLlmWithVersion(version, existingLlm);
        }
        //no update needed since models are not updatable
        return Mono.just(existingLlm);
    }

    private boolean associationDoesNotExist(AiAgentVersion version, AiAgentLlm existingLlm) {
        log.info("Fetching associations for llm {}", existingLlm.getExternalId());
        Set<AiAgentLlmAssociation> associations = _llmAssociationRepo.findAllByLlm(existingLlm);
        log.info("Finished fetching associations for llm {}", existingLlm.getExternalId());

        return associations
                .stream()
                .noneMatch(assoc ->
                        assoc.getRelatedEntityType().equals(VERSION)
                        && assoc.getRelatedEntityUid().equals(version.getUid()));
    }

    public Mono<Void> deleteAllByVersionExcept(
            AiAgentVersion version,
            Set<String> llmIdToRetain,
            BedrockSyncContext context) {

        return getVersionAssociations(version)
                .flatMap(associations -> {
                    Set<Integer> llmToDetachIds = identifyLlmToDetach(associations, llmIdToRetain);

                    if (llmToDetachIds.isEmpty()) {
                        return Mono.empty();
                    }

                    return deleteAssociationsByLlmIdsAndVersionId(llmToDetachIds, version)
                            .then(Mono.defer(() -> findAndDeleteOrphanedLlms(llmToDetachIds, version, context)));
                })
                .onErrorResume(error -> {
                    log.error(ERROR_DELETING_LLM_RAIL, version.getUid(), error);
                    String errorMessage =
                            String.format(ERROR_DELETING_LLM, version.getExternalId(), error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return Mono.empty();
                });
    }


    private Set<Integer> identifyLlmToDetach(
            Set<AiAgentLlmAssociation> associations,
            Set<String> llmToRetainIds) {

        return associations.stream()
                .map(AiAgentLlmAssociation::getLlm)
                .filter(llm -> !llmToRetainIds.contains(llm.getExternalId()))
                .map(AiAgentLlm::getUid)
                .collect(Collectors.toSet());
    }

    private Mono<Void> findAndDeleteOrphanedLlms(
            Set<Integer> llmToDetachIds,
            AiAgentVersion version,
            BedrockSyncContext context) {

        return Mono.fromCallable(() -> _llmAssociationRepo.findLlmIdsWithNoAssociations(llmToDetachIds))
                .flatMap(orphanedLlms -> {
                    if (orphanedLlms.isEmpty()) {
                        return Mono.empty();
                    }
                    return deleteLlmsById(orphanedLlms)
                            .doOnSuccess(deletedCount -> updateDeletionStats(deletedCount, version, context))
                            .then();
                })
                .subscribeOn(Schedulers.boundedElastic());

    }


    private void updateDeletionStats(int deletedCount, AiAgentVersion version, BedrockSyncContext context) {
        _ctxAccessor.getOrCrtVerStats(version, context)
                .getChildrenDeleted()
                .addAndGet(deletedCount);
    }


    private void associateLlmWithVersion(AiAgentVersion version, AiAgentLlm llm) {
        log.info("Creating associations between version {} and llm {}", version.getUid(), llm.getUid());
        AiAgentLlmAssociation assoc = LLM_ASSOC_MAPPER.associateWithVersion(version, llm);
        _llmAssociationRepo.save(assoc);
        log.info("Finished creating associations between version {} and llm {}", version.getUid(), llm.getUid());
    }


    private Optional<AiAgentLlm> findLlm(String llmId, AiAgentProviderAccount account) {
        log.info("Searching for LLM {} for account {}", llmId, account.getProviderAccountName());
        Optional<AiAgentLlm> llm =  _llmRepo.findByExternalIdAndProviderAccount(llmId, account);
        log.info("Finished searching for LLM {} for account {}", llmId, account.getProviderAccountName());
        return llm;
    }

    private Mono<Set<AiAgentLlmAssociation>> getVersionAssociations(AiAgentVersion version) {
        log.info("Fetching llm associations for version {}", version.getUid());
        return Mono.fromCallable(() ->
                        _llmAssociationRepo.findByRelatedEntityUidAndRelatedEntityType(version.getUid(), VERSION))
                .doOnSuccess(unused -> log.info("Finished fetching llm associations for version {}", version.getUid()))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error);
    }

    private Mono<Void> deleteAssociationsByLlmIdsAndVersionId(Set<Integer> llmIds, AiAgentVersion version) {
        log.info("Deleting version {} llm associations with llms: {}", version.getUid(), llmIds);
        return Mono.fromRunnable(() -> _llmAssociationRepo.deleteByLlmUidInAndRelatedEntityUidAndRelatedEntityType(
                        llmIds,
                        version.getUid(),
                        VERSION.name()
                ))
                .doOnSuccess(unused ->
                        log.info(
                                "Finished deleting version {} llm associations with llms: {}",
                                version.getUid(), llmIds)
                )
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error)
                .then();
    }

    private Mono<Integer> deleteLlmsById(Set<Integer> llmIds) {
        log.info("Deleting llms: {}", llmIds);
        return Mono.fromCallable(() -> _llmRepo.deleteLlmWithLock(llmIds))
                .doOnSuccess(unused -> log.info("Finish deleting llms: {}", llmIds))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error);
    }

    private static boolean isInferenceProfile(String modelIdentifier) {
        if (modelIdentifier == null || modelIdentifier.isEmpty()) {
            return false;
        }
        try {
            return modelIdentifier.contains(ARN);
        } catch (Exception e) {
            log.error(INFERENCE_PROFILE_PATTERN_ERROR, modelIdentifier, e);
            return false;
        }
    }

    private String extractFoundationModelName(String modelArn, AiAgentVersion version, BedrockSyncContext context) {
        if (modelArn == null || modelArn.isEmpty()) {
            return StringUtils.EMPTY;
        }

        try {
            int index = modelArn.lastIndexOf(FOUNDATION_MODEL_PREFIX);
            if (index != -1) {
                String modelName = modelArn.substring(index + FOUNDATION_MODEL_PREFIX.length());
                //remove the inference arn from the list of processed llms in context and replace it with the
                // extract model name
                _ctxAccessor.getOrCrtVerStats(version, context).getToRetainLlmIds().remove(modelArn);
                _ctxAccessor.getOrCrtVerStats(version, context).getToRetainLlmIds().add(modelName);

                return modelName;
            }
            return modelArn;
        } catch (Exception e) {
            log.warn(ERROR_EXTRACTING_FOUNDATION_MODEL, modelArn, e);
            return modelArn;
        }
    }
}
