// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Component
@SuperBuilder(toBuilder = true)
@NoArgsConstructor
@Data
@Accessors(prefix = "_")
public abstract class SyncStats {

    public static final String LINE_BREAK = "\n\n";
    private AtomicInteger _entityUid = new AtomicInteger(0);
    private AtomicInteger _childrenAdded = new AtomicInteger(0);
    private AtomicInteger _childrenUpdated = new AtomicInteger(0);
    private AtomicInteger _childrenDeleted = new AtomicInteger(0);
    private AtomicBoolean _hasFailure = new AtomicBoolean(false);
    private AtomicBoolean _hasSuccess = new AtomicBoolean(false);
    private AtomicBoolean _exceededLimit = new AtomicBoolean(false);
    private Set<String> _errorMessage = new HashSet<>();
    private String _externalEntityId = null;
    private AiRegistryEntityType _entityType;

    public void combineError(String errorMessage) {
        if (errorMessage != null && StringUtils.isNotEmpty(errorMessage)) {
            errorMessage = errorMessage.trim();
            _errorMessage.add(errorMessage);
        }
    }

    public String getErrorMessage() {
        if (_errorMessage.isEmpty()) {
            return null;
        }
        return String.join(LINE_BREAK, _errorMessage);
    }


}
