// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.boomigarden;

import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.exception.SyncException;
import com.boomi.aiagentregistry.service.sync.model.GardenAgentSyncStats;
import com.boomi.aiagentregistry.service.sync.model.GardenProviderAcctSyncStats;
import com.boomi.aiagentregistry.service.sync.model.GardenSyncContext;
import com.boomi.aiagentregistry.service.sync.model.GardenTaskSyncStats;
import com.boomi.aiagentregistry.service.sync.model.GardenVersionSyncStats;
import com.boomi.aiagentregistry.service.sync.model.SyncStats;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

@Component
public class GardenSyncContextAccessor {

    public static final String UNKNOWN_SYNC_STATS_TYPE = "Unknown SyncStats type";

    public GardenVersionSyncStats getOrCrtVerStats(String versionId, String agentExternalId,
            GardenSyncContext context) {
        GardenAgentSyncStats agentStats = context.getProviderAcctStats().getOrCreateAgentStats(agentExternalId);
        return agentStats.getOrCreateVersionStats(versionId);
    }


    public GardenTaskSyncStats getOrCrtTaskStats(AiAgentVersion agentVersion,
            String taskId, GardenSyncContext context) {
        GardenVersionSyncStats versionStats =
                getOrCrtVerStats(agentVersion.getGuid(), agentVersion.getAgent().getExternalId(), context);
        return versionStats.getOrCreateTaskStats(taskId);
    }

    public GardenAgentSyncStats getOrCrtAgntStats(AiAgent agent, GardenSyncContext context) {
        return getOrCrtAgntStats(agent.getExternalId(), context);
    }

    public GardenAgentSyncStats getOrCrtAgntStats(com.boomi.gardenagents.model.AgentSummary summary,
            GardenSyncContext context) {
        return getOrCrtAgntStats(summary.getId(), context);
    }

    public GardenAgentSyncStats getOrCrtAgntStats(String agentExternalId, GardenSyncContext context) {
        return context.getProviderAcctStats().getOrCreateAgentStats(agentExternalId);
    }

    public GardenProviderAcctSyncStats getProviderAcctStats(GardenSyncContext context) {
        return context.getProviderAcctStats();
    }

    public AiAgentProviderAccount getPrvAcct(GardenSyncContext context) {
        return context.getProviderAccount();
    }


    public boolean hasSuccess(SyncStats stats) {
        if (stats instanceof GardenTaskSyncStats taskStats) {
            return taskHasSuccess(taskStats);
        } else if (stats instanceof GardenVersionSyncStats verStats) {
            return versionHasSuccess(verStats);
        } else if (stats instanceof GardenAgentSyncStats agentStats) {
            return agentHasSuccess(agentStats);
        } else if (stats instanceof GardenProviderAcctSyncStats prvActStats) {
            return prvActHasSuccess(prvActStats);
        }
        throw new SyncException(UNKNOWN_SYNC_STATS_TYPE);
    }

    public boolean hasFailure(SyncStats stats) {
        if (stats instanceof GardenTaskSyncStats taskStats) {
            return taskHasFailure(taskStats);
        } else if (stats instanceof GardenVersionSyncStats verStats) {
            return versionHasFailure(verStats);
        } else if (stats instanceof GardenAgentSyncStats agentStats) {
            return agentHasFailure(agentStats);
        } else if (stats instanceof GardenProviderAcctSyncStats prvActStats) {
            return prvActHasFailure(prvActStats);
        }
        throw new SyncException(UNKNOWN_SYNC_STATS_TYPE);
    }

    private int getAddedEntities(SyncStats stat) {
        if (stat == null) {
            return 0;
        }

        if (stat instanceof GardenProviderAcctSyncStats providerAcctSyncStats) {
            return countPrvAcctAddedEntities(providerAcctSyncStats);
        } else if (stat instanceof GardenAgentSyncStats agentStats) {
            return countAgentAddedEntities(agentStats);
        } else if (stat instanceof GardenVersionSyncStats versionSyncStats) {
            return countVersionAddedEntities(versionSyncStats);
        } else if (stat instanceof GardenTaskSyncStats taskSyncStats) {
            return countTaskAddedEntities(taskSyncStats);
        }

        return 0;
    }

    private int getUpdatedEntities(SyncStats stat) {
        if (stat == null) {
            return 0;
        }
        if (stat instanceof GardenProviderAcctSyncStats providerAcctSyncStats) {
            return countPrvAcctUpdatedEntities(providerAcctSyncStats);
        } else if (stat instanceof GardenAgentSyncStats agentStats) {
            return countAgentUpdatedEntities(agentStats);
        } else if (stat instanceof GardenVersionSyncStats versionSyncStats) {
            return countVersionUpdatedEntities(versionSyncStats);
        } else if (stat instanceof GardenTaskSyncStats taskSyncStats) {
            return countTaskUpdatedEntities(taskSyncStats);
        }

        return 0;
    }

    private int getDeletedEntities(SyncStats stat) {
        if (stat == null) {
            return 0;
        }

        if (stat instanceof GardenProviderAcctSyncStats providerAcctSyncStats) {
            return countPrvAcctDeletedEntities(providerAcctSyncStats);
        } else if (stat instanceof GardenAgentSyncStats agentStats) {
            return countAgentDeletedEntities(agentStats);
        } else if (stat instanceof GardenVersionSyncStats versionSyncStats) {
            return countVersionDeletedEntities(versionSyncStats);
        } else if (stat instanceof GardenTaskSyncStats taskSyncStats) {
            return countTaskDeletedEntities(taskSyncStats);
        }

        return 0;
    }

    public Set<String> getCombinedError(SyncStats stats) {
        Set<String> combinedMessage;

        if (stats instanceof GardenTaskSyncStats taskStats) {
            combinedMessage = getTaskError(taskStats);
        } else if (stats instanceof GardenVersionSyncStats verStats) {
            combinedMessage = getVersionError(verStats);
        } else if (stats instanceof GardenAgentSyncStats agentStats) {
            combinedMessage = getAgentError(agentStats);
        } else if (stats instanceof GardenProviderAcctSyncStats prvActStats) {
            combinedMessage = getProviderAcctError(prvActStats);
        } else {
            throw new SyncException(UNKNOWN_SYNC_STATS_TYPE);
        }

        return combinedMessage;
    }

    private Set<String> getProviderAcctError(GardenProviderAcctSyncStats stats) {
        Set<String> errorMessage = new HashSet<>();
        String initialError = stats.getErrorMessage();

        if (initialError != null && !initialError.trim().isEmpty()) {
            errorMessage.add(initialError);
        }

        stats.getAgentsStats()
                .values()
                .stream()
                .map(this::getAgentError).filter(Objects::nonNull).flatMap(Set::stream).forEach(errorMessage::add);
        return errorMessage;
    }

    private Set<String> getAgentError(GardenAgentSyncStats stats) {
        Set<String> errorMessage = new HashSet<>();
        String initialError = stats.getErrorMessage();

        if (initialError != null && !initialError.trim().isEmpty()) {
            errorMessage.add(initialError);
        }

        stats.getVersionStatus()
                .values()
                .stream().map(this::getVersionError).filter(Objects::nonNull).flatMap(Set::stream)
                .forEach(errorMessage::add);
        return errorMessage;
    }

    private Set<String> getVersionError(GardenVersionSyncStats stats) {
        Set<String> errorMessage = new HashSet<>();
        String initialError = stats.getErrorMessage();

        if (initialError != null && !initialError.trim().isEmpty()) {
            errorMessage.add(initialError.trim());
        }

        stats.getTaskStatus().values().stream().map(this::getTaskError).filter(Objects::nonNull).flatMap(Set::stream)
                .forEach(errorMessage::add);
        return errorMessage;
    }

    private boolean versionHasSuccess(GardenVersionSyncStats stats) {
        if (stats.getHasSuccess().get()) {
            return true;
        }

        return stats.getTaskStatus()
                .values()
                .stream()
                .anyMatch(this::taskHasSuccess);
    }

    private Set<String> getTaskError(GardenTaskSyncStats stats) {
        Set<String> errorMessages = new HashSet<>();

        String errorMessage = stats.getErrorMessage();
        if (errorMessage != null && !errorMessage.trim().isEmpty()) {
            errorMessages.add(errorMessage.trim());
        }

        return errorMessages;
    }

    private boolean taskHasSuccess(GardenTaskSyncStats stats) {
        return (stats.getHasSuccess().get());
    }

    private boolean agentHasSuccess(GardenAgentSyncStats stats) {
        if (stats.getHasSuccess().get()) {
            return true;
        }

        return stats.getVersionStatus()
                .values()
                .stream()
                .anyMatch(this::versionHasSuccess);
    }

    private boolean prvActHasSuccess(GardenProviderAcctSyncStats stats) {
        if (stats.getHasSuccess().get()) {
            return true;
        }

        return stats.getAgentsStats()
                .values()
                .stream()
                .anyMatch(this::agentHasSuccess);
    }

    private boolean versionHasFailure(GardenVersionSyncStats stats) {
        return (stats.getHasFailure().get());
    }

    private boolean taskHasFailure(GardenTaskSyncStats stats) {
        return (stats.getHasFailure().get());
    }

    private boolean agentHasFailure(GardenAgentSyncStats stats) {
        if (stats.getHasFailure().get()) {
            return true;
        }

        return stats.getVersionStatus()
                .values()
                .stream()
                .anyMatch(this::versionHasFailure);
    }

    private boolean prvActHasFailure(GardenProviderAcctSyncStats stats) {
        if (stats.getHasFailure().get()) {
            return true;
        }

        return stats.getAgentsStats()
                .values()
                .stream()
                .anyMatch(this::agentHasFailure);
    }

    private int countTaskUpdatedEntities(GardenTaskSyncStats taskSyncStats) {
        int totalToolsUpdatedCount = taskSyncStats.getChildrenUpdated().get();
        return totalToolsUpdatedCount;
    }

    private int countVersionUpdatedEntities(GardenVersionSyncStats versionSyncStats) {
        int totalTaskUpdated = versionSyncStats.getChildrenUpdated().get();
        int totalToolsUpdated =
                versionSyncStats.getTaskStatus().values().stream().mapToInt(this::countTaskUpdatedEntities).sum();
        return totalTaskUpdated + totalToolsUpdated;
    }

    private int countAgentUpdatedEntities(GardenAgentSyncStats agentStats) {

        int totalVersionsCount = agentStats.getChildrenUpdated().get();

        int totalTaskCount =
                agentStats.getVersionStatus().values().stream().mapToInt(this::countVersionUpdatedEntities).sum();

        return totalVersionsCount + totalTaskCount;
    }

    private int countPrvAcctUpdatedEntities(GardenProviderAcctSyncStats providerAcctSyncStats) {
        int totalAgentCount = providerAcctSyncStats.getChildrenUpdated().get();

        int totalVersionAndTaskCount =
                providerAcctSyncStats.getAgentsStats().values().stream().mapToInt(this::countAgentUpdatedEntities)
                        .sum();

        return totalAgentCount + totalVersionAndTaskCount;
    }

    private int countTaskDeletedEntities(GardenTaskSyncStats taskSyncStats) {
        int totalToolsDeletedCount = taskSyncStats.getChildrenDeleted().get();
        return totalToolsDeletedCount;
    }

    private int countVersionDeletedEntities(GardenVersionSyncStats versionSyncStats) {
        int totalTaskDeleted = versionSyncStats.getChildrenDeleted().get();
        return totalTaskDeleted;
    }

    private int countAgentDeletedEntities(GardenAgentSyncStats agentStats) {

        int totalVersionsDeleted = agentStats.getChildrenDeleted().get();

        int totaltasksDeleted =
                agentStats.getVersionStatus().values().stream().mapToInt(this::countVersionDeletedEntities).sum();

        return totalVersionsDeleted + totaltasksDeleted;
    }

    private int countPrvAcctDeletedEntities(GardenProviderAcctSyncStats providerAcctSyncStats) {
        int totalAgentsDeleted = providerAcctSyncStats.getAgentsDeleted().get();

        int totalVersionsAndTasksDeleted = providerAcctSyncStats.getAgentsStats().values().stream().mapToInt(
                this::countAgentDeletedEntities).sum();

        return totalAgentsDeleted + totalVersionsAndTasksDeleted;
    }

    private int countVersionAddedEntities(GardenVersionSyncStats versionSyncStats) {
        int totalTasksAdded = versionSyncStats.getChildrenAdded().get();
        int totalToolsAddedInTasks =
                versionSyncStats.getTaskStatus().values().stream().mapToInt(this::countTaskAddedEntities).sum();
        return totalTasksAdded + totalToolsAddedInTasks;
    }

    private int countTaskAddedEntities(GardenTaskSyncStats taskSyncStats) {
        int toolsCount = taskSyncStats.getChildrenAdded().get();
        return toolsCount;
    }

    private int countAgentAddedEntities(GardenAgentSyncStats agentStats) {
        int totalVersionAdded = agentStats.getChildrenAdded().get();
        int totalTasksTools = agentStats.getVersionStatus().values().stream().mapToInt(
                this::countVersionAddedEntities).sum();
        return totalVersionAdded + totalTasksTools;
    }

    private int countPrvAcctAddedEntities(GardenProviderAcctSyncStats providerAcctSyncStats) {
        int totalAgentsAdded = providerAcctSyncStats.getAgentsAdded().get();

        // The following line will get count for all grandchildren(version) and great grandchildren (task)
        int totalVersionTasksTools = providerAcctSyncStats.getAgentsStats().values().stream().mapToInt(
                this::countAgentAddedEntities).sum();

        return totalAgentsAdded + totalVersionTasksTools;
    }

    public boolean exceededLimit(SyncStats stats) {
        return stats.getExceededLimit().get();
    }

    public record EntityCounts(int added, int updated, int deleted) {
    }

    public EntityCounts getAllCounts(SyncStats stat) {
        return new EntityCounts(
                getAddedEntities(stat),
                getUpdatedEntities(stat),
                getDeletedEntities(stat)
        );
    }
}
