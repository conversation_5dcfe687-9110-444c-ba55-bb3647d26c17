// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.bedrock;

import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentLargeTextContent;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.repo.AiAgentLargeTextContentRepository;
import com.boomi.aiagentregistry.repo.AiAgentVersionRepository;
import com.boomi.aiagentregistry.service.sync.SyncContextAccessor;
import com.boomi.aiagentregistry.service.sync.SyncHistoryService;
import com.boomi.aiagentregistry.service.sync.model.BedrockSyncContext;
import com.boomi.aiagentregistry.service.sync.model.BedrockVersionSyncStats;
import com.boomi.aiagentregistry.service.sync.model.SyncEntityPointInTimeInfo;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GeneralUtil;
import com.boomi.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import software.amazon.awssdk.services.bedrockagent.model.ActionGroupSummary;
import software.amazon.awssdk.services.bedrockagent.model.Agent;
import software.amazon.awssdk.services.bedrockagent.model.AgentKnowledgeBaseSummary;
import software.amazon.awssdk.services.bedrockagent.model.AgentVersion;
import software.amazon.awssdk.services.bedrockagent.model.AgentVersionSummary;
import software.amazon.awssdk.services.bedrockagent.model.GetAgentRequest;
import software.amazon.awssdk.services.bedrockagent.model.GetAgentVersionRequest;
import software.amazon.awssdk.services.bedrockagent.model.GuardrailConfiguration;
import software.amazon.awssdk.services.bedrockagent.model.ListAgentActionGroupsRequest;
import software.amazon.awssdk.services.bedrockagent.model.ListAgentKnowledgeBasesRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.AGENT_VERSION_STATUS_DRAFT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_DELETE_VERSION_WITH_ID;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_DELETING_UNPROCESSED_CHILDREN;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_FETCHING_ACTION_GROUP;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_FETCHING_AGENT_DETAILS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_FETCH_VERSION_DETAILS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_LIST_ACTION_GROUPS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_PROCESSING_ACTION_GROUP;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_PROCESSING_VERSION_CHILD_ENTITIES;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_UPDATE_VERSION;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FAILED_TO_CREATE_VERSION_FOR_AGENT;
import static com.boomi.aiagentregistry.mapper.AiAgentVersionMapper.AI_AGENT_VERSION_MAPPER;
import static com.boomi.aiagentregistry.mapper.LargeTextContentMapper.LRG_TXT_MAPPER;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.VERSION;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class VersionSyncHandler extends BedrockSyncHandler {

    public static final String FINISHED_PROCESSING_VERSION_OF_AGENT_FOR_ACCOUNT =
            "Finished processing version {} of agent {} for account {}";
    private final AiAgentVersionRepository _versionRepository;
    private final ActGrpSyncHandler _actionGroupHandler;
    private final KbSyncHandler _kbHandler;
    private final GuardrailSyncHandler _guardrailHandler;
    private final LlmSyncHandler _llmHandler;
    private final AiAgentLargeTextContentRepository _largeTextContentRepository;

    private static final String ERROR_CREATING_VERSION =
            "Error creating new version for agent with agent external ID: %s \n The error message from amazon is %s";

    private static final String ERROR_VERSION_SYNC =
            "Error syncing with Bedrock for version with ID: %s \n The error message from amazon is %s";
    private static final String ERROR_PROCESSING_KNOWLEDGE_BASE =
            "Error processing KB for version with version ID: %s \n The error message from amazon is %s";

    private static final String SYNC_ERROR_PROCESSING_ACTION_GROUP =
            "Error processing Action group for version with version ID: %s \n The error message from amazon is %s";

    private static final String SYNC_ERROR_PROCESSING_GUARDRAIL =
            "Error processing Guardrail for version with version ID: %s \n The error message from amazon is %s";

    private static final String SYNC_ERROR_PROCESSING_LLM =
            "Error processing LLM for version with version ID: %s \n The error message from amazon is %s";

    private static final String SYNC_ERROR_UPDATE_VERSION =
            "Error saving to DB for version with  ID as: %s \n The error message from amazon is %s";

    private static final String SYNC_ERROR_DELETING_UNPROCESSED_CHILDREN = "Error deleting unprocessed children for " +
                    "version with version ID: %s \n The error message from amazon is %s";
    private static final String ERROR_FETCHING_KNOWLEDGE_BASES =
            "Error fetching knowledge bases for version {} of agent {}.";

    public VersionSyncHandler(
            AiAgentVersionRepository versionRepository,
            AwsBedrockService awsBedrockService,
            ActGrpSyncHandler actionGroupHandler,
            SyncHistoryService syncHistoryService,
            SyncContextAccessor ctxAccessor,
            KbSyncHandler kbHandler,
            GuardrailSyncHandler guardrailHandler,
            LlmSyncHandler llmHandler,
            AiAgentLargeTextContentRepository largeTextContentRepository) {
        super(syncHistoryService, ctxAccessor, awsBedrockService);

        _versionRepository = versionRepository;
        _actionGroupHandler = actionGroupHandler;
        _kbHandler = kbHandler;
        _guardrailHandler = guardrailHandler;
        _llmHandler = llmHandler;
        _largeTextContentRepository = largeTextContentRepository;
    }


    public Mono<AiAgentVersion> processVersion(
            AiAgent agent,
            AgentVersionSummary versionSummary,
            BedrockSyncContext context) {

        initSyncData(agent, versionSummary, context);

        log.info("Searching for version {} of agent {} for account {} ",
                versionSummary.agentVersion(),
                agent.getUid(),
                context.getProviderAccount().getProviderAccountName());

        Optional<AiAgentVersion> versionOptional = getVersionReactive(agent.getUid(), versionSummary.agentVersion());
        log.info("Finished searching for version {} of agent {} for account {}",
                versionSummary.agentVersion(),
                agent.getUid(),
                context.getProviderAccount().getProviderAccountName());


        log.info("Processing version {} of agent {} for account {}",
                versionSummary.agentVersion(), agent.getUid(),
                context.getProviderAccount().getProviderAccountName());

        return versionOptional
                .map(aiAgentVersion ->
                        handleExistingVersion(agent, aiAgentVersion, versionSummary, context)
                                .doOnSuccess(logOnSuccess(agent, versionSummary, context))
                )
                .orElseGet(() ->
                        handleNewVersion(agent, versionSummary, context)
                                .doOnSuccess(logOnSuccess(agent, versionSummary, context))
                );
    }

    private static  Consumer<AiAgentVersion> logOnSuccess(
            AiAgent agent,
            AgentVersionSummary versionSummary,
            BedrockSyncContext context) {
        return unused ->
                log.info(FINISHED_PROCESSING_VERSION_OF_AGENT_FOR_ACCOUNT,
                        versionSummary.agentVersion(), agent.getUid(),
                        context.getProviderAccount().getProviderAccountName());
    }

    private Mono<AiAgentVersion> handleExistingVersion(
            AiAgent agent,
            AiAgentVersion version,
            AgentVersionSummary summary,
            BedrockSyncContext context) {

        if (shouldUpdateEntity(summary.updatedAt(), version)) {
            updateUpdateStats(_ctxAccessor.getOrCrtAgntStats(agent, context));
        }

        return syncVersionAndChildren(agent, version, summary, false, context);
    }

    private Mono<AiAgentVersion> handleNewVersion(
            AiAgent agent,
            AgentVersionSummary versionSummary,
            BedrockSyncContext context) {

        updateAddStats(_ctxAccessor.getOrCrtAgntStats(agent, context));

        return createVersion(versionSummary, agent, context)
                .flatMap(newVersion ->
                        syncVersionAndChildren(agent, newVersion, versionSummary, true, context));
    }

    private Mono<AiAgentVersion> syncVersionAndChildren(
            AiAgent agent,
            AiAgentVersion dbVersion,
            AgentVersionSummary versionSummary,
            boolean isNew,
            BedrockSyncContext context
    ) {
        SyncEntityPointInTimeInfo entityInfo = SyncEntityPointInTimeInfo.builder()
                                                        .entityUid(dbVersion.getUid())
                                                        .entityType(VERSION)
                                                        .entityGuid(dbVersion.getGuid())
                                                        .entityName(versionSummary.agentName())
                                                        .entityVersion(dbVersion.getVersionString())
                                                        .build();

        return createSyncUserAudit(entityInfo, context)

                .flatMap(syncUserAudit -> syncVersionWithBedrock(agent, dbVersion, versionSummary, isNew, context)

                        .flatMap(updatedVersion -> processChildEntities(agent, dbVersion, context)

                                .then(Mono.defer(() -> deleteChildrenExcept(dbVersion, context)))

                                .then(Mono.defer(() -> handleSyncCompletion(syncUserAudit, dbVersion, context)))

                                .onErrorResume(unused -> {
                                    log.error(
                                            ERROR_PROCESSING_VERSION_CHILD_ENTITIES,
                                            versionSummary.agentVersion(),
                                            agent.getUid()
                                    );
                                    return handleSyncCompletion(syncUserAudit, dbVersion, context)
                                            .then(Mono.empty());
                                })
                        )
                )
                .thenReturn(dbVersion);
    }

    private Mono<AiAgentVersion> createVersion(
            AgentVersionSummary versionSummary,
            AiAgent agent,
            BedrockSyncContext context) {

        AiAgentProviderAccount providerAccount = context.getProviderAccount();

        AiAgentVersion newVersion =
                AI_AGENT_VERSION_MAPPER.toEntityFromAwsVersionSummary(versionSummary, agent, providerAccount);
        log.info("Creating new version {} for agent {} for account {}",
                versionSummary.agentVersion(),
                agent.getUid(),
                providerAccount.getProviderAccountName());
        return saveReactive(newVersion)
                .doOnSuccess(unused -> {
                    log.info("Finished creating new version {} for agent {} for account {}",
                            versionSummary.agentVersion(),
                            agent.getUid(),
                            providerAccount.getProviderAccountName());
                })
                .onErrorResume(error -> {
                    log.error(FAILED_TO_CREATE_VERSION_FOR_AGENT, agent.getUid(), error);
                    String errorMessage =
                            String.format(ERROR_CREATING_VERSION, agent.getExternalId(), error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(agent, versionSummary, context));
                    return Mono.empty();
                })
                .onErrorResume(Mono::error);

    }

    private Mono<AiAgentVersion> syncVersionWithBedrock(
            AiAgent agent,
            AiAgentVersion dbVersion,
            AgentVersionSummary versionSummary,
            boolean isNew,
            BedrockSyncContext context) {
        BedrockVersionSyncStats versionSyncStats = _ctxAccessor.getOrCrtVerStats(agent, versionSummary, context);
        return fetchVersionDetails(dbVersion, context)
                .flatMap(awsVersion -> {
                    // Store version details in context for later use
                    _ctxAccessor.storeVersionDetails(agent, awsVersion, context);

                    if (!shouldUpdateEntity(versionSummary.updatedAt(), dbVersion) && !isNew) {
                        return Mono.just(dbVersion);
                    }
                    AiAgentProviderAccount account = context.getProviderAccount();
                    AI_AGENT_VERSION_MAPPER.toEntityFromAwsAgentVersion(dbVersion, awsVersion, account);

                    String dbAction = isNew? "Creating new version ": "Updating existing version ";
                    log.info("{} {} with bedrock info for agent {} of account {}",
                            dbAction,
                            versionSummary.agentVersion(),
                            agent.getExternalId(),
                            account.getProviderAccountName());
                    Mono<AiAgentVersion> dbSavedVersion = saveToDb(agent, dbVersion, versionSummary, context)
                            .doOnSuccess(unused ->
                                    log.info("Finished {} {} with bedrock info for agent {} of account {}",
                                    dbAction,
                                    versionSummary.agentVersion(),
                                    agent.getExternalId(),
                                    account.getProviderAccountName()))
                            .flatMap(savedVersion -> saveVersionInstructions(savedVersion, awsVersion.instruction()));
                    if (isNew) {
                        updateAddStats(versionSyncStats);
                    } else {
                        updateUpdateStats(versionSyncStats);
                    }
                    return dbSavedVersion;
                })
                .onErrorResume(error -> {
                    String errorMessage =
                            String.format(ERROR_VERSION_SYNC, dbVersion.getExternalId(), error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(agent, versionSummary, context));
                    return Mono.empty();
                });
    }

    private Mono<AiAgentVersion> saveVersionInstructions(AiAgentVersion savedVersion, String instructions) {
        if (StringUtil.isBlank(instructions)) {
            return Mono.just(savedVersion);
        }

        AiAgentLargeTextContent instructionsContent = LRG_TXT_MAPPER
                .createLargeTextContent(savedVersion.getUid(), VERSION.name(), instructions);
        log.info("Saving instructions for version {} of agent {}", savedVersion.getUid(),savedVersion.getExternalId());
        return saveLargeTextContent(instructionsContent, savedVersion)
                .doOnSuccess(unused ->
                        log.info("Finished saving instructions for version {} of agent {}",
                        savedVersion.getUid(),
                        savedVersion.getExternalId()))
                .thenReturn(savedVersion);
    }


    private Mono<Void> processChildEntities(AiAgent agent, AiAgentVersion version, BedrockSyncContext context) {
        return Flux.concat(
                        processKbs(version, context),
                        processActionGroups(version, context),
                        processGuardrails(agent, version, context),
                        processLlms(agent, version, context)
                )
                .then();
    }

    private Mono<Void> processActionGroups(AiAgentVersion version, BedrockSyncContext context) {
        return listActionGroupRecursively(
                        version,
                        context)
                .flatMap(actionGroupSummary -> processActionGroup(version, context, actionGroupSummary))
                .onErrorResume(error -> {
                    String errorMessage =
                            "Error processing action groups with version external ID as" + version.getExternalId() +
                                    " and entity type as" + VERSION + " the error message from amazon is " +
                                    error.getMessage();
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return Mono.empty();
                })
                .then();
    }

    private Mono<Void> processKbs(AiAgentVersion version, BedrockSyncContext context) {

        BedrockVersionSyncStats versionStats = _ctxAccessor.getOrCrtVerStats(version, context);
        return listKnowledgeBaseRecursively(version, context)
                .flatMap(kbSummary -> {
                    versionStats.getToRetainKbIds().add(kbSummary.knowledgeBaseId());
                    _ctxAccessor.getOrCrtVerStats(version, context).getToRetainKbIds().add(kbSummary.knowledgeBaseId());

                    return _kbHandler.processKnowledgeBase(version, kbSummary, context);
                })

                .onErrorResume(error -> {
                    String errorMessage =
                            String.format(ERROR_PROCESSING_KNOWLEDGE_BASE, version.getExternalId(), error.getMessage());
                    updateFailureStatus(errorMessage, versionStats);
                    return Mono.empty();
                })
                .then();
    }

    private Mono<AiAgentTool> processActionGroup(
            AiAgentVersion version,
            BedrockSyncContext context,
            ActionGroupSummary actionGroupSummary) {

        _ctxAccessor.getOrCrtVerStats(version, context).getToRetainActGrpIds().add(actionGroupSummary.actionGroupId());

        return _actionGroupHandler.processActionGroup(actionGroupSummary, version, context)

                .onErrorResume(error -> {
                    log.error(ERROR_PROCESSING_ACTION_GROUP, actionGroupSummary.actionGroupId(), error);
                    String errorMessage = String.format(SYNC_ERROR_PROCESSING_ACTION_GROUP, version.getExternalId(),
                            error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return Mono.empty();
                });
    }

    private Mono<Void> processGuardrails(AiAgent agent, AiAgentVersion version, BedrockSyncContext context) {

        Optional<AgentVersion> versionDetailsOpt = getAgentVersionDetailsForGuardrail(agent, version, context);

        return versionDetailsOpt.map(agentVersion ->
                _guardrailHandler.processGuardrail(version, agentVersion.guardrailConfiguration(), context)
                        .onErrorResume(error -> {
                            String errorMessage =
                                    String.format(SYNC_ERROR_PROCESSING_GUARDRAIL, version.getExternalId(),
                                            error.getMessage());
                            updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                            return Mono.empty();
                        }).then()).orElseGet(Mono::empty);

    }

    private Mono<Void> processLlms(AiAgent agent, AiAgentVersion version, BedrockSyncContext context) {

        Optional<AgentVersion> versionDetailsOpt = getAgentVersionDetails(agent, version, context);

        if (versionDetailsOpt.isEmpty() || versionDetailsOpt.get().foundationModel() == null) {
            return Mono.empty();
        }
        AgentVersion versionDetails = versionDetailsOpt.get();
        _ctxAccessor.getOrCrtVerStats(version, context).getToRetainLlmIds().add(versionDetails.foundationModel());

        return _llmHandler.processLlm(version, versionDetails.foundationModel(), context)
                .onErrorResume(error -> {
                    String errorMessage =
                            String.format(SYNC_ERROR_PROCESSING_LLM, version.getExternalId(), error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return Mono.empty();
                }).then();
    }

    private Optional<AgentVersion> getAgentVersionDetails(
            AiAgent agent,
            AiAgentVersion version,
            BedrockSyncContext context) {
        return _ctxAccessor.getVersionDetails(agent, version, context);
    }

    /*get version details which contains guard rail details from context, however if the version is not updatable
        because it is a non draft version and up to date(we don't make call to get details in that case) then fetch the
        guardrail id and version from db and call bedrock in case it has been updated.
     */
    private Optional<AgentVersion> getAgentVersionDetailsForGuardrail(
            AiAgent agent,
            AiAgentVersion version,
            BedrockSyncContext context) {

        return getAgentVersionDetails(agent, version, context)
                .or(() -> _guardrailHandler.getGuardrailByVersion(version)
                        .map(guardrail -> AgentVersion.builder()
                                .guardrailConfiguration(
                                        GuardrailConfiguration.builder()
                                                .guardrailIdentifier(guardrail.getExternalId())
                                                .guardrailVersion(guardrail.getVersionString())
                                                .build()
                                )
                                .build()
                        )
                );
    }

    private Mono<AiAgentVersion> saveToDb(
            AiAgent agent, AiAgentVersion dbVersion,
            AgentVersionSummary versionSummary,
            BedrockSyncContext context) {

        return saveReactive(dbVersion)
                .onErrorResume(error -> {
                    log.error(ERROR_UPDATE_VERSION, dbVersion.getUid(), error);
                    String errorMessage =
                            String.format(SYNC_ERROR_UPDATE_VERSION, dbVersion.getExternalId(), error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(agent, versionSummary, context));
                    return Mono.error(error);
                });
    }

    public Mono<Void> deleteAllByAgentExcept(AiAgent agent, Set<String> versionsToRetain, BedrockSyncContext context) {

        return findByAgentReactive(agent)
                .flatMapIterable(versions -> versions)
                .filter(version -> !versionsToRetain.contains(version.getVersionString()))

                .flatMap(version -> deleteVersionAndChildren(agent, version, context))
                .then();

    }

    private Mono<Void> deleteVersionAndChildren(AiAgent agent, AiAgentVersion version, BedrockSyncContext context) {

        SyncEntityPointInTimeInfo entityInfo = SyncEntityPointInTimeInfo.builder()
                                                        .entityUid(version.getUid())
                                                        .entityType(VERSION)
                                                        .entityGuid(version.getGuid())
                                                        .entityName(version.getName())
                                                        .entityVersion(version.getVersionString())
                                                        .build();
        return createSyncUserAudit(entityInfo, context)

                .flatMap(versionSyncUserAudit -> deleteChildrenExcept(version, context)

                        .then(deleteVersion(agent, version, context))

                        .then(Mono.defer(() -> handleSyncCompletion(versionSyncUserAudit, version, context)))

                        .onErrorResume(DataAccessException.class, ex -> {
                            log.error(ERROR_DELETE_VERSION_WITH_ID, version.getUid(), ex);
                            return handleSyncCompletion(versionSyncUserAudit, version, context)
                                    .then(Mono.error(ex));
                        })).then();
    }

    private Mono<Void> deleteChildrenExcept(AiAgentVersion version, BedrockSyncContext context) {
        BedrockVersionSyncStats stats = _ctxAccessor.getOrCrtVerStats(version, context);

        Set<String> actGrpToRetain = _ctxAccessor.getOrCrtVerStats(version, context).getToRetainActGrpIds();
        Set<String> kbToRetain = _ctxAccessor.getOrCrtVerStats(version, context).getToRetainKbIds();
        Set<String> llmToRetain = _ctxAccessor.getOrCrtVerStats(version, context).getToRetainLlmIds();
        Set<GuardrailConfiguration> guardrailToRetain =
                _ctxAccessor.getOrCrtVerStats(version, context).getToRetainGuardrails();

        List<Mono<Void>> deleteOperations = new ArrayList<>();

        if (!stats.getFailedToFetchActionGroups().get()) {
            deleteOperations.add(_actionGroupHandler.deleteAllByVersionExcept(version, actGrpToRetain, context));
        }

        if (!stats.getFailedToFetchKbs().get()) {
            deleteOperations.add(_kbHandler.deleteAllByVersionExcept(version, kbToRetain, context));
        }

        deleteOperations.add(_llmHandler.deleteAllByVersionExcept(version, llmToRetain, context));
        deleteOperations.add(_guardrailHandler.deleteAllByVersionExcept(version, guardrailToRetain, context));

        return Flux.concat(deleteOperations)
                .then()
                .onErrorResume(error -> {
                    log.error(ERROR_DELETING_UNPROCESSED_CHILDREN, version.getUid(), error);
                    String errorMessage =
                            String.format(SYNC_ERROR_DELETING_UNPROCESSED_CHILDREN, version.getExternalId(),
                                    error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return Mono.empty();
                });

    }

    private Mono<Void> deleteVersion(AiAgent agent, AiAgentVersion version, BedrockSyncContext context) {
        log.info("Marking version {} for agent {} of account {} as deleted",
                version.getUid(),
                agent.getUid(),
                context.getProviderAccount().getProviderAccountName());
        version.setIsDeleted(true);
        version.setModifiedTime(AuditUtilImpl.getCurrentTime());
        return saveReactive(version)

                .flatMap(savedVersion -> {
                    log.info("Finished marking version {} for agent {} of account {} as deleted",
                            version.getUid(),
                            agent.getUid(),
                            context.getProviderAccount().getProviderAccountName());
                    updateDeleteStats(_ctxAccessor.getOrCrtAgntStats(agent, context));
                    return Mono.just(savedVersion);
                })
                .onErrorResume(DataAccessException.class, ex -> {
                    log.error(ERROR_DELETE_VERSION_WITH_ID, version.getUid(), ex);
                    return Mono.error(ex);
                }).then();

    }


    private void initSyncData(AiAgent agent, AgentVersionSummary versionSummary, BedrockSyncContext context) {
        _ctxAccessor.getOrCrtAgntStats(agent, context).getToRetainVerIds().add(versionSummary.agentVersion());
        _ctxAccessor.getOrCrtVerStats(agent, versionSummary, context);

    }

    private Mono<SyncUserAudit> handleSyncCompletion(
            SyncUserAudit versionSyncAudit,
            AiAgentVersion updatedVersion,
            BedrockSyncContext context) {

        BedrockVersionSyncStats stats = _ctxAccessor.getOrCrtVerStats(updatedVersion, context);
        return handleSyncCompletion(versionSyncAudit, stats);
    }

    private Optional<AiAgentVersion> getVersionReactive(Integer agentUid, String version) {
        return _versionRepository.findByAgentUidAndVersionString(agentUid, version, false);

    }


    private Mono<List<AiAgentVersion>> findByAgentReactive(AiAgent agent) {
        log.info("Fetching versions for agent {}", agent.getUid());
        return Mono.fromCallable(() -> _versionRepository.findByAgentAndIsDeletedFalse(agent))
                .doOnSuccess(unused -> log.info("Finished fetching versions for agent {}", agent.getUid()))
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(GeneralUtil::optionalToMono);
    }


    private Mono<AiAgentVersion> saveReactive(AiAgentVersion version) {
        return Mono.fromCallable(() -> _versionRepository.save(version))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error);
    }

    private Mono<AiAgentLargeTextContent> saveLargeTextContent(
            AiAgentLargeTextContent largeTextContent,
            AiAgentVersion savedVersion) {
        return Mono.fromCallable(() -> {
                    //delete existing instructions if any
                    _largeTextContentRepository
                            .deleteByRelatedEntityUidAndRelatedEntityType(savedVersion.getUid(), VERSION.name());
                    return _largeTextContentRepository.save(largeTextContent);
                })
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error);
    }

    private Flux<ActionGroupSummary> listActionGroupRecursively(
            AiAgentVersion version,
            BedrockSyncContext context) {

        return Flux.defer(() -> {
            ListAgentActionGroupsRequest request = ListAgentActionGroupsRequest.builder()
                    .agentId(version.getExternalId())
                    .agentVersion(version.getVersionString())
                    .build();
            return listActionGroupsWithPagination(request, context)
                    .onErrorResume(error -> {
                        _ctxAccessor.getOrCrtVerStats(version, context).getFailedToFetchActionGroups().set(true);
                        log.error(ERROR_FETCHING_ACTION_GROUP, version.getExternalId(), error);
                        return Mono.error(error);
                    });
        });
    }

    private Flux<ActionGroupSummary> listActionGroupsWithPagination(
            ListAgentActionGroupsRequest request,
            BedrockSyncContext context) {
        log.info("Listing action groups with pagination for version {} of agent: {}",
                request.agentVersion(),
                request.agentId());
        return Flux.create(sink ->
                context.getBedrockAgentClient()
                        .listAgentActionGroups(request)
                        .whenCompleteAsync((response, throwable) -> {
                            if (throwable != null) {
                                log.error(ERROR_LIST_ACTION_GROUPS, throwable);
                                sink.error(throwable);
                                return;
                            }

                            response.actionGroupSummaries().forEach(sink::next);

                            if (response.nextToken() != null) {
                                ListAgentActionGroupsRequest nextRequest = request.toBuilder()
                                        .nextToken(response.nextToken())
                                        .build();

                                listActionGroupsWithPagination(nextRequest, context)
                                        .subscribe(sink::next, sink::error, sink::complete);
                            } else {
                                log.info("Completed listing action groups with pagination for version {} of agent: {}",
                                        request.agentVersion(),
                                        request.agentId());
                                sink.complete();
                            }
                        })
        );
    }
    private Flux<AgentKnowledgeBaseSummary> listKnowledgeBaseRecursively(
            AiAgentVersion version,
            BedrockSyncContext context) {

        String agentId = version.getExternalId();
        String agentVersion = version.getVersionString();

        return Flux.defer(() -> {
            ListAgentKnowledgeBasesRequest request = ListAgentKnowledgeBasesRequest.builder()
                    .agentId(agentId)
                    .agentVersion(agentVersion)
                    .build();
            return listKnowledgeBaseWithPagination(request, agentId, agentVersion, context)
                    .onErrorResume(error -> {
                        _ctxAccessor.getOrCrtVerStats(version, context).getFailedToFetchKbs().set(true);
                        log.error(ERROR_FETCHING_KNOWLEDGE_BASES, agentVersion, agentId, error);
                        return Mono.error(error);
                    });
        });
    }
    private static Flux<AgentKnowledgeBaseSummary> listKnowledgeBaseWithPagination(
            ListAgentKnowledgeBasesRequest request,
            String agentId,
            String agentVersion,
            BedrockSyncContext context) {
        log.info("Listing knowledge bases with pagination for version {} of agent: {}", agentVersion, agentId);

        return Flux.create(sink ->
                context.getBedrockAgentClient()
                        .listAgentKnowledgeBases(request)
                        .whenCompleteAsync((response, throwable) -> {
                            if (throwable != null) {
                                log.error(ERROR_FETCHING_KNOWLEDGE_BASES, agentVersion, agentId, throwable);
                                sink.error(throwable);
                                return;
                            }

                            response.agentKnowledgeBaseSummaries().forEach(sink::next);

                            if (response.nextToken() != null) {
                                ListAgentKnowledgeBasesRequest nextRequest = request.toBuilder()
                                        .nextToken(response.nextToken())
                                        .build();

                                listKnowledgeBaseWithPagination(nextRequest, agentId, agentVersion, context)
                                        .subscribe(sink::next, sink::error, sink::complete);
                            } else {
                                log.info(
                                        "Completed listing knowledge bases with pagination for version {} of agent: {}",
                                        agentVersion,
                                        agentId);
                                sink.complete();
                            }
                        })
        );
    }

    private Mono<AgentVersion> fetchVersionDetails(AiAgentVersion version, BedrockSyncContext context) {
        if (AGENT_VERSION_STATUS_DRAFT.equalsIgnoreCase(version.getVersionString())) {
            return fetchAgentDetails(version, context)
                    .map(this::toAwsVersionFromAgent)
                    .onErrorResume(Mono::error);

        }
        log.info("Fetching version {} of agent {} details", version.getExternalId(), version.getVersionString());
        return Mono.create(sink -> {
            GetAgentVersionRequest request = GetAgentVersionRequest.builder()
                    .agentId(version.getExternalId())
                    .agentVersion(version.getVersionString())
                    .build();

            context.getBedrockAgentClient()
                    .getAgentVersion(request)
                    .whenCompleteAsync((response, throwable) -> {
                        if (throwable != null) {
                            log.error(ERROR_FETCH_VERSION_DETAILS, version.getExternalId(), throwable);
                            sink.error(throwable);
                            return;
                        }
                        log.info("Fetched version {} of agent {} details",
                                version.getExternalId(),
                                version.getVersionString());
                        sink.success(response.agentVersion());
                    });
        });
    }

    private AgentVersion toAwsVersionFromAgent(Agent agent) {
        return AI_AGENT_VERSION_MAPPER.fromAwsAgent(agent);
    }

    private Mono<Agent> fetchAgentDetails(AiAgentVersion version, BedrockSyncContext context) {
        log.info("Fetching agent {} details", version.getExternalId());

        return Mono.create(sink -> {
            GetAgentRequest request = GetAgentRequest.builder()
                    .agentId(version.getExternalId())
                    .build();

            context.getBedrockAgentClient()
                    .getAgent(request)
                    .whenCompleteAsync((response, throwable) -> {
                        if (throwable != null) {
                            log.error(ERROR_FETCHING_AGENT_DETAILS, version.getExternalId(), throwable);
                            sink.error(throwable);
                            return;
                        }
                        log.info("Fetched agent {} details", version.getExternalId());

                        sink.success(response.agent());
                    });
        });
    }
}




