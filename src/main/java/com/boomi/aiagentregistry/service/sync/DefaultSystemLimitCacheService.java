// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.sync;


import com.boomi.aiagentregistry.aop.ReactiveLogging;
import com.boomi.aiagentregistry.entity.AiAgentRegistryLicense;
import com.boomi.aiagentregistry.repo.AiRegistryLicenseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@ReactiveLogging
@Slf4j
@Service
public class DefaultSystemLimitCacheService {
    private final AiRegistryLicenseRepository _licenseRepo;
    private final ConcurrentHashMap<Integer, AiAgentRegistryLicense> _systemDefaultLimitCache;

    public DefaultSystemLimitCacheService(AiRegistryLicenseRepository aiAgentRegistryLicense) {
        _licenseRepo = aiAgentRegistryLicense;
        _systemDefaultLimitCache = new ConcurrentHashMap<>();
    }

    public void loadDefaultSystemLimitCache() {
        log.info("Loading default system limits into cache");

        List<AiAgentRegistryLicense> limits = _licenseRepo.findAll();
        limits.forEach(limit ->
                _systemDefaultLimitCache.put(limit.getTierId(), limit)
        );

    }

    public int getDefaultLimitByTierId(Integer tier) {
        return _systemDefaultLimitCache.get(tier).getSystemDefaultLimit();
    }

    public int getDefaultLimitByTierType(String type){
        for (AiAgentRegistryLicense limit : _systemDefaultLimitCache.values()) {
            if (limit.getTierType().equals(type)) {
                return limit.getSystemDefaultLimit();
            }
        }
        return 0;
    }


}
