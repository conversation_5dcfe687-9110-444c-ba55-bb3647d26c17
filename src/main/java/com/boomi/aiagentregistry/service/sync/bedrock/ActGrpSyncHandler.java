// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.bedrock;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import software.amazon.awssdk.services.bedrockagent.model.ActionGroupSummary;
import software.amazon.awssdk.services.bedrockagent.model.AgentActionGroup;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.entity.AiAgentToolResource;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.repo.AiAgentToolRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolResourceRepository;
import com.boomi.aiagentregistry.service.AiAgentToolService;
import com.boomi.aiagentregistry.service.sync.SyncContextAccessor;
import com.boomi.aiagentregistry.service.sync.SyncHistoryService;
import com.boomi.aiagentregistry.service.sync.model.BedrockSyncContext;
import com.boomi.aiagentregistry.util.GuidUtil;

import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.ConcurrentModificationException;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.boomi.aiagentregistry.constant.ErrorMessages.TOOL_CREATION_FAILED;
import static com.boomi.aiagentregistry.mapper.AiAgentToolMapper.AI_AGENT_TOOL_MAPPER;
import static com.boomi.aiagentregistry.mapper.AiAgentToolResourceMapper.TOOL_RES_MAPPER;
import static com.boomi.graphql.server.schema.types.AiAgentToolType.AWS_ACTION_GROUP;


/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class ActGrpSyncHandler extends BedrockSyncHandler {

    private static final String ERROR_PROCESSING_AC_GRP = "Error processing action group: {}";
    private static final String ERROR_DELETING_AC_GRP = "Error deleting action group for version: {}";
    private static final String ERROR_PROCESSING_ACTION_GROUP =
            "Error processing Action group that has ID %s \n The error message from amazon is %s";
    private static final String ERROR_DELETING_ACTION_GROUP =
            "Error deleting Action group that has VERSION ID %s \n The error message from amazon is %s";

    private final AiAgentToolRepository _toolRepo;
    private final AiAgentToolService _toolService;
    private final AiAgentToolResourceRepository _toolResourceRepo;

    public ActGrpSyncHandler(
            AiAgentToolRepository toolRepository,
            AwsBedrockService awsBedrockService,
            SyncContextAccessor ctxAccessor,
            SyncHistoryService syncHistoryService,
            AiAgentToolService toolService,
            AiAgentToolResourceRepository toolResourceRepo) {

        super(syncHistoryService, ctxAccessor, awsBedrockService);

        _toolRepo = toolRepository;
        _toolService = toolService;
        _toolResourceRepo = toolResourceRepo;
    }


    public Mono<AiAgentTool> processActionGroup(ActionGroupSummary summary,
                                                AiAgentVersion version,
                                                BedrockSyncContext context) {
        log.info("Processing action group {} for agent version {} for account {}",
                    summary.actionGroupId(),
                    version.getUid(),
                    context.getProviderAccount().getProviderAccountName());

        AiAgentProviderAccount prvAcct = _ctxAccessor.getPrvAcct(context);
        Optional<AiAgentTool> toolOpt = _toolService.findByExternalIdReactive(
                summary.actionGroupId(),
                AWS_ACTION_GROUP,
                prvAcct);
        return toolOpt.map(
                aiAgentTool ->
                        handleExistingActionGroup(version, summary, aiAgentTool, context)

                        .doOnSuccess(logOnSuccess(summary, version, context))
                        .onErrorResume(error -> handleSyncError(summary, version, context, error)))
                .orElseGet(() -> handleNewActionGroup(version, summary, context)
                        .doOnSuccess(logOnSuccess(summary, version, context))
                        .onErrorResume(error -> handleSyncError(summary, version, context, error)));

    }

    private static Consumer<AiAgentTool> logOnSuccess(ActionGroupSummary summary, AiAgentVersion version, BedrockSyncContext context) {
        return unused ->
                log.info("Finished processing action group {} for agent version {} for account {}",
                        summary.actionGroupId(),
                        version.getUid(),
                        context.getProviderAccount().getProviderAccountName());
    }

    private Mono<AiAgentTool> handleSyncError(
            ActionGroupSummary summary,
            AiAgentVersion version,
            BedrockSyncContext context,
            Throwable error) {
        log.error(ERROR_PROCESSING_AC_GRP, summary.actionGroupId(), error);
        String errorMessage = String.format(ERROR_PROCESSING_ACTION_GROUP, summary.actionGroupId(), error.getMessage());
        updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
        return Mono.empty();
    }


    private Mono<AiAgentTool> handleNewActionGroup(AiAgentVersion version,
                                                   ActionGroupSummary summary, BedrockSyncContext context) {
        String versionString = version.getVersionString();
        String externalId = version.getExternalId();
        return _awsBedrockService.fetchActionGroupDetails(externalId, versionString, summary.actionGroupId(), context)
                .flatMap(fullDetails -> createFreshActionGroupEntries(version, fullDetails, context)
                        .flatMap(Mono::just)
                ).onErrorResume(Mono::error);
    }


    private Mono<AiAgentTool> handleExistingActionGroup(
            AiAgentVersion version,
            ActionGroupSummary summary,
            AiAgentTool existingTool,
            BedrockSyncContext context) {

        _toolService.associateToolWithVersion(version, existingTool, summary.actionGroupState().name());

        if (shouldUpdateEntity(summary.updatedAt(), existingTool)) {
            String externalId = version.getExternalId();
            return _awsBedrockService
                    .fetchActionGroupDetails(externalId, version.getVersionString(), summary.actionGroupId(), context)
                    .flatMap(awsActionGroup -> refreshExistingActionGroup(existingTool, awsActionGroup))
                    .flatMap(updatedTool -> {
                        updateUpdateStats(_ctxAccessor.getOrCrtVerStats(version, context));
                        return Mono.just(updatedTool);
                    })
                    .onErrorResume(Mono::error);
        } else {
            return Mono.just(existingTool);
        }


    }

    public Mono<Void> deleteAllByVersionExcept(
            AiAgentVersion version,
            Set<String> toolIdsToRetain,
            BedrockSyncContext context) {

        return _toolService.getVersionAssociations(version)
                .flatMap(associations -> {
                    Set<Integer> toolToDetachIds =
                            _toolService.identifyToolsToDetach(associations, toolIdsToRetain, AWS_ACTION_GROUP);

                    if (toolToDetachIds.isEmpty()) {
                        return Mono.empty();
                    }

                    return _toolService.deleteAssociationsByToolIdsAndVersionId(toolToDetachIds, version)
                            .then(Mono.defer(() -> findAndDeleteOrphanedTools(toolToDetachIds, version, context)));
                })
                .onErrorResume(error -> {
                    log.error(ERROR_DELETING_AC_GRP, version.getUid(), error);
                    String errorMessage =
                            String.format(ERROR_DELETING_ACTION_GROUP, version.getExternalId(), error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return Mono.empty();
                }).then();
    }


    private Mono<Integer> findAndDeleteOrphanedTools(
            Set<Integer> toolToDetachIds,
            AiAgentVersion version,
            BedrockSyncContext context) {

        return _toolService.findOrphanTools(toolToDetachIds)
                .flatMap(orphanedTools -> {
                    if (orphanedTools.isEmpty()) {
                        return Mono.empty();
                    }
                    return _toolService.deleteToolsById(orphanedTools)
                            .doOnSuccess(deletedCount -> updateDeletionStats(deletedCount, version, context));
                });
    }


    private void updateDeletionStats(int deletedCount, AiAgentVersion version, BedrockSyncContext context) {
        _ctxAccessor.getOrCrtVerStats(version, context)
                .getChildrenDeleted()
                .addAndGet(deletedCount);
    }


    private Mono<AiAgentTool> createToolResources(AgentActionGroup awsActionGroup, AiAgentTool tool) {

        return Mono.fromCallable(() -> {

                    log.info("Deleting tool {} resources", tool.getUid());
                    _toolResourceRepo.deleteByTool(tool);
                    _toolRepo.save(tool);
                    log.info("Finished deleting tool {} resources", tool.getUid());

                    // Create fresh resources
                    log.info("Refreshing account group {} resources", tool.getUid());
                    List<AiAgentToolResource> resources = Stream.concat(
                                    createActionGroupFunctionResource(awsActionGroup, tool).stream(),
                                    Optional.ofNullable(createActionGroupApiResource(awsActionGroup, tool))
                                            .stream()
                            )
                            .collect(Collectors.toList());

                    if (!resources.isEmpty()) {
                        _toolResourceRepo.saveAll(resources);
                    }

                    AiAgentTool updatedTool =  _toolRepo.save(tool);
                    log.info("Finished refreshing account group {} resources", tool.getUid());
                    return updatedTool;
                })
                .subscribeOn(Schedulers.boundedElastic());
    }


    public Mono<AiAgentTool> createFreshActionGroupEntries(
            AiAgentVersion version,
            AgentActionGroup awsActionGroup,
            BedrockSyncContext context) {
        // Create new tool
        AiAgentTool newTool = AI_AGENT_TOOL_MAPPER.fromAwsActionGroupCreate(
                awsActionGroup,
                _ctxAccessor.getPrvAcct(context)
        );
        return saveToolWithRetry(version, newTool, awsActionGroup, context)
                .flatMap(savedTool -> createToolResources(awsActionGroup, savedTool));
    }

    private Mono<AiAgentTool> saveToolWithRetry(
            AiAgentVersion version,
            AiAgentTool newTool,
            AgentActionGroup awsActionGroup,
            BedrockSyncContext context) {

        return Mono.fromCallable(() -> {
                    log.info("Updating new action group {} with bedrock data", awsActionGroup.actionGroupId());
                    AiAgentTool savedTool = _toolRepo.save(newTool);
                    log.info("Finished updating new action group {} with bedrock data", awsActionGroup.actionGroupId());

                    _toolService.associateToolWithVersion(version, savedTool, awsActionGroup.actionGroupState().name());
                    updateAddStats(_ctxAccessor.getOrCrtVerStats(version, context));
                    return savedTool;
                }).subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(DataIntegrityViolationException.class, e ->
                        handleToolCreationConflict(version, newTool, awsActionGroup, context)
                );
    }

    private Mono<AiAgentTool> handleToolCreationConflict(
            AiAgentVersion version,
            AiAgentTool newTool,
            AgentActionGroup awsActionGroup,
            BedrockSyncContext context) {

        Optional<AiAgentTool> toolOptional = _toolService.findByExternalIdReactive(
                newTool.getExternalId(),
                AWS_ACTION_GROUP,
                _ctxAccessor.getPrvAcct(context)
        );

        return toolOptional
                .map(existingTool -> {
                    _toolService.associateToolWithVersion(
                            version,
                            toolOptional.get(),
                            awsActionGroup.actionGroupState().name()
                    );
                    return Mono.just(toolOptional.get());
                }).orElseGet(() -> {
                    ConcurrentModificationException e = new ConcurrentModificationException(TOOL_CREATION_FAILED);
                    String errorMessage = String.format(ERROR_PROCESSING_ACTION_GROUP, awsActionGroup.actionGroupId(),
                            e.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return Mono.error(e);
                });

    }

    public Mono<AiAgentTool> refreshExistingActionGroup(AiAgentTool existingTool, AgentActionGroup awsActionGroup) {
        // Update existing tool
        AI_AGENT_TOOL_MAPPER.fromAwsActionGroupUpdate(existingTool, awsActionGroup);

        return createToolResources(awsActionGroup, existingTool);
    }

    private AiAgentToolResource createActionGroupApiResource(
            AgentActionGroup awsActionGroup,
            AiAgentTool savedTool) {
        if (awsActionGroup.apiSchema() != null) {
            return Optional.of(awsActionGroup.apiSchema())
                    .map(schema -> TOOL_RES_MAPPER.apiSchemaToToolResource(
                            schema,
                            awsActionGroup,
                            savedTool,
                            GuidUtil.createToolResourceGuid()))
                    .orElse(null);
        }
        return null;
    }


    private List<AiAgentToolResource> createActionGroupFunctionResource(
            AgentActionGroup awsActionGroup,
            AiAgentTool savedTool) {
        if (awsActionGroup.functionSchema() != null && awsActionGroup.functionSchema().hasFunctions()) {
            return Optional.ofNullable(awsActionGroup.functionSchema().functions())
                    .map(functions -> functions
                            .stream()
                            .map(function -> TOOL_RES_MAPPER.functionToToolResource(
                                    function,
                                    awsActionGroup,
                                    savedTool,
                                    GuidUtil.createToolResourceGuid()))
                            .toList())
                    .orElse(Collections.emptyList());
        }
        return Collections.emptyList();
    }

}
