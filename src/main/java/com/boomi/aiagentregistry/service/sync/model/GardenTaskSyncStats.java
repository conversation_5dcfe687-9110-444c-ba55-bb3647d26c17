// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@EqualsAndHashCode(callSuper = true)
@Setter
@Getter
@SuperBuilder
@Accessors(prefix = "_")
public class GardenTaskSyncStats extends SyncStats {

    private Set<String> _toRetainTools;


    GardenTaskSyncStats() {
        super();
        _toRetainTools = ConcurrentHashMap.newKeySet();
    }
}
