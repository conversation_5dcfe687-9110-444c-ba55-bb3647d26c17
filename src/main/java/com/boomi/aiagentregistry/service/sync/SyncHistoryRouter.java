// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.sync;

import reactor.core.publisher.Mono;
import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.service.sync.model.SyncStats;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus;

import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class SyncHistoryRouter {

    private final Map<AiAgentProviderType, SyncHistoryHandler> handlers;

    public SyncHistoryRouter(BedrockSyncHistoryHandler bedrockHandler, GardenSyncHistoryHandler gardenHandler) {
        this.handlers =
                Map.of(AiAgentProviderType.AWS_BEDROCK, bedrockHandler, AiAgentProviderType.BOOMI, gardenHandler);
    }

    public Mono<SyncUserAudit> route(AiAgentProviderType providerType, SyncUserAudit existingSyncUserAudit, SyncStats stats,
            AiRegistryEntitySyncStatus status, String syncDetails) {
        SyncHistoryHandler handler = handlers.get(providerType);
        if (handler == null) {
            throw new IllegalArgumentException("No handler found for provider type: " + providerType);
        }
        return handler.completeSyncSuccess(existingSyncUserAudit, stats, status, syncDetails);
    }
}
