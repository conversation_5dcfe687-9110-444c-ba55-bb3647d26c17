// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.bedrock;

import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.entity.AiAgentToolResource;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.exception.SyncException;
import com.boomi.aiagentregistry.repo.AiAgentToolAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolRepository;
import com.boomi.aiagentregistry.service.AiAgentToolService;
import com.boomi.aiagentregistry.service.sync.SyncContextAccessor;
import com.boomi.aiagentregistry.service.sync.SyncHistoryService;
import com.boomi.aiagentregistry.service.sync.model.BedrockKbSyncStats;
import com.boomi.aiagentregistry.service.sync.model.BedrockSyncContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import software.amazon.awssdk.services.bedrock.model.ResourceNotFoundException;
import software.amazon.awssdk.services.bedrockagent.model.AgentKnowledgeBaseSummary;
import software.amazon.awssdk.services.bedrockagent.model.DataSourceSummary;
import software.amazon.awssdk.services.bedrockagent.model.ListDataSourcesRequest;

import java.util.ConcurrentModificationException;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;

import static com.boomi.aiagentregistry.constant.ErrorMessages.TOOL_CREATION_FAILED;
import static com.boomi.aiagentregistry.mapper.AiAgentToolMapper.AI_AGENT_TOOL_MAPPER;
import static com.boomi.graphql.server.schema.types.AiAgentToolType.AWS_KNOWLEDGE_BASE;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class KbSyncHandler extends BedrockSyncHandler {
    private static final String FAILED_TO_SYNC_KB = "Failed to sync KB: ";
    private static final String ERROR_DELETING_DS = "Error in deleting orphan data sources for knowledge base {}.";
    private static final String ERROR_PROCESSING_DS = "Error while processing data source {} for knowledge base {}.";
    private static final String ERROR_DELETE_KB = "Error in deleting knowledge base with id {}.";
    private static final String KB_NOT_FOUND = "Knowledge base {} not found in Bedrock.";
    private static final String FAILED_TO_DELETE_KNOWLEDGE_BASE = "Failed to delete knowledge base {}.";
    private static final String FAILED_TO_CREATE_TOOl = "Failed to create Knowledge base {}";
    private static final String ERROR_PROCESSING_KB = "Error while processing knowledge base {} for version {}.";
    private static final String SYNC_ERROR_PROCESSING_KB =
            "Error processing Knowledge Base with ID: %s \n The error message from amazon is %ss";
    private static final String SYNC_ERROR_DELETING_KB_VERSION_ID =
            "Error deleting Knowledge Base that belongs to Version with version ID: %s \n " +
                    "The error message from amazon is %s";
    private static final String SYNC_ERROR_DELETING_KB =
            "Error deleting Knowledge Base with ID: %s \n The error message from amazon is %s";
    private static final String SYNC_ERROR_CREATING_KB_VERSION_ID =
            "Error while creating tool that belongs to Version with version ID: %s \n " +
                    "The error message from amazon is %s";
    private static final String ERROR_PROCESSING_DS_IN_KB =
            "Error processing data sources in KB that has KB external ID %s \n The error message from amazon is %s";
    private static final String ERROR_DELETING_DS_IN_KB =
            "Error deleting data sources in KB that has KB external ID %s \n The error message from amazon is %s";
    private static final String ERROR_FETCHING_DATA_SOURCE = "Error fetching data sources  for knowledge base {}.";
    private static final String ERROR_LIST_DATA_SOURCES = "Error listing data sources for knowledge base {}.";

    private final AiAgentToolRepository _toolRepo;
    private final DataSrcSycHandler _dataSrcHandler;
    private final AiAgentToolAssociationRepository _toolAssociationRepo;
    private final AiAgentToolService _toolService;

    public KbSyncHandler(
            AwsBedrockService awsBedrockService,
            SyncHistoryService syncHistoryService,
            SyncContextAccessor contextAccessor,
            AiAgentToolService toolService,
            AiAgentToolRepository toolRepo,
            DataSrcSycHandler dataSrcHandler,
            AiAgentToolAssociationRepository toolAssociationRepo) {

        super(syncHistoryService, contextAccessor, awsBedrockService);
        _toolService = toolService;
        _toolRepo = toolRepo;
        _dataSrcHandler = dataSrcHandler;
        _toolAssociationRepo = toolAssociationRepo;
    }


    public Mono<AiAgentTool> processKnowledgeBase(
            AiAgentVersion version,
            AgentKnowledgeBaseSummary kbSummary,
            BedrockSyncContext context) {

        log.info("Processing knowledge base {} for agent version {} of account {}",
                kbSummary.knowledgeBaseId(),
                version.getUid(),
                context.getProviderAccount().getProviderAccountName());

        initSyncData(version, kbSummary, context);

        String externalId = kbSummary.knowledgeBaseId();
        return _toolService.findByExternalIdReactive(externalId, AWS_KNOWLEDGE_BASE, _ctxAccessor.getPrvAcct(context))
                .map(aiAgentTool -> handleExistingKb(version, aiAgentTool, kbSummary, context)
                        .doOnSuccess(logOnSuccess(version, kbSummary, context))
                )
                .orElseGet(() -> handleNewKb(version, kbSummary, context)
                        .doOnSuccess(logOnSuccess(version, kbSummary, context))
                );
    }

    private static  Consumer<AiAgentTool> logOnSuccess(
            AiAgentVersion version,
            AgentKnowledgeBaseSummary kbSummary,
            BedrockSyncContext context) {
        return unused -> log.info("Finished processing knowledge base {} for agent version {} of account {}",
                kbSummary.knowledgeBaseId(),
                version.getUid(),
                context.getProviderAccount().getProviderAccountName());
    }


    public Mono<Void> deleteAllByVersionExcept(
            AiAgentVersion version,
            Set<String> toolIdsToRetain,
            BedrockSyncContext context) {

        return _toolService.getVersionAssociations(version)
                .flatMap(associations -> {
                    Set<Integer> toolToDetachIds =
                            _toolService.identifyToolsToDetach(associations, toolIdsToRetain, AWS_KNOWLEDGE_BASE);

                    if (toolToDetachIds.isEmpty()) {
                        return Mono.empty();
                    }

                    return _toolService.deleteAssociationsByToolIdsAndVersionId(toolToDetachIds, version)
                            .then(Mono.defer(() -> findAndDeleteOrphanedTools(toolToDetachIds, version, context)));
                })
                .onErrorResume(error -> {
                    log.error(ERROR_DELETE_KB, version.getUid(), error);
                    String errorMessage = String.format(SYNC_ERROR_DELETING_KB_VERSION_ID, version.getExternalId(),
                            error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return Mono.empty();
                })
                .then();
    }


    private Mono<Integer> findAndDeleteOrphanedTools(
            Set<Integer> toolToDetachIds,
            AiAgentVersion version,
            BedrockSyncContext context) {

        return _toolService.findOrphanTools(toolToDetachIds)
                .flatMap(orphanedTools -> {
                    if (orphanedTools.isEmpty()) {
                        return Mono.empty();
                    }
                    return _toolService.deleteToolsById(orphanedTools)
                            .doOnSuccess(deletedCount -> updateDeletionStats(deletedCount, version, context));
                });
    }


    private void updateDeletionStats(int deletedCount, AiAgentVersion version, BedrockSyncContext context) {
        _ctxAccessor.getOrCrtVerStats(version, context)
                .getChildrenDeleted()
                .addAndGet(deletedCount);
    }


    private Mono<AiAgentTool> handleNewKb(
            AiAgentVersion version,
            AgentKnowledgeBaseSummary summary,
            BedrockSyncContext context) {

        return _awsBedrockService.fetchKnowledgeBaseDetails(summary.knowledgeBaseId(), context)
                .flatMap(kb -> {
                    AiAgentTool tool = AI_AGENT_TOOL_MAPPER.fromKnowledgeBaseCreate(kb, context.getProviderAccount());
                    return saveToolWithRetry(version, tool, summary, context)
                            .onErrorMap(e -> !(e instanceof SyncException),
                                    e -> new SyncException(FAILED_TO_SYNC_KB + summary.knowledgeBaseId(), e));
                })
                .flatMap(newKnowledgeable ->
                        processDataSources(version, newKnowledgeable, context).thenReturn(newKnowledgeable))
                .onErrorResume(error -> {
                    if (error instanceof ResourceNotFoundException) {
                        log.warn(KB_NOT_FOUND, summary.knowledgeBaseId());
                        return Mono.empty();
                    }
                    log.error(FAILED_TO_CREATE_TOOl, summary.knowledgeBaseId(), error);
                    String errorMessage = String.format(SYNC_ERROR_DELETING_KB_VERSION_ID, version.getExternalId(),
                            error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return handleSyncError(version, summary, context, error)
                            .then(Mono.empty());
                });

    }

    private Mono<AiAgentTool> handleExistingKb(
            AiAgentVersion version,
            AiAgentTool tool,
            AgentKnowledgeBaseSummary summary,
            BedrockSyncContext context) {
        //create the association whether the kb needs to be updated or not
        _toolService.associateToolWithVersion(version, tool, summary.knowledgeBaseState().name());

        if (shouldUpdateEntity(summary.updatedAt(), tool)) {
            updateUpdateStats(_ctxAccessor.getOrCrtVerStats(version, context));
            return syncKbWithBedrock(version, tool, summary, context)
                    .flatMap(updatedKb -> processKbDataSources(version, updatedKb, context))
                    .onErrorResume(error -> handleSyncError(version, summary, context, error))
                    .thenReturn(tool);
        } else {
            return processKbDataSources(version, tool, context)
                    .thenReturn(tool);
        }
    }


    public Mono<AiAgentTool> saveToolWithRetry(
            AiAgentVersion version,
            AiAgentTool newTool,
            AgentKnowledgeBaseSummary summary,
            BedrockSyncContext context) {

        return Mono.fromCallable(() -> {

                    log.info("Creating new knowledgebase {} for agent version {} for account {}",
                            summary.knowledgeBaseId(),
                            version.getUid(),
                            context.getProviderAccount().getProviderAccountName());

                    AiAgentTool savedTool = _toolRepo.save(newTool);

                    log.info("Finished creating new knowledgebase {} for agent version {} for account {}",
                            summary.knowledgeBaseId(),
                            version.getUid(),
                            context.getProviderAccount().getProviderAccountName());

                    _toolService.associateToolWithVersion(version, savedTool, summary.knowledgeBaseState().name());
                    updateAddStats(_ctxAccessor.getOrCrtVerStats(version, context));
                    return savedTool;
                })
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(DataIntegrityViolationException.class, e ->
                        handleToolCreationConflict(version, newTool, summary, context)
                );
    }

    private Mono<AiAgentTool> handleToolCreationConflict(
            AiAgentVersion version,
            AiAgentTool tool,
            AgentKnowledgeBaseSummary summary,
            BedrockSyncContext context) {

        AiAgentProviderAccount prvAcct = _ctxAccessor.getPrvAcct(context);

        Optional<AiAgentTool> toolOptional = _toolService.findByExternalIdReactive(
                tool.getExternalId(),
                AWS_KNOWLEDGE_BASE,
                prvAcct
        );

        if (toolOptional.isPresent()) {
            _toolService.associateToolWithVersion(version, toolOptional.get(), summary.knowledgeBaseState().name());
            return Mono.just(toolOptional.get());
        } else {
            ConcurrentModificationException e = new ConcurrentModificationException(TOOL_CREATION_FAILED);
            String errorMessage =
                    String.format(SYNC_ERROR_CREATING_KB_VERSION_ID, version.getExternalId(), e.getMessage());
            updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtKbStats(version, tool, context));
            return Mono.empty();
        }
    }

    private Mono<Void> processKbDataSources(
            AiAgentVersion version,
            AiAgentTool kb,
            BedrockSyncContext context) {

        return processDataSources(version, kb, context)
                .then(Mono.defer(() -> deleteChildrenExcept(version, kb, context)));
    }

    private Mono<Void> handleSyncError(
            AiAgentVersion version,
            AgentKnowledgeBaseSummary summary,
            BedrockSyncContext context,
            Throwable error) {
        String errorMessage = String.format(SYNC_ERROR_PROCESSING_KB, summary.knowledgeBaseId(), error.getMessage());
        updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtKbStats(version, summary, context));
        log.error(ERROR_PROCESSING_KB, summary.knowledgeBaseId(), version.getUid(), error);

        return Mono.fromCallable(() -> _ctxAccessor.getOrCrtKbStats(version, summary, context))
                .then(Mono.empty());
    }


    private Mono<Void> processDataSources(
            AiAgentVersion version,
            AiAgentTool kb,
            BedrockSyncContext context) {
        return listDataSourceRecursively(version, kb, context)
                .flatMap(summary -> processDataSource(version, kb, summary, context))
                .onErrorResume(error -> {
                    BedrockKbSyncStats stats = _ctxAccessor.getOrCrtKbStats(version, kb, context);
                    String errorMessage =
                            String.format(ERROR_PROCESSING_DS_IN_KB, kb.getExternalId(), error.getMessage());
                    updateFailureStatus(errorMessage, stats);
                    return Mono.empty();
                })
                .then();
    }

    private Mono<Void> deleteChildrenExcept(AiAgentVersion version, AiAgentTool kb, BedrockSyncContext context) {
        if(_ctxAccessor.getOrCrtKbStats(version, kb, context).getFailedToFetchDataSources().get()){
            return Mono.empty();
        }
        Set<String> dsToRetain = _ctxAccessor.getOrCrtKbStats(version, kb, context).getToRetainDataSrcIds();

        return _dataSrcHandler.deleteAllByKnowledgeBaseExcept(version, kb, dsToRetain, context)
                .onErrorResume(error -> {
                    log.error(ERROR_DELETING_DS, version.getUid(), error);
                    String errorMessage =
                            String.format(ERROR_DELETING_DS_IN_KB, kb.getExternalId(), error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return Mono.empty();
                });

    }

    private Mono<AiAgentToolResource> processDataSource(
            AiAgentVersion version,
            AiAgentTool kb,
            DataSourceSummary summary,
            BedrockSyncContext context) {
        _ctxAccessor.getOrCrtKbStats(version, kb, context)
                .getToRetainDataSrcIds().add(summary.dataSourceId());

        return _dataSrcHandler.processDataSource(version, kb, summary, context)
                .onErrorResume(error -> {
                    log.error(ERROR_PROCESSING_DS, summary.dataSourceId(), kb.getUid(), error);
                    String errorMessage =
                            String.format(ERROR_PROCESSING_DS_IN_KB, kb.getExternalId(), error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return Mono.empty();
                });
    }

    private Mono<AiAgentTool> syncKbWithBedrock(
            AiAgentVersion version,
            AiAgentTool tool,
            AgentKnowledgeBaseSummary summary,
            BedrockSyncContext context) {

        return _awsBedrockService.fetchKnowledgeBaseDetails(summary.knowledgeBaseId(), context)
                .flatMap(kb -> {
                    if(!shouldUpdateEntity(kb.updatedAt(), tool)){
                        return Mono.just(tool);
                    }
                    AI_AGENT_TOOL_MAPPER.fromKnowledgeBaseUpdate(tool, kb, context.getProviderAccount());
                    return saveAiAgentTool(tool);
                })

                .onErrorResume(error -> {
                    if (error instanceof ResourceNotFoundException) {
                        log.warn(KB_NOT_FOUND, tool.getExternalId());
                        return handleDeletedKnowledgeBase(tool, version, context)
                                .then(Mono.empty());
                    }
                    log.error(ERROR_PROCESSING_KB, summary.knowledgeBaseId(), version.getUid(), error);
                    return handleSyncError(version, summary, context, error)
                            .then(Mono.empty());
                });
    }

    private Mono<AiAgentTool> saveAiAgentTool(AiAgentTool tool) {
        log.info("Saving tool {} to db", tool.getExternalId());
        return Mono.fromCallable(() -> _toolRepo.save(tool))
                .doOnSuccess(unused -> log.info("Finished saving tool {} to db", tool.getExternalId()))
                .subscribeOn(Schedulers.boundedElastic());
    }

    public Mono<Void> handleDeletedKnowledgeBase(AiAgentTool tool, AiAgentVersion version, BedrockSyncContext context) {
        //delete all associations then delete the knowledge base since it no longer exist in aws
        try {
            return Mono.fromCallable(() -> {
                        log.info("Deleting associations for tool {}", tool.getUid());
                        _toolAssociationRepo.deleteByToolUidIn(Set.of(tool.getUid()));
                        log.info("Finished deleting associations for tool {}", tool.getUid());

                        log.info("Deleting tool {}", tool.getUid());
                        _toolRepo.delete(tool);
                        log.info("Finished deleting tool {}", tool.getUid());

                        updateDeleteStats(_ctxAccessor.getOrCrtVerStats(version, context));
                        return null;
                    }).subscribeOn(Schedulers.boundedElastic())
                    .then();

        } catch (Exception e) {
            String errorMessage = String.format(SYNC_ERROR_DELETING_KB, tool.getExternalId(), e.getMessage());
            updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
            log.error(FAILED_TO_DELETE_KNOWLEDGE_BASE, tool.getUid(), e);
            return Mono.empty();
        }

    }

    private void initSyncData(AiAgentVersion version, AgentKnowledgeBaseSummary kbSummary, BedrockSyncContext context) {

        _ctxAccessor.getOrCrtVerStats(version, context);

        _ctxAccessor.getOrCrtKbStats(version, kbSummary, context);
    }

    public Flux<DataSourceSummary> listDataSourceRecursively(
            AiAgentVersion version,
            AiAgentTool kb,
            BedrockSyncContext context) {
        String externalId = kb.getExternalId();
        ListDataSourcesRequest request = ListDataSourcesRequest.builder()
                .knowledgeBaseId(externalId)
                .build();
        return listDataSourceWithPagination(externalId, request, context)
                .onErrorResume(error -> {
                    _ctxAccessor.getOrCrtKbStats(version, kb, context).getFailedToFetchDataSources().set(true);
                    log.error(ERROR_FETCHING_DATA_SOURCE, externalId, error);
                    return Mono.error(error);
                });
    }

    private static Flux<DataSourceSummary> listDataSourceWithPagination(
            String kbId,
            ListDataSourcesRequest request,
            BedrockSyncContext context) {
        log.info("Listing data sources with pagination for knowledge base: {}", kbId);

        return Flux.create(sink ->
                context.getBedrockAgentClient()
                        .listDataSources(request)
                        .whenCompleteAsync((response, throwable) -> {
                            if (throwable != null) {
                                log.error(ERROR_LIST_DATA_SOURCES, kbId, throwable);
                                sink.error(throwable);
                                return;
                            }

                            response.dataSourceSummaries().forEach(sink::next);

                            if (response.nextToken() != null) {
                                ListDataSourcesRequest nextRequest = request.toBuilder()
                                        .nextToken(response.nextToken())
                                        .build();

                                listDataSourceWithPagination(kbId, nextRequest, context)
                                        .subscribe(sink::next, sink::error, sink::complete);
                            } else {
                                log.info("Completed listing data sources with pagination for knowledge base: {}", kbId);
                                sink.complete();
                            }
                        })
        );
    }

}
