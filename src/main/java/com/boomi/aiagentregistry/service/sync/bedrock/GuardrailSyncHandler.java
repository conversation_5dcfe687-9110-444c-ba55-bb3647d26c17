// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.bedrock;

import com.boomi.aiagentregistry.entity.AiAgentGuardrail;
import com.boomi.aiagentregistry.entity.AiAgentGuardrailAssociation;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.repo.AiAgentGuardrailAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentGuardrailRepository;
import com.boomi.aiagentregistry.service.sync.SyncContextAccessor;
import com.boomi.aiagentregistry.service.sync.SyncHistoryService;
import com.boomi.aiagentregistry.service.sync.model.BedrockSyncContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import software.amazon.awssdk.services.bedrock.model.ResourceNotFoundException;
import software.amazon.awssdk.services.bedrockagent.model.GuardrailConfiguration;

import java.util.ConcurrentModificationException;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.boomi.aiagentregistry.mapper.AiAgentGuardrailAssociationMapper.GRD_RL_ASSOC;
import static com.boomi.aiagentregistry.mapper.AiAgentGuardrailMapper.AI_AGENT_GUARDRAIL_MAPPER;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.VERSION;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class GuardrailSyncHandler extends BedrockSyncHandler {
    private static final String ERROR_UPDATING_GUARDRAIL = "Error updating guardrail {}";
    private static final String ERROR_PROCESSING_GUARDRAIL = "Error processing guardrail {}.";
    public static final String GUARD_RAIL_VERSION_DRAFT = "DRAFT";
    private static final String ERROR_DELETING_GUARD_RAIL = "Error while deleting guardrails for version {}";
    private static final String GUARDRAIL_CREATION_FAILED = "Guardrail creation failed due to concurrent modification";
    private static final String FAILED_TO_DELETED_GUARDRAIL = "Failed to delete guardrail {}";
    private final AiAgentGuardrailRepository _guardrailRepo;
    private final AiAgentGuardrailAssociationRepository _guardrailAssociationRepo;

    private static final String SYNC_ERROR_PROCESSING_GUARDRAIL =
            "Error processing guardrail with ID: %s \n The error message from amazon is %s";

    private static final String SYNC_ERROR_DELETING_GUARDRAIL = "Error deleting guardrail the belongs to version with "+
                                                                "version ID: %s \n The error message from amazon is %s";

    private static final String SYNC_ERROR_DELETING_GUARDRAIL_ID =
            "Error deleting guardrail with ID: %s \n The error message from amazon is %s";

    public GuardrailSyncHandler(
            AwsBedrockService awsBedrockService,
            SyncContextAccessor ctxAccessor,
            SyncHistoryService syncHistoryService,
            AiAgentGuardrailRepository guardrailRepo,
            AiAgentGuardrailAssociationRepository guardrailAssociationRepo) {

        super(syncHistoryService, ctxAccessor, awsBedrockService);
        _guardrailRepo = guardrailRepo;
        _guardrailAssociationRepo = guardrailAssociationRepo;
    }


    public Mono<AiAgentGuardrail> processGuardrail(
            AiAgentVersion version,
            GuardrailConfiguration grConfig,
            BedrockSyncContext context) {
        if (grConfig == null) {
            return Mono.empty();
        }

        log.info("Processing guardrail {} version {} for version {} of account {}",
                grConfig.guardrailIdentifier(),
                grConfig.guardrailVersion(),
                version.getUid(),
                context.getProviderAccount().getProviderAccountName());

        _ctxAccessor.getOrCrtVerStats(version, context)
                .getToRetainGuardrails()
                .add(grConfig);

        return findGuardRail(
                grConfig.guardrailIdentifier(),
                grConfig.guardrailVersion(),
                _ctxAccessor.getPrvAcct(context))
                .map(aiAgentGuardrail ->
                        handleExistingGuardrail(version, aiAgentGuardrail, grConfig, context)
                                .doOnSuccess(logOnSuccess(version, grConfig, context))
                                .onErrorResume(error -> handleSyncError(version, grConfig, context, error))
                )
                .orElseGet(() -> handleNewGuardrail(version, grConfig, context)
                        .doOnSuccess(unused -> logOnSuccess(version, grConfig, context))
                        .onErrorResume(error -> handleSyncError(version, grConfig, context, error)));
    }

    private static  Consumer<AiAgentGuardrail> logOnSuccess(
            AiAgentVersion version,
            GuardrailConfiguration grConfig,
            BedrockSyncContext context) {
        return unused ->
            log.info("Finished processing guardrail {} version {} for version {} of account {}",
                    grConfig.guardrailIdentifier(),
                    grConfig.guardrailVersion(),
                    version.getUid(),
                    context.getProviderAccount().getProviderAccountName());

    }

    private Mono<AiAgentGuardrail> handleSyncError(
            AiAgentVersion version,
            GuardrailConfiguration grConfig,
            BedrockSyncContext context,
            Throwable error) {

        log.error(ERROR_PROCESSING_GUARDRAIL, grConfig.guardrailIdentifier(), error);
        String errorMessage =
                String.format(SYNC_ERROR_PROCESSING_GUARDRAIL, grConfig.guardrailIdentifier(), error.getMessage());
        updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
        return Mono.empty();
    }

    private Mono<AiAgentGuardrail> handleNewGuardrail(
            AiAgentVersion version,
            GuardrailConfiguration grConfig,
            BedrockSyncContext context) {
        return _awsBedrockService.fetchGuardrailDetails(grConfig, context)
                .flatMap(grDetails -> {
                    AiAgentProviderAccount prvAcct = _ctxAccessor.getPrvAcct(context);
                    AiAgentGuardrail guardrail = AI_AGENT_GUARDRAIL_MAPPER.toEntityFrmAwsGuardrails(grDetails, prvAcct);
                    return saveGuardrailWithRetry(version, guardrail, context);

                })
                .onErrorResume(error -> {
                    log.error(ERROR_PROCESSING_GUARDRAIL, grConfig.guardrailIdentifier(), error);
                    String errorMessage = String.format(SYNC_ERROR_PROCESSING_GUARDRAIL, grConfig.guardrailIdentifier(),
                            error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return Mono.empty();
                });
    }

    private Mono<AiAgentGuardrail> saveGuardrailWithRetry(
            AiAgentVersion version,
            AiAgentGuardrail guardrail,
            BedrockSyncContext context) {

        return Mono.fromCallable(() -> {

            log.info("Creating new guardrail {} version {} for account {}",
                    guardrail.getExternalId(),
                    guardrail.getVersionString(),
                    context.getProviderAccount().getProviderAccountName());

            AiAgentGuardrail savedGuardrail = _guardrailRepo.save(guardrail);

            log.info("Finished creating new guardrail {} version {} for account {}",
                    guardrail.getExternalId(),
                    guardrail.getVersionString(),
                    context.getProviderAccount().getProviderAccountName());

            associateGuardrailWithVersion(version, savedGuardrail);

            updateAddStats(_ctxAccessor.getOrCrtVerStats(version, context));
            return savedGuardrail;
        }).onErrorResume(DataIntegrityViolationException.class, e ->
                handleGuardrailCreationConflict(version, guardrail, context)
        );
    }

    private Mono<AiAgentGuardrail> handleGuardrailCreationConflict(
            AiAgentVersion version,
            AiAgentGuardrail guardrail,
            BedrockSyncContext context) {

        return findGuardRail(
                guardrail.getExternalId(),
                guardrail.getVersionString(),
                _ctxAccessor.getPrvAcct(context)
        ).map(existingGuardrail -> {
            associateGuardrailWithVersion(version, existingGuardrail);
            return Mono.just(existingGuardrail);
        }).orElseGet(() -> {
            ConcurrentModificationException e = new ConcurrentModificationException(GUARDRAIL_CREATION_FAILED);
            String errorMessage =
                    String.format(SYNC_ERROR_PROCESSING_GUARDRAIL, guardrail.getExternalId(), e.getMessage());
            updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
            return Mono.empty();
        });
    }

    private Mono<AiAgentGuardrail> handleExistingGuardrail(
            AiAgentVersion version,
            AiAgentGuardrail existingGr,
            GuardrailConfiguration grConfig,
            BedrockSyncContext context) {

        //if there is an association already for the version, don't create one
        if (associationDoesNotExist(version, existingGr)) {
            associateGuardrailWithVersion(version, existingGr);
        }
        //the only updatable guardrail is the working draft
        if (grConfig.guardrailVersion().equals(GUARD_RAIL_VERSION_DRAFT)) {
            return _awsBedrockService.fetchGuardrailDetails(grConfig, context)
                    .flatMap(grDetails -> {
                        if (shouldUpdateEntity(grDetails.updatedAt(), existingGr)) {
                            AI_AGENT_GUARDRAIL_MAPPER.updatedEntityFromAwsGuardRail(existingGr, grDetails);

                            log.info("Updating guardrail {} version {} with data from Bedrock for account {}",
                                    grConfig.guardrailIdentifier(),
                                    grConfig.guardrailVersion(),
                                    context.getProviderAccount().getProviderAccountName()
                                    );
                            _guardrailRepo.save(existingGr);
                            log.info("Finished updating guardrail {} version {} with data from Bedrock for account {}",
                                    grConfig.guardrailIdentifier(),
                                    grConfig.guardrailVersion(),
                                    context.getProviderAccount().getProviderAccountName()
                            );

                            updateUpdateStats(_ctxAccessor.getOrCrtVerStats(version, context));
                            return Mono.just(existingGr);
                        } else {
                            return Mono.just(existingGr);
                        }
                    })
                    .onErrorResume(error -> {
                        if(error instanceof ResourceNotFoundException){
                            return handleDeletedGuardrail(existingGr, version, context)
                                    .then(Mono.empty());
                        }else{
                            log.error(ERROR_UPDATING_GUARDRAIL, grConfig.guardrailIdentifier(), error);
                            String errorMessage =
                                    String.format(SYNC_ERROR_PROCESSING_GUARDRAIL, grConfig.guardrailIdentifier(),
                                            error.getMessage());
                            updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                            return Mono.empty();
                        }

                    });
        } else {
            return Mono.just(existingGr);
        }


    }

    private boolean associationDoesNotExist(AiAgentVersion version, AiAgentGuardrail existingGr) {
        log.info("Fetching associations for guardrail {}", existingGr.getUid());
        Set<AiAgentGuardrailAssociation> associations = _guardrailAssociationRepo.findAllByGuardrail(existingGr);
        log.info("Finished fetching associations for guardrail {}", existingGr.getUid());

        return associations
                .stream()
                .noneMatch(assoc ->
                        assoc.getRelatedEntityType().equals(VERSION)
                        && assoc.getRelatedEntityUid().equals(version.getUid()));
    }


    public Mono<Void> deleteAllByVersionExcept(
            AiAgentVersion version,
            Set<GuardrailConfiguration> guardrailIdsToRetain,
            BedrockSyncContext context) {

        return getVersionAssociations(version)
                .flatMap(associations -> {
                    Set<Integer> guardrailsToDetachIds = identifyGuardrailsToDetach(associations, guardrailIdsToRetain);

                    if (guardrailsToDetachIds.isEmpty()) {
                        return Mono.empty();
                    }

                    return deleteAssociationsByGuardrailIds(guardrailsToDetachIds, version)
                            .then(Mono.defer(() -> findAndDeleteOrphanedGuardrails(
                                    guardrailsToDetachIds,
                                    version,
                                    context)));
                })
                .onErrorResume(error -> {
                    log.error(ERROR_DELETING_GUARD_RAIL, version.getUid(), error);
                    String errorMessage =
                            String.format(SYNC_ERROR_DELETING_GUARDRAIL, version.getExternalId(), error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
                    return Mono.empty();
                });
    }


    private static Set<Integer> identifyGuardrailsToDetach(
            Set<AiAgentGuardrailAssociation> associations,
            Set<GuardrailConfiguration> guardrailIdsToRetain) {

        return associations.stream()
                .map(AiAgentGuardrailAssociation::getGuardrail)
                .filter(guardrail -> guardrailIdsToRetain.stream()
                        .noneMatch(grConfig ->
                                guardrail.getExternalId().equals(grConfig.guardrailIdentifier()) &&
                                guardrail.getVersionString().equals(grConfig.guardrailVersion())
                        )
                )
                .map(AiAgentGuardrail::getUid)
                .collect(Collectors.toSet());
    }


    private Mono<Void> findAndDeleteOrphanedGuardrails(
            Set<Integer> guardrailsToDetachIds,
            AiAgentVersion version,
            BedrockSyncContext context) {

        log.info("Finding association for guardrails: {}", guardrailsToDetachIds);
        Set<Integer> associationsIds =
                _guardrailAssociationRepo.findGuardrailIdsWithNoAssociations(guardrailsToDetachIds);
        log.info("Finished finding association for guardrails: {}", guardrailsToDetachIds);

        return Mono.just(associationsIds)
                .flatMap(orphanedGuardrails -> {
                    if (orphanedGuardrails.isEmpty()) {
                        return Mono.empty();
                    }
                    return deleteGuardrailsById(orphanedGuardrails)
                            .doOnSuccess(deletedCount -> updateDeletionStats(deletedCount, version, context))
                            .then();
                });
    }


    private void updateDeletionStats(int deletedCount, AiAgentVersion version, BedrockSyncContext context) {
        _ctxAccessor.getOrCrtVerStats(version, context)
                .getChildrenDeleted()
                .addAndGet(deletedCount);
    }


    private void associateGuardrailWithVersion(AiAgentVersion version, AiAgentGuardrail guardrail) {
        AiAgentGuardrailAssociation assoc = GRD_RL_ASSOC.associateWithVersion(version, guardrail);
        log.info("Creating association for guardrail {} and agent version {}", guardrail.getUid(), version.getUid());
        _guardrailAssociationRepo.save(assoc);
        log.info("Finished creating association for guardrail {} and agent version {}",
                guardrail.getUid(),
                version.getUid());
    }

    public Mono<Void> handleDeletedGuardrail(
            AiAgentGuardrail guardrail,
            AiAgentVersion version,
            BedrockSyncContext context) {
        //delete all associations then delete the guardrail  since it no longer exist in aws
        try {
            return Mono.fromCallable(() -> {

                        log.info("Deleting guardrail {} associations", guardrail.getUid());
                        _guardrailAssociationRepo.deleteByGuardrailUidIn(Set.of(guardrail.getUid()));
                        log.info("Finished deleting guardrail {} associations", guardrail.getUid());

                        log.info("Deleting guardrail {}", guardrail.getUid());
                        _guardrailRepo.delete(guardrail);
                        log.info("Finished deleting guardrail {}", guardrail.getUid());

                        updateDeleteStats(_ctxAccessor.getOrCrtVerStats(version, context));
                        return null;
                    }).subscribeOn(Schedulers.boundedElastic())
                    .then();

        } catch (Exception e) {
            String errorMessage =
                    String.format(SYNC_ERROR_DELETING_GUARDRAIL_ID, guardrail.getExternalId(), e.getMessage());
            updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtVerStats(version, context));
            log.error(FAILED_TO_DELETED_GUARDRAIL, guardrail.getUid(), e);
            return Mono.empty();
        }

    }
    private Optional<AiAgentGuardrail> findGuardRail(
            String externalId,
            String grVersion,
            AiAgentProviderAccount account) {

        log.info("Searching for guardrail {} version {} for account {}",
                externalId,
                grVersion,
                account.getProviderAccountName());

        Optional<AiAgentGuardrail> guardrail =
                _guardrailRepo.findByExternalIdAndVersionStringAndAiAgentProviderAccount(
                        externalId,
                        grVersion,
                        account);

        log.info("Finished searching guardrail {} version {} for account {}",
                externalId,
                grVersion,
                account.getProviderAccountName());

        return guardrail;

    }

    private Mono<Set<AiAgentGuardrailAssociation>> getVersionAssociations(AiAgentVersion version) {
        log.info("Fetching version {} guardrail's associations", version.getUid());
        return Mono.fromCallable(() ->
                        _guardrailAssociationRepo.findByRelatedEntityUidAndRelatedEntityType(version.getUid(), VERSION))
                .doOnSuccess(unused ->
                        log.info("Finished fetching version {} guardrail's associations",
                                version.getUid()))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error);
    }

    private Mono<Void> deleteAssociationsByGuardrailIds(Set<Integer> guardrailIds, AiAgentVersion version) {
        log.info("Deleting version {} guardrail associations for guardrails: {}", version.getUid(), guardrailIds);
        return Mono.fromRunnable(() ->
                        _guardrailAssociationRepo.deleteByGuardrailUidInAndRelatedEntityUidAndRelatedEntityType(
                                guardrailIds,
                                version.getUid(),
                                VERSION.name())
                )
                .doOnSuccess(unused -> log.info("Deleting version {} guardrail associations for guardrails: {}",
                        version.getUid(),
                        guardrailIds))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error)
                .then();
    }

    private Mono<Integer> deleteGuardrailsById(Set<Integer> guardrailIds) {
        log.info("Deleting guardrails {}", guardrailIds);
        return Mono.fromCallable(() -> _guardrailRepo.deleteGuardrailsWithLock(guardrailIds))
                .doOnSuccess(unused -> log.info("Finished deleting guardrails {}", guardrailIds))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error);
    }

    public Optional<AiAgentGuardrail> getGuardrailByVersion(AiAgentVersion version) {
        log.info("Fetching guardrails that are linked to agent version {}", version.getUid());
        Optional<List<AiAgentGuardrail>> guardrails =
                _guardrailAssociationRepo
                        .findAiAgentGuardrailByRelatedEntityGuidAndRelatedEntityType(version.getUid(), VERSION);
        log.info("Finished fetching guardrails that are linked to agent version {}", version.getUid());
        return Optional.ofNullable(guardrails)
                .filter(guardrailList -> guardrailList.isPresent() && !guardrailList.get().isEmpty())
                .map(list -> list.get().get(0));
    }

}
