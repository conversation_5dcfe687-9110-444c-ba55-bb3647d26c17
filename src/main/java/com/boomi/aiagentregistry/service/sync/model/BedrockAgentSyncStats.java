// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import software.amazon.awssdk.services.bedrockagent.model.AgentAliasSummary;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder(toBuilder = true)
@Accessors(prefix = "_")
public class BedrockAgentSyncStats extends SyncStats {

    private Map<String, BedrockVersionSyncStats> _versionStats;

    private Set<String> _toRetainVerIds;

    private Set<AgentAliasSummary> _toRetainAliases;

    private AtomicBoolean _failedToFetchAliases = new AtomicBoolean(false);
    private AtomicBoolean _failedToFetchVersions = new AtomicBoolean(false);

    public BedrockAgentSyncStats() {
        super();
        _toRetainAliases = ConcurrentHashMap.newKeySet();
        _toRetainVerIds = ConcurrentHashMap.newKeySet();
        _versionStats = new ConcurrentHashMap<>();
    }

    public BedrockVersionSyncStats getOrCreateVersionStats(String versionString) {
        BedrockVersionSyncStats stats = _versionStats.get(versionString);
        if (stats == null) {
            stats = _versionStats.putIfAbsent(versionString, new BedrockVersionSyncStats());
            if (stats == null) {
                stats = _versionStats.get(versionString);
            }
        }
        return stats;
    }
}
