// Copyright (c) 2025 Boom<PERSON>, LP
package com.boomi.aiagentregistry.service.sync;

import com.boomi.aiagentregistry.service.sync.model.SyncContext;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
public interface SyncLimitManager {
    Mono<Void> setAccountLimit(SyncContext context);
    boolean isLimitReached(SyncContext context);
    Mono<Void> disableAccount(SyncContext context);
}
