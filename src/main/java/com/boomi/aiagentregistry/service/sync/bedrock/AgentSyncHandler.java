// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.bedrock;

import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentAlias;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.repo.AiAgentRepository;
import com.boomi.aiagentregistry.service.sync.SyncContextAccessor;
import com.boomi.aiagentregistry.service.sync.SyncHistoryService;
import com.boomi.aiagentregistry.service.sync.SyncLimitManager;
import com.boomi.aiagentregistry.service.sync.model.BedrockAgentSyncStats;
import com.boomi.aiagentregistry.service.sync.model.BedrockSyncContext;
import com.boomi.aiagentregistry.service.sync.model.SyncEntityPointInTimeInfo;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GeneralUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import software.amazon.awssdk.services.bedrockagent.model.AgentAliasSummary;
import software.amazon.awssdk.services.bedrockagent.model.AgentSummary;
import software.amazon.awssdk.services.bedrockagent.model.AgentVersionSummary;
import software.amazon.awssdk.services.bedrockagent.model.ListAgentAliasesRequest;
import software.amazon.awssdk.services.bedrockagent.model.ListAgentVersionsRequest;

import java.util.List;
import java.util.Set;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_DELETING_UNPROCESSED_ENTITIES_FOR_AGENT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_FETCHING_ALIASES;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_FETCHING_VERSIONS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_LIST_ALIASES;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_LIST_VERSIONS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_PROCESSING_AGENT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_PROCESSING_VERSION;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FAILED_TO_CREATE_AGENT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FAILED_TO_DELETE_AGENT;
import static com.boomi.aiagentregistry.mapper.AiAgentMapper.AI_AGENT_MAPPER;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.AGENT;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class AgentSyncHandler extends BedrockSyncHandler {

    private static final String ERROR_DELETING_AGENT =
            "Error deleting Agent that has ID %s \n The error message from amazon is %s";
    private static final String ERROR_DELETING_UNPROCESSED_ENTITIES =
            "Error deleting Agent that has ID %s \n The error message from amazon is %s";
    private static final String ERROR_PROCESSING_ALIAS_FOR_AGENT =
            "Error processing Alias for Agent that has Agent ID %s \n The error message from amazon is %s";
    private static final String ERROR_PROCESSING_VERSION_FOR_AGENT =
            "Error processing Version for Agent that has Agent ID %s \n The error message from amazon is %s";

    private final AiAgentRepository _agentRepository;
    private final AliasSyncHandler _aliasHandler;
    private final VersionSyncHandler _versionHandler;
    private final SyncLimitManager _limitManager;

    AgentSyncHandler(
            AiAgentRepository agentRepository,
            AliasSyncHandler aliasBedRckSyncHandler,
            VersionSyncHandler verBedRckSyncHandler,
            SyncHistoryService syncHistoryService,
            AwsBedrockService awsBedrockService,
            SyncContextAccessor ctxAccessor,
            SyncLimitManager limitManager) {
        super(syncHistoryService, ctxAccessor, awsBedrockService);
        _agentRepository = agentRepository;
        _aliasHandler = aliasBedRckSyncHandler;
        _versionHandler = verBedRckSyncHandler;
        _limitManager = limitManager;
    }

    public Mono<SyncUserAudit> processAgent(AgentSummary agentSummary, BedrockSyncContext context) {
        log.info("Processing agent {} for account {}",
                agentSummary.agentId(),
                context.getProviderAccount().getProviderAccountName());
        return Mono.defer(() -> getOrCreateAgent(agentSummary, context)
                .flatMap(agent -> {
                    SyncEntityPointInTimeInfo entityInfo = SyncEntityPointInTimeInfo.builder()
                            .entityUid(agent.getUid())
                            .entityType(AGENT)
                            .entityGuid(agent.getGuid())
                            .build();
                    return createSyncUserAudit(entityInfo, context)
                            .flatMap(agentSyncUserAudit -> processChildren(agent, agentSyncUserAudit, context)
                                    .doOnSuccess(saved -> log.info("Finished processing agent {} for account {}",
                                            agentSummary.agentId(),
                                            context.getProviderAccount().getProviderAccountName())));
                }));
    }

    private Mono<SyncUserAudit> processChildren(AiAgent agent, SyncUserAudit agentSyncUserAudit,
            BedrockSyncContext context) {
        BedrockAgentSyncStats agentStats = _ctxAccessor.getOrCrtAgntStats(agent, context);
        return processChildEntities(agent, context)
                .then(Mono.defer(() -> deleteChildrenExcept(agent, context)))
                .then(Mono.defer(() -> handleSyncCompletion(agentSyncUserAudit, agentStats)))
                .onErrorResume(error -> {
                    log.error(ERROR_PROCESSING_AGENT, agent.getUid());
                    return handleSyncCompletion(agentSyncUserAudit, agentStats);
                });
    }

    public Mono<Void> deleteAllByAccountExcept(
            AiAgentProviderAccount account,
            Set<String> agentsToRetain,
            BedrockSyncContext context) {
        return findAgentByRegistryAccountReactive(account)
                .flatMapIterable(agents -> agents)
                .filter(agent -> !agentsToRetain.contains(agent.getExternalId()))
                .flatMap(agent -> deleteAgentAndChildren(agent, context))
                .then();
    }

    private Mono<Void> deleteAgent(AiAgent agent, BedrockSyncContext context) {
        agent.setIsDeleted(true);
        agent.setModifiedTime(AuditUtilImpl.getCurrentTime());
        log.info("Marking agent {} as deleted in db", agent.getUid());
        return saveAgentReactive(agent)
                .flatMap(savedAgent -> {
                    log.info("Finished marking agent {} as deleted in db", agent.getUid());
                    updateDeleteStats(_ctxAccessor.getOrCrtAgntStats(agent, context));
                    return Mono.empty();
                })
                .then()
                .onErrorResume(error -> {
                    String errorMessage =
                            String.format(ERROR_DELETING_AGENT, agent.getExternalId(), error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtAgntStats(agent, context));
                    return Mono.empty();
                });
    }

    //if this method is called for an agent that has been deleted on provider side, it will send
    //an empty set for the processed child entity which in return will delete all children
    private Mono<Void> deleteChildrenExcept(AiAgent agent, BedrockSyncContext context) {
        Set<String> versionsToRetain = _ctxAccessor.getOrCrtAgntStats(agent, context).getToRetainVerIds();

        Set<AgentAliasSummary> aliasesToRetain = _ctxAccessor.getOrCrtAgntStats(agent, context).getToRetainAliases();

        BedrockAgentSyncStats stats = _ctxAccessor.getOrCrtAgntStats(agent, context);

        return Mono.defer(() -> {
            Mono<Void> aliasDeleteMono = stats.getFailedToFetchAliases().get() ? Mono.empty() :
                    _aliasHandler.deleteByAgentExcept(agent, aliasesToRetain, context);

            Mono<Void> versionDeleteMono = stats.getFailedToFetchVersions().get() ? Mono.empty() :
                    _versionHandler.deleteAllByAgentExcept(agent, versionsToRetain, context);

            return aliasDeleteMono
                    .then(versionDeleteMono)
                    .onErrorResume(
                            error -> {
                                log.error(ERROR_DELETING_UNPROCESSED_ENTITIES_FOR_AGENT, agent.getUid(), error);
                                String errorMessage =
                                        String.format(ERROR_DELETING_UNPROCESSED_ENTITIES, agent.getExternalId(),
                                                error.getMessage());
                                updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtAgntStats(agent, context));
                                return Mono.empty();
                            });
        });

    }

    private Mono<Void> processChildEntities(AiAgent agent, BedrockSyncContext context) {

        return processVersions(agent, context)
                .count()
                .then(processAliases(agent, context)
                        .then());
    }

    private Flux<AiAgentAlias> processAliases(AiAgent agent, BedrockSyncContext context) {

        return listAliasesRecursively(agent.getExternalId(), context)
                .flatMap(aliasSummary -> {

                    if(_limitManager.isLimitReached(context)){
                        _ctxAccessor.getProviderAcctStats(context).getExceededLimit().set(true);
                        return Mono.empty();
                    }
                    //increment the count for number of aliases processed
                    context.getSyncedEntitiesCount().getAndIncrement();

                    return _aliasHandler.processAlias(agent, aliasSummary, context);
                })
                .onErrorResume(error -> {

                    log.error(ERROR_PROCESSING_AGENT, agent.getUid(), error);

                    String errorMessage =
                            String.format(ERROR_PROCESSING_ALIAS_FOR_AGENT, agent.getExternalId(), error.getMessage());
                    updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtAgntStats(agent, context));
                    return Mono.empty();
                });
    }

    private Flux<AiAgentVersion> processVersions(AiAgent agent, BedrockSyncContext context) {
        return listVersionsRecursively(agent.getExternalId(), context)
                .concatMap(versionSummary -> processVersion(agent, versionSummary, context));
    }

    private Mono<AiAgentVersion> processVersion(
            AiAgent agent,
            AgentVersionSummary versionSummary,
            BedrockSyncContext context) {
        return _versionHandler.processVersion(agent, versionSummary, context)
                .onErrorResume(
                        error -> {
                            log.error(ERROR_PROCESSING_VERSION,versionSummary.agentVersion(), agent.getUid(), error);
                            String errorMessage =
                                    String.format(ERROR_PROCESSING_VERSION_FOR_AGENT, agent.getExternalId(),
                                            error.getMessage());
                            updateFailureStatus(errorMessage, _ctxAccessor.getOrCrtAgntStats(agent, context));
                            return Mono.empty();
                        });
    }

    private Mono<AiAgent> getOrCreateAgent(AgentSummary agentSummary, BedrockSyncContext context) {

        return findExistingAgent(_ctxAccessor.getPrvAcct(context), agentSummary.agentId())
                .switchIfEmpty(Mono.defer(() -> handleNewAgent(agentSummary, context)));
    }

    private Mono<AiAgent> handleNewAgent(AgentSummary agentSummary, BedrockSyncContext context) {
        BedrockAgentSyncStats stats = _ctxAccessor.getOrCrtAgntStats(agentSummary, context);
        updateAddStats(stats);
        return createAgent(agentSummary, context);
    }

    private Mono<AiAgent> createAgent(AgentSummary agentSummary, BedrockSyncContext context) {
        try {
            AiAgentProviderAccount providerAccount = context.getProviderAccount();
            AiAgent newAgent = AI_AGENT_MAPPER.toEntityFromAwsAgent(agentSummary, providerAccount);
            log.info("Creating new agent {} for account {}", agentSummary.agentId(), providerAccount);
            return saveAgentReactive(newAgent)
                    .doOnSuccess(saved -> log.info("Finished creating new agent {} for account {}",
                            agentSummary.agentId(),
                            providerAccount.getProviderAccountName()))
                    .onErrorResume(ex -> {
                        log.error(FAILED_TO_CREATE_AGENT, ex);
                        return Mono.error(ex);
                    });
        } catch (Exception ex) {
            return Mono.error(ex);
        }
    }

    private Mono<SyncUserAudit> deleteAgentAndChildren(AiAgent agent, BedrockSyncContext context) {
        _ctxAccessor.getOrCrtAgntStats(agent, context);
        BedrockAgentSyncStats agentStats = _ctxAccessor.getOrCrtAgntStats(agent, context);
        SyncEntityPointInTimeInfo entityInfo = SyncEntityPointInTimeInfo.builder()
                .entityUid(agent.getUid())
                .entityType(AGENT)
                .entityGuid(agent.getGuid())
                .build();
        return createSyncUserAudit(entityInfo, context).flatMap(agentSyncUserAudit -> deleteChildrenExcept(agent,
                context)
                .then(deleteAgent(agent, context))
                .then(Mono.defer(() -> handleSyncCompletion(agentSyncUserAudit, agentStats)))
                .onErrorResume(error -> {
                    log.error(FAILED_TO_DELETE_AGENT, agent.getUid(), error);
                    return handleSyncCompletion(agentSyncUserAudit, agentStats);
                }));
    }

    private Mono<AiAgent> findExistingAgent(AiAgentProviderAccount account, String externalId) {
        log.info("Searching for agent {} for provider {}", externalId, account.getProviderAccountName());
        return Mono.fromCallable(() -> _agentRepository.findByAiAgentProviderAccountAndExternalId(account, externalId))
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(GeneralUtil::optionalToMono)
                .doOnSuccess(searched -> log.info("Finished searching for agent {} for provider {}",
                        externalId,
                        account.getProviderAccountName()))
                .onErrorResume(Mono::error);
    }

    private Mono<AiAgent> saveAgentReactive(AiAgent agent) {

        return Mono.fromCallable(() -> _agentRepository.save(agent))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error);
    }

    private Mono<List<AiAgent>> findAgentByRegistryAccountReactive(AiAgentProviderAccount account) {
        log.info("Fetching non deleted agents for account {}", account.getProviderAccountName());
        return Mono.fromCallable(() ->
                        _agentRepository.findByAiAgentProviderAccountAndIsDeleted(account, false))
                .doOnSuccess(unused -> log.info("Finished fetching non deleted agents for account {}",
                        account.getProviderAccountName()))
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(GeneralUtil::optionalToMono)
                .onErrorResume(Mono::error);
    }

    private Flux<AgentAliasSummary> listAliasesRecursively(
            String agentId,
            BedrockSyncContext context) {

        return Flux.defer(() -> {
            ListAgentAliasesRequest request = ListAgentAliasesRequest.builder()
                    .agentId(agentId)
                    .build();
            return listAliasesWithPagination(request, context)
                    .onErrorResume(error -> {
                        _ctxAccessor.getOrCrtAgntStats(agentId, context).getFailedToFetchAliases().set(true);
                        log.error(ERROR_FETCHING_ALIASES, agentId, error);
                        return Mono.error(error);
                    });
        });
    }

    private Flux<AgentAliasSummary> listAliasesWithPagination(
            ListAgentAliasesRequest request,
            BedrockSyncContext context) {
        log.info("Listing aliases with pagination for agent: {}", request.agentId());

        return Flux.create(sink ->
                context.getBedrockAgentClient()
                        .listAgentAliases(request)
                        .whenCompleteAsync((response, throwable) -> {
                            if (throwable != null) {
                                log.error(ERROR_LIST_ALIASES, throwable);
                                sink.error(throwable);
                                return;
                            }

                            response.agentAliasSummaries().forEach(sink::next);

                            if (response.nextToken() != null) {
                                ListAgentAliasesRequest nextRequest = request.toBuilder()
                                        .nextToken(response.nextToken())
                                        .build();

                                listAliasesWithPagination(nextRequest, context)
                                        .subscribe(sink::next, sink::error, sink::complete);
                            } else {
                                log.info("Completed listing aliases with pagination for agent: {}", request.agentId());
                                sink.complete();
                            }
                        })
        );
    }
    private Flux<AgentVersionSummary> listVersionsRecursively(
            String agentId,
            BedrockSyncContext context) {

        return Flux.defer(() -> {
            ListAgentVersionsRequest request = ListAgentVersionsRequest.builder()
                    .agentId(agentId)
                    .build();
            return listVersionsWithPagination(request, context)
                    .onErrorResume(error -> {
                        _ctxAccessor.getOrCrtAgntStats(agentId, context).getFailedToFetchVersions().set(true);
                        log.error(ERROR_FETCHING_VERSIONS, agentId, error);
                        return Mono.error(error);
                    });
        });
    }

    private Flux<AgentVersionSummary> listVersionsWithPagination(
            ListAgentVersionsRequest request,
            BedrockSyncContext context) {
        log.info("Listing versions with pagination for agent: {}", request.agentId());

        return Flux.create(sink ->
                context.getBedrockAgentClient()
                        .listAgentVersions(request)
                        .whenCompleteAsync((response, throwable) -> {
                            if (throwable != null) {
                                log.error(ERROR_LIST_VERSIONS, throwable);
                                sink.error(throwable);
                                return;
                            }

                            response.agentVersionSummaries().forEach(sink::next);

                            if (response.nextToken() != null) {
                                ListAgentVersionsRequest nextRequest = request.toBuilder()
                                        .nextToken(response.nextToken())
                                        .build();

                                listVersionsWithPagination(nextRequest, context)
                                        .subscribe(sink::next, sink::error, sink::complete);
                            } else {
                                log.info(
                                        "Completed listing versions with pagination for agent: {}",
                                        request.agentId());
                                sink.complete();
                            }
                        })
        );
    }
}
