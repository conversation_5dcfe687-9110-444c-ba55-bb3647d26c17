// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.sync;

import com.boomi.aiagentregistry.aop.ReactiveLogging;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentRegistryLicenseAssociation;
import com.boomi.aiagentregistry.exception.DatabaseUpdateException;
import com.boomi.aiagentregistry.exception.SyncException;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.repo.AiAgentRegistryLicenseAssociationRepository;
import com.boomi.aiagentregistry.service.sync.model.SyncContext;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@ReactiveLogging
@Slf4j
@Service
public class SyncLimitManagerImpl implements SyncLimitManager {
    private final DefaultSystemLimitCacheService _systemLimitCacheService;
    private final AiAgentRegistryLicenseAssociationRepository _licenseAssocRepo;
    private final AiAgentProviderAccountRepository _accountRepo;
    private static final String ACCOUNT_TIER_FREE = "FREE";

    public SyncLimitManagerImpl(DefaultSystemLimitCacheService systemLimitCacheService,
                                AiAgentRegistryLicenseAssociationRepository licenseAssocRepo,
                                AiAgentProviderAccountRepository accountRepo) {
        _systemLimitCacheService = systemLimitCacheService;
        _licenseAssocRepo = licenseAssocRepo;
        _accountRepo = accountRepo;
    }

    @Override
    public Mono<Void> setAccountLimit(SyncContext context) {
        AiAgentProviderAccount account = context.getProviderAccount();

        return getAccountLicenseAssociation(account)
                .switchIfEmpty(Mono.defer(() -> {
                    //if empty then default to free tier
                    int defaultFreeLimit = _systemLimitCacheService.getDefaultLimitByTierType(ACCOUNT_TIER_FREE);
                    if(defaultFreeLimit == 0){
                        return Mono.error(new SyncException("Failed to fetch default free limit"));
                    }
                    context.setAccountSystemLimit(defaultFreeLimit);
                    return Mono.empty();
                }))
                .flatMap(association -> {
                    if (association.getSystemLimit() != null) {
                        // Use account limit override
                        context.setAccountSystemLimit(association.getSystemLimit());
                    } else {
                        // Use system default limit
                        int accountTierId = association.getAiAgentRegistryLicense().getTierId();
                        int accountLimitOverride = _systemLimitCacheService.getDefaultLimitByTierId(accountTierId);
                        context.setAccountSystemLimit(accountLimitOverride);
                    }
                    return Mono.empty();
                });
    }

    @Override
    public boolean isLimitReached(SyncContext context) {
        int accountLimit = context.getAccountSystemLimit();
        int syncedEntitiesCount = context.getSyncedEntitiesCount().get();
        log.error("account limit {} and the synced entities are {}", accountLimit, syncedEntitiesCount);
        return accountLimit == syncedEntitiesCount;
    }

    @Override
    public Mono<Void> disableAccount(SyncContext context) {
        return Mono.defer(() -> {
            AiAgentProviderAccount account = context.getProviderAccount();
            account.setProviderAccountStatus(AiAgentProviderAccountStatus.DISABLED);

            log.info(
                    "Disabling account {} because it reached the {} hard limit",
                    account.getProviderAccountName(),
                    context.getAccountSystemLimit()
            );

            return Mono.fromCallable(() -> _accountRepo.save(account))
                    .then()
                    .doOnSuccess(unused -> log.info(
                            "Successfully disabled account: {}",
                            account.getProviderAccountName()
                    ))
                    .doOnError(error -> log.error(
                            "Failed to disable account: {}. Error: {}",
                            account.getProviderAccountName(),
                            error.getMessage(),
                            error
                    ))
                    .onErrorResume(error -> Mono.error(new DatabaseUpdateException(
                            "Failed to disable account: " + account.getProviderAccountName(),
                            error
                    )))
                    .subscribeOn(Schedulers.boundedElastic());
        });

    }

    private Mono<AiAgentRegistryLicenseAssociation> getAccountLicenseAssociation(AiAgentProviderAccount account){
        log.info("Fetch account license association");
        return Mono.fromCallable(() -> _licenseAssocRepo.findByIdpAccountId(account.getIdpAccountId()))
                .doOnSuccess(unused -> log.info("Successfully fetched account license association"))
                .subscribeOn(Schedulers.boundedElastic());
    }
}
