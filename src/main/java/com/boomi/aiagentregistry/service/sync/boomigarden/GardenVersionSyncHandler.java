// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.boomigarden;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTask;
import com.boomi.aiagentregistry.entity.AiAgentTaskAssociation;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.repo.AiAgentTaskAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentTaskRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentVersionRepository;
import com.boomi.aiagentregistry.service.sync.SyncHistoryService;
import com.boomi.aiagentregistry.service.sync.model.GardenAgentSyncStats;
import com.boomi.aiagentregistry.service.sync.model.GardenSyncContext;
import com.boomi.aiagentregistry.service.sync.model.GardenVersionSyncStats;
import com.boomi.aiagentregistry.service.sync.model.SyncEntityPointInTimeInfo;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GeneralUtil;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.gardenagents.model.AgentSummary;
import com.boomi.gardenagents.model.Guardrail;
import com.boomi.gardenagents.model.Task;
import com.boomi.util.StringUtil;

import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_DELETE_VERSION_WITH_ID;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_DELETING_UNPROCESSED_CHILDREN;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_PROCESSING_TASKS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_PROCESSING_VERSION_CHILD_ENTITIES;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_UPDATE_VERSION;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FAILED_TO_CREATE_VERSION_FOR_AGENT;
import static com.boomi.aiagentregistry.mapper.AiAgentVersionMapper.AI_AGENT_VERSION_MAPPER;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.TASK;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.VERSION;

@Service
@Slf4j
public class GardenVersionSyncHandler extends GardenSyncHandler {

    private final AiAgentVersionRepository _versionRepository;
    private final AiAgentTaskAssociationRepository _taskAssociationRepository;
    private final AiAgentToolAssociationRepository _toolAssociationRepository;
    private final AiAgentTaskRepository _taskRepository;
    private final GardenTaskSyncHandler _taskHandler;
    private final GardenGuardrailSyncHandler _guardrailHandler;

    public GardenVersionSyncHandler(
            AiAgentVersionRepository versionRepository,
            AiAgentTaskRepository taskRepository,
            AiAgentTaskAssociationRepository taskAssociationRepository,
            AiAgentToolAssociationRepository toolAssociationRepository,
            GardenTaskSyncHandler gardenTaskSyncHandler,
            GardenGuardrailSyncHandler guardrailHandler,
            SyncHistoryService syncHistoryService,
            GardenSyncContextAccessor ctxAccessor,
            GardenService gardenService) {
        super(syncHistoryService, ctxAccessor, gardenService);

        _versionRepository = versionRepository;
        _taskRepository = taskRepository;
        _taskAssociationRepository = taskAssociationRepository;
        _toolAssociationRepository = toolAssociationRepository;
        _taskHandler = gardenTaskSyncHandler;
        _guardrailHandler = guardrailHandler;
    }

    public Mono<AiAgentVersion> processVersion(
            AiAgent agent,
            AgentSummary versionSummary,
            GardenSyncContext context) {

        Optional<AiAgentVersion> versionOptional = getVersionReactive(agent);

        return versionOptional
                .map(aiAgentVersion ->
                        handleExistingVersion(agent, aiAgentVersion, versionSummary, context)
                )
                .orElseGet(() ->
                        handleNewVersion(agent, versionSummary, context)
                );
    }

    private Mono<AiAgentVersion> handleExistingVersion(
            AiAgent agent,
            AiAgentVersion version,
            AgentSummary summary,
            GardenSyncContext context) {

        initSyncData(agent, version, context);
        if (shouldUpdateEntity(summary.getLastUpdatedOn(), version)) {
            updateUpdateStats(_ctxAccessor.getOrCrtAgntStats(agent, context));
            updateUpdateStats(_ctxAccessor.getProviderAcctStats(context));
        }

        return syncVersionAndChildren(agent, version, summary, context);
    }

    private Mono<AiAgentVersion> handleNewVersion(
            AiAgent agent,
            AgentSummary versionSummary,
            GardenSyncContext context) {

        String guid = GuidUtil.createAIAgentVersionGuid();

        GardenAgentSyncStats stats = _ctxAccessor.getOrCrtAgntStats(agent, context);
        stats.getToRetainVerIds().add(guid);
        _ctxAccessor.getOrCrtVerStats(guid, agent.getExternalId(), context);

        updateAddStats(_ctxAccessor.getOrCrtAgntStats(agent, context));

        return createVersion(versionSummary, guid, agent, context)
                .flatMap(newVersion ->
                        syncVersionAndChildren(agent, newVersion, versionSummary, context));
    }

    private Mono<AiAgentVersion> syncVersionAndChildren(
            AiAgent agent,
            AiAgentVersion dbVersion,
            AgentSummary versionSummary,
            GardenSyncContext context
    ) {
        SyncEntityPointInTimeInfo entityInfo = SyncEntityPointInTimeInfo.builder()
                                                        .entityUid(dbVersion.getUid())
                                                        .entityType(VERSION)
                                                        .entityGuid(dbVersion.getGuid())
                                                        .entityName(versionSummary.getName())
                                                        .entityVersion(dbVersion.getVersionString())
                                                        .build();
        return createSyncUserAudit(entityInfo, context)

                .flatMap(versionSyncUserAudit -> syncVersionWithGarden(dbVersion, versionSummary, context)

                        .flatMap(updatedVersion -> processChildEntities(dbVersion, versionSummary, context)

                                .then(Mono.defer(() -> deleteChildrenExcept(dbVersion, context)))

                                .then(Mono.defer(() -> handleSyncCompletion(versionSyncUserAudit, dbVersion, context)))

                                .onErrorResume(unused -> {
                                    log.error(
                                            ERROR_PROCESSING_VERSION_CHILD_ENTITIES,
                                            dbVersion.getGuid(),
                                            agent.getUid()
                                    );
                                    return handleSyncCompletion(versionSyncUserAudit, dbVersion, context)
                                            .then(Mono.empty());
                                })
                        )
                )
                .thenReturn(dbVersion);
    }

    private Mono<AiAgentVersion> createVersion(
            AgentSummary versionSummary,
            String guid,
            AiAgent agent,
            GardenSyncContext context) {

        AiAgentProviderAccount providerAccount = context.getProviderAccount();

        AiAgentVersion newVersion =
                AI_AGENT_VERSION_MAPPER.toEntityFromGardenVersionSummary(versionSummary, guid, agent, providerAccount);

        return saveReactive(newVersion)
                .onErrorResume(error -> {
                    log.error(FAILED_TO_CREATE_VERSION_FOR_AGENT, agent.getUid(), error);
                    updateFailureStatus(error, _ctxAccessor.getOrCrtVerStats(guid, agent.getExternalId(), context));
                    return Mono.empty();
                })
                .onErrorResume(Mono::error);

    }

    private Mono<AiAgentVersion> syncVersionWithGarden(
            AiAgentVersion existingVersion,
            AgentSummary versionSummary,
            GardenSyncContext context) {
        return Mono.defer(() -> {
                   if (!shouldUpdateEntity(versionSummary.getLastUpdatedOn(), existingVersion)) {
                        return Mono.just(existingVersion);
                    }

                    Timestamp updatedAtProviderTime = GeneralUtil.tmStmpFromInst(
                            GeneralUtil.getInstantFromString(versionSummary.getLastUpdatedOn()));
                    AI_AGENT_VERSION_MAPPER.toUpdatedEntityFromGardenAgentVersion(existingVersion,
                            versionSummary,
                            updatedAtProviderTime);
                    return saveToDb(existingVersion, context);
                })
                .onErrorResume(error -> {
                    updateFailureStatus(error, _ctxAccessor.getOrCrtVerStats(existingVersion.getGuid(),
                            existingVersion.getAgent().getExternalId(), context));
                    return Mono.empty();
                });
    }

    private Mono<Void> processChildEntities(
            AiAgentVersion agentVersion,
            AgentSummary agentSummary,
            GardenSyncContext context) {
        return _gardenService
                .getGardenAgentById(context.getProviderAccount(), agentSummary.getId())
                .flatMap(agentDetail ->
                        Flux.concat(savePersonalityTraits(agentVersion, agentDetail.getPersonalityTraits()),
                                saveGuardrails(agentVersion, agentDetail.getGuardrails(), context),
                                processTasks(agentVersion, agentDetail.getTasks(), context)
                        ))
                .then();
    }

    private Mono<Void> saveGuardrails(AiAgentVersion savedVersion,
            Guardrail guardrails, GardenSyncContext context) {
        if (guardrails == null || guardrails.getId() == null) {
            return Mono.empty();
        }
        return _guardrailHandler.processGuardrail(savedVersion, guardrails, context)
                .onErrorResume(error -> {
                    updateFailureStatus(error, _ctxAccessor.getOrCrtVerStats(savedVersion.getGuid(),
                            savedVersion.getAgent().getExternalId(), context));
                    return Mono.empty();
                }).then();
    }

    private Mono<Void> savePersonalityTraits(AiAgentVersion savedVersion, String personalityTraits) {
        if (StringUtil.isBlank(personalityTraits)) {
            return Mono.empty();
        }
        savedVersion.setPersonalityTraits(personalityTraits);
        return Mono.fromCallable(() -> _versionRepository.save(savedVersion))
                .subscribeOn(Schedulers.boundedElastic())
                .then();
    }

    private Flux<AiAgentTask> processTasks(
            AiAgentVersion agentVersion,
            List<Task> inputTasks,
            GardenSyncContext context) {
        removeAllTaskAssociationsForAgent(agentVersion, context);
        if (inputTasks == null || inputTasks.isEmpty()) {
            return Flux.empty();
        }
        return Flux.fromIterable(inputTasks)
                .concatMap(task -> processTask(
                        agentVersion, task, context))
                .onErrorResume(
                        error -> {
                            log.error(ERROR_PROCESSING_TASKS, agentVersion.getGuid(), error);
                            updateFailureStatus(error, _ctxAccessor.getOrCrtVerStats(agentVersion.getGuid(),
                                    agentVersion.getAgent().getExternalId(), context));
                            return Mono.empty();
                        });
    }

    private Mono<AiAgentTask> processTask(
            AiAgentVersion agentVersion,
            Task inputTask,
            GardenSyncContext context) {
        return _taskHandler.processTask(agentVersion, inputTask, context).onErrorResume(error -> {
            log.error(ERROR_PROCESSING_TASKS, agentVersion.getGuid(), error);
            updateFailureStatus(error,
                    _ctxAccessor.getOrCrtVerStats(agentVersion.getGuid(), agentVersion.getAgent().getExternalId(),
                            context));
            return Mono.empty();
        });
    }

    private void removeAllTaskAssociationsForAgent(AiAgentVersion version, GardenSyncContext context) {
        List<AiAgentTaskAssociation> agentVersionTaskAssociation = _taskAssociationRepository
                .findByRelatedEntityUidAndRelatedEntityType(version.getUid(), VERSION);

        List<Integer> taskIds = agentVersionTaskAssociation
                .stream()
                .map(agentTaskAssociation -> agentTaskAssociation
                        .getTask()
                        .getUid())
                .toList();
        _taskAssociationRepository
                .deleteAllByUidIn(agentVersionTaskAssociation.stream()
                        .map(AiAgentTaskAssociation::getUid)
                        .collect(Collectors.toList()));
        _toolAssociationRepository.deleteAllByRelatedEntityTypeAndRelatedEntityUidIn(
                TASK, taskIds);
        _toolAssociationRepository.deleteAllByRelatedEntityTypeAndRelatedEntityUid(
                VERSION, version.getUid());

        //delete existing instructions if any
        _taskHandler.deleteAllInstructionsForTask(taskIds);
        _taskHandler.deleteAllInstructionsForVersion(version);

        _taskRepository.deleteAllByUidIn(taskIds);
        GardenVersionSyncStats versionSyncStats =
                _ctxAccessor.getOrCrtVerStats(version.getGuid(), version.getAgent().getExternalId(), context);
        taskIds.forEach(taskId -> updateDeleteStats(versionSyncStats));
    }

    private Mono<AiAgentVersion> saveToDb(
            AiAgentVersion dbVersion,
            GardenSyncContext context) {

        return saveReactive(dbVersion)
                .onErrorResume(error -> {
                    log.error(ERROR_UPDATE_VERSION, dbVersion.getUid(), error);
                    updateFailureStatus(error,
                            _ctxAccessor.getOrCrtVerStats(dbVersion.getGuid(), dbVersion.getAgent().getExternalId(),
                                    context));
                    return Mono.error(error);
                });
    }

    public Mono<Void> deleteAllByAgentExcept(AiAgent agent, Set<String> versionsToRetain, GardenSyncContext context) {

        return findByAgentReactive(agent)
                .flatMapIterable(versions -> versions)
                .filter(version -> !versionsToRetain.contains(version.getGuid()))
                .flatMap(version -> deleteVersionAndChildren(agent, version, context))
                .then();

    }

    private Mono<Void> deleteVersionAndChildren(AiAgent agent, AiAgentVersion version, GardenSyncContext context) {

        SyncEntityPointInTimeInfo entityInfo = SyncEntityPointInTimeInfo.builder()
                                                            .entityUid(version.getUid())
                                                            .entityType(VERSION)
                                                            .entityGuid(version.getGuid())
                                                            .entityName(version.getName())
                                                            .entityVersion(version.getVersionString())
                                                            .build();
        return createSyncUserAudit(entityInfo, context)

                .flatMap(versionHistory -> deleteChildrenExcept(version, context)

                        .then(deleteVersion(agent, version, context))

                        .then(Mono.defer(() -> handleSyncCompletion(versionHistory, version, context)))

                        .onErrorResume(DataAccessException.class, ex -> {
                            log.error(ERROR_DELETE_VERSION_WITH_ID, version.getUid(), ex);
                            return handleSyncCompletion(versionHistory, version, context)
                                    .then(Mono.error(ex));
                        })).then();
    }

    private Mono<Void> deleteChildrenExcept(AiAgentVersion version, GardenSyncContext context) {

        Set<String> guardrailToRetain =
                _ctxAccessor.getOrCrtVerStats(version.getGuid(), version.getAgent().getExternalId(), context)
                .getToRetainGuardrails();

        return Flux.concat(
                        _guardrailHandler.deleteAllByVersionExcept(version, guardrailToRetain, context)
                )
                .then()
                .onErrorResume(error -> {
                    log.error(ERROR_DELETING_UNPROCESSED_CHILDREN, version.getUid(), error);
                    updateFailureStatus(error,
                            _ctxAccessor.getOrCrtVerStats(version.getGuid(), version.getAgent().getExternalId(),
                                    context));
                    return Mono.empty();
                });

    }

    private Mono<Void> deleteVersion(AiAgent agent, AiAgentVersion version,
            GardenSyncContext context) {
        version.setIsDeleted(true);
        version.setModifiedTime(AuditUtilImpl.getCurrentTime());
        return saveReactive(version)
                .flatMap(savedVersion -> {
                    updateDeleteStats(_ctxAccessor.getOrCrtAgntStats(agent, context));
                    return Mono.just(savedVersion);
                })
                .onErrorResume(DataAccessException.class, ex -> {
                    log.error(ERROR_DELETE_VERSION_WITH_ID, version.getUid(), ex);
                    return Mono.error(ex);
                }).then();
    }

    private void initSyncData(AiAgent agent, AiAgentVersion version, GardenSyncContext context) {
        GardenAgentSyncStats stats = _ctxAccessor.getOrCrtAgntStats(agent, context);
        stats.getToRetainVerIds()
                .add(version.getGuid());
        _ctxAccessor.getOrCrtVerStats(version.getGuid(), agent.getExternalId(), context);
    }

    private Mono<SyncUserAudit> handleSyncCompletion(
            SyncUserAudit versionSyncUserAudit,
            AiAgentVersion agentVersion,
            GardenSyncContext context) {

        GardenVersionSyncStats stats =
                _ctxAccessor.getOrCrtVerStats(agentVersion.getGuid(), agentVersion.getAgent().getExternalId(), context);
        return handleSyncCompletion(versionSyncUserAudit, stats);
    }

    private Optional<AiAgentVersion> getVersionReactive(AiAgent agent) {
         Optional<List<AiAgentVersion>> versions = _versionRepository.findByAgentAndIsDeletedFalse(agent);
         if(versions.isPresent() && !versions.get().isEmpty()) {
             return Optional.of(versions.get().get(0));
         }
         return Optional.empty();
    }

    private Mono<List<AiAgentVersion>> findByAgentReactive(AiAgent agent) {
        return Mono.fromCallable(() -> _versionRepository.findByAgentAndIsDeletedFalse(agent))
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(GeneralUtil::optionalToMono);
    }

    private Mono<AiAgentVersion> saveReactive(AiAgentVersion version) {
        return Mono.fromCallable(() -> _versionRepository.save(version))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error);
    }
}




