// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.boomigarden;

import com.boomi.aiagentregistry.entity.ProviderFields;
import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.exception.SyncException;
import com.boomi.aiagentregistry.service.sync.SyncHistoryService;
import com.boomi.aiagentregistry.service.sync.model.SyncContext;
import com.boomi.aiagentregistry.service.sync.model.SyncEntityPointInTimeInfo;
import com.boomi.aiagentregistry.service.sync.model.SyncStats;
import reactor.core.publisher.Mono;

import java.sql.Timestamp;
import java.time.Instant;

import static com.boomi.aiagentregistry.util.GeneralUtil.getInstantFromString;
import static com.boomi.aiagentregistry.util.GeneralUtil.tmStmpFromInst;
import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.COMPLETED;
import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.COMPLETED_WITH_ERROR;
import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.FAILED;
import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.SYSTEM_LIMIT_REACHED;

public class GardenSyncHandler  {

    protected final GardenService _gardenService;
    protected final SyncHistoryService _syncHistoryService;
    protected final GardenSyncContextAccessor _ctxAccessor;

    protected GardenSyncHandler(
            SyncHistoryService syncHistoryService,
            GardenSyncContextAccessor ctxAccessor,
            GardenService gardenService) {
        _syncHistoryService = syncHistoryService;
        _ctxAccessor = ctxAccessor;
        _gardenService = gardenService;
    }

    protected Mono<SyncUserAudit> handleSyncCompletion(
            SyncUserAudit syncUserAudit,
            SyncStats stats) {

        if(_ctxAccessor.exceededLimit(stats)){
            syncUserAudit.setSyncStatus(SYSTEM_LIMIT_REACHED);
            return _syncHistoryService.completeSyncFailure(syncUserAudit, "Exceeded limit");
        }

        String syncDetails = null;

        if (!_ctxAccessor.hasFailure(stats)) {
            return _syncHistoryService.completeSyncSuccess(syncUserAudit, stats, COMPLETED, syncDetails);

        } else if (_ctxAccessor.hasSuccess(stats)) {

            syncDetails = String.join(System.lineSeparator(), _ctxAccessor.getCombinedError(stats));
            return _syncHistoryService.completeSyncSuccess(syncUserAudit, stats, COMPLETED_WITH_ERROR, syncDetails);
        }

        String combinedError = String.join(System.lineSeparator(), _ctxAccessor.getCombinedError(stats));
        syncUserAudit.setSyncStatus(FAILED);
        return _syncHistoryService.completeSyncFailure(syncUserAudit, combinedError)
                      .flatMap(audit -> Mono.error(new SyncException(combinedError)));
    }

    protected Mono<SyncUserAudit> createSyncUserAudit(
            SyncEntityPointInTimeInfo entityInfo,
            SyncContext context) {
        return _syncHistoryService.startSync(entityInfo, context);
    }

    protected void updateFailureStatus(Throwable error, SyncStats stats) {

        stats.getHasFailure().set(true);
        stats.combineError(error.getMessage());
    }

    protected void updateLimitExceededStatus(SyncStats stats) {

        stats.getExceededLimit().set(true);
    }

    protected void updateAddStats(SyncStats stats) {
        stats.getChildrenAdded().incrementAndGet();
        stats.getHasSuccess().set(true);

    }

    protected void updateUpdateStats(SyncStats stats) {
        stats.getChildrenUpdated().incrementAndGet();
        stats.getHasSuccess().set(true);
    }

    protected void updateDeleteStats(SyncStats stats) {
        stats.getChildrenDeleted().incrementAndGet();
        stats.getHasSuccess().set(true);
    }

    protected boolean shouldUpdateEntity(String updatedAt, ProviderFields entity) {
        if (entity == null) {
            return true;
        }
        return isAfter(getInstantFromString(updatedAt), entity.getUpdatedAtProviderTime());
    }

    protected boolean shouldUpdateEntity(String updatedAtFromGarden, Timestamp existingEntityUpdatedAt) {
        if (existingEntityUpdatedAt == null) {
            return true;
        }
        return isAfter(getInstantFromString(updatedAtFromGarden), existingEntityUpdatedAt);
    }

    private boolean isAfter(Instant later, Timestamp earlier) {
        Timestamp laterTimestamp = tmStmpFromInst(later);
        return laterTimestamp.after(earlier);
    }
}
