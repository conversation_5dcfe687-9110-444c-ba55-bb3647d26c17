// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.model;

import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import software.amazon.awssdk.services.bedrock.BedrockAsyncClient;
import software.amazon.awssdk.services.bedrockagent.BedrockAgentAsyncClient;
import software.amazon.awssdk.services.bedrockagent.model.AgentVersion;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@Accessors(prefix = "_")
public class BedrockSyncContext extends SyncContext {

    private ProviderAcctSyncStats _providerAcctStats;
    private BedrockAgentAsyncClient _bedrockAgentClient;
    private BedrockAsyncClient _bedrockClient;

    private Map<VersionKey, AgentVersion> versionDetailsMap;

    //Needed to store agent version details that we get from aws that contains guardrails information to avoid
    //the need to call aws twice to get version details when processing guardrails of a version
    public void storeVersionDetails(String agentId, String versionString, AgentVersion awsVersion) {
        versionDetailsMap.put(new VersionKey(agentId, versionString), awsVersion);
    }

    public Optional<AgentVersion> getVersionDetails(String agentId, String versionString) {
        return Optional.ofNullable(versionDetailsMap.get(new VersionKey(agentId, versionString)));
    }


    public BedrockSyncContext(AiAgentProviderAccount aiAgentRegistryAccount) {
        super(aiAgentRegistryAccount);
        _providerAcctStats = new ProviderAcctSyncStats();
        versionDetailsMap = new ConcurrentHashMap<>();
    }

    private record VersionKey(String agentId, String versionString) {
    }

}
