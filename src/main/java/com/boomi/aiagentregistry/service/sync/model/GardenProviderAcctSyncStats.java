// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@Setter
@Getter
@Accessors(prefix = "_")
public class GardenProviderAcctSyncStats extends SyncStats {

    private Map<String, GardenAgentSyncStats> _agentsStats;
    private Set<String> _retainAgentsIds;
    private AtomicInteger _agentsAdded;
    private AtomicInteger _agentsDeleted;

    public GardenProviderAcctSyncStats() {
        super();
        _agentsStats = new ConcurrentHashMap<>();
        _retainAgentsIds = ConcurrentHashMap.newKeySet();
        _agentsAdded = new AtomicInteger(0);
        _agentsDeleted = new AtomicInteger(0);
    }

    public GardenAgentSyncStats getOrCreateAgentStats(String agentExternalId) {
        GardenAgentSyncStats stats = _agentsStats.get(agentExternalId);
        if (stats == null) {
            stats = _agentsStats.putIfAbsent(agentExternalId, new GardenAgentSyncStats());
            if (stats == null) {
                stats = _agentsStats.get(agentExternalId);
            }
        }
        return stats;
    }

    public void incrementAgentAdded() {
        // Add a new AtomicInteger field for tracking agents
        if (_agentsAdded == null) {
            _agentsAdded = new AtomicInteger(0);
        }
        _agentsAdded.incrementAndGet();
    }

    public void incrementAgentDeleted() {
        // Add a new AtomicInteger field for tracking agents
        if (_agentsDeleted == null) {
            _agentsDeleted = new AtomicInteger(0);
        }
        _agentsDeleted.incrementAndGet();
    }
}

