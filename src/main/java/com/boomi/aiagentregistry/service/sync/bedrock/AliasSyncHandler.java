// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.bedrock;

import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentAlias;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.repo.AiAgentAliasRepository;
import com.boomi.aiagentregistry.repo.AiAgentVersionRepository;
import com.boomi.aiagentregistry.service.sync.SyncContextAccessor;
import com.boomi.aiagentregistry.service.sync.SyncHistoryService;
import com.boomi.aiagentregistry.service.sync.model.BedrockSyncContext;
import com.boomi.aiagentregistry.util.AuditUtilImpl;
import com.boomi.aiagentregistry.util.GeneralUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import software.amazon.awssdk.services.bedrockagent.model.AgentAlias;
import software.amazon.awssdk.services.bedrockagent.model.AgentAliasRoutingConfigurationListItem;
import software.amazon.awssdk.services.bedrockagent.model.AgentAliasSummary;
import software.amazon.awssdk.services.bedrockagent.model.GetAgentAliasRequest;
import software.amazon.awssdk.utils.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.boomi.aiagentregistry.constant.ErrorMessages.AGENT_OR_ALIAS_SUMMARY_IS_NULL;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_DELETE_ALIAS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_FETCH_ALIAS_DETAILS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FAILED_TO_CREATE_ALIAS_FOR_AGENT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FAILED_TO_UPDATED_ALIAS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.NO_ROUTING_CONFIGURATION_FOUND_FOR_ALIAS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.TARGET_VERSION_IS_EMPTY_FOR_ALIAS;
import static com.boomi.aiagentregistry.mapper.AiAgentAliasMapper.ALIAS_MAPPER;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class AliasSyncHandler extends BedrockSyncHandler {

    private final AiAgentAliasRepository _aliasRepository;
    private final AiAgentVersionRepository _versionRepository;

    public AliasSyncHandler(
            AiAgentAliasRepository aliasRepository,
            AwsBedrockService awsBedrockService,
            AiAgentVersionRepository versionRepository,
            SyncContextAccessor ctxAccessor,
            SyncHistoryService syncHistoryService) {

        super(syncHistoryService, ctxAccessor, awsBedrockService);
        _aliasRepository = aliasRepository;
        _versionRepository = versionRepository;
    }

    public Mono<AiAgentAlias> processAlias(AiAgent agent, AgentAliasSummary aliasSummary, BedrockSyncContext context) {
        log.info("Processing alias {} of agent {} for account {}",
                aliasSummary.agentAliasId(),
                agent.getExternalId(),
                context.getProviderAccount().getProviderAccountName());
        _ctxAccessor.getOrCrtAgntStats(agent, context).getToRetainAliases().add(aliasSummary);

        Optional<AiAgentAlias> existingAliasOptional = findAliasByExternalIdAndAgent(agent, aliasSummary);

        if (!shouldUpdateEntity(aliasSummary.updatedAt(), existingAliasOptional.orElse(null))) {
            return Mono.empty();
        }

        Optional<AiAgentVersion> newVersion = findVersionForAlias(agent, aliasSummary);

        if (newVersion.isEmpty()) {
            return Mono.empty();
        }
        return fetchAgentAliasDetails(agent, aliasSummary, context)
                .flatMap(awsAlias -> handleAliasUpdate(agent, awsAlias, existingAliasOptional, newVersion, context)
                        .doOnSuccess(unused -> log.info("Finished processing alias {} of agent {} for account {}",
                                aliasSummary.agentAliasId(),
                                agent.getExternalId(),
                                context.getProviderAccount().getProviderAccountName())));
    }

    public Mono<Void> deleteByAgentExcept(
            AiAgent agent,
            Set<AgentAliasSummary> aliasesToRetain,
            BedrockSyncContext context) {

        return findVersionsByAgentReactive(agent)
                .flatMapIterable(versions -> versions)
                .flatMap(version -> deleteAliasesForVersion(agent, version, aliasesToRetain, context))
                .then();
    }

    private Mono<AiAgentAlias> handleAliasUpdate(
            AiAgent agent,
            AgentAlias awsAlias,
            Optional<AiAgentAlias> existingAlias,
            Optional<AiAgentVersion> newVersion,
            BedrockSyncContext context) {

        return existingAlias
                .map(alias -> updateExistingAlias(alias, awsAlias, agent, newVersion, context))
                .orElseGet(() -> createNewAlias(awsAlias, agent, newVersion, context));
    }

    private Mono<AiAgentAlias> createNewAlias(
            AgentAlias awsAlias,
            AiAgent agent,
            Optional<AiAgentVersion> newVersion,
            BedrockSyncContext context) {

        AiAgentProviderAccount account = context.getProviderAccount();
        AiAgentAlias newAlias = ALIAS_MAPPER.toEntityFromAwsAgentAliasCreate(awsAlias, newVersion.get(), account);
        log.info("Creating new alias {} for agent {}, version {} for account {}",
                awsAlias.agentAliasId(),
                agent.getUid(),
                newVersion.get().getUid(),
                account.getProviderAccountName());
        return saveReactive(newAlias)
                .flatMap(savedAlias -> {
                    log.info("Finished creating new alias {} for agent {}, version {} for account {}",
                            awsAlias.agentAliasId(),
                            agent.getUid(),
                            newVersion.get().getUid(),
                            account.getProviderAccountName());
                    updateAddStats(_ctxAccessor.getOrCrtAgntStats(agent, context));
                    return Mono.just(savedAlias);
                })
                .onErrorResume(error -> {
                    log.error(FAILED_TO_CREATE_ALIAS_FOR_AGENT, agent.getUid(), error);
                    return Mono.error(error);
                });
    }

    private Mono<AiAgentAlias> updateExistingAlias(
            AiAgentAlias existingAlias,
            AgentAlias awsAlias,
            AiAgent agent,
            Optional<AiAgentVersion> newVersion,
            BedrockSyncContext context) {

        ALIAS_MAPPER.toEntityFromAwsAgentAliasUpdate(existingAlias, awsAlias, newVersion.get());
        log.info("Updating  alias {} for agent {}, version {} for account {}",
                awsAlias.agentAliasId(),
                agent.getUid(),
                newVersion.get().getUid(),
                context.getProviderAccount().getProviderAccountName());

        existingAlias.setUpdatedAtProviderTime(GeneralUtil.tmStmpFromInst(awsAlias.updatedAt()));
        //incase it got deleted in previous sync
        existingAlias.setIsDeleted(false);
        return saveReactive(existingAlias)

                .flatMap(updatedAlias -> {
                    log.info("Finished updating  alias {} for agent {}, version {} for account {}",
                            awsAlias.agentAliasId(),
                            agent.getUid(),
                            newVersion.get().getUid(),
                            context.getProviderAccount().getProviderAccountName());
                    updateUpdateStats(_ctxAccessor.getOrCrtAgntStats(agent, context));
                    return Mono.just(updatedAlias);
                })

                .onErrorResume(error -> {
                    log.error(FAILED_TO_UPDATED_ALIAS, agent.getUid(), error);
                    return Mono.error(error);
                });
    }

    private Optional<AiAgentAlias> findAliasByExternalIdAndAgent(AiAgent agent, AgentAliasSummary aliasSummary) {
        log.info("Searching for alias {} for agent {}", aliasSummary.agentAliasId(), agent.getUid());
        Optional<AiAgentAlias> alias = _aliasRepository.findByExternalIdAndAgent(aliasSummary.agentAliasId(),
                agent.getUid());
        log.info("Finished searching for alias {} for agent {}", aliasSummary.agentAliasId(), agent.getUid());
        return alias;
    }

    private Optional<AiAgentVersion> findVersionForAlias(AiAgent agent, AgentAliasSummary aliasSummary) {
        if (agent == null || aliasSummary == null) {
            log.warn(AGENT_OR_ALIAS_SUMMARY_IS_NULL);
            return Optional.empty();
        }
        log.info("Searching for version for alias {} of agent {}",
                aliasSummary.agentAliasName(),
                agent.getUid());

        List<AgentAliasRoutingConfigurationListItem> routingConfigs = aliasSummary.routingConfiguration();
        if (!aliasSummary.hasRoutingConfiguration() || routingConfigs.isEmpty()) {
            log.warn(NO_ROUTING_CONFIGURATION_FOUND_FOR_ALIAS, aliasSummary.agentAliasId());
            return Optional.empty();
        }

        String targetVersion = routingConfigs.get(0).agentVersion();
        if (StringUtils.isEmpty(targetVersion)) {
            log.warn(TARGET_VERSION_IS_EMPTY_FOR_ALIAS, aliasSummary.agentAliasId());
            return Optional.empty();
        }

        Optional<AiAgentVersion> version =
                _versionRepository.findByAgentUidAndVersionString(agent.getUid(), targetVersion);
        log.info("Finished searching for version for alias {} of agent {}",
                aliasSummary.agentAliasName(),
                agent.getUid());
        return version;
    }

    private Mono<Void> deleteAliasesForVersion(
            AiAgent agent,
            AiAgentVersion version,
            Set<AgentAliasSummary> processedAliases,
            BedrockSyncContext context) {

        return  findAliasesByVersionReactive(version)
                .flatMapIterable(aliases -> aliases)
                .filter(dbAlias -> shouldDeleteAlias(dbAlias, version, processedAliases))
                .flatMap(alias -> markAliasAsDeleted(agent, alias, context))
                .onErrorResume(ex -> {
                    log.error(ERROR_DELETE_ALIAS, ex);
                    return Mono.error(ex);
                }).then();
    }

    private static boolean shouldDeleteAlias(
            AiAgentAlias dbAlias,
            AiAgentVersion version,
            Set<AgentAliasSummary> processedAliases) {

        return processedAliases
                .stream()
                .filter(summary -> {
                    List<AgentAliasRoutingConfigurationListItem> routingConfigs = summary.routingConfiguration();
                    return routingConfigs != null && !routingConfigs.isEmpty();
                })
                .noneMatch(summary -> isMatchingAlias(summary, version, dbAlias));
    }

    private static boolean isMatchingAlias(AgentAliasSummary summary, AiAgentVersion version, AiAgentAlias dbAlias) {

        String versionStr = summary.routingConfiguration().get(0).agentVersion();

        boolean isVersionEqual = version.getVersionString().equals(versionStr);

        boolean isExternalEqual = dbAlias.getExternalId().equals(summary.agentAliasId());

        return isVersionEqual && isExternalEqual;
    }

    private Mono<Void> markAliasAsDeleted(AiAgent agent, AiAgentAlias alias, BedrockSyncContext context) {
        log.info("Marking alias {} of agent {} for account {} as deleted",
                alias.getUid(),
                agent.getUid(),
                context.getProviderAccount().getProviderAccountName());
        alias.setIsDeleted(true);
        alias.setModifiedTime(AuditUtilImpl.getCurrentTime());
        return saveReactive(alias)

                .flatMap(savedAlias -> {
                    log.info("Finished marking alias {} of agent {} for account {} as deleted",
                            alias.getUid(),
                            agent.getUid(),
                            context.getProviderAccount().getProviderAccountName());
                    updateDeleteStats(_ctxAccessor.getOrCrtAgntStats(agent, context));
                    return Mono.just(savedAlias);
                })

                .onErrorResume(DataAccessException.class, ex -> {
                    log.error(ERROR_DELETE_ALIAS, ex);
                    return Mono.error(ex);
                }).then();
    }

    private Mono<List<AiAgentVersion>> findVersionsByAgentReactive(AiAgent agent) {
        log.info("Fetching versions for agent {}", agent.getUid());
        return Mono.fromCallable(() -> _versionRepository.findByAgentAndIsDeletedFalse(agent))
                .doOnSuccess(unused -> log.info("Fetched versions for agent {}", agent.getUid()))
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(GeneralUtil::optionalToMono);
    }

    public Mono<List<AiAgentAlias>> findAliasesByVersionReactive(AiAgentVersion version) {
        log.info("Fetching aliases for version {}", version.getUid());
        return Mono.fromCallable(() -> _aliasRepository.findByAgentVersionAndIsDeletedFalse(version))
                .doOnSuccess(unused -> log.info("Finished fetching aliases for version {}",
                        version.getUid()))
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(GeneralUtil::optionalToMono);
    }

    public Mono<AiAgentAlias> saveReactive(AiAgentAlias alias) {
        return Mono.fromCallable(() -> _aliasRepository.save(alias))
                .subscribeOn(Schedulers.boundedElastic());
    }
    private Mono<AgentAlias> fetchAgentAliasDetails(
            AiAgent agent,
            AgentAliasSummary aliasSummary,
            BedrockSyncContext context) {
        log.info("Fetching alias {} for agent {}", agent.getExternalId(), aliasSummary.agentAliasId());

        return Mono.create(sink -> {
            GetAgentAliasRequest request = GetAgentAliasRequest.builder()
                    .agentId(agent.getExternalId())
                    .agentAliasId(aliasSummary.agentAliasId())
                    .build();

            context.getBedrockAgentClient()
                    .getAgentAlias(request)
                    .whenCompleteAsync((response, throwable) -> {
                        if (throwable != null) {
                            log.error(ERROR_FETCH_ALIAS_DETAILS, throwable);
                            sink.error(throwable);
                            return;
                        }
                        log.info("Fetched alias {} for agent {}", agent.getExternalId(), aliasSummary.agentAliasId());
                        sink.success(response.agentAlias());
                    });
        });
    }
}


