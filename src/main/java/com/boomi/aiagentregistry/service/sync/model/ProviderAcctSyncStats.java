// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */

@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@Setter
@Getter
@Accessors(prefix = "_")
public class ProviderAcctSyncStats extends SyncStats {

    private Map<String, BedrockAgentSyncStats> _agentsStats;
    private Set<String> _retainAgntsIds;

    public ProviderAcctSyncStats() {
        super();
        _agentsStats = new ConcurrentHashMap<>();
        _retainAgntsIds = ConcurrentHashMap.newKeySet();
    }

    public BedrockAgentSyncStats getOrCreateAgentStats(String agentExternalId) {
        BedrockAgentSyncStats stats = _agentsStats.get(agentExternalId);
        if (stats == null) {
            stats = _agentsStats.putIfAbsent(agentExternalId, new BedrockAgentSyncStats());
            if (stats == null) {
                stats = _agentsStats.get(agentExternalId);
            }
        }
        return stats;
    }


}

