// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.model;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.util.GuidUtil;

import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */

@SuperBuilder
@Data
@Accessors(prefix = "_")
public abstract class SyncContext {
    public static final int SESSION_ID_LENGTH = 8;
    public static final String REACTIVE_LOGGING_PATTERN = "sync-provider-%s-%s-%s";
    private AiAgentProviderAccount _providerAccount;
    private final String _correlationId;
    private final String _syncTransactionGuid;
    private final AtomicInteger _syncedEntitiesCount = new AtomicInteger(0);
    private  int _accountSystemLimit;

    SyncContext(AiAgentProviderAccount aiAgentRegistryAccount) {
        _providerAccount = aiAgentRegistryAccount;
        _correlationId = correlationId(aiAgentRegistryAccount);
        _accountSystemLimit = 0;
        _syncTransactionGuid = syncTransactionGuid();
    }

    private static String correlationId(AiAgentProviderAccount account) {
        return String.format(REACTIVE_LOGGING_PATTERN,
                account.getUid(),
                UUID.randomUUID().toString().substring(0, SESSION_ID_LENGTH),
                System.currentTimeMillis()
        );
    }

    private static String syncTransactionGuid() {
        return GuidUtil.createSyncTransactionGuid();
    }
}
