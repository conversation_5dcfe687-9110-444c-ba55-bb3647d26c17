// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.boomigarden;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.util.UriComponentsBuilder;

@Configuration
@ConfigurationProperties(prefix = "garden.service")
@Getter
@Setter
@Accessors(prefix = "_")
public class GardenServiceProperties {
    private String _agentsPath;
    private String _toolPath;
    private String _agentPath;
    private String _agentInstallPath;
    private String _agentUninstallPath;

    public String buildToolPath(String toolType, String toolId) {
        return UriComponentsBuilder.fromPath(_toolPath)
                .buildAndExpand(toolType, toolId)
                .toUriString();
    }

    public String buildAgentPath(String agentId) {
        return UriComponentsBuilder.fromPath(_agentPath)
                .buildAndExpand(agentId)
                .toUriString();
    }

    public String buildAgentInstallPath(String agentId) {
        return UriComponentsBuilder.fromPath(_agentInstallPath).buildAndExpand(agentId).toUriString();
    }

    public String buildAgentUninstallPath(String agentId) {
        return UriComponentsBuilder.fromPath(_agentUninstallPath).buildAndExpand(agentId).toUriString();
    }
}
