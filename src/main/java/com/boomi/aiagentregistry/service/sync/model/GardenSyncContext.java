// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;

@Data
@SuperBuilder
@Accessors(prefix = "_")
@EqualsAndHashCode(callSuper = true)
public class GardenSyncContext extends SyncContext{

    private GardenProviderAcctSyncStats _providerAcctStats;

    public GardenSyncContext(AiAgentProviderAccount aiAgentRegistryAccount) {
        super(aiAgentRegistryAccount);
        _providerAcctStats = new GardenProviderAcctSyncStats();
    }
}
