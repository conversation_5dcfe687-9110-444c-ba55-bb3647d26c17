// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync;

import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.constant.ErrorMessages;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class SyncProviderCollection {
    private final Map<AiAgentProviderType, SyncProvider> _strategies;


    public SyncProviderCollection(List<SyncProvider> strategyList) {
        _strategies = strategyList.stream()
                .collect(Collectors.toMap(SyncProvider::getType, Function.identity()));
    }

    public SyncProvider getStrategy(AiAgentProviderType providerType) {
        SyncProvider strategy = _strategies.get(providerType);
        if (strategy == null) {
            log.warn(ErrorMessages.UNSUPPORTED_PROVIDER);
            throw new IllegalArgumentException(ErrorMessages.format(ErrorMessages.UNSUPPORTED_PROVIDER, providerType));
        }
        return strategy;
    }
}
