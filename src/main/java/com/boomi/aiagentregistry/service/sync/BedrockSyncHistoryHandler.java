// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.sync;

import reactor.core.publisher.Mono;
import com.boomi.aiagentregistry.entity.AgentEntitySyncLatest;
import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.service.sync.model.SyncStats;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus;

import org.springframework.stereotype.Component;

import static com.boomi.aiagentregistry.mapper.SyncLatestMapper.SYNC_LATEST_MAPPER;
import static com.boomi.aiagentregistry.mapper.SyncUserAuditMapper.SYNC_USER_AUDIT_MAPPER;

/**
 * <AUTHOR> Satpute
 */
@Component
public class BedrockSyncHistoryHandler implements SyncHistoryHandler {

    private final SyncContextAccessor _ctxAccessor;
    private final CommonSyncOperations _commonSyncOperations;

    public BedrockSyncHistoryHandler(SyncContextAccessor ctxAccessor, CommonSyncOperations commonSyncOperations) {
        _ctxAccessor = ctxAccessor;
        _commonSyncOperations = commonSyncOperations;
    }

    @Override
    public Mono<SyncUserAudit> completeSyncSuccess(SyncUserAudit existingSyncUserAudit, SyncStats stats,
            AiRegistryEntitySyncStatus status, String syncDetails) {
        SyncContextAccessor.EntityCounts counts = _ctxAccessor.getAllCounts(stats);
        SyncUserAudit syncUserAuditComplete =
                SYNC_USER_AUDIT_MAPPER.toSyncUserAuditEntityComplete(existingSyncUserAudit, status, counts,
                        syncDetails);
        syncUserAuditComplete.setLastUpdatedDate(syncUserAuditComplete.getSyncEndDate());

        return _commonSyncOperations.saveSyncUserAudit(syncUserAuditComplete).flatMap(savedSyncUserAuditComplete -> {
            AgentEntitySyncLatest latest = SYNC_LATEST_MAPPER.toSyncLatest(savedSyncUserAuditComplete);
            return _commonSyncOperations.upsertLatestSync(latest).thenReturn(savedSyncUserAuditComplete);
        }).onErrorResume(Mono::error);
    }
}
