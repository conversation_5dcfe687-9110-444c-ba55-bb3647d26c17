// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync;

import com.boomi.aiagentregistry.aop.ReactiveLogging;
import com.boomi.aiagentregistry.constant.ChangeOriginEnum;
import com.boomi.aiagentregistry.entity.AgentEntitySyncLatest;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.service.sync.model.SyncAudit;
import com.boomi.aiagentregistry.service.sync.model.SyncContext;
import com.boomi.aiagentregistry.service.sync.model.SyncEntityPointInTimeInfo;
import com.boomi.aiagentregistry.service.sync.model.SyncStats;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import static com.boomi.aiagentregistry.mapper.SyncLatestMapper.SYNC_LATEST_MAPPER;
import static com.boomi.aiagentregistry.mapper.SyncUserAuditMapper.SYNC_USER_AUDIT_MAPPER;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@Accessors(prefix = "_")
@ReactiveLogging
public class SyncHistoryService {

    private final SyncHistoryRouter _syncHistoryRouter;
    private final CommonSyncOperations _commonSyncOperations;

    public SyncHistoryService(SyncHistoryRouter syncHistoryRouter, CommonSyncOperations commonSyncOperations) {
        _syncHistoryRouter = syncHistoryRouter;
        _commonSyncOperations = commonSyncOperations;
    }

    public Mono<SyncUserAudit> startSync(SyncEntityPointInTimeInfo entityInfo, SyncContext context) {

        AiAgentProviderAccount providerAccount = context.getProviderAccount();
        SyncAudit syncAuditDto = SyncAudit.builder()
                                          .providerAccountUid(providerAccount.getUid())
                                          .providerAccountGuid(providerAccount.getGuid())
                                          .providerType(providerAccount.getProviderType())
                                          .providerAccountName(providerAccount.getProviderAccountName())
                                          .syncTransactionGuid(context.getSyncTransactionGuid())
                                          .entityUid(entityInfo.getEntityUid())
                                          .entityType(entityInfo.getEntityType())
                                          .entityGuid(entityInfo.getEntityGuid())
                                          .entityName(entityInfo.getEntityName())
                                          .entityVersion(entityInfo.getEntityVersion())
                                          .actionType(providerAccount.getSyncAuditActionType())
                                          .userId(providerAccount.getSyncAuditUserId())
                                          .idpAccountId(providerAccount.getSyncAuditIdpAccountId())
                                          .changeOrigin(ChangeOriginEnum.SYNC)
                                          .build();

        return Mono.defer(() -> createAndSaveSyncUserAudit(syncAuditDto));
    }

    private Mono<SyncUserAudit> createAndSaveSyncUserAudit(SyncAudit syncAudit) {

        SyncUserAudit syncUserAuditStart = SYNC_USER_AUDIT_MAPPER.toSyncUserAuditEntityStart(syncAudit);
        syncUserAuditStart.setLastUpdatedDate(syncUserAuditStart.getSyncStartDate());
        return _commonSyncOperations.saveSyncUserAudit(syncUserAuditStart).flatMap(savedSyncUserAuditStart -> {
            AgentEntitySyncLatest latest = SYNC_LATEST_MAPPER.toSyncLatest(savedSyncUserAuditStart);
            return _commonSyncOperations.upsertLatestSync(latest).thenReturn(savedSyncUserAuditStart);
                });
    }

    public Mono<SyncUserAudit> completeSyncSuccess(SyncUserAudit existingSyncUserAudit,
            SyncStats stats,
            AiRegistryEntitySyncStatus status,
            String syncDetails) {

        return _syncHistoryRouter.route(existingSyncUserAudit.getProviderType(), existingSyncUserAudit, stats, status,
                syncDetails);
    }

    public Mono<SyncUserAudit> completeSyncFailure(SyncUserAudit existingSyncUserAudit, String error) {

        SyncUserAudit syncUserAuditFailure = SYNC_USER_AUDIT_MAPPER.toSyncUserAuditEntityFailure(existingSyncUserAudit,
                error);
        syncUserAuditFailure.setLastUpdatedDate(syncUserAuditFailure.getSyncEndDate());

        return _commonSyncOperations.saveSyncUserAudit(syncUserAuditFailure)
                .flatMap(savedSyncUserAuditFailure -> {
                  AgentEntitySyncLatest latest = SYNC_LATEST_MAPPER.toSyncLatest(savedSyncUserAuditFailure);
                    return _commonSyncOperations.upsertLatestSync(latest)
                            .thenReturn(savedSyncUserAuditFailure);
              })
                .onErrorResume(Mono::error);
    }
}


