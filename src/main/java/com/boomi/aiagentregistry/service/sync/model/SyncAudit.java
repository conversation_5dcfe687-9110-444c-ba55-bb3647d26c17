// Copyright (c) 2025 Boom<PERSON>, LP

package com.boomi.aiagentregistry.service.sync.model;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import com.boomi.aiagentregistry.model.SyncUserAuditSharedFields;

@Data
@SuperBuilder
@Accessors(prefix = "_")
public class SyncAudit extends SyncUserAuditSharedFields {

    private String _syncTransactionGuid;
}
