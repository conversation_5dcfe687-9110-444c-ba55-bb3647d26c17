// Copyright (c) 2025 Boom<PERSON>, LP
package com.boomi.aiagentregistry.service.sync;

import reactor.core.publisher.Mono;
import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.service.sync.model.SyncStats;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus;

/**
 * <AUTHOR>
 */
public interface SyncHistoryHandler {
    public Mono<SyncUserAudit> completeSyncSuccess(SyncUserAudit existingSyncUserAudit,
            SyncStats stats,
            AiRegistryEntitySyncStatus status,
            String syncDetails);

}
