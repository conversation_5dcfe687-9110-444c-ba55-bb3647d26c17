// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.boomigarden;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.repo.AiAgentRepository;
import com.boomi.aiagentregistry.service.sync.SyncHistoryService;
import com.boomi.aiagentregistry.service.sync.model.GardenAgentSyncStats;
import com.boomi.aiagentregistry.service.sync.model.GardenProviderAcctSyncStats;
import com.boomi.aiagentregistry.service.sync.model.GardenSyncContext;
import com.boomi.aiagentregistry.service.sync.model.SyncEntityPointInTimeInfo;
import com.boomi.aiagentregistry.util.GeneralUtil;
import com.boomi.gardenagents.model.AgentSummary;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_DELETING_UNPROCESSED_ENTITIES_FOR_AGENT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_PROCESSING_AGENT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FAILED_TO_CREATE_AGENT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FAILED_TO_DELETE_AGENT;
import static com.boomi.aiagentregistry.mapper.AiAgentMapper.AI_AGENT_MAPPER;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.AGENT;

@Service
@Slf4j
public class GardenAgentSyncHandler extends GardenSyncHandler {

    private final AiAgentRepository _agentRepository;
    private final GardenVersionSyncHandler _versionHandler;


    GardenAgentSyncHandler(
            AiAgentRepository agentRepository,
            GardenVersionSyncHandler gardenVersionSyncHandler,
            SyncHistoryService syncHistoryService,
            GardenSyncContextAccessor ctxAccessor,
            GardenService gardenService) {
        super(syncHistoryService, ctxAccessor, gardenService);
        _agentRepository = agentRepository;
        _versionHandler = gardenVersionSyncHandler;
    }

    public Mono<SyncUserAudit> processAgent(AgentSummary agentSummary, GardenSyncContext context) {

        return Mono.defer(() -> getOrCreateAgent(agentSummary, context).flatMap(agent -> {
            SyncEntityPointInTimeInfo entityInfo = SyncEntityPointInTimeInfo.builder()
                                                                            .entityUid(agent.getUid())
                                                                            .entityType(AGENT)
                                                                            .entityGuid(agent.getGuid())
                                                                            .build();
            return createSyncUserAudit(entityInfo, context).flatMap(
                    agentSyncUserAudit -> processChildren(agent, agentSummary, agentSyncUserAudit, context));
        }));
    }

    private Mono<SyncUserAudit> processChildren(AiAgent agent,
            AgentSummary versionSummary, SyncUserAudit agentSyncUserAudit,
            GardenSyncContext context) {
        GardenAgentSyncStats agentStats = _ctxAccessor.getOrCrtAgntStats(agent, context);
        return processChildEntities(agent, versionSummary, context)
               .then(Mono.defer(() -> handleSyncCompletion(agentSyncUserAudit,
                        agentStats)))
                .onErrorResume(unused -> {
                    log.error(ERROR_PROCESSING_AGENT, agent.getUid());
                    return handleSyncCompletion(agentSyncUserAudit, agentStats);
                });
    }

    public Mono<Void> deleteAllByAccountExcept(
            AiAgentProviderAccount account,
            Set<String> agentsToRetain,
            GardenSyncContext context) {
        return findAgentByRegistryAccountReactive(account)
                .flatMapIterable(agents -> agents)
                .filter(agent -> !agentsToRetain.contains(agent.getExternalId()))
                .flatMap(agent -> deleteAgentAndChildren(agent, context))
                .then();
    }

    private Mono<Void> deleteAgent(AiAgent agent, GardenSyncContext context) {
        agent.setIsDeleted(true);
        return saveAgentReactive(agent)
                .flatMap(savedAgent -> {
                    GardenProviderAcctSyncStats providerAccountStats = _ctxAccessor.getProviderAcctStats(context);
                    providerAccountStats.incrementAgentDeleted();
                    return Mono.empty();
                })
                .then()
                .onErrorResume(error -> {
                    updateFailureStatus(error, _ctxAccessor.getOrCrtAgntStats(agent, context));
                    return Mono.empty();
                });

    }

    //if this method is called for an agent that has been deleted on provider side, it will send
    //an empty set for the processed child entity which in return will delete all children
    private Mono<Void> deleteChildrenExcept(AiAgent agent, GardenSyncContext context) {
        Set<String> versionsToRetain = _ctxAccessor.getOrCrtAgntStats(agent, context).getToRetainVerIds();

        return _versionHandler.deleteAllByAgentExcept(agent, versionsToRetain, context)
                .onErrorResume(
                        error -> {
                            log.error(ERROR_DELETING_UNPROCESSED_ENTITIES_FOR_AGENT, agent.getUid(), error);
                            updateFailureStatus(error, _ctxAccessor.getOrCrtAgntStats(agent, context));
                            return Mono.empty();
                        });
    }

    private Mono<Void> processChildEntities(AiAgent agent,
            AgentSummary versionSummary,
            GardenSyncContext context) {

        return processVersion(agent, versionSummary, context)
                .then();
    }

    private Mono<AiAgentVersion> processVersion(
            AiAgent agent,
            AgentSummary versionSummary,
            GardenSyncContext context) {
        return _versionHandler.processVersion(agent, versionSummary, context)
                .onErrorResume(
                        error -> {
                            log.error(ERROR_PROCESSING_AGENT, agent.getUid(), error);
                            updateFailureStatus(error, _ctxAccessor.getOrCrtAgntStats(agent, context));
                            return Mono.empty();
                        });
    }

    private Mono<AiAgent> getOrCreateAgent(AgentSummary agentSummary, GardenSyncContext context) {

        return findExistingAgent(_ctxAccessor.getPrvAcct(context), agentSummary.getId())
                .switchIfEmpty(Mono.defer(() -> handleNewAgent(agentSummary, context)));
    }


    private Mono<AiAgent> handleNewAgent(AgentSummary agentSummary, GardenSyncContext context) {
        GardenProviderAcctSyncStats stats = _ctxAccessor.getProviderAcctStats(context);
        stats.incrementAgentAdded();
        return createAgent(agentSummary, context);
    }

    private Mono<AiAgent> createAgent(AgentSummary agentSummary, GardenSyncContext context) {
        try {
            AiAgent newAgent = AI_AGENT_MAPPER.toEntityFromGardenAgent(agentSummary, context.getProviderAccount());

            return saveAgentReactive(newAgent)
                    .onErrorResume(ex -> {
                        log.error(FAILED_TO_CREATE_AGENT, ex);
                        return Mono.error(ex);
                    });
        } catch (Exception ex) {
            return Mono.error(ex);
        }
    }

    private Mono<SyncUserAudit> deleteAgentAndChildren(AiAgent agent, GardenSyncContext context) {
        _ctxAccessor.getOrCrtAgntStats(agent, context);
        GardenAgentSyncStats agentStats = _ctxAccessor.getOrCrtAgntStats(agent, context);
        SyncEntityPointInTimeInfo entityInfo = SyncEntityPointInTimeInfo.builder()
                                                                        .entityUid(agent.getUid())
                                                                        .entityType(AGENT)
                                                                        .entityGuid(agent.getGuid())
                                                                        .build();
        return createSyncUserAudit(entityInfo, context).flatMap(agentSyncUserAudit -> deleteChildrenExcept(agent,
                context)
                        .then(deleteAgent(agent, context))
                        .then(Mono.defer(() -> handleSyncCompletion(agentSyncUserAudit, agentStats)))
                        .onErrorResume(error -> {
                            log.error(FAILED_TO_DELETE_AGENT, agent.getUid(), error);
                            return handleSyncCompletion(agentSyncUserAudit, agentStats
                            );
                        }));

    }

    private Mono<AiAgent> findExistingAgent(AiAgentProviderAccount account, String externalId) {
        return Mono.fromCallable(() -> _agentRepository.findByAiAgentProviderAccountAndExternalId(account, externalId))
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(GeneralUtil::optionalToMono)
                .onErrorResume(Mono::error);
    }


    private Mono<AiAgent> saveAgentReactive(AiAgent agent) {
        return Mono.fromCallable(() -> _agentRepository.save(agent))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error);
    }


    private Mono<List<AiAgent>> findAgentByRegistryAccountReactive(AiAgentProviderAccount account) {
        return Mono.fromCallable(() ->
                        _agentRepository.findByAiAgentProviderAccountAndIsDeleted(account, false))
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(GeneralUtil::optionalToMono)
                .onErrorResume(Mono::error);
    }
}
