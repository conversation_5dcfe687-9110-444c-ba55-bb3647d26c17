// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */

@EqualsAndHashCode(callSuper = true)
@Setter
@Getter
@SuperBuilder
@Accessors(prefix = "_")
public class BedrockKbSyncStats extends SyncStats {
    private Set<String> _toRetainDataSrcIds;
    private AtomicBoolean _failedToFetchDataSources = new AtomicBoolean(false);

    BedrockKbSyncStats() {
        super();
        _toRetainDataSrcIds = ConcurrentHashMap.newKeySet();
    }
}
