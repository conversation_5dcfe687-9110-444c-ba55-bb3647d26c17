// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.service.sync;

import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */

public interface SyncProvider {

    AiAgentProviderType getType();

    Mono<SyncUserAudit> sync(AiAgentProviderAccount aiAgentRegistryAccount);

}
