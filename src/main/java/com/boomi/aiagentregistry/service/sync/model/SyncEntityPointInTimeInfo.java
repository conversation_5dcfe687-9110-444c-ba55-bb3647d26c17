// Copyright (c) 2025 Boomi, LP

package com.boomi.aiagentregistry.service.sync.model;

import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(prefix = "_")
public class SyncEntityPointInTimeInfo {
    private Integer _entityUid;
    private AiRegistryEntityType _entityType;
    private String _entityVersion;
    private String _entityName;
    private String _entityGuid;
}

