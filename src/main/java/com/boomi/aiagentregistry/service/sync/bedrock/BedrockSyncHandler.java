// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.bedrock;

import com.boomi.aiagentregistry.aop.ReactiveLogging;
import com.boomi.aiagentregistry.entity.ProviderFields;
import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.exception.SyncException;
import com.boomi.aiagentregistry.service.sync.SyncContextAccessor;
import com.boomi.aiagentregistry.service.sync.SyncHistoryService;
import com.boomi.aiagentregistry.service.sync.model.BedrockSyncContext;
import com.boomi.aiagentregistry.service.sync.model.SyncEntityPointInTimeInfo;
import com.boomi.aiagentregistry.service.sync.model.SyncStats;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.sql.Timestamp;
import java.time.Instant;

import static com.boomi.aiagentregistry.util.GeneralUtil.tmStmpFromInst;
import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.COMPLETED;
import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.COMPLETED_WITH_ERROR;
import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.FAILED;
import static com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus.SYSTEM_LIMIT_REACHED;

/**
 * <AUTHOR>
 */

@Service
@Setter
@Getter
@SuperBuilder
@Accessors(prefix = "_")
@Slf4j
@ReactiveLogging
public abstract class BedrockSyncHandler {
    protected final SyncHistoryService _syncHistoryService;
    protected final SyncContextAccessor _ctxAccessor;
    protected final AwsBedrockService _awsBedrockService;

    protected BedrockSyncHandler(
            SyncHistoryService syncHistoryService,
            SyncContextAccessor ctxAccessor,
            AwsBedrockService awsBedrockService) {
        _syncHistoryService = syncHistoryService;
        _ctxAccessor = ctxAccessor;
        _awsBedrockService = awsBedrockService;
    }

    protected Mono<SyncUserAudit> handleSyncCompletion(
            SyncUserAudit syncUserAudit,
            SyncStats stats) {

        if(_ctxAccessor.exceededLimit(stats)){
            syncUserAudit.setSyncStatus(SYSTEM_LIMIT_REACHED);
            return _syncHistoryService.completeSyncFailure(syncUserAudit, "Exceeded limit");
        }

        String syncDetails = null;

        if (!_ctxAccessor.hasFailure(stats)) {
            return _syncHistoryService.completeSyncSuccess(syncUserAudit, stats, COMPLETED, syncDetails);

        } else if (_ctxAccessor.hasSuccess(stats)) {

            syncDetails = String.join(System.lineSeparator(), _ctxAccessor.getCombinedError(stats));
            return _syncHistoryService.completeSyncSuccess(syncUserAudit, stats, COMPLETED_WITH_ERROR, syncDetails);
        }

        String combinedError = String.join(System.lineSeparator(), _ctxAccessor.getCombinedError(stats));
        syncUserAudit.setSyncStatus(FAILED);
        return _syncHistoryService.completeSyncFailure(syncUserAudit, combinedError)
                 .flatMap(audit -> Mono.error(new SyncException(combinedError)));
    }

    protected Mono<SyncUserAudit> createSyncUserAudit(
            SyncEntityPointInTimeInfo entityInfo,
            BedrockSyncContext context) {
        return _syncHistoryService.startSync(entityInfo, context);
    }

    protected void updateFailureStatus(String error, SyncStats stats) {
        stats.getHasFailure().set(true);
        stats.combineError(error + "\n");
    }

    protected void updateAddStats(SyncStats stats) {
        stats.getChildrenAdded().incrementAndGet();
        stats.getHasSuccess().set(true);

    }

    protected void updateUpdateStats(SyncStats stats) {
        stats.getChildrenUpdated().incrementAndGet();
        stats.getHasSuccess().set(true);
    }

    protected void updateDeleteStats(SyncStats stats) {
        stats.getChildrenDeleted().incrementAndGet();
        stats.getHasSuccess().set(true);
    }

    protected boolean shouldUpdateEntity(Instant updatedAt, ProviderFields entity) {
        if (entity == null) {
            return true;
        }
        return isAfter(updatedAt, entity.getUpdatedAtProviderTime());
    }

    private boolean isAfter(Instant later, Timestamp earlier) {
        Timestamp laterTimestamp = tmStmpFromInst(later);
        return laterTimestamp.after(earlier);
    }

    protected void updateLimitExceededStatus(SyncStats stats) {
        stats.getExceededLimit().set(true);
    }


}
