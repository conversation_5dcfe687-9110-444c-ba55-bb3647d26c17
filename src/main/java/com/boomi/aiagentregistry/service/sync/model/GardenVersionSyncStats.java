// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@EqualsAndHashCode(callSuper = true)
@Setter
@Getter
@SuperBuilder
@Accessors(prefix = "_")
public class GardenVersionSyncStats extends SyncStats {

    private Map<String, GardenTaskSyncStats> _taskStatus;
    private Set<String> _toRetainGuardrails;
    private Set<String> _toRetainTasks;


    GardenVersionSyncStats() {
        super();
        _taskStatus = new ConcurrentHashMap<>();
        _toRetainGuardrails = ConcurrentHashMap.newKeySet();
        _toRetainTasks = ConcurrentHashMap.newKeySet();
    }

    public GardenTaskSyncStats getOrCreateTaskStats(String taskName) {
        GardenTaskSyncStats stats = _taskStatus.get(taskName);
        if (stats == null) {
            stats = _taskStatus.putIfAbsent(taskName,
                    new GardenTaskSyncStats());
            if (stats == null) {
                stats = _taskStatus.get(taskName);
            }
        }
        return stats;
    }
}
