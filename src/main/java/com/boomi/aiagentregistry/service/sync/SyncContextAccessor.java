// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync;

import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.exception.SyncException;
import com.boomi.aiagentregistry.service.sync.model.BedrockAgentSyncStats;
import com.boomi.aiagentregistry.service.sync.model.BedrockKbSyncStats;
import com.boomi.aiagentregistry.service.sync.model.BedrockSyncContext;
import com.boomi.aiagentregistry.service.sync.model.BedrockVersionSyncStats;
import com.boomi.aiagentregistry.service.sync.model.ProviderAcctSyncStats;
import com.boomi.aiagentregistry.service.sync.model.SyncStats;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.bedrockagent.model.AgentKnowledgeBaseSummary;
import software.amazon.awssdk.services.bedrockagent.model.AgentSummary;
import software.amazon.awssdk.services.bedrockagent.model.AgentVersion;
import software.amazon.awssdk.services.bedrockagent.model.AgentVersionSummary;

import java.util.HashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */

@Component
public class SyncContextAccessor {

    public static final String UNKNOWN_SYNC_STATS_TYPE = "Unknown SyncStats type";
    public static final String EMPTY_STRING = "";

    public BedrockVersionSyncStats getOrCrtVerStats(AiAgentVersion version, BedrockSyncContext context) {

        BedrockAgentSyncStats agentStats = context.getProviderAcctStats()
                .getOrCreateAgentStats(version.getExternalId());
        return agentStats.getOrCreateVersionStats(version.getVersionString());
    }

    public BedrockKbSyncStats getOrCrtKbStats(
            AiAgentVersion version,
            AgentKnowledgeBaseSummary kb,
            BedrockSyncContext context) {
        BedrockVersionSyncStats versionStats = getOrCrtVerStats(version, context);
        return versionStats.getOrCreateKbStats(kb.knowledgeBaseId());
    }

    public BedrockKbSyncStats getOrCrtKbStats(
            AiAgentVersion version,
            AiAgentTool tool,
            BedrockSyncContext context) {
        BedrockVersionSyncStats stats = getOrCrtVerStats(version, context);
        return stats.getOrCreateKbStats(tool.getExternalId());
    }


    private BedrockVersionSyncStats getOrCrtVerStats(AiAgent agent, String version, BedrockSyncContext context) {
        BedrockAgentSyncStats agentStats = context.getProviderAcctStats().getOrCreateAgentStats(agent.getExternalId());
        return agentStats.getOrCreateVersionStats(version);
    }

    public BedrockVersionSyncStats getOrCrtVerStats(AiAgent agent, AgentVersionSummary version,
            BedrockSyncContext context) {
        return getOrCrtVerStats(agent, version.agentVersion(), context);
    }

    public BedrockAgentSyncStats getOrCrtAgntStats(AiAgent agent, BedrockSyncContext context) {
        return getOrCrtAgntStats(agent.getExternalId(), context);
    }

    public BedrockAgentSyncStats getOrCrtAgntStats(AgentSummary summary, BedrockSyncContext context) {
        return getOrCrtAgntStats(summary.agentId(), context);

    }

    public BedrockAgentSyncStats getOrCrtAgntStats(String agentExternalId, BedrockSyncContext context) {
        return context.getProviderAcctStats().getOrCreateAgentStats(agentExternalId);
    }

    public ProviderAcctSyncStats getProviderAcctStats(BedrockSyncContext context) {
        return context.getProviderAcctStats();
    }

    public AiAgentProviderAccount getPrvAcct(BedrockSyncContext context) {
        return context.getProviderAccount();
    }


    public boolean hasSuccess(SyncStats stats) {
        if (stats instanceof BedrockKbSyncStats kbStats) {
            return kbHasSuccess(kbStats);

        } else if (stats instanceof BedrockVersionSyncStats verStats) {
            return versionHasSuccess(verStats);
        } else if (stats instanceof BedrockAgentSyncStats agentStats) {
            return agentHasSuccess(agentStats);
        } else if (stats instanceof ProviderAcctSyncStats prvActStats) {
            return prvActHasSuccess(prvActStats);
        }
        throw new SyncException(UNKNOWN_SYNC_STATS_TYPE);
    }

    public boolean hasFailure(SyncStats stats) {
        if (stats instanceof BedrockKbSyncStats kbStats) {
            return kbHasFailure(kbStats);
        } else if (stats instanceof BedrockVersionSyncStats verStats) {
            return versionHasFailure(verStats);
        } else if (stats instanceof BedrockAgentSyncStats agentStats) {
            return agentHasFailure(agentStats);
        } else if (stats instanceof ProviderAcctSyncStats prvActStats) {
            return prvActHasFailure(prvActStats);
        }
        throw new SyncException(UNKNOWN_SYNC_STATS_TYPE);
    }

    private int getAddedEntities(SyncStats stat) {
        if (stat == null) {
            return 0;
        }

        if (stat instanceof BedrockKbSyncStats kbStats) {
            return countKbAddedEntities(kbStats);
        } else {
            if (stat instanceof ProviderAcctSyncStats providerAcctSyncStats) {
                return countPrvAcctAddedEntities(providerAcctSyncStats);
            } else {
                if (stat instanceof BedrockAgentSyncStats agentStats) {
                    return countAgentAddedEntities(agentStats);
                } else {
                    if (stat instanceof BedrockVersionSyncStats versionSyncStats) {
                        return countVersionAddedEntities(versionSyncStats);
                    }
                }
            }
        }

        return 0;
    }

    private int getUpdatedEntities(SyncStats stat) {
        if (stat == null) {
            return 0;
        }
        if (stat instanceof BedrockKbSyncStats kbStats) {
            return countKbUpdatedEntities(kbStats);
        } else if (stat instanceof ProviderAcctSyncStats providerAcctSyncStats) {
            return countPrvAcctUpdatedEntities(providerAcctSyncStats);
        } else if (stat instanceof BedrockAgentSyncStats agentStats) {
            return countAgentUpdatedEntities(agentStats);
        } else if (stat instanceof BedrockVersionSyncStats versionSyncStats) {
            return countVersionUpdatedEntities(versionSyncStats);
        }

        return 0;
    }

    private int getDeletedEntities(SyncStats stat) {
        if (stat == null) {
            return 0;
        }

        if (stat instanceof BedrockKbSyncStats kbStats) {
            return countKbDeletedEntities(kbStats);
        } else if (stat instanceof ProviderAcctSyncStats providerAcctSyncStats) {
            return countPrvAcctDeletedEntities(providerAcctSyncStats);
        } else if (stat instanceof BedrockAgentSyncStats agentStats) {
            return countAgentDeletedEntities(agentStats);
        } else if (stat instanceof BedrockVersionSyncStats versionSyncStats) {
            return countVersionDeletedEntities(versionSyncStats);
        }

        return 0;
    }

    public Set<String> getCombinedError(SyncStats stats) {
        Set<String> combinedMessage;

        if (stats instanceof BedrockKbSyncStats kbStats) {
            combinedMessage = getKbError(kbStats);
        } else if (stats instanceof BedrockVersionSyncStats verStats) {
            combinedMessage = getVersionError(verStats);
        } else if (stats instanceof BedrockAgentSyncStats agentStats) {
            combinedMessage = getAgentError(agentStats);
        } else if (stats instanceof ProviderAcctSyncStats prvActStats) {
            combinedMessage = getProviderAcctError(prvActStats);
        } else {
            throw new SyncException(UNKNOWN_SYNC_STATS_TYPE);
        }

        return combinedMessage;


    }

    private Set<String> getProviderAcctError(ProviderAcctSyncStats stats) {
        Set<String> errorMessages = new HashSet<>();

        // Add the initial error message if it exists
        String initialError = stats.getErrorMessage();
        if (initialError != null && !initialError.trim().isEmpty()) {
            errorMessages.add(initialError.trim());
        }

        // Collect and add all agent errors
        stats.getAgentsStats().values().stream().map(this::getAgentError).flatMap(Set::stream).map(String::trim)
                .forEach(errorMessages::add);

        return errorMessages;
    }

    private Set<String> getAgentError(BedrockAgentSyncStats stats) {
        Set<String> errorMessages = new HashSet<>();

        // Add the initial error message if it exists
        String initialError = stats.getErrorMessage();
        if (initialError != null && !initialError.trim().isEmpty()) {
            errorMessages.add(initialError.trim());
        }

        // Add all version errors
        stats.getVersionStats().values().stream().map(this::getVersionError).forEach(errorMessages::addAll);

        return errorMessages;
    }

    private Set<String> getKbError(BedrockKbSyncStats stats) {
        Set<String> errorMessages = new HashSet<>();

        String errorMessage = stats.getErrorMessage();
        if (errorMessage != null && !errorMessage.trim().isEmpty()) {
            errorMessages.add(errorMessage.trim());
        }

        return errorMessages;
    }

    private Set<String> getVersionError(BedrockVersionSyncStats stats) {
        Set<String> errorMessages = new HashSet<>();

        String initialError = stats.getErrorMessage();
        if (initialError != null && !initialError.trim().isEmpty()) {
            errorMessages.add(initialError.trim());
        }

        // Add each set of errors directly
        stats.getKbStats().values().stream().map(this::getKbError).filter(Objects::nonNull).flatMap(Set::stream)
                .forEach(errorMessages::add);

        return errorMessages;
    }

    private boolean kbHasSuccess(BedrockKbSyncStats stats) {
        return stats.getHasSuccess().get();
    }

    private boolean versionHasSuccess(BedrockVersionSyncStats stats) {
        if (stats.getHasSuccess().get()) {
            return true;
        }
        return stats.getKbStats()
                .values()
                .stream()
                .anyMatch(this::kbHasSuccess);
    }

    private boolean agentHasSuccess(BedrockAgentSyncStats stats) {
        if (stats.getHasSuccess().get()) {
            return true;
        }

        return stats.getVersionStats()
                .values()
                .stream()
                .anyMatch(this::versionHasSuccess);
    }

    private boolean prvActHasSuccess(ProviderAcctSyncStats stats) {
        if (stats.getHasSuccess().get()) {
            return true;
        }

        return stats.getAgentsStats()
                .values()
                .stream()
                .anyMatch(this::agentHasSuccess);
    }

    private boolean kbHasFailure(BedrockKbSyncStats stats) {
        return stats.getHasFailure().get();
    }

    private boolean versionHasFailure(BedrockVersionSyncStats stats) {
        if (stats.getHasFailure().get()) {
            return true;
        }

        return stats.getKbStats()
                .values()
                .stream()
                .anyMatch(this::kbHasFailure);
    }

    private boolean agentHasFailure(BedrockAgentSyncStats stats) {
        if (stats.getHasFailure().get()) {
            return true;
        }

        return stats.getVersionStats()
                .values()
                .stream()
                .anyMatch(this::versionHasFailure);
    }

    private boolean prvActHasFailure(ProviderAcctSyncStats stats) {
        if (stats.getHasFailure().get()) {
            return true;
        }

        return stats.getAgentsStats()
                .values()
                .stream()
                .anyMatch(this::agentHasFailure);
    }

    private int countKbUpdatedEntities(BedrockKbSyncStats kbStats) {
        return kbStats.getChildrenUpdated().get();
    }

    private int countVersionUpdatedEntities(BedrockVersionSyncStats versionSyncStats) {
        int totalCount = versionSyncStats.getChildrenUpdated().get();

        totalCount += versionSyncStats.getKbStats().values().stream().mapToInt(
                this::countKbUpdatedEntities).sum();

        return totalCount;
    }

    private int countKbDeletedEntities(BedrockKbSyncStats kbStats) {
        return kbStats.getChildrenDeleted().get();
    }

    private int countVersionDeletedEntities(BedrockVersionSyncStats versionSyncStats) {
        int totalCount = versionSyncStats.getChildrenDeleted().get();

        totalCount += versionSyncStats.getKbStats().values().stream().mapToInt(
                this::countKbDeletedEntities).sum();

        return totalCount;
    }

    private int countPrvAcctDeletedEntities(ProviderAcctSyncStats providerAcctSyncStats) {
        int totalCount = providerAcctSyncStats.getChildrenDeleted().get();

        totalCount += providerAcctSyncStats.getAgentsStats().values().stream().mapToInt(
                this::countAgentDeletedEntities).sum();

        return totalCount;
    }

    private int countPrvAcctUpdatedEntities(ProviderAcctSyncStats providerAcctSyncStats) {
        int totalCount = providerAcctSyncStats.getChildrenUpdated().get();

        totalCount += providerAcctSyncStats.getAgentsStats().values().stream().mapToInt(
                this::countAgentUpdatedEntities).sum();

        return totalCount;
    }

    private int countKbAddedEntities(BedrockKbSyncStats kbStats) {
        return kbStats.getChildrenAdded().get();
    }

    private int countVersionAddedEntities(BedrockVersionSyncStats versionSyncStats) {
        int totalCount = versionSyncStats.getChildrenAdded().get();

        totalCount += versionSyncStats.getKbStats().values().stream().mapToInt(
                this::countKbAddedEntities).sum();

        return totalCount;
    }

    private int countAgentAddedEntities(BedrockAgentSyncStats agentStats) {

        int totalCount = agentStats.getChildrenAdded().get();

        totalCount += agentStats.getVersionStats().values().stream().mapToInt(
                this::countVersionAddedEntities).sum();

        return totalCount;
    }

    private int countPrvAcctAddedEntities(ProviderAcctSyncStats providerAcctSyncStats) {
        int totalCount = providerAcctSyncStats.getChildrenAdded().get();

        totalCount += providerAcctSyncStats.getAgentsStats().values().stream().mapToInt(
                this::countAgentAddedEntities).sum();

        return totalCount;
    }

    private int countAgentUpdatedEntities(BedrockAgentSyncStats agentStats) {

        int totalCount = agentStats.getChildrenUpdated().get();

        totalCount += agentStats.getVersionStats().values().stream().mapToInt(
                this::countVersionUpdatedEntities).sum();

        return totalCount;
    }

    private int countAgentDeletedEntities(BedrockAgentSyncStats agentStats) {

        int totalCount = agentStats.getChildrenDeleted().get();

        totalCount += agentStats.getVersionStats().values().stream().mapToInt(
                this::countVersionDeletedEntities).sum();

        return totalCount;
    }

    public boolean exceededLimit(SyncStats stats) {
        if (stats instanceof BedrockAgentSyncStats agentStats) {
            return agentExceededLimit(agentStats);
        } else if (stats instanceof ProviderAcctSyncStats prvActStats) {
            return providerAccountExceededLimit(prvActStats);
        }
        return false;
    }

    private boolean providerAccountExceededLimit(ProviderAcctSyncStats stats) {
        if (stats.getExceededLimit().get()) {
            return true;
        }

        return stats.getAgentsStats()
                .values()
                .stream()
                .anyMatch(this::agentExceededLimit);
    }

    private boolean agentExceededLimit(BedrockAgentSyncStats agentStats) {
        return agentStats.getExceededLimit().get();
    }


    public record EntityCounts(int added, int updated, int deleted) {
    }

    public EntityCounts getAllCounts(SyncStats stat) {
        return new EntityCounts(
                getAddedEntities(stat),
                getUpdatedEntities(stat),
                getDeletedEntities(stat)
        );
    }


    public void storeVersionDetails(AiAgent agent, AgentVersion awsVersion, BedrockSyncContext context) {
        context.storeVersionDetails(agent.getExternalId(), awsVersion.version(), awsVersion);
    }

    public Optional<AgentVersion> getVersionDetails(AiAgent agent, AiAgentVersion version, BedrockSyncContext context) {
        return context.getVersionDetails(agent.getExternalId(), version.getVersionString());

    }
}
