// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.boomigarden;

import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.exception.SyncException;
import com.boomi.aiagentregistry.repo.AiAgentVersionRepository;
import com.boomi.aiagentregistry.service.sync.SyncHistoryService;
import com.boomi.aiagentregistry.service.sync.SyncLimitManager;
import com.boomi.aiagentregistry.service.sync.model.GardenProviderAcctSyncStats;
import com.boomi.aiagentregistry.service.sync.model.GardenSyncContext;
import com.boomi.aiagentregistry.service.sync.model.SyncEntityPointInTimeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_DURING_AGENT_SYNC_JOB;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_DURING_CLEANUP_OF_UNPROCESSED_ENTITIES;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_DURING_SYNC_COMPLETION;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_PROCESSING_ACCOUNT;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.PROVIDER_ACCOUNT;

@Service
@Slf4j
public class GardenPrvAcctSyncHandler extends GardenSyncHandler {

    private final GardenAgentSyncHandler _agentHandler;
    private final AiAgentVersionRepository _aiAgentVersionRepository;
    private final SyncLimitManager _limitManager;

    public GardenPrvAcctSyncHandler(GardenAgentSyncHandler gardenAgentSyncHandler,
                                    SyncHistoryService syncHistoryService,
                                    GardenSyncContextAccessor contextAccessor,
                                    GardenService gardenService,
                                    AiAgentVersionRepository aiAgentVersionRepository,
                                    SyncLimitManager limitManager) {

        super(syncHistoryService, contextAccessor, gardenService);

        _agentHandler = gardenAgentSyncHandler;
        _aiAgentVersionRepository = aiAgentVersionRepository;
        _limitManager = limitManager;
    }

    public Flux<SyncUserAudit> processAccount(GardenSyncContext context) {
       return processAccountWithSync(context);
    }

    public Flux<SyncUserAudit> processAccountWithSync(GardenSyncContext context) {
        SyncEntityPointInTimeInfo entityInfo = SyncEntityPointInTimeInfo.builder()
                                                    .entityUid(context.getProviderAccount().getUid())
                                                    .entityType(PROVIDER_ACCOUNT)
                                                    .entityGuid(context.getProviderAccount().getGuid())
                                                    .entityName(context.getProviderAccount().getProviderAccountName())
                                                    .build();
        List<SyncUserAudit> changedAgentAudits = new ArrayList<>();
        return createSyncUserAudit(entityInfo, context).flatMapMany(
                   accountSyncUserAudit -> _gardenService.listAgentsRecursively(context.getProviderAccount())
                           .flatMap(agent -> {

                               if (_limitManager.isLimitReached(context)){
                                   log.warn("Limit reached for account {}", context.getProviderAccount().getUid());
                                   return Mono.empty();
                               }

                               context.getSyncedEntitiesCount().getAndIncrement();
                               String lastUpdatedDateForAgentFromGarden = agent.getLastUpdatedOn();
                               String agentExternalID = agent.getId();
                               // Get the version using this agentExternalId
                               Optional<AiAgentVersion> aiAgentVersion =
                                       _aiAgentVersionRepository.findByExternalId(agentExternalID);

                               Timestamp lastModifiedDateForExistingAgent =
                                       aiAgentVersion.map(AiAgentVersion::getUpdatedAtProviderTime)
                                               .orElse(null);

                               boolean resultValue = shouldUpdateEntity(lastUpdatedDateForAgentFromGarden,
                                       lastModifiedDateForExistingAgent);
                               _ctxAccessor.getOrCrtAgntStats(agent, context);
                               _ctxAccessor.getProviderAcctStats(context).getRetainAgentsIds().add(agent.getId());
                               if (resultValue) {
                                   changedAgentAudits.add(accountSyncUserAudit);
                                   return _agentHandler.processAgent(agent, context).onErrorResume(error -> {
                                       log.error(ERROR_DURING_AGENT_SYNC_JOB, agent.getId(), error);
                                       updateFailureStatus(error, _ctxAccessor.getProviderAcctStats(context));
                                       return Mono.empty();
                                   });
                               }
                               return Mono.empty();
                           })
                        .onErrorResume(
                                error -> {
                                    log.error(ERROR_PROCESSING_ACCOUNT,
                                            _ctxAccessor.getPrvAcct(context).getProviderAccountName(),
                                            error);
                                    updateFailureStatus(error, _ctxAccessor.getProviderAcctStats(context));
                                    return Mono.empty();
                                })
                        // Wait for all agents to complete
                        .count()
                        .then(Mono.defer(() -> deleteChildren(context)))
                                 .then(Mono.defer(() -> handleAccountSyncCompletion(accountSyncUserAudit,context)
                                .onErrorResume(error -> {
                                    log.error(ERROR_DURING_SYNC_COMPLETION, error);
                                    return Mono.empty();
                                }))
                        )
                        .onErrorResume(error -> {
                            log.error(ERROR_DURING_CLEANUP_OF_UNPROCESSED_ENTITIES, error);
                            updateFailureStatus(error, _ctxAccessor.getProviderAcctStats(context));
                            return Mono.empty();
                        })
                )
                .onErrorResume(error -> {
                    log.error(ERROR_PROCESSING_ACCOUNT, context.getProviderAccount().getUid(), error);
                    throw new SyncException(error.getMessage());
                });
    }

    private Mono<Void> deleteChildren(GardenSyncContext context) {
        //only delete if there are no failure to prevent the deletion of children that were not processed successfully
        if (_ctxAccessor.hasFailure(_ctxAccessor.getProviderAcctStats(context))) {
            return Mono.empty();
        }
        Set<String> agentsToRetain = _ctxAccessor.getProviderAcctStats(context).getRetainAgentsIds();
        return _agentHandler.deleteAllByAccountExcept(_ctxAccessor.getPrvAcct(context), agentsToRetain, context);
    }

    private Mono<SyncUserAudit> handleAccountSyncCompletion(
            SyncUserAudit accountSyncUserAudit,
            GardenSyncContext context) {
        if (_limitManager.isLimitReached(context)){
            return handleLimitExceeded(context, accountSyncUserAudit);
        } else {
            return handleSyncCompletion(accountSyncUserAudit, _ctxAccessor.getProviderAcctStats(context));
        }
    }
    private Mono<SyncUserAudit> handleLimitExceeded(GardenSyncContext context, SyncUserAudit accountSyncUserAudit) {
        GardenProviderAcctSyncStats stats = _ctxAccessor.getProviderAcctStats(context);
        updateLimitExceededStatus(stats);
        return _limitManager.disableAccount(context)
                .then(Mono.defer(() -> handleSyncCompletion(accountSyncUserAudit, stats)))
                .onErrorResume(error -> {
                    log.error(ERROR_DURING_SYNC_COMPLETION, error);
                    return Mono.empty();
                });
    }
}
