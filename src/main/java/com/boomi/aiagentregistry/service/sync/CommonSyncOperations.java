// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.sync;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import com.boomi.aiagentregistry.entity.AgentEntitySyncLatest;
import com.boomi.aiagentregistry.entity.SyncUserAudit;
import com.boomi.aiagentregistry.repo.AiAgentLatestSyncRepository;
import com.boomi.aiagentregistry.repo.SyncUserAuditRepository;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static com.boomi.aiagentregistry.mapper.SyncLatestMapper.SYNC_LATEST_MAPPER;

/**
 * <AUTHOR> <PERSON>tpute
 */
@Slf4j
@Service
public class CommonSyncOperations {

    private final SyncUserAuditRepository _syncUserAuditRepository;
    private final AiAgentLatestSyncRepository _latestSyncRepository;
    private final boolean _writeNonSummaryLogs;

    public CommonSyncOperations(SyncUserAuditRepository syncUserAuditRepository,
            AiAgentLatestSyncRepository latestSyncRepository,
            @Value("${boomi.services.aiagentregistry.write.non.summary.logs}") boolean writeNonSummaryLogs) {
        log.info("The write non summary logs feature flag value is {}", writeNonSummaryLogs);
        _syncUserAuditRepository = syncUserAuditRepository;
        _latestSyncRepository = latestSyncRepository;
        _writeNonSummaryLogs = writeNonSummaryLogs;
    }

    public Mono<AgentEntitySyncLatest> upsertLatestSync(AgentEntitySyncLatest newSync) {
        Optional<AgentEntitySyncLatest> existingSyncOptional =
                findPreviousEntryForEntity(newSync.getSyncedEntityUid(), newSync.getSyncedEntityType());
        if (existingSyncOptional.isEmpty()) {
            return saveLatest(newSync);
        } else {
            AgentEntitySyncLatest existingSync =
                    SYNC_LATEST_MAPPER.updateExistingSync(existingSyncOptional.get(), newSync);
            return saveLatest(existingSync);
        }
    }

    public Mono<SyncUserAudit> saveSyncUserAudit(SyncUserAudit syncUserAudit) {
        log.info("Saving sync audit for entity  {} of type {}", syncUserAudit.getEntityUid(),
                syncUserAudit.getEntityType());
        log.info("The flag value in CommonSyncOperations is {}", _writeNonSummaryLogs);
        // If it's not a provider account and feature flag is off, just return the audit object without saving
        if (syncUserAudit.getEntityType() != AiRegistryEntityType.PROVIDER_ACCOUNT && !_writeNonSummaryLogs) {
            log.info("Just returning empty value without saving to DB because the flag value is {}",
                    _writeNonSummaryLogs);
            return Mono.just(syncUserAudit);
        }
        log.info("Saving to the DB, because the flag values is {}", _writeNonSummaryLogs);
        // Otherwise, save to the DB
        return Mono.fromCallable(() -> _syncUserAuditRepository.save(syncUserAudit))
                .doOnSuccess(savedHistory -> log.info("Sync audit saved with guid {}", syncUserAudit.getGuid()))
                .doOnError(error -> log.error("Error saving sync audit for entity {} of type {}",
                        syncUserAudit.getEntityUid(), syncUserAudit.getEntityType()))
                .subscribeOn(Schedulers.boundedElastic()).onErrorResume(Mono::error);
    }

    public Optional<AgentEntitySyncLatest> findPreviousEntryForEntity(Integer syncedEntityUid,
            AiRegistryEntityType entityType) {
        log.info("Searching for previous sync history for entity {} of type {}", syncedEntityUid, entityType);
        Optional<AgentEntitySyncLatest> latestSync =
                _latestSyncRepository.findBySyncedEntityUidAndSyncedEntityType(syncedEntityUid, entityType);
        log.info("Finished searching for previous sync history for entity {} of type {}", syncedEntityUid, entityType);
        return latestSync;
    }

    public Mono<AgentEntitySyncLatest> saveLatest(AgentEntitySyncLatest latestSync) {
        log.info("Saving latest sync for entity {} of type {}", latestSync.getSyncedEntityUid(),
                latestSync.getSyncedEntityType());
        return Mono.fromCallable(() -> _latestSyncRepository.save(latestSync)).subscribeOn(Schedulers.boundedElastic())
                .doOnSuccess(savedLatest -> log.info("Latest sync saved with id {}", savedLatest.getUid())).doOnError(
                        error -> log.error("Error saving latest sync for entity {} of type {}",
                                latestSync.getSyncedEntityUid(), latestSync.getSyncedEntityType()))
                .onErrorResume(Mono::error);
    }
}
