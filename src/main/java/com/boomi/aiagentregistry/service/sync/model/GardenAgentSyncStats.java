// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder(toBuilder = true)
@Accessors(prefix = "_")
public class GardenAgentSyncStats extends SyncStats {

    private Map<String, GardenVersionSyncStats> _versionStatus;

    private Set<String> _toRetainVerIds;

    public GardenAgentSyncStats() {
        super();
        _toRetainVerIds = ConcurrentHashMap.newKeySet();
        _versionStatus = new ConcurrentHashMap<>();
    }

    public GardenVersionSyncStats getOrCreateVersionStats(String versionString) {
        GardenVersionSyncStats stats = _versionStatus.get(versionString);
        if (stats == null) {
            stats = _versionStatus.putIfAbsent(versionString, new GardenVersionSyncStats());
            if (stats == null) {
                stats = _versionStatus.get(versionString);
            }
        }
        return stats;
    }
}
