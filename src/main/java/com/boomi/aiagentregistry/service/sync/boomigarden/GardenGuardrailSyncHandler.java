// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.sync.boomigarden;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import com.boomi.aiagentregistry.entity.AiAgentGuardrail;
import com.boomi.aiagentregistry.entity.AiAgentGuardrailAssociation;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.repo.AiAgentGuardrailAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentGuardrailRepository;
import com.boomi.aiagentregistry.service.sync.SyncHistoryService;
import com.boomi.aiagentregistry.service.sync.model.GardenSyncContext;
import com.boomi.gardenagents.model.Guardrail;

import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.util.ConcurrentModificationException;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_CREATING_GUARDRAIL;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_DELETING_GUARD_RAIL;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_PROCESSING_GUARDRAIL;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_UPDATING_GUARDRAIL;
import static com.boomi.aiagentregistry.mapper.AiAgentGuardrailAssociationMapper.GRD_RL_ASSOC;
import static com.boomi.aiagentregistry.mapper.AiAgentGuardrailMapper.AI_AGENT_GUARDRAIL_MAPPER;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.VERSION;

@Service
@Slf4j
public class GardenGuardrailSyncHandler extends GardenSyncHandler {

    private final AiAgentGuardrailRepository _guardrailRepository;
    private final AiAgentGuardrailAssociationRepository _guardrailAssociationRepository;

    public GardenGuardrailSyncHandler(AiAgentGuardrailRepository guardrailRepository,
                                      AiAgentGuardrailAssociationRepository guardrailAssociationRepository,
                                      SyncHistoryService syncHistoryService,
                                      GardenSyncContextAccessor ctxAccessor,
                                      GardenService gardenService) {
        super(syncHistoryService, ctxAccessor, gardenService);
        _guardrailRepository = guardrailRepository;
        _guardrailAssociationRepository = guardrailAssociationRepository;
    }

    public Mono<AiAgentGuardrail> processGuardrail(
            AiAgentVersion agentVersion,
            Guardrail guardrail,
            GardenSyncContext context) {
        if (guardrail == null) {
            return Mono.empty();
        }
        _ctxAccessor.getOrCrtVerStats(agentVersion.getGuid(), agentVersion.getAgent().getExternalId(), context)
                .getToRetainGuardrails()
                .add(guardrail.getId());

        return findGuardRail(
                guardrail.getId(),
                _ctxAccessor.getPrvAcct(context))
                .map(aiAgentGuardrail ->
                        handleExistingGuardrail(agentVersion, aiAgentGuardrail, guardrail, context)
                                .onErrorResume(error -> handleSyncError(agentVersion, guardrail, context, error))
                )
                .orElseGet(() -> handleNewGuardrail(agentVersion, guardrail, context)
                        .onErrorResume(error -> handleSyncError(agentVersion, guardrail, context, error)));
    }

    private Mono<AiAgentGuardrail> handleSyncError(
            AiAgentVersion version,
            Guardrail guardrail,
            GardenSyncContext context,
            Throwable error) {

        log.error(ERROR_PROCESSING_GUARDRAIL, guardrail.getId(), error);
        updateFailureStatus(error,
                _ctxAccessor.getOrCrtVerStats(version.getGuid(), version.getAgent().getExternalId(), context));
        return Mono.empty();
    }

    private Mono<AiAgentGuardrail> handleNewGuardrail(
            AiAgentVersion version,
            Guardrail inputGuardrail,
            GardenSyncContext context) {
        AiAgentProviderAccount prvAcct = _ctxAccessor.getPrvAcct(context);
        AiAgentGuardrail guardrail = AI_AGENT_GUARDRAIL_MAPPER
                .toEntityFromGardenGuardrails(prvAcct, inputGuardrail);
        return saveGuardrailWithRetry(version, guardrail, context)
                .onErrorResume(error -> {
                    log.error(ERROR_PROCESSING_GUARDRAIL, inputGuardrail.getId(), error);
                    updateFailureStatus(error,
                            _ctxAccessor.getOrCrtVerStats(version.getGuid(), version.getAgent().getExternalId(),
                                    context));
                    return Mono.empty();
                });
    }

    private Mono<AiAgentGuardrail> saveGuardrailWithRetry(
            AiAgentVersion version,
            AiAgentGuardrail guardrail,
            GardenSyncContext context) {

        return Mono.fromCallable(() -> {
            AiAgentGuardrail savedGuardrail = _guardrailRepository.save(guardrail);
            associateGuardrailWithVersion(version, savedGuardrail);
            updateAddStats(
                    _ctxAccessor.getOrCrtVerStats(version.getGuid(), version.getAgent().getExternalId(), context));
            return savedGuardrail;
        }).onErrorResume(DataIntegrityViolationException.class, e ->
                handleGuardrailCreationConflict(version, guardrail, context)
        );
    }

    private Mono<AiAgentGuardrail> handleGuardrailCreationConflict(
            AiAgentVersion version,
            AiAgentGuardrail guardrail,
            GardenSyncContext context) {

        return findGuardRail(
                guardrail.getExternalId(),
                _ctxAccessor.getPrvAcct(context)
        ).map(existingGuardrail -> {
            associateGuardrailWithVersion(version, existingGuardrail);
            return Mono.just(existingGuardrail);
        }).orElseGet(() -> {
            ConcurrentModificationException e = new ConcurrentModificationException(ERROR_CREATING_GUARDRAIL);
            updateFailureStatus(e,
                    _ctxAccessor.getOrCrtVerStats(version.getGuid(), version.getAgent().getExternalId(), context));
            return Mono.empty();
        });
    }

    private Mono<AiAgentGuardrail> handleExistingGuardrail(
            AiAgentVersion version,
            AiAgentGuardrail existingGuardrail,
            Guardrail inputGuardrail,
            GardenSyncContext context) {

        //if there is an association already for the version, don't create one
        if (associationDoesNotExist(version, existingGuardrail)) {
            associateGuardrailWithVersion(version, existingGuardrail);
        }

        return Mono.defer(() -> {
                    AI_AGENT_GUARDRAIL_MAPPER.updatedEntityFromGardenGuardrail(existingGuardrail, inputGuardrail);
                    _guardrailRepository.save(existingGuardrail);
                    return Mono.just(existingGuardrail);
                })
                .onErrorResume(error -> {
                    log.error(ERROR_UPDATING_GUARDRAIL, inputGuardrail.getId(), error);
                    updateFailureStatus(error,
                            _ctxAccessor.getOrCrtVerStats(version.getGuid(), version.getAgent().getExternalId(),
                                    context));
                    return Mono.empty();
                });
    }

    private boolean associationDoesNotExist(AiAgentVersion version, AiAgentGuardrail existingGuardrail) {
        return _guardrailAssociationRepository.findAllByGuardrail(existingGuardrail)
                .stream()
                .noneMatch(assoc ->
                        assoc.getRelatedEntityType().equals(VERSION)
                        && assoc.getRelatedEntityUid().equals(version.getUid()));
    }

    public Mono<Void> deleteAllByVersionExcept(
            AiAgentVersion version,
            Set<String> guardrailIdsToRetain,
            GardenSyncContext context) {

        return getVersionAssociations(version)
                .flatMap(associations -> {
                    Set<Integer> guardrailsToDetachIds = identifyGuardrailsToDetach(associations, guardrailIdsToRetain);

                    if (guardrailsToDetachIds.isEmpty()) {
                        return Mono.empty();
                    }

                    return deleteAssociationsByGuardrailIds(guardrailsToDetachIds, version, context)
                            .then(findAndDeleteOrphanedGuardrails(guardrailsToDetachIds, version, context));
                })
                .onErrorResume(error -> {
                    log.error(ERROR_DELETING_GUARD_RAIL, version.getUid(), error);
                    updateFailureStatus(error,
                            _ctxAccessor.getOrCrtVerStats(version.getGuid(), version.getAgent().getExternalId(),
                                    context));
                    return Mono.empty();
                });
    }


    private Set<Integer> identifyGuardrailsToDetach(
            Set<AiAgentGuardrailAssociation> associations,
            Set<String> guardrailIdsToRetain) {

        return associations.stream()
                .map(AiAgentGuardrailAssociation::getGuardrail)
                .filter(guardrail -> !guardrailIdsToRetain.contains(guardrail.getExternalId()))
                .map(AiAgentGuardrail::getUid)
                .collect(Collectors.toSet());
    }

    private Mono<Void> findAndDeleteOrphanedGuardrails(
            Set<Integer> guardrailsToDetachIds,
            AiAgentVersion version,
            GardenSyncContext context) {

        return Mono.just(getOrphanedGuardrails(guardrailsToDetachIds))
                .flatMap(orphanedGuardrails -> {
                    if (orphanedGuardrails.isEmpty()) {
                        return Mono.empty();
                    }
                    return deleteGuardrailsById(orphanedGuardrails)
                            .doOnSuccess(unused -> updateDeletionStats(orphanedGuardrails.size(), version, context));
                });
    }

    private Set<Integer> getOrphanedGuardrails(Set<Integer> guardrailsToDetachIds) {
        return _guardrailAssociationRepository
                .findGuardrailIdsWithNoAssociations(guardrailsToDetachIds);
    }

    private void updateDeletionStats(int deletedCount, AiAgentVersion version, GardenSyncContext context) {
        _ctxAccessor.getOrCrtVerStats(version.getGuid(), version.getAgent().getExternalId(), context)
                .getChildrenDeleted()
                .addAndGet(deletedCount);
    }


    private void associateGuardrailWithVersion(AiAgentVersion version, AiAgentGuardrail guardrail) {
        AiAgentGuardrailAssociation assoc = GRD_RL_ASSOC.associateWithVersion(version, guardrail);
        _guardrailAssociationRepository.save(assoc);
    }


    private Optional<AiAgentGuardrail> findGuardRail(String externalId, AiAgentProviderAccount account) {
        return _guardrailRepository.findByExternalIdAndAiAgentProviderAccount(externalId, account);
    }

    private Mono<Set<AiAgentGuardrailAssociation>> getVersionAssociations(AiAgentVersion version) {
        return Mono.fromCallable(() ->
                        _guardrailAssociationRepository
                                .findByRelatedEntityUidAndRelatedEntityType(version.getUid(), VERSION))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error);
    }

    private Mono<Void> deleteAssociationsByGuardrailIds(Set<Integer> guardrailIds, AiAgentVersion version,
            GardenSyncContext context) {
        updateDeleteStats(_ctxAccessor.getOrCrtVerStats(version.getGuid(),version.getAgent().getExternalId(), context));
        return Mono.fromRunnable(() ->
                        _guardrailAssociationRepository.deleteByGuardrailUidInAndRelatedEntityUidAndRelatedEntityType(
                                guardrailIds,
                                version.getUid(),
                                VERSION.name()))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error)
                .then();
    }

    private Mono<Void> deleteGuardrailsById(Set<Integer> guardrailIds) {
        return Mono.fromRunnable(() -> _guardrailRepository.deleteAllById(guardrailIds))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error)
                .then();
    }

}
