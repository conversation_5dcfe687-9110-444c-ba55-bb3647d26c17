// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.entity.AiAgentLargeTextContent;
import com.boomi.aiagentregistry.entity.AiAgentToolAssociation;
import com.boomi.aiagentregistry.mapper.AiAgentToolMapper;
import com.boomi.aiagentregistry.repo.AiAgentLargeTextContentRepository;
import com.boomi.aiagentregistry.repo.AiAgentTaskRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolAssociationRepository;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiAgentTask;
import com.boomi.graphql.server.schema.types.AiAgentTool;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.boomi.graphql.server.servlet.ErrorUtil;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_AGENT_TASK_NOT_FOUND;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.TASK;

/**
 * <AUTHOR> Satpute
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AiAgentTaskToolServiceImpl implements AiAgentTaskToolService {

    private final AiAgentToolAssociationRepository _aiAgentToolAssociationRepository;
    private final AiAgentTaskRepository _aiAgentTaskRepository;
    private final AiAgentLargeTextContentRepository _largeTxtRepo;

    @Override
    public CompletionStage<List<AiAgentTool>> getAiAgentTaskTools(AiAgentTask aiAgentTask,
                                                                  DataFetchingEnvironment dfe) {
        // Get task entity from database using the task GUID
        com.boomi.aiagentregistry.entity.AiAgentTask taskEntity = _aiAgentTaskRepository
                .findByGuid(aiAgentTask.getId())
                .orElse(null);

        if (taskEntity == null) {
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

        // Get tool associations for this task
        List<AiAgentToolAssociation> toolAssociations = _aiAgentToolAssociationRepository
                .findByRelatedEntityUidAndRelatedEntityType
                        (taskEntity.getUid(), AiRegistryEntityType.TASK).stream().toList();

        if (toolAssociations.isEmpty()) {
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

        // Get tool and convert tool entity to graphql dto
        List<AiAgentTool> agentToolDtoList = toolAssociations.stream().map(
                toolAssociation -> AiAgentToolMapper.AI_AGENT_TOOL_MAPPER
                        .toAIAgentToolDto(toolAssociation.getTool(),
                                toolAssociation.getAssociationStatus())).collect(Collectors.toList());

        return CompletableFuture.completedFuture(agentToolDtoList);
    }

    @Override
    public CompletionStage<List<String>> getInstructions(AiAgentTask aiAgentTask, DataFetchingEnvironment dfe) {
        String taskId = aiAgentTask.getId();
        com.boomi.aiagentregistry.entity.AiAgentTask taskFromDB = findAiAgentTaskByGuid(taskId);
        if (taskFromDB == null) {
            log.warn(ERROR_AGENT_TASK_NOT_FOUND, taskId);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.AI_AGENT_TASK_NOT_FOUND);
            return CompletableFuture.completedFuture(null);
        }

        return getInstructionsContent(taskFromDB.getUid());
    }

    private CompletionStage<List<String>> getInstructionsContent(Integer taskUid) {
        List<AiAgentLargeTextContent> textContent =
                _largeTxtRepo.findListByRelatedEntityUidAndRelatedEntityType(taskUid, TASK.name());

        List<String> contentStrings = !textContent.isEmpty() ?
                textContent.stream().map(AiAgentLargeTextContent::getContent).toList() :
                new ArrayList<>();

        return CompletableFuture.completedFuture(contentStrings);
    }

    private com.boomi.aiagentregistry.entity.AiAgentTask findAiAgentTaskByGuid(String guid) {
        return _aiAgentTaskRepository.findByGuid(guid).orElse(null);
    }
}
