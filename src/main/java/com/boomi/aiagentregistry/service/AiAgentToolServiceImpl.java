// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service;

import com.boomi.aiagentregistry.aop.IdpAccountFilter;
import com.boomi.aiagentregistry.constant.ActionEnum;
import com.boomi.aiagentregistry.constant.ActionResultEnum;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTask;
import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.entity.AiAgentToolAssociation;
import com.boomi.aiagentregistry.entity.AiAgentToolResource;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.mapper.Translators;
import com.boomi.aiagentregistry.model.AuditLogEntry;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolResourceRepository;
import com.boomi.aiagentregistry.util.AuditUtil;
import com.boomi.aiagentregistry.util.GeneralUtil;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.UserUtil;
import com.boomi.aiagentregistry.util.ValidationUtil;
import com.boomi.graphql.server.schema.types.AiAgentToolCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentToolResourceInput;
import com.boomi.graphql.server.schema.types.AiAgentToolType;
import com.boomi.graphql.server.schema.types.AiAgentToolUpdateInput;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.boomi.graphql.server.servlet.ErrorUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import graphql.schema.DataFetchingEnvironment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.CORRELATION_PROPERTY_NAME;
import static com.boomi.aiagentregistry.mapper.AiAgentToolAssociationMapper.TOOL_ASSOC_MAPPER;
import static com.boomi.aiagentregistry.mapper.AiAgentToolMapper.AI_AGENT_TOOL_MAPPER;
import static com.boomi.aiagentregistry.util.ValidationUtil.isInputFieldPresent;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_TOOL_ID;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.STALE_UPDATE;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.TOOL_NOT_FOUND;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.TASK;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.VERSION;
import static com.boomi.graphql.server.servlet.ErrorUtil.GeneralErrorCode.SYSTEM_ERROR;

/**
 * <AUTHOR> Wang.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AiAgentToolServiceImpl implements AiAgentToolService {

    private final AiAgentToolRepository _aiAgentToolRepository;
    private final AiAgentToolResourceRepository _aiAgentToolResourceRepository;
    private final AiAgentToolAssociationRepository _associationRepo;
    private final AuditUtil _auditUtil;
    private final AuditLogService _auditLogService;
    private final UserUtil _userUtil;
    private final AiAgentProviderAccountRepository _registryAccountRepository;
    private final ObjectMapper _objectMapper;

    private static final int AI_AGENT_TOOL_AUDIT_LOG_CHANGE_SET_VERSION = 1;

    @Override
    @Transactional
    public CompletableFuture<com.boomi.graphql.server.schema.types.AiAgentTool> createAIAgentTool(
            AiAgentToolCreateInput input, DataFetchingEnvironment dfe) {
        // Tool create implementation to handle ONLY the Custom Type Provider (aka. Internal Registry Provider)
        CompletableFuture<com.boomi.graphql.server.schema.types.AiAgentTool> completion = new CompletableFuture<>();
        try {
            // Validate provider account input id
            if (!ValidationUtil.validateProviderAccountInputId(input.getProviderAccountId(), dfe)) {
                completion.complete(null);
                return null;
            }

            // Retrieve Provider Account Entity
            AiAgentProviderAccount providerAccount = ValidationUtil.getProviderAccount(input.getProviderAccountId(),
                    _registryAccountRepository, dfe);
            if (providerAccount == null) {
                completion.complete(null);
                return null;
            }

            // Create and save tool
            AiAgentTool aiAgentToolEntity = AI_AGENT_TOOL_MAPPER.toAIAgentToolEntityFromToolCreateInput(input,
                    providerAccount);
            // Use Tool’s GUID for its external id (aka. fake external id, due to the Custom Type provider.)
            aiAgentToolEntity.setExternalId(aiAgentToolEntity.getGuid());
            _auditUtil.setCreatedAudit(aiAgentToolEntity, dfe);
            log.info("Saving Tool Details into the Database");
            aiAgentToolEntity = _aiAgentToolRepository.save(aiAgentToolEntity);

            // Create audit log for creating new tool action
            log.info("Saving Audit Log for creating a new AI Agent");
            createAuditLogForAiAgentToolCreate(input, aiAgentToolEntity.getGuid(), dfe);

            // Convert entity to graphql and return response
            com.boomi.graphql.server.schema.types.AiAgentTool aiAgentTool = AI_AGENT_TOOL_MAPPER.toAIAgentToolDto(
                    aiAgentToolEntity);
            completion.complete(aiAgentTool);
        } catch (Exception dae) {
            ErrorUtil.addError(dfe, SYSTEM_ERROR);
            log.error("Error while Adding the AI Agent Tool details.", dae);
            completion.complete(null);
        }
        return completion;
    }

    @Override
    @Transactional
    @IdpAccountFilter
    public CompletableFuture<com.boomi.graphql.server.schema.types.AiAgentTool> updateAIAgentTool(
            AiAgentToolUpdateInput input, DataFetchingEnvironment dfe) {
        // Tool update implementation to handle ONLY the Custom Type Provider (aka. Internal Registry Provider)
        CompletableFuture<com.boomi.graphql.server.schema.types.AiAgentTool> completion = new CompletableFuture<>();
        try {
            // Validate tool ID
            if (!validateToolInputId(input.getId(), dfe)) {
                completion.complete(null);
                return completion;
            }
            // Get existing tool entity
            AiAgentTool existingTool = getAiAgentToolEntity(input, dfe);
            if (existingTool == null) {
                completion.complete(null);
                return completion;
            }

            // Check if the update is newer than the existing tool
            if (!GeneralUtil.isNewerTimestamp(input.getUpdatedAtProviderTime(),
                    existingTool.getUpdatedAtProviderTime())) {
                ErrorUtil.addError(dfe, STALE_UPDATE);
                completion.complete(null);
                return completion;
            }

            // Update existing tool with the fields that are present in the input
            populateUpdateInputFields(input, existingTool, dfe);
            _auditUtil.setUpdatedAudit(existingTool, dfe);
            _aiAgentToolRepository.save(existingTool);

            // Convert entity to GraphQL type and return
            com.boomi.graphql.server.schema.types.AiAgentTool aiAgentTool = AI_AGENT_TOOL_MAPPER.toAIAgentToolDto(
                    existingTool);
            completion.complete(aiAgentTool);
        } catch (Exception dae) {
            ErrorUtil.addError(dfe, SYSTEM_ERROR);
            log.error("Error while Updating the AI Agent Tool details.", dae);
            completion.complete(null);
        }
        return completion;
    }

    @Override
    @Transactional
    public CompletionStage<List<com.boomi.graphql.server.schema.types.AiAgentToolResource>> getAiAgentToolResources(
            com.boomi.graphql.server.schema.types.AiAgentTool aiAgentTool, DataFetchingEnvironment dfe) {
        // Get tool entity
        com.boomi.aiagentregistry.entity.AiAgentTool aiAgentToolFromDB = _aiAgentToolRepository.findByGuid(
                aiAgentTool.getId()).orElse(null);
        if (aiAgentToolFromDB == null) {
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

        // Get tool resources entity
        Set<com.boomi.aiagentregistry.entity.AiAgentToolResource> toolResourcesFromDB =
                _aiAgentToolResourceRepository.findByToolUid(aiAgentToolFromDB.getUid());

        if (toolResourcesFromDB == null) {
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

        // Convert tool resource entity to graphql dto
        List<com.boomi.graphql.server.schema.types.AiAgentToolResource> toolResources =
                toolResourcesFromDB.stream().map(AI_AGENT_TOOL_MAPPER::toAiAgentToolResourceDto).toList();

        return CompletableFuture.completedFuture(toolResources);
    }

    private void createAuditLogForAiAgentToolCreate(AiAgentToolCreateInput input, String aiAgentToolGuid,
                                                    DataFetchingEnvironment dfe) throws JsonProcessingException {
        Object changeSet = AI_AGENT_TOOL_MAPPER.toAuditLogChangeSet(input, AI_AGENT_TOOL_AUDIT_LOG_CHANGE_SET_VERSION);
        AuditLogEntry auditLogEntry = AuditLogEntry.builder().entityName(AiRegistryEntityType.TOOL).entityGuid(
                aiAgentToolGuid).action(ActionEnum.CREATE).userId(_userUtil.getUserName(dfe)).accountId(
                _userUtil.getAccountId(dfe)).entityVersion(AI_AGENT_TOOL_AUDIT_LOG_CHANGE_SET_VERSION).changeSet(
                _objectMapper.writeValueAsString(changeSet)).requestId(MDC.get(CORRELATION_PROPERTY_NAME)).actionResult(
                ActionResultEnum.SUCCESS).build();
        _auditLogService.logAction(auditLogEntry);
    }

    private boolean validateToolInputId(String toolId, DataFetchingEnvironment dfe) {
        try {
            UUID.fromString(toolId);
            return true;
        } catch (IllegalArgumentException e) {
            log.warn("Invalid tool ID UUID format: {}", toolId, e);
            ErrorUtil.addError(dfe, INVALID_TOOL_ID);
        }
        return false;
    }

    private AiAgentTool getAiAgentToolEntity(AiAgentToolUpdateInput input, DataFetchingEnvironment dfe) {
        Optional<AiAgentTool> tool = _aiAgentToolRepository.findByGuid(input.getId());
        if (tool.isEmpty()) {
            ErrorUtil.addError(dfe, TOOL_NOT_FOUND, input.getId());
            return null;
        }
        return tool.get();
    }

    private void populateUpdateInputFields(AiAgentToolUpdateInput input, AiAgentTool existingTool,
                                           DataFetchingEnvironment dfe) {

        if (isInputFieldPresent(dfe, AiAgentToolUpdateInput.PROP_VERSION)) {
            existingTool.setVersionString(Translators.mapVersionToString(input.getVersion()));
            existingTool.setVersionInt(Translators.mapVersionToInt(input.getVersion()));
        }
        if (isInputFieldPresent(dfe, AiAgentToolUpdateInput.PROP_NAME)) {
            existingTool.setName(input.getName());
        }
        if (isInputFieldPresent(dfe, AiAgentToolUpdateInput.PROP_DESCRIPTION)) {
            existingTool.setDescription(input.getDescription());
        }
        if (isInputFieldPresent(dfe, AiAgentToolUpdateInput.PROP_UPDATED_BY_ORIGIN)) {
            existingTool.setUpdatedByOrigin(input.getUpdatedByOrigin());
        }
        if (isInputFieldPresent(dfe, AiAgentToolUpdateInput.PROP_UPDATED_AT_PROVIDER_TIME)) {
            existingTool.setUpdatedAtProviderTime(GeneralUtil.dateToTimestamp(input.getUpdatedAtProviderTime()));
        }
        if (isInputFieldPresent(dfe, AiAgentToolUpdateInput.PROP_PROVIDER)) {
            existingTool.setToolProvider(input.getProvider());
        }
        if (isInputFieldPresent(dfe, AiAgentToolUpdateInput.PROP_TYPE)) {
            existingTool.setToolType(input.getType());
        }
        if (isInputFieldPresent(dfe, AiAgentToolUpdateInput.PROP_JSON)) {
            existingTool.setToolJson(input.getJson());
        }
        if (isInputFieldPresent(dfe, AiAgentToolUpdateInput.PROP_STATUS)) {
            existingTool.setStatus(input.getStatus().name());
        }
        if (isInputFieldPresent(dfe, AiAgentToolUpdateInput.PROP_RESOURCES)) {
            _aiAgentToolResourceRepository.deleteByTool(existingTool);
            existingTool.setToolResources(mapToolResources(input.getResources(), existingTool));
        }
    }

    private static Set<AiAgentToolResource> mapToolResources(List<AiAgentToolResourceInput> inputToolResources,
                                                             AiAgentTool existingTool) {
        if (inputToolResources == null) {
            return Collections.emptySet();
        }
        return inputToolResources.stream()
                .map(inputToolResource ->
                        AI_AGENT_TOOL_MAPPER.toAiAgentToolResourceEntity(
                                inputToolResource,
                                existingTool,
                                GuidUtil.createToolResourceGuid()))
                .collect(Collectors.toSet());
    }

    @Override
    public Mono<Set<AiAgentToolAssociation>> getVersionAssociations(
            AiAgentVersion version
    ) {
        log.info("Fetching tools association for agent version {}", version.getUid());
        return Mono.fromCallable(() ->
                        _associationRepo.findByRelatedEntityUidAndRelatedEntityType(version.getUid(), VERSION))
                .doOnSuccess(unused ->
                        log.info("Finished fetching tools association for agent version {}", version.getUid()))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error);
    }

    @Override
    public Mono<Void> deleteAssociationsByToolIdsAndVersionId(Set<Integer> toolIds, AiAgentVersion version) {
        log.info("Deleting associations for agent version {} and tools {}", version, toolIds);
        return Mono.fromRunnable(() -> _associationRepo.deleteByToolUidInAndRelatedEntityUidAndRelatedEntityType(
                        toolIds,
                        version.getUid(),
                        VERSION.name())
                )
                .doOnSuccess(unused ->
                        log.info("Finished deleting associations for agent version {} and tools {}", version, toolIds))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error)
                .then();
    }

    @Override
    public Mono<Void> deleteAssociationsByToolIdsAndTaskId(Set<Integer> toolIds, AiAgentTask task) {
        return Mono.fromRunnable(() -> _associationRepo.deleteByToolUidInAndRelatedEntityUidAndRelatedEntityType(
                        toolIds,
                        task.getUid(),
                        TASK.name())
                )
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error)
                .then();
    }

    @Override
    public Mono<Integer> deleteToolsById(Set<Integer> toolIds) {
        log.info("Deleting tools {}", toolIds);
        return Mono.fromCallable(() -> {
                    _aiAgentToolResourceRepository.deleteByToolIds(toolIds);
                    return _aiAgentToolRepository.deleteToolsWithLock(toolIds);
                })
                .doOnSuccess(unused -> log.info("Finished deleting tools {}", toolIds))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error);
    }

    @Override
    public Optional<AiAgentTool> findByExternalIdReactive(
            String externalId,
            AiAgentToolType type,
            AiAgentProviderAccount account) {

        log.info("Fetching tool by external id {} and type {} for account {}",
                externalId,
                type,
                account.getProviderAccountName());

        Optional<AiAgentTool> tool =
                _aiAgentToolRepository.findByExternalIdAndToolTypeAndAiAgentProviderAccount(externalId, type, account);
        log.info("Finished fetching tool by external id {} and type {} for account {}",
                externalId,
                type,
                account.getProviderAccountName());

        return tool;
    }

    @Override
    public Set<Integer> identifyToolsToDetach(
            Set<AiAgentToolAssociation> associations,
            Set<String> toolIdsToRetain,
            AiAgentToolType toolType) {

        return associations.stream()
                .map(AiAgentToolAssociation::getTool)
                .filter(tool -> !toolIdsToRetain.contains(tool.getExternalId())
                                && tool.getToolType().equals(toolType))
                .map(com.boomi.aiagentregistry.entity.AiAgentTool::getUid)
                .collect(Collectors.toSet());
    }

    @Override
    public void associateToolWithVersion(AiAgentVersion version, AiAgentTool tool, String relationshipStatus) {
        //ensure that the association does not exist before attempting to create one
        log.info("Fetching association for agent version {} and tool {}", version.getUid(), tool.getUid());
        boolean associationDoesNotExists =
                _associationRepo.findByRelatedEntityUidAndRelatedEntityTypeAndTool(version.getUid(), VERSION, tool)
                .isEmpty();
        log.info("Finished fetching association for agent version {} and tool {}", version.getUid(), tool.getUid());

        if (associationDoesNotExists) {
            AiAgentToolAssociation assoc = TOOL_ASSOC_MAPPER.associateWithVersion(version, tool, relationshipStatus);
            log.info("Creating association for agent version {} and tool {}", version.getUid(), tool.getUid());
            _associationRepo.save(assoc);
            log.info("Finished creating association for agent version {} and tool {}", version.getUid(), tool.getUid());
        }
    }

    @Override
    public Mono<Set<Integer>> findOrphanTools(Set<Integer> toolToDetachIds) {
        log.info("Finding tools without associations for tools in : {}", toolToDetachIds);
        return Mono.fromCallable(() -> _associationRepo.findToolIdsWithNoAssociations(toolToDetachIds))
                .doOnSuccess(unused ->
                        log.info("Finding tools without associations for tools in : {}", toolToDetachIds))
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(Mono::error);

    }
}
