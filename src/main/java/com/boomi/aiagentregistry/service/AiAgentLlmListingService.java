// Copyright (c) 2025 Boomi, Inc.
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.entity.AiAgentLlm;
import com.boomi.aiagentregistry.mapper.AiAgentLlmMapper;
import com.boomi.aiagentregistry.repo.AiAgentLlmListingRepository;
import com.boomi.aiagentregistry.util.UserUtil;
import com.boomi.graphql.server.schema.types.AiAgentLlmsQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentLlmsQueryResponse;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.servlet.ErrorUtil;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

@Slf4j
@Service
@RequiredArgsConstructor
public class AiAgentLlmListingService {

    private static final int DEFAULT_START_INDEX = 0;
    private static final int DEFAULT_END_INDEX = 50;
    private static final String LLM_NAME_FIELD = "name";

    private final AiAgentLlmListingRepository _aiAgentLlmListingRepository;
    private final UserUtil _userUtil;

    public CompletionStage<AiAgentLlmsQueryResponse> getAiAgentLlms(AiAgentLlmsQueryInput input,
            DataFetchingEnvironment dfe) {
        CompletableFuture<AiAgentLlmsQueryResponse> completion = new CompletableFuture<>();
        String idpAccountId = _userUtil.getAccountId(dfe);

        try {
            int startIndex = input.getStartIndex() == null ? DEFAULT_START_INDEX : input.getStartIndex();
            int endIndex = input.getEndIndex() == null ? DEFAULT_END_INDEX : input.getEndIndex();
            int pageSize = Math.max(endIndex - startIndex, 1);
            int pageNumber = startIndex / pageSize;

            log.info("Fetching AI Agent LLMs for account: {}, pageNumber: {}, pageSize: {}",
                    idpAccountId, pageNumber, pageSize);

            Pageable pageable = PageRequest.of(pageNumber, pageSize, Sort.by(LLM_NAME_FIELD).ascending());
            Specification<AiAgentLlm> whereSpecification = _aiAgentLlmListingRepository.idpAccountId(idpAccountId);
            Page<AiAgentLlm> aiAgentLlmsPage = _aiAgentLlmListingRepository.findAll(whereSpecification, pageable);

            if (!aiAgentLlmsPage.isEmpty()) {
                log.info(" Ai Agent LLMs content is not empty");
                completion.complete(createResponse(aiAgentLlmsPage));
                return completion;
            }

            log.info("No Agent LLMs found. Returning null");
            completion.complete(getEmptyResponse());
        }
        catch (Exception e) {
            log.error("Error while retrieving AI Agent LLMs.", e);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.FETCH_AI_AGENT_LLMS_ERROR, idpAccountId);
            completion.complete(null);
        }
        return completion;
    }

    private static AiAgentLlmsQueryResponse createResponse(Page<AiAgentLlm> aiAgentLlmsPage) {
        List<com.boomi.graphql.server.schema.types.AiAgentLlm> aiAgentLlmList = aiAgentLlmsPage.getContent().stream()
                        .map(AiAgentLlmMapper.LLM_MAPPER::toAIAgentLlmDto)
                        .toList();

        AiAgentLlmsQueryResponse aiAgentLlmsQueryResponse = new AiAgentLlmsQueryResponse();
        aiAgentLlmsQueryResponse.setTotalResults(aiAgentLlmsPage.getTotalElements());
        aiAgentLlmsQueryResponse.setCurrentPageSize(aiAgentLlmsPage.getNumberOfElements());
        aiAgentLlmsQueryResponse.setLlms(aiAgentLlmList);

        return aiAgentLlmsQueryResponse;
    }

    private static AiAgentLlmsQueryResponse getEmptyResponse() {
        AiAgentLlmsQueryResponse aiAgentLlmsQueryResponse = new AiAgentLlmsQueryResponse();
        aiAgentLlmsQueryResponse.setLlms(Collections.emptyList());
        return aiAgentLlmsQueryResponse;
    }
}