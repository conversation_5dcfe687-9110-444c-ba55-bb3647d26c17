// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.agent.action;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
import com.boomi.aiagentregistry.config.AppPatternConfig;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;

import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class BedrockAiAgentActionService implements AiAgentActionService {

    private final AppPatternConfig _appPatternConfig;
    private static final String NOT_IMPLEMENTED = "Not Implemented";

    @Override
    public AiAgentProviderType getAgentProviderType() {
        return AiAgentProviderType.AWS_BEDROCK;
    }

    @Override
    public Mono<AiAgentActionResponse> enableAiAgentVersion(AiAgentProviderAccount dbProviderAccount,
            String aiAgentVersionExternalId) {
        return Mono.just(AiAgentActionResponse.builder()
                .success(false)
                .error(NOT_IMPLEMENTED)
                .build());
    }

    @Override
    public Mono<AiAgentActionResponse> disableAiAgentVersion(AiAgentProviderAccount dbProviderAccount,
            String aiAgentVersionExternalId) {
        return Mono.just(AiAgentActionResponse.builder()
                .success(false)
                .error(NOT_IMPLEMENTED)
                .build());
    }
}
