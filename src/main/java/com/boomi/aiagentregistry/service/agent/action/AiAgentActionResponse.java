// Copyright (c) 2025 Boomi, LP

package com.boomi.aiagentregistry.service.agent.action;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

@Getter
@Setter
@Accessors(prefix = "_")
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
public class AiAgentActionResponse {
    private boolean _success;
    private String _error;
    private String _aiAgentId;
    private String _aiAgentStatus;
}

