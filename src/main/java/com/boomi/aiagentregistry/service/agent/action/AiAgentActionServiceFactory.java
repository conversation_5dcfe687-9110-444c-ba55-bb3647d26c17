// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.service.agent.action;

import com.boomi.aiagentregistry.constant.ErrorMessages;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class AiAgentActionServiceFactory {

    private final Map<AiAgentProviderType, AiAgentActionService> _strategies;

    public AiAgentActionServiceFactory(List<AiAgentActionService> strategyList) {
        _strategies = strategyList.stream().collect(
                Collectors.toMap(AiAgentActionService::getAgentProviderType, Function.identity()));
    }

    public AiAgentActionService getStrategy(AiAgentProviderType aiAgentProviderType) {
        AiAgentActionService strategy = _strategies.get(aiAgentProviderType);
        if (strategy == null) {
            throw new IllegalArgumentException(ErrorMessages.
                    format(ErrorMessages.UNSUPPORTED_PROVIDER, aiAgentProviderType));
        }
        return strategy;
    }
}
