// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.service.agent.action;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.service.sync.boomigarden.GardenService;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;

import org.springframework.stereotype.Component;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_DISABLING_BOOMI_AGENT;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_ENABLING_BOOMI_AGENT;

@Component
@RequiredArgsConstructor
@Slf4j
public class BoomiAiAgentActionService implements AiAgentActionService {

    private final GardenService _gardenService;

    @Override
    public AiAgentProviderType getAgentProviderType() {
        return AiAgentProviderType.BOOMI;
    }

    @Override
    public Mono<AiAgentActionResponse> enableAiAgentVersion(AiAgentProviderAccount providerAccount,
            String aiAgentVersionExternalId) {
        return _gardenService.installAgent(providerAccount, aiAgentVersionExternalId)
                .doOnSubscribe(subscription ->
                        log.debug("Starting Garden Ai Agent {} Install API call from BoomiAiAgentActionService",
                                aiAgentVersionExternalId))
                .map(installResponse -> {
                    log.debug("Garden Ai Agent Install API response received in BoomiAiAgentActionService: {}.",
                            installResponse);
                    return AiAgentActionResponse.builder()
                            .success(installResponse.isSuccess())
                            .error(installResponse.getError())
                            .aiAgentId(installResponse.getAgentId())
                            .aiAgentStatus(installResponse.getAgentStatus())
                            .build();
                })
                .onErrorMap(error -> {
                    log.error(ERROR_ENABLING_BOOMI_AGENT, aiAgentVersionExternalId, error);
                    return new RuntimeException(ERROR_ENABLING_BOOMI_AGENT + error.getMessage());
                });
    }

    @Override
    public Mono<AiAgentActionResponse> disableAiAgentVersion(AiAgentProviderAccount providerAccount,
            String aiAgentVersionExternalId) {
        return _gardenService.uninstallAgent(providerAccount, aiAgentVersionExternalId)
                .doOnSubscribe(subscription ->
                        log.debug("Starting Garden Ai Agent {} Uninstall API call from BoomiAiAgentActionService",
                                aiAgentVersionExternalId))
                .map(installResponse -> {
                    log.debug("Garden Ai Agent Uninstall API response received in BoomiAiAgentActionService: {}.",
                            installResponse);
                    return AiAgentActionResponse.builder()
                            .success(installResponse.isSuccess())
                            .error(installResponse.getError())
                            .aiAgentId(installResponse.getAgentId())
                            .aiAgentStatus(installResponse.getAgentStatus())
                            .build();
                })
                .onErrorMap(error -> {
                    log.error(ERROR_DISABLING_BOOMI_AGENT, aiAgentVersionExternalId, error);
                    return new RuntimeException(ERROR_DISABLING_BOOMI_AGENT + error.getMessage());
                });
    }
}
