// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.agent.action;

import reactor.core.publisher.Mono;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.NOT_IMPLEMENTED;

public interface AiAgentActionService {

    AiAgentProviderType getAgentProviderType();

    default Mono<AiAgentActionResponse> enableAiAgentVersion(AiAgentProviderAccount providerAccount,
            String aiAgentVersionExternalId) {
        throw new IllegalArgumentException(NOT_IMPLEMENTED);
    }

    default Mono<AiAgentActionResponse> disableAiAgentVersion(AiAgentProviderAccount providerAccount,
            String aiAgentVersionExternalId) {
        throw new IllegalArgumentException(NOT_IMPLEMENTED);
    }
}
