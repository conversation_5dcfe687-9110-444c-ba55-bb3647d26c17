// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.s3;

import lombok.extern.slf4j.Slf4j;

import com.boomi.aiagentregistry.exception.AiAgentRegistryException;

import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Optional;

@Slf4j
public class FileStorageServiceLocalImpl implements FileStorageService {

    private static final String LOCAL_FOLDER = "s3/";
    private static final int PORT = 3037;

    @Override
    public Optional<URL> putSmallObject(String bucketName, String key, byte[] content) {
        String fileNameAndPath = LOCAL_FOLDER + key;
        Path path = Paths.get(fileNameAndPath);
        if (!path.getParent().toFile().exists() && !path.getParent().toFile().mkdirs()) {
            log.warn("Error creating directories for {} ", (path.getParent().toFile()));
        }

        File targetFile = new File(fileNameAndPath);
        try {
            Files.write(targetFile.toPath(), content, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            return Optional.of(targetFile.toURI().toURL());
        } catch (Exception exception) {
            log.error("Error uploading logo to s3: {}", exception.getMessage(), exception);
            throw new AiAgentRegistryException("Error while storing small file >>> " + exception.getMessage(),
                    exception);
        }
    }

    @Override
    public String createPresignedGetUrl(String bucketName, String key, long expiryInSeconds) {
        try {
            URL url = new URL("http", "localhost", PORT, "/" + LOCAL_FOLDER + key);
            return url.toString();
        } catch (MalformedURLException exception) {
            log.error("Error while creating presigned url", exception);
            throw new AiAgentRegistryException("Error while storing small file >>> " + exception.getMessage(),
                    exception);
        }
    }

    @Override
    public void deleteObject(String bucketName, String key) {
        try {
                java.nio.file.Files.delete(Paths.get(LOCAL_FOLDER + key));
            } catch (IOException e) {
                log.error("Error while deleting file {}", key, e);
                throw new AiAgentRegistryException("Error while deleting file " + key,  e);
            }
    }
}
