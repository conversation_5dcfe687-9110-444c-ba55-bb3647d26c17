// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.s3;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.Delete;
import software.amazon.awssdk.services.s3.model.DeleteObjectsRequest;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.ObjectIdentifier;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Exception;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

import java.net.URL;
import java.time.Duration;
import java.util.List;
import java.util.Optional;

@Slf4j
@AllArgsConstructor
public class FileStorageServiceS3Impl implements FileStorageService {

    private static final String CREATED_PRESIGNED_URL = "created presigned url {} for bucket {} and key {}";
    private final S3Client _s3Client;

    @Override
    public Optional<URL> putSmallObject(String bucketName, String key, byte[] content) {
        try {
            PutObjectRequest request = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(key)
                .build();

            RequestBody requestBody = RequestBody.fromBytes(content);
            _s3Client.putObject(request, requestBody);
            return Optional.of(_s3Client
                    .utilities()
                    .getUrl( urlBuilder ->
                            urlBuilder.bucket(bucketName).key(key).build()));


        } catch (S3Exception exception) {
            log.error("Error writing file with bucketname {} and key {} in S3", bucketName, key, exception);
        }
        return Optional.empty();
    }

    @Override
    public String createPresignedGetUrl(String bucketName, String key, long expiryInSeconds) {
       try (S3Presigner presigner = S3Presigner.create()) {

           GetObjectRequest objectRequest = GetObjectRequest.builder()
                   .bucket(bucketName)
                   .key(key)
                   .build();

           GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                   .signatureDuration(Duration.ofSeconds(expiryInSeconds))
                   .getObjectRequest(objectRequest)
                   .build();

           PresignedGetObjectRequest presignedRequest = presigner.presignGetObject(presignRequest);
           String preSignedUrl = presignedRequest.url().toExternalForm();
           log.info(CREATED_PRESIGNED_URL, preSignedUrl, bucketName, key);
           return preSignedUrl;

       }
    }

    @Override
    public void deleteObject(String bucketName, String key) {
        List<ObjectIdentifier> keys = List.of(ObjectIdentifier.builder().key(key).build());
        Delete delete = Delete.builder()
                .objects(keys)
                .build();
        DeleteObjectsRequest request = DeleteObjectsRequest.builder()
                .bucket(bucketName)
                .delete(delete)
                .build();

        log.debug("Deleting  {} from s3.", request);
        _s3Client.deleteObjects(request);
        log.info("Successfully deleted  {} from s3.", key);
    }
}
