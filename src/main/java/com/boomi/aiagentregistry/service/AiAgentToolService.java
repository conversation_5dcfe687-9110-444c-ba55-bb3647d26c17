// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service;

import com.boomi.aiagentregistry.aop.ReactiveLogging;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTask;
import com.boomi.aiagentregistry.entity.AiAgentToolAssociation;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.graphql.server.schema.types.AiAgentTool;
import com.boomi.graphql.server.schema.types.AiAgentToolCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentToolResource;
import com.boomi.graphql.server.schema.types.AiAgentToolType;
import com.boomi.graphql.server.schema.types.AiAgentToolUpdateInput;
import graphql.schema.DataFetchingEnvironment;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

/**
 * <AUTHOR>
 */
public interface AiAgentToolService {

    CompletableFuture<AiAgentTool> createAIAgentTool(
            AiAgentToolCreateInput input,
            DataFetchingEnvironment dfe);

    CompletableFuture<AiAgentTool> updateAIAgentTool(AiAgentToolUpdateInput input, DataFetchingEnvironment dfe);

    CompletionStage<List<AiAgentToolResource>> getAiAgentToolResources(AiAgentTool aiAgentTool,
                                                                       DataFetchingEnvironment dfe);
    @ReactiveLogging
    Mono<Set<AiAgentToolAssociation>> getVersionAssociations(AiAgentVersion version);

    @ReactiveLogging
    Mono<Void> deleteAssociationsByToolIdsAndVersionId(Set<Integer> toolIds, AiAgentVersion version);

    @ReactiveLogging
    Mono<Void> deleteAssociationsByToolIdsAndTaskId(Set<Integer> toolIds, AiAgentTask task);

    @ReactiveLogging
    Mono<Integer> deleteToolsById(Set<Integer> toolIds);

    @ReactiveLogging
    Optional<com.boomi.aiagentregistry.entity.AiAgentTool> findByExternalIdReactive(
            String externalId,
            AiAgentToolType type,
            AiAgentProviderAccount account);

    @ReactiveLogging
    Set<Integer> identifyToolsToDetach(
            Set<AiAgentToolAssociation> associations,
            Set<String> toolIdsToRetain,
            AiAgentToolType toolType);

    @ReactiveLogging
    void associateToolWithVersion(
            AiAgentVersion version,
            com.boomi.aiagentregistry.entity.AiAgentTool tool, String relationshipStatus);

    @ReactiveLogging
    Mono<Set<Integer>> findOrphanTools(Set<Integer> toolToDetachIds);
}
