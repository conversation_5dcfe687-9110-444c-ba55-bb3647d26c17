// Copyright (c) 2024 Boom<PERSON>, LP.
package com.boomi.aiagentregistry.service;

import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.repo.AiAgentVersionRepository;
import org.springframework.stereotype.Component;


@Component("VERSION")
public class AiAgentVersionManager implements ReferenceEntityManager {

    AiAgentVersionRepository _aiAgentVersionRepository;

    public AiAgentVersionManager(AiAgentVersionRepository aiAgentVersionRepository) {
        _aiAgentVersionRepository = aiAgentVersionRepository;
    }

    @Override
    public AiAgent getAgent(String entityId) {
        AiAgentVersion version = _aiAgentVersionRepository
                .findByGuid(entityId).orElse(null);
        if (version != null) {
            return version.getAgent();
        }
        return null;
    }

    @Override
    public Integer getRelatedEntityUid(String entityId) {
        AiAgentVersion version = _aiAgentVersionRepository
                .findByGuid(entityId).orElse(null);
        assert version != null;
        return version.getUid();
    }
}
