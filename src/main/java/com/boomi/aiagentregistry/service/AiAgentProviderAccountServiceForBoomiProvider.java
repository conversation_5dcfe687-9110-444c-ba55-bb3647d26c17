// Copyright (c) 2024 Boomi, LP

package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import com.boomi.aiagentregistry.constant.AWSBedrockEnabledRegionConstant;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.util.AiAgentProviderAccountCommonServiceUtil;
import com.boomi.aiagentregistry.util.AuditUtil;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccount;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountUpdateInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.graphql.server.servlet.ErrorUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_ON_ADDING_PROVIDER_DETAILS_FOR_BOOMI;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_ON_UPDATING_PROVIDER_DETAILS_FOR_BOOMI;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_REGISTRY_ACCOUNT_ID_EMPTY;
import static com.boomi.aiagentregistry.mapper.AIAgentProviderAccountMapper.PROVIDER_MAPPER;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.AUTHENTICATED_ACCOUNT_ID_MISSING;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.REGION_REQUIRED_IF_AUTH;
import static com.boomi.graphql.server.servlet.ErrorUtil.GeneralErrorCode.SYSTEM_ERROR;

@Service("aiAgentProviderAccountServiceForBoomiProvider")
@Slf4j
public class AiAgentProviderAccountServiceForBoomiProvider implements AiAgentProviderAccountService{

    @Autowired
    private AiAgentProviderAccountCommonServiceUtil _aiAgentProviderAccountCommonServiceUtil;

    @Override
    public AiAgentProviderType getProviderType() {
        return AiAgentProviderType.BOOMI;
    }

    @Override
    @Transactional
    public CompletionStage<AiAgentProviderAccount> createAiAgentProviderAccount(AiAgentProviderAccountCreateInput input,
            DataFetchingEnvironment dfe) {
        log.info("Creating a new Boomi Provider with name: {}", input.getProviderAccountName());

        CompletableFuture<com.boomi.graphql.server.schema.types.AiAgentProviderAccount> completion =
                new CompletableFuture<>();
        try {

            String idpAccountId = _aiAgentProviderAccountCommonServiceUtil.getIdpAccountId(dfe);
            validateAiAgentProviderAccountCreateInputForBoomi(input, idpAccountId, dfe);

            if (ErrorUtil.hasErrors(dfe)) {
                completion.complete(null);
                return completion;
            }

            return _aiAgentProviderAccountCommonServiceUtil.createAiAgentProviderAccount(input, idpAccountId,
                            input.getAuthSchema(), dfe);
        } catch (Exception dae) {
            ErrorUtil.addError(dfe, SYSTEM_ERROR);
            log.error(ERROR_ON_ADDING_PROVIDER_DETAILS_FOR_BOOMI, dae);
            completion.complete(null);
            return completion;
        }
    }

    @Transactional
    @Override
    public CompletionStage<AiAgentProviderAccount> updateAiAgentProviderAccount(AiAgentProviderAccountUpdateInput input,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {
        log.info("Updating Boomi Provider");

        return validateAndUpdateAiAgentProviderAccount(input, existingProviderAccount, dfe);
    }

    private CompletionStage<AiAgentProviderAccount> validateAndUpdateAiAgentProviderAccount(
            AiAgentProviderAccountUpdateInput input,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {

        CompletableFuture<com.boomi.graphql.server.schema.types.AiAgentProviderAccount> completion =
                new CompletableFuture<>();

        validateAiAgentProviderAccountUpdateInputForBoomi(input, existingProviderAccount, dfe).then(
                        Mono.defer(() -> {
                            if (ErrorUtil.hasErrors(dfe)) {
                                return Mono.fromRunnable(() -> completion.complete(null));
                            }

                            return Mono.fromFuture(
                                    () -> _aiAgentProviderAccountCommonServiceUtil.updateAiAgentProviderAccount(
                                                    input,
                                                    existingProviderAccount,
                                                    completion,
                                                    dfe)
                                            .toCompletableFuture());
                        }))
                .onErrorResume(e -> {
                    ErrorUtil.addError(dfe, SYSTEM_ERROR);
                    log.error(ERROR_ON_UPDATING_PROVIDER_DETAILS_FOR_BOOMI, existingProviderAccount.getGuid(), e);
                    return Mono.fromRunnable(() -> completion.complete(null));
                })
                .subscribe();

        return completion;
    }

    private void validateAiAgentProviderAccountCreateInputForBoomi(AiAgentProviderAccountCreateInput input,
            String idpAccountId, DataFetchingEnvironment dfe) {
        _aiAgentProviderAccountCommonServiceUtil.validateIdpAccountId(dfe);
        _aiAgentProviderAccountCommonServiceUtil.validateProviderAccountName(input.getProviderAccountName(), null, dfe);
        _aiAgentProviderAccountCommonServiceUtil.validateAiAgentProviderAccountCreateInput(input, idpAccountId,
                input.getAuthSchema(), dfe);

    }

    private Mono<Void> validateAiAgentProviderAccountUpdateInputForBoomi(AiAgentProviderAccountUpdateInput input,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {

        _aiAgentProviderAccountCommonServiceUtil.validateIdpAccountId(dfe);
        _aiAgentProviderAccountCommonServiceUtil
                .validateProviderAccountName(input.getProviderAccountName(), existingProviderAccount, dfe);
        _aiAgentProviderAccountCommonServiceUtil.validateMetadataJson(input.getMetadataJson(), dfe);
        _aiAgentProviderAccountCommonServiceUtil
                .validateAiAgentProviderAccountStatus(input.getProviderAccountStatus(), dfe);
        return _aiAgentProviderAccountCommonServiceUtil
                .validateCredentialsForUpdateProviderAccount(input, existingProviderAccount, dfe);
    }
}
