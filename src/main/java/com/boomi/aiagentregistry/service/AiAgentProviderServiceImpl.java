// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.model.AiAgentProviderCount;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.repo.AiAgentRepository;
import com.boomi.aiagentregistry.util.UserUtil;
import com.boomi.graphql.server.schema.types.AiAgentProvider;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentProvidersQueryResponse;
import com.boomi.graphql.server.servlet.ErrorUtil;

import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_WHILE_RETRIEVING_AI_AGENT_PROVIDERS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.FETCHING_AI_AGENT_PROVIDERS_FOR_ACCOUNT;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.FETCH_AI_AGENT_PROVIDER_ERROR;
@Service
@RequiredArgsConstructor
@Slf4j
public class AiAgentProviderServiceImpl implements AiAgentProviderService {

    private final UserUtil _userUtil;
    private final AiAgentProviderAccountRepository _aiAgentProviderAccountRepository;
    private final AiAgentRepository _aiAgentRepository;
    private final IAiAgentLicensingService _iAiAgentLicensingService;

    @Override
    public CompletionStage<AiAgentProvidersQueryResponse> aiAgentProviders(DataFetchingEnvironment dfe) {
        CompletableFuture<AiAgentProvidersQueryResponse> completion = new CompletableFuture<>();
        AiAgentProvidersQueryResponse response = new AiAgentProvidersQueryResponse();
        try {
            String idpAccountId = _userUtil.getAccountId(dfe);
            log.debug(FETCHING_AI_AGENT_PROVIDERS_FOR_ACCOUNT, idpAccountId);
            // fetch all provider types from graphql enum
            List<AiAgentProviderType> providersEnum = Arrays.asList(AiAgentProviderType.values());
            //  fetch number of agents and no of accounts provider wise
            Map<String, Long> agentCountByProviderType = getAgentCountsByProvider(idpAccountId);
            Map<String, Long> registryAccountCountByProviderType = getAccountCountsByProvider(idpAccountId);
            // for each provider get agentCount and accountCount and construct AiAgentProvider
            List<AiAgentProvider> agentProviders = providersEnum.stream()
                    .map(provider -> createAiAgentProvider(
                            provider,
                            agentCountByProviderType.getOrDefault(provider.name(), 0L),
                            registryAccountCountByProviderType.getOrDefault(provider.name(), 0L))).toList();
            response.setAiAgentProviders(agentProviders);
            response.setLicenseLimit(_iAiAgentLicensingService.getLicenseLimit(idpAccountId, dfe));

            completion.complete(response);
        } catch (Exception e) {
            log.error(ERROR_WHILE_RETRIEVING_AI_AGENT_PROVIDERS, e);
            ErrorUtil.addError(dfe, FETCH_AI_AGENT_PROVIDER_ERROR);
            completion.complete(null);
        }
        return completion;
    }

    private Map<String, Long> getAgentCountsByProvider(String idpAccountId) {
        //   get all agent counts grouped by provider
        return countByProviderTypeAndIdpAccountId(_aiAgentRepository.countAiAgentsGroupByProviderType(idpAccountId));
    }

    private Map<String, Long> getAccountCountsByProvider(String idpAccountId) {
        //  get all account counts grouped by provider
        return countByProviderTypeAndIdpAccountId(
                _aiAgentProviderAccountRepository.countAiAgentProviderAccountsGroupByProviderType(idpAccountId));
    }

    Map<String, Long> countByProviderTypeAndIdpAccountId(List<AiAgentProviderCount> countGroupByProviderType) {
        return Optional.ofNullable(countGroupByProviderType)
                .map(list -> list.stream()
                        .filter(Objects::nonNull)
                        .filter(element -> element.getProviderType() != null)
                        .collect(Collectors.toMap(
                                AiAgentProviderCount::getProviderType,
                                AiAgentProviderCount::getCount,
                                (existing, replacement) -> existing,
                                HashMap::new)))
                .orElse(new HashMap<>());
    }
    private AiAgentProvider createAiAgentProvider(AiAgentProviderType providerType, Long agentCount,
            Long accountCount) {
        AiAgentProvider provider = new AiAgentProvider();
        provider.setAiAgentProviderType(providerType);
        provider.setNumberOfAgents(agentCount.intValue());
        provider.setNumberOfAccounts(accountCount.intValue());
        return provider;
    }
}



