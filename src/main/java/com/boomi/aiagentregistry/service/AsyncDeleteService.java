// Copyright (c) 2024 Boomi, LP

package com.boomi.aiagentregistry.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;

import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

@Service
@Slf4j
@RequiredArgsConstructor
public class AsyncDeleteService {
    private final AiAgentProviderAccountRepository _accountRepo;
    private final TransactionTemplate _transactionTemplate;

    public void executeAsyncDelete(Integer providerAccountUid) {
        try {
            // Execute each delete operation in its own transaction
            _transactionTemplate.execute(status -> {
                _accountRepo.deleteTagAssociationForProviderAccountDependents(providerAccountUid);
                return null;
            });

            _transactionTemplate.execute(status -> {
                _accountRepo.deleteLargeTextContentForProviderAccount(providerAccountUid);
                return null;
            });

            _transactionTemplate.execute(status -> {
                _accountRepo.deleteLargeTextContentForProviderAccountDependents(providerAccountUid);
                return null;
            });

            _transactionTemplate.execute(status -> {
                _accountRepo.deleteProviderAccount(providerAccountUid);
                return null;
            });
        } catch (Exception e) {
            log.error("Error during async delete operations for provider account UID: {}", providerAccountUid, e);
            throw e;
        }
    }
}
