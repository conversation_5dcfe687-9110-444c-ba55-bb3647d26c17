// Copyright (c) 2024 Boom<PERSON>, LP.
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.graphql.server.schema.types.AiAgentTagMutationInput;
import com.boomi.graphql.server.schema.types.AiAgentTagMutationOutput;
import com.boomi.graphql.server.schema.types.AiAgentTagQueryResponse;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

public interface IAiAgentTagService {

    CompletableFuture<AiAgentTagQueryResponse> getAiAgentTags(DataFetchingEnvironment dfe);

    CompletionStage<AiAgentTagMutationOutput> aiAgentAddTags(AiAgentTagMutationInput input,
            DataFetchingEnvironment dfe);

}
