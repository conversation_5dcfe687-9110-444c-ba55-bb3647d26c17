// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.graphql.server.schema.types.AiAgentProvidersQueryResponse;

import java.util.concurrent.CompletionStage;

public interface AiAgentProviderService {

    CompletionStage<AiAgentProvidersQueryResponse> aiAgentProviders(
            DataFetchingEnvironment dfe);
}
