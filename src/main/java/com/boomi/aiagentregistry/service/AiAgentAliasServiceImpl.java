// Copyright (c) 2024 Boomi, LP.

package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.entity.AgentEntitySyncLatest;
import com.boomi.aiagentregistry.mapper.AiAgentSyncLatestMapper;
import com.boomi.aiagentregistry.mapper.AiAgentTagMapper;
import com.boomi.aiagentregistry.repo.AiAgentAliasRepository;
import com.boomi.aiagentregistry.repo.AiAgentLatestSyncRepository;
import com.boomi.aiagentregistry.repo.AiAgentTagAssociationRepository;
import com.boomi.graphql.server.schema.types.AiAgentAlias;
import com.boomi.graphql.server.schema.types.AiAgentTag;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncData;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.boomi.util.StringUtil;

import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_ALIAS_NOT_FOUND;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_INVALID_ALIAS_ID;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INFO_NO_SYNC_DATA_FOR_ALIAS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INFO_NO_TAGS_ASSOCIATED_WITH_ALIAS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INFO_TAGS_FOUND_FOR_ALIAS;

@Slf4j
@Component
public class AiAgentAliasServiceImpl implements AiAgentAliasService {

    private final AiAgentAliasRepository _aiAgentAliasRepository;
    private final AiAgentTagAssociationRepository _aiAgentTagAssociationRepository;
    private final AiAgentLatestSyncRepository _aiAgentLatestSyncRepository;

    public AiAgentAliasServiceImpl(AiAgentAliasRepository aiAgentAliasRepository,
            AiAgentTagAssociationRepository aiAgentTagAssociationRepository,
            AiAgentLatestSyncRepository aiAgentLatestSyncRepository) {
        _aiAgentAliasRepository = aiAgentAliasRepository;
        _aiAgentTagAssociationRepository = aiAgentTagAssociationRepository;
        _aiAgentLatestSyncRepository = aiAgentLatestSyncRepository;
    }

    @Override
    public List<AiAgentTag> getTagsForAlias(AiAgentAlias aiAgentAlias, DataFetchingEnvironment dfe) {

        if (StringUtil.isBlank(aiAgentAlias.getId())) {
            log.warn(ERROR_INVALID_ALIAS_ID);
            return Collections.emptyList();
        }

        final com.boomi.aiagentregistry.entity.AiAgentAlias agentAlias =
                _aiAgentAliasRepository.findByGuid(aiAgentAlias.getId()).orElse(null);

        if (agentAlias == null) {
            log.warn(ERROR_ALIAS_NOT_FOUND, aiAgentAlias.getId());
            return Collections.emptyList();
        } else {
            final List<com.boomi.aiagentregistry.entity.AiAgentTag> aiAgentTagEntities =
                    _aiAgentTagAssociationRepository.findAiAgentTagFromAiAgentTagAssociationByRelatedEntityTypeAndRelatedEntityId(
                            AiRegistryEntityType.ALIAS, agentAlias.getUid());
            if (aiAgentTagEntities.isEmpty()) {
                log.info(INFO_NO_TAGS_ASSOCIATED_WITH_ALIAS, agentAlias.getGuid());
                return Collections.emptyList();
            }
            log.info(INFO_TAGS_FOUND_FOR_ALIAS, aiAgentTagEntities.size(), agentAlias.getGuid());
            return AiAgentTagMapper.AI_AGENT_TAG_MAPPER.toAIAgentTagList(aiAgentTagEntities);
        }
    }

    @Override
    public CompletionStage<AiRegistryEntitySyncData> getLatestSyncData(AiAgentAlias aiAgentAlias,
            DataFetchingEnvironment dfe) {

        if (StringUtil.isBlank(aiAgentAlias.getId())) {
            log.warn(ERROR_INVALID_ALIAS_ID);
            return null;
        }

        final com.boomi.aiagentregistry.entity.AiAgentAlias agentAlias =
                _aiAgentAliasRepository.findByGuid(aiAgentAlias.getId()).orElse(null);

        if (agentAlias == null) {
            log.warn(ERROR_ALIAS_NOT_FOUND, aiAgentAlias.getId());
            return null;
        } else {
            AgentEntitySyncLatest aiRegistryEntitySyncData =
                    _aiAgentLatestSyncRepository.findBySyncedEntityUidAndSyncedEntityType(agentAlias.getUid(),
                            AiRegistryEntityType.ALIAS).orElse(null);
            if (aiRegistryEntitySyncData == null) {
                log.info(INFO_NO_SYNC_DATA_FOR_ALIAS, agentAlias.getGuid());
                return null;
            } else {
                final AiRegistryEntitySyncData latestSyncData =
                        AiAgentSyncLatestMapper.AI_AGENT_ALIAS_SYNC_LATEST_MAPPER.toAiRegistrySyncData(
                        aiRegistryEntitySyncData);
                return CompletableFuture.completedFuture(latestSyncData);
            }
        }
    }
}
