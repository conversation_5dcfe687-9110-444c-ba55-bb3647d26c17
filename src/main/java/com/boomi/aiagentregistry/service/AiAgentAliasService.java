// Copyright (c) 2024 Boom<PERSON>, LP.

package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.graphql.server.schema.types.AiAgentAlias;
import com.boomi.graphql.server.schema.types.AiAgentTag;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncData;

import java.util.List;
import java.util.concurrent.CompletionStage;

public interface AiAgentAliasService {

    List<AiAgentTag> getTagsForAlias(AiAgentAlias aiAgentAlias, DataFetchingEnvironment dfe);

    CompletionStage<AiRegistryEntitySyncData> getLatestSyncData(AiAgentAlias aiAgentAlias, DataFetchingEnvironment dfe);
}
