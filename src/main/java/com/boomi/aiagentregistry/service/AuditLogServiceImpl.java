// Copyright (c) 2024 Boomi, LP

package com.boomi.aiagentregistry.service;

import com.boomi.aiagentregistry.entity.AuditLog;
import com.boomi.aiagentregistry.model.AuditLogEntry;
import com.boomi.aiagentregistry.repo.AuditLogRepository;
import com.boomi.aiagentregistry.util.GuidUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuditLogServiceImpl implements AuditLogService {

    private final AuditLogRepository _auditLogRepository;

    @Override
    public void logAction(AuditLogEntry auditLogEntry) {
        try {
            String auditLogGuid = GuidUtil.createAuditLogGuid();
            AuditLog auditLog = new AuditLog();
            auditLog.setGuid(auditLogGuid);
            auditLog.setEntityName(auditLogEntry.getEntityName().name());
            auditLog.setEntityGuid(auditLogEntry.getEntityGuid());
            auditLog.setAction(auditLogEntry.getAction().name());
            auditLog.setUserId(auditLogEntry.getUserId());
            auditLog.setAccountId(auditLogEntry.getAccountId());
            auditLog.setActionDate(com.boomi.aiagentregistry.util.AuditUtilImpl.getCurrentTime());
            auditLog.setEntityVersion(auditLogEntry.getEntityVersion());
            auditLog.setChangeSet(auditLogEntry.getChangeSet());
            auditLog.setRequestId(auditLogEntry.getRequestId());
            auditLog.setActionResult(auditLogEntry.getActionResult().name());

            _auditLogRepository.save(auditLog);
        } catch (Exception e) {
            log.error("Failed to create audit log entry", e);
        }
    }
}

