// Copyright (c) 2024 Boomi, LP

package com.boomi.aiagentregistry.service;

import com.boomi.aiagentregistry.entity.AiAgentLargeTextContent;
import com.boomi.aiagentregistry.entity.AwsAssumeRoleProviderAccountMetadataJson;
import com.boomi.aiagentregistry.exception.OamException;
import com.boomi.aiagentregistry.repo.AiAgentLargeTextContentRepository;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.service.auth.AuthorizationParsingService;
import com.boomi.aiagentregistry.service.auth.AwsAssumeRoleAuthorizationParserStrategy;
import com.boomi.aiagentregistry.service.auth.AwsCredentials;
import com.boomi.aiagentregistry.service.metrics.CloudformationFileService;
import com.boomi.aiagentregistry.service.metrics.OamLinkService;
import com.boomi.aiagentregistry.service.metrics.OamPolicyService;
import com.boomi.aiagentregistry.service.metrics.OamSinkArnRetrieverService;
import com.boomi.aiagentregistry.service.s3.FileStorageService;
import com.boomi.aiagentregistry.util.AiAgentProviderAccountCommonServiceUtil;
import com.boomi.aiagentregistry.util.AuditUtil;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.UserUtil;
import com.boomi.graphql.server.schema.types.AiAgentCloudProviderAccountConfirmInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccount;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountOnboardingTemplate;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountUpdateInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.graphql.server.servlet.ErrorUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import graphql.schema.DataFetchingEnvironment;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import software.amazon.awssdk.services.oam.model.ConflictException;
import software.amazon.awssdk.services.secretsmanager.model.SecretsManagerResponse;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.AWS_ACCOUNT_ID_PATTERN;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.CF_PARAM_ACT_OAM_CUSTOMER_ROLE_NAME;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.CLOUDFORMATION_S3_KEY_PREFIX;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.HOURS_12;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_ON_ADDING_PROVIDER_DETAILS_FOR_AWS_BEDROCK;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_ON_UPDATING_PROVIDER_DETAILS_FOR_AWS_BEDROCK;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_VALIDATING_CONNECTION;
import static com.boomi.aiagentregistry.mapper.AIAgentProviderAccountMapper.PROVIDER_MAPPER;
import static com.boomi.aiagentregistry.mapper.LargeTextContentMapper.LRG_TXT_MAPPER;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.AUTHENTICATED_ACCOUNT_ID_MISSING;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.CROSS_ACCOUNT_MONITORING_CONFIG_FAILED;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.EXTERNAL_ID_NOT_FOUND;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_AUTH_SCHEMA;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_AWS_ACCOUNT_ID;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_CONNECTION_DETAILS;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.INVALID_EXTERNAL_PROVIDER_ACCOUNT_ID;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.OAM_LINK_ALREADY_PRESENT_ERROR;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.OAM_LINK_CREATION_FAILED_ERROR;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PRESIGNED_URL_GENERATION_FAILED;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.REGION_REQUIRED_IF_AUTH;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.SSM_UPDATE_FAILED;
import static com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode.UNSUPPORTED_REGION;
import static com.boomi.graphql.server.schema.types.AiRegistryEntityType.PROVIDER_ACCOUNT;
import static com.boomi.graphql.server.servlet.ErrorUtil.GeneralErrorCode.SYSTEM_ERROR;

@Service("aiAgentProviderAccountServiceForAWSBedrockProvider")
@Slf4j
public class AiAgentProviderAccountServiceForAWSBedrockProvider implements AiAgentProviderAccountService {

    private static final boolean IS_SUCCESS = true;
    private static final String CREATING_PROVIDER_WITH_ASSUME_ROLE_CAPABILITY =
            "Creating a Provider with assume role capability";
    private static final String IS_ACCOUNT_PRESENT = "Is account present? {}";
    private static final String STEP_1_ERROR = "Step 1:  error";
    private static final String STEP_1_SSM_CREDENTIALS_CREATED = "Step1: SSM Credentials created";
    private static final String START_PROVISIONING_NEW_CUSTOMER_WITH_ASSUME_ROLE =
            "Start provisioning a new  customer with assume role. Secret Key is the aws account ID.";
    private static final String FETCHING_EXTERNAL_ID_FOR_ASSUME_ROLE = "Fetching external ID for assume role in step2"
            + " because provider account {} has not-connected status {}";
    private static final String STEP_VALIDATING_CONNECTION = "Validating connection in step {}";
    private static final String UPDATING_SECRET = "Updating secret with temp access token for assume role in step {}";
    private static final String SSM_CREDENTIALS_UPDATED = "SSM Credentials Updated for step {} "
            + "Provider Account name: {}";
    private static final String PROVIDER_ACCOUNT_IS_ALREADY_CONNECTED =
            "provider account {} has connected status of {}, skipping assume role in step2";
    private static final String SKIPPING_CREATING_SECRET_FOR =
            "AWS Account already exists in ACT. So, skipping creating secret {} for {}. ";
    private static final String STEP_1_LOG_PATTERN =
               "Step 1: External account ID %s, provider account name %s, Idp account ID %s";
    private static final String STEP2_LOG_PATTERN =
               "Step 2: Provider Account %s, External account ID %s, provider account name %s, " + "Idp account ID: %s";
    private static final String RECHECK_ASSUME_ROLE_LOG =
                   "RECHECK_ASSUME_ROLE_LOG: Provider Account %s, External account ID %s, provider account name %s, " +
                           "Idp account ID: %s";
    private static final String EXTERNAL_ACCOUNT_WAS_PREVIOUSLY_FULLY_PROVISIONED =
               "This external account {} was previously fully provisioned with another provider account {}. "
                       + "Using secret key name {}";

    private static final String CF_PARAM_ACT_SERVICE_ACCOUNT_ID = "%ACT_SERVICE_ACCOUNT_ID%";
    private static final String CF_PARAM_ACT_SERVICE_ROLE_NAME = "%ACT_SERVICE_ROLE_NAME%";
    private static final String CF_PARAM_ACT_CUSTOMER_POLICY_NAME = "%ACT_CUSTOMER_POLICY_NAME%";
    private static final String CF_PARAM_ACT_CUSTOMER_ROLE_NAME = "%ACT_CUSTOMER_ROLE_NAME%";
    private static final String CF_PARAM_ACT_OAM_SINK_ARN = "%ACT_OAM_SINK_ARN%";
    private static final String CF_PARAM_ACT_BEDROCK_CUSTOMER_POLICY_NAME = "%ACT_BEDROCK_CUSTOMER_POLICY_NAME%";
    private static final String CF_PARAM_ACT_BEDROCK_CUSTOMER_ROLE_NAME = "%ACT_BEDROCK_CUSTOMER_ROLE_NAME%";
    private static final String CF_PARAM_ACT_OAM_CUSTOMER_POLICY_NAME = "%ACT_OAM_CUSTOMER_POLICY_NAME%";
    private static final String EXPECTED_CUSTOMER_ACCOUNT_ID = "%EXPECTED_CUSTOMER_ACCOUNT_ID%";
    private static final String EXPECTED_CUSTOMER_REGION = "%EXPECTED_CUSTOMER_REGION%";
    private static final String BEFORE_AWS_ASSUME_ROLE_AUTH = "before _awsAssumeRoleAuthorizationParserStrategy";
    private static final String SAVING_METADATA_FOR_PROVIDER_ACCOUNT = "Saving metadata {} for provider account UID {}";
    private static final String DELETED_OAM_LINK = "Deleted oam link {} for provider account {}";
    private static final String DELETE_PROVIDER_ACCOUNT = "Inside delete bedrock assume role provider account {}";
    private static final String CF_PARAM_ACT_SYNC_AUTO_SERVICE_ROLE_NAME = "%ACT_SERVICE_SYNC_AUTO_ROLE_NAME%";
    private static final String CF_PARAM_ACT_SYNC_MANUAL_SERVICE_ROLE_NAME = "%ACT_SERVICE_SYNC_MANUAL_ROLE_NAME%";
    private static final String SKIP_ASSUME_ROLE_RECHECK =
            "Skipping assume role recheck because provider account status is not disconnected. The status is {}";

    @Autowired
    private AiAgentProviderAccountCommonServiceUtil _aiAgentProviderAccountCommonServiceUtil;

    @Autowired
    private AiAgentProviderAccountRepository _accountRepo;

    @Autowired
    private FeatureManager _featureManager;

    @Autowired
    private AuditUtil _auditUtil;

    @Autowired
    private AuthorizationParsingService _authorizationParsingService;

    @Autowired
    private AwsAssumeRoleAuthorizationParserStrategy _awsAssumeRoleAuthorizationParserStrategy;

    @Autowired
    private SecretsManagerService _secretsManagerService;

    @Autowired
    private ObjectMapper _objectMapper;

    @Autowired
    private OamPolicyService _oamPolicyService;

    @Autowired
    private FileStorageService _fileStorageService;

    @Autowired
    @Qualifier("s3Bucket")
    private String _s3Bucket;

    @Autowired
    private CloudformationFileService _cloudformationFileService;

    @Autowired
    private OamSinkArnRetrieverService _oamSinkArnRetrieverService;

    @Autowired
    private UserUtil _userUtil;

    @Autowired
    private BedrockAssumeRoleService _bedrockAssumeRoleService;

    private final String _serviceRegistryRoleName;
    private final String _monitoringAccountId;
    private final OamLinkService _oamLinkService;
    private final AiAgentLargeTextContentRepository _aiAgentLargeTextContentRepository;
    private final String _serviceSyncAutoRoleName;
    private final String _serviceSyncManualRoleName;

    private enum ProvisioningStep {
        // Step 1
        CREATING_DRAFT,
        // Step 2
        CONNECTING,
        // Recheck assume role when editing a bedrock provider account
        RECHECK_ASSUME_ROLE
    }

    @Autowired
    public AiAgentProviderAccountServiceForAWSBedrockProvider(
            @Value("${spring.aws.region}") String awsRegion,
            @Value("${boomi.services.aiagentregistry.monitoring.account.id}") String monitoringAccountId,
            @Value("${boomi.services.aiagentregistry.service.account.role}") String serviceRegistryRoleName,
            OamLinkService oamLinkService,
            AiAgentLargeTextContentRepository aiAgentLargeTextContentRepository,
            @Value("${boomi.services.aiagentsync.service.auto.account.role}") String serviceSyncAutoRoleName,
            @Value("${boomi.services.aiagentsync.service.manual.account.role}") String serviceSyncManualRoleName) {
        _serviceRegistryRoleName = serviceRegistryRoleName;
        _monitoringAccountId = monitoringAccountId;
        _oamLinkService = oamLinkService;
        _aiAgentLargeTextContentRepository = aiAgentLargeTextContentRepository;
        _serviceSyncAutoRoleName = serviceSyncAutoRoleName;
        _serviceSyncManualRoleName = serviceSyncManualRoleName;
    }

    @Override
    public AiAgentProviderType getProviderType() {
        return AiAgentProviderType.AWS_BEDROCK;
    }

    @Transactional
    @Override
    public CompletionStage<Boolean> deleteProviderAccount(
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount providerAccount,
            DataFetchingEnvironment dfe) {

        String providerAccountGuid = providerAccount.getGuid();
        log.info(DELETE_PROVIDER_ACCOUNT, providerAccountGuid);
        return CompletableFuture.supplyAsync(() -> {
            AiAgentLargeTextContent largeTextContent = _aiAgentLargeTextContentRepository
                    .findByRelatedEntityUidAndRelatedEntityType(providerAccount.getUid(), PROVIDER_ACCOUNT.name());
            if (largeTextContent == null || StringUtils.isBlank(largeTextContent.getContent())) {
                return null;
            }
            return parseMetadata(largeTextContent.getContent(), providerAccountGuid);
        })
        .thenCompose(metadataJson -> {
            if (metadataJson == null) {
                return CompletableFuture.completedFuture(null);
            }
            return getExternalId(providerAccount, providerAccount.getAuthSchema(), providerAccountGuid, dfe)
                   .thenApply(externalId -> Pair.of(metadataJson, externalId));
        })
        .thenApply(pair -> {
            if (pair == null) {
                return true;
            }
            AwsAssumeRoleProviderAccountMetadataJson metadataJson = pair.getFirst();
            String externalId = pair.getSecond();

            if (StringUtils.isBlank(externalId)) {
                log.warn("Missing External Id for provider account {}", providerAccountGuid);
                ErrorUtil.addError(dfe, EXTERNAL_ID_NOT_FOUND);
                return true;
            }
            _oamLinkService.deleteOamLink(
                metadataJson.getOamLinkArn(), providerAccountGuid,
                providerAccount.getExternalProviderAccountId(),
                providerAccount.getRegion(),
                externalId
            );
            log.info(DELETED_OAM_LINK, metadataJson.getOamLinkArn(), providerAccountGuid);
            return true;
        })
        .whenComplete((result, error) -> _fileStorageService.deleteObject(_s3Bucket,
                getS3ObjectKey(providerAccount)))
        .exceptionally(error -> {
            // CompletionException from parseMetadata is caught here
            // do not add to dfe to allow the rest of the deletion code to be executed
            log.error("Error in deleteProviderAccount {} for Bedrock", providerAccountGuid, error);
            return true;
        });
    }

    private AwsAssumeRoleProviderAccountMetadataJson parseMetadata(String content, String guid) {
        try {
            return _objectMapper.readValue(content, AwsAssumeRoleProviderAccountMetadataJson.class);
        } catch (JsonProcessingException exception) {
            log.error("Error in parsing large text content {} for provider account {}", content, guid, exception);
            throw new CompletionException(exception);
        }
    }

    @Override
    @Transactional
    public CompletionStage<AiAgentProviderAccount> createAiAgentProviderAccount(AiAgentProviderAccountCreateInput input,
            DataFetchingEnvironment dfe) {
        log.info("Creating a new AWS Bedrock Agent Provider with name: {}", input.getProviderAccountName());

        if (input.getAuthSchema() == AiProviderAuthSchema.AWS_ASSUME_ROLE) {
            if (!_featureManager.isDoProvisioningWithAssumeRole()) {
                throw  new IllegalArgumentException("Auths schema is not support AiProviderAuthSchema.AWS_ASSUME_ROLE");
            }
            return createAiAgentProviderAccountWithAssumeRoleCapability(input, dfe);
        }

        CompletableFuture<AiAgentProviderAccount> completion =
                new CompletableFuture<>();
        try {

            String idpAccountId = _aiAgentProviderAccountCommonServiceUtil.getIdpAccountId(dfe);
            validateAiAgentProviderAccountCreateInputForAwsBedrock(input, idpAccountId, dfe);

            if (ErrorUtil.hasErrors(dfe)) {
                completion.complete(null);
                return completion;
            }

            return _aiAgentProviderAccountCommonServiceUtil
                    .createAiAgentProviderAccount(input, idpAccountId, input.getAuthSchema(), dfe);

        } catch (Exception dae) {
            ErrorUtil.addError(dfe, SYSTEM_ERROR);
            log.error(ERROR_ON_ADDING_PROVIDER_DETAILS_FOR_AWS_BEDROCK, dae);
            completion.complete(null);
            return completion;
        }
    }

    @Transactional
    @Override
    public CompletionStage<AiAgentProviderAccount> updateAiAgentProviderAccount(AiAgentProviderAccountUpdateInput input,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {
        log.info("Updating AWS Bedrock Agent Provider");

        return validateAndUpdateAiAgentProviderAccount(input, existingProviderAccount, dfe);
    }

    @Transactional
    @Override
    public CompletionStage<AiAgentProviderAccount> aiAgentCloudProviderAccountConfirm(
            AiAgentCloudProviderAccountConfirmInput input,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {
        CompletableFuture<AiAgentProviderAccount> completion =
                        new CompletableFuture<>();
        _aiAgentProviderAccountCommonServiceUtil.validateIdpAccountId(dfe);
        if (ErrorUtil.hasErrors(dfe)) {
            completion.complete(null);
            return completion;
        }
        String idpAccountId = _aiAgentProviderAccountCommonServiceUtil.getIdpAccountId(dfe);
        AiAgentProviderAccountStatus providerAccountStatus = existingProviderAccount.getProviderAccountStatus();
        boolean isProviderAccountConnected = getConnectedAccountStatuses()
                .contains(providerAccountStatus);
        String providerAccountId = existingProviderAccount.getGuid();
        if (!isProviderAccountConnected) {
            log.info(FETCHING_EXTERNAL_ID_FOR_ASSUME_ROLE, providerAccountId, providerAccountStatus);
            printProvisioningStepLog(IS_SUCCESS, existingProviderAccount, idpAccountId, ProvisioningStep.CONNECTING);
            fullyProvisionProviderAccount(existingProviderAccount, dfe, completion, ProvisioningStep.CONNECTING);
        } else {
            log.info(PROVIDER_ACCOUNT_IS_ALREADY_CONNECTED, providerAccountId, providerAccountStatus);
            printProvisioningStepLog(IS_SUCCESS, existingProviderAccount, idpAccountId, ProvisioningStep.CONNECTING);
            persistProviderAccount(existingProviderAccount, dfe);
            AiAgentProviderAccount mappedProviderAccount = mapProviderAccount(existingProviderAccount);
            _aiAgentProviderAccountCommonServiceUtil
                      .handleAfterProviderCreation(mappedProviderAccount, dfe)
                      .thenAccept(ignored -> completion.complete(mappedProviderAccount));
        }
        return completion;
    }

    /*
      * - Assume role Boomi-ACT-bedrock-customer-role-<environment e.g. sandbox> in customer's account. Note that
      *   External ID (similar to MFA code) that is generated when creating DRAFT provider account is needed to do
      *   assume role
      * - Fetch temp AWS credentials
      * - Store this credentials in secrets manager at location "/aws/<aws-account-id>". This will be used
      *   in sync operations to fetch agent metadata from the customer's aws account and region
      * - Update monitoring account's (ACT) OAM sink policy json in the region present in the input. Add the
      *   customer aws account ID (the source account)
      * - Assume role Boomi-ACT-oam-customer-role-<environment e.g. sandbox> in customer's account
      * - Create the OAM link in the customer's aws account and region
U     * - Update monitoring account's (ACT) OAM sink policy json to remove the
      *   customer aws account ID (the source account). The entry is only needed when creating the link. Also,
      *   the policy has a size limit of 2 KB. Hence, another reason for removing the source account from the
      *   policy json to save space
      * - Set provider account status as Connected in DB
     */
    private void fullyProvisionProviderAccount(com.boomi.aiagentregistry.entity.AiAgentProviderAccount
                    providerAccount, DataFetchingEnvironment dfe,
            CompletableFuture<AiAgentProviderAccount> completion, ProvisioningStep step) {
        String idpAccountId = _aiAgentProviderAccountCommonServiceUtil.getIdpAccountId(dfe);
        String externalProviderAccountId = providerAccount.getExternalProviderAccountId();
        _secretsManagerService.getSecret(providerAccount.getCredentialsKey())
                .single()
                .doOnSuccess(credentials -> {

                     if (StringUtils.isBlank(credentials)) {
                        log.warn("No credentials found for account {}. Is Step1 completed?",
                                providerAccount.getGuid());
                        printProvisioningStepLog(!IS_SUCCESS, providerAccount, idpAccountId, step);
                        ErrorUtil.addError(dfe, AUTHENTICATED_ACCOUNT_ID_MISSING);
                        completion.complete(null);
                        return;
                    }
                    validateConnectionAndSaveTempAccessToken(providerAccount, dfe, credentials,
                            idpAccountId, step)
                        .thenCompose(validatedCredentials -> {
                            if (validatedCredentials != null && !ErrorUtil.hasErrors(dfe)) {
                                String oamLinkArn = null;
                                if (_featureManager.isAutoCreateOamLink()) {
                                    oamLinkArn = createOamLinkForProviderAccount(providerAccount,
                                            validatedCredentials, externalProviderAccountId, dfe);
                                    if (StringUtils.isBlank(oamLinkArn)) {
                                        return CompletableFuture.completedFuture(null);
                                    }
                                }
                                persistProviderAccount(providerAccount, dfe);
                                persistOamLinkArn(providerAccount, oamLinkArn);
                                AiAgentProviderAccount mappedProviderAccount = mapProviderAccount(providerAccount);
                                return _aiAgentProviderAccountCommonServiceUtil
                                           .handleAfterProviderCreation(mappedProviderAccount, dfe)
                                           .thenApply(ignored -> mappedProviderAccount);
                            } else {
                                return CompletableFuture.completedFuture(null);
                            }
                        })
                        .thenAccept(completion::complete)
                        .exceptionally(throwable -> {
                            printProvisioningStepLog(!IS_SUCCESS, providerAccount, idpAccountId, step);
                            log.error("Error during healthcheck in step {}: ",  step, throwable);
                            ErrorUtil.addError(dfe, INVALID_CONNECTION_DETAILS);
                            completion.complete(null);
                            return null;
                        });
                })
                .doOnError(error -> {
                    log.warn("Outer Error processing credentials for account {} in step {}: ",
                            providerAccount.getGuid(), step, error);
                    printProvisioningStepLog(!IS_SUCCESS, providerAccount, idpAccountId, step);
                    ErrorUtil.addError(dfe, INVALID_CONNECTION_DETAILS);
                    completion.complete(null);
                })
                .subscribe();
    }

    private void persistOamLinkArn(com.boomi.aiagentregistry.entity.AiAgentProviderAccount providerAccount,
            String oamLinkArn) {
        AwsAssumeRoleProviderAccountMetadataJson metadataJson = new AwsAssumeRoleProviderAccountMetadataJson();
        metadataJson.setOamLinkArn(oamLinkArn);
        String metadata;
        try {
             metadata = _objectMapper.writeValueAsString(metadataJson);
        } catch (JsonProcessingException jsonProcessingException) {
            String message = "Failed to convert metadata object to string " + metadataJson;
            log.error(message, jsonProcessingException);
            throw new OamException(message, jsonProcessingException);
        }
        AiAgentLargeTextContent providerAccountMetadata = LRG_TXT_MAPPER
                .createLargeTextContent(providerAccount.getUid(),
                        PROVIDER_ACCOUNT.name(), metadata);
        log.info(SAVING_METADATA_FOR_PROVIDER_ACCOUNT, providerAccountMetadata, providerAccount.getUid());
        _aiAgentLargeTextContentRepository.save(providerAccountMetadata);
    }

    private String createOamLinkForProviderAccount(com.boomi.aiagentregistry.entity.AiAgentProviderAccount
                    existingProviderAccount, AwsCredentials validatedCredentials, String externalProviderAccountId,
            DataFetchingEnvironment dfe) {
        try {
            String region = existingProviderAccount.getRegion();
            // create OAM link customer's account and region and return the ARN
            return _oamLinkService.createOamLink(existingProviderAccount.getGuid(),
                    externalProviderAccountId, region, validatedCredentials.getExternalId());
        }
        catch (ConflictException e) {
            logConflictException(dfe, e);
        }
        catch (Exception e) {
            Throwable currentException = e;
            boolean isConflictException = false;
            while (currentException != null) {
                if (currentException instanceof ConflictException) {
                    isConflictException = true;
                    logConflictException(dfe, (RuntimeException) e);
                    break;
                }
                currentException = currentException.getCause();
            }
            if (!isConflictException) {
                log.error("Oam Link creation failed due to an unknown reason.", e);
                ErrorUtil.addError(dfe, OAM_LINK_CREATION_FAILED_ERROR);
            }
        }
        return StringUtils.EMPTY;
    }

    private void logConflictException(DataFetchingEnvironment dfe, RuntimeException e) {
        log.error("Oam Link is already present from your AWS account to the provider account.", e);
        ErrorUtil.addError(dfe, OAM_LINK_ALREADY_PRESENT_ERROR);
    }

    @Override
    public List<CompletableFuture<AiAgentProviderAccount>> populateOnboardingTemplate(
                @NotNull List<AiAgentProviderAccount> accounts, DataFetchingEnvironment dfe) {

        Map<String, AiAgentProviderAccount> idToProviderAccountMap = accounts
                .stream()
                .collect(Collectors.toMap(
                        AiAgentProviderAccount::getId, account -> account
                    ));

        if (CollectionUtils.isEmpty(accounts)) {
            return Collections.emptyList();
        }

        List<String> accountIds = accounts
                .stream()
                // add this explicit check here instead of creating a factory because in the long term the previous
                // aws auth schema would be removed. At that time remove this filter condition
                .filter(providerAccount -> providerAccount.getAuthSchema() ==
                        AiProviderAuthSchema.AWS_ASSUME_ROLE)
                .map(AiAgentProviderAccount::getId).toList();

        List<com.boomi.aiagentregistry.entity.AiAgentProviderAccount> dbProviderAccounts =
                _accountRepo.findByGuidInAndIdpAccountId(accountIds, _userUtil.getAccountId(dfe));

        return dbProviderAccounts.stream()
            .map(dbProviderAccount ->
                getOnboardingTemplate(dbProviderAccount, dfe)
                    .thenApply(template -> {
                        AiAgentProviderAccount account = idToProviderAccountMap.get(dbProviderAccount.getGuid());
                        account.setOnboardingTemplate(template);
                        return account;
                    })
                    .exceptionally(throwable -> {
                        log.error("Error populating template for account {}",
                                dbProviderAccount.getGuid(), throwable);
                        return null;
                    })
            )
            .toList();
    }

    private CompletableFuture<AiAgentProviderAccountOnboardingTemplate> getOnboardingTemplate(
            @NotNull com.boomi.aiagentregistry.entity.AiAgentProviderAccount dbProviderAccount,
            DataFetchingEnvironment dfe) {

        printGetOnboardingTemplateData(IS_SUCCESS, dbProviderAccount);

        if (dbProviderAccount.getAuthSchema() != AiProviderAuthSchema.AWS_ASSUME_ROLE) {
            ErrorUtil.addError(dfe, INVALID_AUTH_SCHEMA);
            log.warn("Invalid auth schema: {}", dbProviderAccount.getAuthSchema());
            return CompletableFuture.completedFuture(null);
        }

        return getExternalId(
                dbProviderAccount,
                dbProviderAccount.getAuthSchema(),
                dbProviderAccount.getGuid(),
                dfe)
            .thenApply(externalId -> {
                if (StringUtils.isBlank(externalId)) {
                    printGetOnboardingTemplateData(!IS_SUCCESS, dbProviderAccount);
                    log.warn("Missing External Id for provider account {}: ", dbProviderAccount.getGuid());
                    ErrorUtil.addError(dfe, EXTERNAL_ID_NOT_FOUND);
                    return null;
                }
                String preSignedUrl;
                if (_featureManager.isAutoCreateOamLink()) {
                    preSignedUrl = getPreSignedUrlV2(dbProviderAccount, dfe);
                } else {
                    preSignedUrl = getPreSignedUrl(dbProviderAccount, dfe);
                }
                log.info("Got presigned Url {}", preSignedUrl);
                if (preSignedUrl == null) {
                    ErrorUtil.addError(dfe, PRESIGNED_URL_GENERATION_FAILED);
                    return null;
                }
                var onboardingTemplate = new AiAgentProviderAccountOnboardingTemplate();
                onboardingTemplate.setExternalId(externalId);
                onboardingTemplate.setTemplateUrl(preSignedUrl);
                onboardingTemplate.setExpiresIn(HOURS_12);
                return onboardingTemplate;
            })
            .exceptionally(error -> {
                printGetOnboardingTemplateData(!IS_SUCCESS, dbProviderAccount);
                log.error("Error in get onboarding template", error);
                ErrorUtil.addError(dfe, SYSTEM_ERROR);
                return null;
            });
    }

    private @Nullable String getPreSignedUrl(
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount dbProviderAccount,
            DataFetchingEnvironment dfe) {
        log.info("Obtaining CF template for provider account {}", dbProviderAccount.getGuid());
        try {
            String oamSinkArn = _oamSinkArnRetrieverService.getSinkArn(dbProviderAccount.getRegion());
            if (StringUtils.isBlank(oamSinkArn)) {
                log.warn("Missing OAM sink ARN for region {}", dbProviderAccount.getRegion());
                ErrorUtil.addError(dfe, SYSTEM_ERROR);
                return null;
            }

            String template = isExternalAccountNotConnected(dbProviderAccount.getExternalProviderAccountId())
                ? getCompleteTemplate(oamSinkArn)
                : getOamLinkTemplate(oamSinkArn);

            return getPresignedUrlCommon(dbProviderAccount, template);
        } catch (Exception e) {
            log.error("Error in presigned Url", e);
            ErrorUtil.addError(dfe, PRESIGNED_URL_GENERATION_FAILED);
            return null;
        }
    }

    private @Nullable String getPresignedUrlCommon(
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount dbProviderAccount,
            String template) {
        String s3ObjectKey = getS3ObjectKey(dbProviderAccount);
        return _fileStorageService.putSmallObject(_s3Bucket, s3ObjectKey, template.getBytes(StandardCharsets.UTF_8))
                .map(value -> _fileStorageService.createPresignedGetUrl(_s3Bucket, s3ObjectKey, HOURS_12)).orElse(
                        null);
    }

    private static @NotNull String getS3ObjectKey(
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount dbProviderAccount) {
        return CLOUDFORMATION_S3_KEY_PREFIX + dbProviderAccount.getGuid();
    }

    private @Nullable String getPreSignedUrlV2(
             com.boomi.aiagentregistry.entity.AiAgentProviderAccount dbProviderAccount,
             DataFetchingEnvironment dfe) {
         log.info("Obtaining V2 CF template for provider account {}", dbProviderAccount.getGuid());
         try {
             String template = getCompleteTemplateV2(dbProviderAccount.getExternalProviderAccountId(),
                     dbProviderAccount.getRegion());
             return getPresignedUrlCommon(dbProviderAccount, template);
         } catch (Exception e) {
             log.error("Error in presigned Url V2", e);
             ErrorUtil.addError(dfe, PRESIGNED_URL_GENERATION_FAILED);
             return null;
         }
     }

    private Optional<com.boomi.aiagentregistry.entity.AiAgentProviderAccount> findExternalAccountThatIsConnected(
            String externalProviderAccountId) {
        return _accountRepo
                .findFirstByExternalProviderAccountIdAndAuthSchemaAndProviderAccountStatusInAndIsDeletedFalse(
                externalProviderAccountId,
                AiProviderAuthSchema.AWS_ASSUME_ROLE,
                getConnectedAccountStatuses()
        );
    }

    private boolean isExternalAccountNotConnected(String externalProviderAccountId) {
        return !_accountRepo.existsByExternalProviderAccountIdAndAuthSchemaAndProviderAccountStatusInAndIsDeletedFalse(
                externalProviderAccountId,
                AiProviderAuthSchema.AWS_ASSUME_ROLE,
                getConnectedAccountStatuses()
        );
    }

    private String getCompleteTemplate(String oamSinkArn) {
        String template = _cloudformationFileService.getCompleteTemplate();
        StringBuilder sb = new StringBuilder(template);
        Map<@NotNull String, @NotNull String>
                completeTemplateParamsMap = Map.of(
                        CF_PARAM_ACT_SERVICE_ACCOUNT_ID, _monitoringAccountId,
                        CF_PARAM_ACT_SERVICE_ROLE_NAME, _serviceRegistryRoleName,
                        CF_PARAM_ACT_CUSTOMER_POLICY_NAME, _bedrockAssumeRoleService.getCustomerPolicyName(),
                        CF_PARAM_ACT_CUSTOMER_ROLE_NAME, _bedrockAssumeRoleService.getCustomerRoleName(),
                        CF_PARAM_ACT_OAM_SINK_ARN, oamSinkArn,
                        CF_PARAM_ACT_SYNC_AUTO_SERVICE_ROLE_NAME, _serviceSyncAutoRoleName,
                        CF_PARAM_ACT_SYNC_MANUAL_SERVICE_ROLE_NAME, _serviceSyncManualRoleName);
        for (Map.Entry<String, String> entry : completeTemplateParamsMap.entrySet()) {
            int start;
            while ((start = sb.indexOf(entry.getKey())) != -1) {
                sb.replace(start, start + entry.getKey().length(), entry.getValue());
            }
        }

        return sb.toString();
    }

    private String getCompleteTemplateV2(String customerAccountId, String customerRegion) {
        String template = _cloudformationFileService.getCompleteTemplateV2();

        Map<@NotNull String, @NotNull String>
                completeTemplateParamsMap = Map.of(
                        CF_PARAM_ACT_SERVICE_ACCOUNT_ID, _monitoringAccountId,
                        CF_PARAM_ACT_SERVICE_ROLE_NAME, _serviceRegistryRoleName,
                        CF_PARAM_ACT_BEDROCK_CUSTOMER_POLICY_NAME,
                        _bedrockAssumeRoleService.getBedrockCustomerPolicyName(),
                        CF_PARAM_ACT_BEDROCK_CUSTOMER_ROLE_NAME, _bedrockAssumeRoleService.getBedrockCustomerRoleName(),
                        CF_PARAM_ACT_OAM_CUSTOMER_POLICY_NAME, _bedrockAssumeRoleService.getOamCustomerPolicyName(),
                        CF_PARAM_ACT_SYNC_AUTO_SERVICE_ROLE_NAME, _serviceSyncAutoRoleName,
                        CF_PARAM_ACT_SYNC_MANUAL_SERVICE_ROLE_NAME, _serviceSyncManualRoleName,
                        CF_PARAM_ACT_OAM_CUSTOMER_ROLE_NAME, _bedrockAssumeRoleService.getOamCustomerRoleName(),
                        EXPECTED_CUSTOMER_ACCOUNT_ID, customerAccountId,
                        EXPECTED_CUSTOMER_REGION, customerRegion);

        for (Map.Entry<String, String> entry : completeTemplateParamsMap.entrySet()) {
            template = template.replace(entry.getKey(), entry.getValue());
        }

        return template;
    }

    private String getOamLinkTemplate(String oamSinkArn) {
        String template = _cloudformationFileService.getOamPolicyOnlyTemplate();
        StringBuilder sb = new StringBuilder(template);

        Map<String, String> replacements = Map.of(
                CF_PARAM_ACT_SERVICE_ACCOUNT_ID, _monitoringAccountId,
                CF_PARAM_ACT_OAM_SINK_ARN, oamSinkArn
        );

        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            int start;
            while ((start = sb.indexOf(entry.getKey())) != -1) {
                sb.replace(start, start + entry.getKey().length(), entry.getValue());
            }
        }

        return sb.toString();
    }

    private CompletableFuture<AwsCredentials> validateConnectionAndSaveTempAccessToken(
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe,
            String credentials,
            String idpAccountId, ProvisioningStep step) {

        log.info(STEP_VALIDATING_CONNECTION, step);
        CompletableFuture<AwsCredentials> validationFuture = new CompletableFuture<>();

        _authorizationParsingService.createAuthorization(credentials, existingProviderAccount.getAuthSchema())
                .map(AwsCredentials.class::cast)
                .publishOn(Schedulers.boundedElastic())
                .doOnSuccess(credentialsWithExternalId -> {
                    try {
                        validateAssumeRoleConnectionAndSaveTempAccessToken(
                                existingProviderAccount,
                                dfe,
                                credentialsWithExternalId,
                                validationFuture,
                                idpAccountId, step);
                        if (ErrorUtil.hasErrors(dfe)) {
                            validationFuture.complete(null);
                            return;
                        }
                        validationFuture.complete(credentialsWithExternalId);
                    } catch (Exception e) {
                        log.warn("Try Catch error processing credentials for account {} in step {}",
                                existingProviderAccount.getGuid(), step, e);
                        validationFuture.completeExceptionally(e);
                    }
                })
                .doOnError(error -> {
                    log.warn("Error processing credentials for account {} in step {}",
                            existingProviderAccount.getGuid(), step, error);
                    printProvisioningStepLog(!IS_SUCCESS, existingProviderAccount, idpAccountId, step);
                    ErrorUtil.addError(dfe, INVALID_CONNECTION_DETAILS);
                    validationFuture.complete(null);
                })
                .subscribe();

        return validationFuture;
    }

    private void persistProviderAccount(
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {
        existingProviderAccount
                .setProviderAccountStatus(AiAgentProviderAccountStatus.CONNECTED);
        _auditUtil.setUpdatedAudit(existingProviderAccount, dfe);
        _accountRepo.saveAndFlush(existingProviderAccount);
    }

    private static AiAgentProviderAccount mapProviderAccount(
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount) {
        // avoid lazy initialization errors
        existingProviderAccount.setAgents(Collections.emptySet());
        return PROVIDER_MAPPER.toAiAgentProviderAccount(existingProviderAccount);
    }

    // suppress log JsonProcessingException message because it should not log secrets
    @SuppressWarnings("java:S1166")
    private void validateAssumeRoleConnectionAndSaveTempAccessToken(
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe, AwsCredentials credentialsWithExternalId,
            CompletableFuture<AwsCredentials> completion, String idpAccountId, ProvisioningStep step) {
        log.info(BEFORE_AWS_ASSUME_ROLE_AUTH);
        Optional<AwsCredentials> optionalAwsCredentials = _awsAssumeRoleAuthorizationParserStrategy
                  .validateConnection(existingProviderAccount.getExternalProviderAccountId(),
                          existingProviderAccount.getRegion(), credentialsWithExternalId.getExternalId());
        if (optionalAwsCredentials.isEmpty()) {
            log.warn(ERROR_VALIDATING_CONNECTION);
            ErrorUtil.addError(dfe, INVALID_CONNECTION_DETAILS);
            completion.complete(null);
            return;
        }
        log.info(UPDATING_SECRET, step);
        String secret;
        try {
            secret = _objectMapper.writeValueAsString(optionalAwsCredentials.get());
        } catch (JsonProcessingException e) {
            log.error("JsonProcessingException in writing temp aws credentials");
            printProvisioningStepLog(!IS_SUCCESS, existingProviderAccount, idpAccountId, step);
            ErrorUtil.addError(dfe, SSM_UPDATE_FAILED);
            completion.complete(null);
            return;
        }
        String secretName = existingProviderAccount.getCredentialsKey();
        SecretsManagerResponse updateSecret = _secretsManagerService.updateSecret(secretName, secret);
        if (updateSecret == null) {
            log.error("Failed to write temp aws creds to SSM");
            printProvisioningStepLog(!IS_SUCCESS, existingProviderAccount, idpAccountId, step);
            ErrorUtil.addError(dfe, SSM_UPDATE_FAILED);
            completion.complete(null);
            return;
        }
        log.info(SSM_CREDENTIALS_UPDATED, step, existingProviderAccount.getProviderAccountName());
    }

    private CompletionStage<AiAgentProviderAccount> validateAndUpdateAiAgentProviderAccount(
            AiAgentProviderAccountUpdateInput input,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {

        CompletableFuture<AiAgentProviderAccount> completion =
                new CompletableFuture<>();

        validateAiAgentProviderAccountUpdateInputForAwsBedrock(input, existingProviderAccount, dfe)
            .then(Mono.defer(() -> {
                    if (ErrorUtil.hasErrors(dfe)) {
                        return Mono.fromRunnable(() -> completion.complete(null));
                    }

                return Mono.fromFuture(() ->
                    recheckAssumeRole(existingProviderAccount, dfe)
                        .thenCompose(recheckResult -> {
                            if (recheckResult == null || ErrorUtil.hasErrors(dfe)) {
                                return CompletableFuture.completedFuture(null);
                            }
                            return _aiAgentProviderAccountCommonServiceUtil.updateAiAgentProviderAccount(
                                    input,
                                    existingProviderAccount,
                                    completion,
                                    dfe).toCompletableFuture();
                        }));
                }))
                .onErrorResume(e -> {
                    ErrorUtil.addError(dfe, SYSTEM_ERROR);
                    log.error(ERROR_ON_UPDATING_PROVIDER_DETAILS_FOR_AWS_BEDROCK,
                            existingProviderAccount.getGuid(), e);
                    return Mono.fromRunnable(() -> completion.complete(null));
                })
                .subscribe();

        return completion;
    }

    private void validateAiAgentProviderAccountCreateInputForAwsBedrock(AiAgentProviderAccountCreateInput input,
            String idpAccountId, DataFetchingEnvironment dfe) {
        _aiAgentProviderAccountCommonServiceUtil.validateIdpAccountId(dfe);
        _aiAgentProviderAccountCommonServiceUtil.validateProviderAccountName(input.getProviderAccountName(), null, dfe);
        _aiAgentProviderAccountCommonServiceUtil.validateAiAgentProviderAccountCreateInput(input, idpAccountId,
                input.getAuthSchema(), dfe);
    }

    private Mono<Void> validateAiAgentProviderAccountUpdateInputForAwsBedrock(AiAgentProviderAccountUpdateInput input,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount,
            DataFetchingEnvironment dfe) {

        _aiAgentProviderAccountCommonServiceUtil.validateIdpAccountId(dfe);
        _aiAgentProviderAccountCommonServiceUtil
                .validateProviderAccountName(input.getProviderAccountName(), existingProviderAccount, dfe);
        _aiAgentProviderAccountCommonServiceUtil.validateMetadataJson(input.getMetadataJson(), dfe);
        _aiAgentProviderAccountCommonServiceUtil
                .validateAiAgentProviderAccountStatus(input.getProviderAccountStatus(), dfe);
        return _aiAgentProviderAccountCommonServiceUtil
                .validateCredentialsForUpdateProviderAccount(input, existingProviderAccount, dfe);
    }

   private CompletionStage<AiAgentProviderAccount> createAiAgentProviderAccountWithAssumeRoleCapability
            (AiAgentProviderAccountCreateInput input, DataFetchingEnvironment dfe) {
        String idpAccountId = _aiAgentProviderAccountCommonServiceUtil.getIdpAccountId(dfe);
        log.info(CREATING_PROVIDER_WITH_ASSUME_ROLE_CAPABILITY);
        printProvisioningStep1Log(IS_SUCCESS, input, idpAccountId);

        CompletableFuture<AiAgentProviderAccount> completion =
                new CompletableFuture<>();

        try {
            validateCreateAccountWithAssumeRoleInput(input, dfe);

            if (ErrorUtil.hasErrors(dfe)) {
                completion.complete(null);
                return completion;
            }

            String externalAccountId = input.getExternalAccountId();
            if (_featureManager.isOneExternalAccountWithOneIdpAccountOneAuthType()) {
                boolean exists =
                        _aiAgentProviderAccountCommonServiceUtil
                                .isProviderAccountExistsForOneExternalIdWithOneIdpOneAuthCheck(
                                    externalAccountId, idpAccountId, input.getAuthSchema(), input.getRegion());
                if (Boolean.TRUE.equals(exists)) {
                    log.warn(PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT.name());
                    ErrorUtil.addError(dfe, PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT);
                    completion.complete(null);
                    return completion;
                }
            }
            Optional<com.boomi.aiagentregistry.entity.AiAgentProviderAccount> existingProviderAccount =
                    _accountRepo.findByIdpAccountIdAndAuthSchemaAndExternalProviderAccountIdAndRegionAndIsDeletedFalse(
                        idpAccountId, input.getAuthSchema(), externalAccountId, input.getRegion());
            log.info(IS_ACCOUNT_PRESENT, existingProviderAccount.isPresent());
            if (existingProviderAccount.isPresent() &&
                   existingProviderAccount.get().getProviderAccountStatus() !=
                           AiAgentProviderAccountStatus.PROVISIONING_STEP1_COMPLETE) {
                log.warn(PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT.name());
                ErrorUtil.addError(dfe, PROVIDER_ACCOUNT_DUPLICATE_CONSTRAINT);
                completion.complete(null);
                return completion;
           }

           com.boomi.aiagentregistry.entity.AiAgentProviderAccount providerAccount;
           if (existingProviderAccount.isPresent()) {
               providerAccount = existingProviderAccount.get();
               AiAgentProviderAccount mappedAiAgentProviderAccount =
                       PROVIDER_MAPPER.toAiAgentProviderAccount(providerAccount);
               completion.complete(mappedAiAgentProviderAccount);
           } else {
                createDraftOrFullyProvisionedProviderAccount(input, dfe, completion);
           }
        }
        catch (OamException oamException) {
            ErrorUtil.addError(dfe, CROSS_ACCOUNT_MONITORING_CONFIG_FAILED, input.getRegion());
            printProvisioningStep1Log(!IS_SUCCESS, input, idpAccountId);
            log.error(STEP_1_ERROR, oamException);
            completion.complete(null);
       }
        catch (Exception exception) {
            ErrorUtil.addError(dfe, SYSTEM_ERROR);
            printProvisioningStep1Log(!IS_SUCCESS, input, idpAccountId);
            log.error(STEP_1_ERROR, exception);
            completion.complete(null);
        }
        return completion;
    }

    private void validateCreateAccountWithAssumeRoleInput(AiAgentProviderAccountCreateInput input,
            DataFetchingEnvironment dfe) {
        _aiAgentProviderAccountCommonServiceUtil.validateIdpAccountId(dfe);
        _aiAgentProviderAccountCommonServiceUtil.validateProviderAccountName(input.getProviderAccountName(),
                null, dfe);
        validateAssumeRoleInput(input, dfe);
    }

    private void validateAssumeRoleInput(AiAgentProviderAccountCreateInput input, DataFetchingEnvironment dfe) {
        String externalAccountId = input.getExternalAccountId();
        if (StringUtils.isBlank(externalAccountId)) {
           ErrorUtil.addError(dfe, INVALID_EXTERNAL_PROVIDER_ACCOUNT_ID);
           log.warn(INVALID_EXTERNAL_PROVIDER_ACCOUNT_ID.name());
        } else if (!AWS_ACCOUNT_ID_PATTERN.matcher(externalAccountId).matches()) {
            ErrorUtil.addError(dfe, INVALID_AWS_ACCOUNT_ID, externalAccountId);
            log.warn(INVALID_AWS_ACCOUNT_ID.name());
        }
        if (StringUtils.isBlank(input.getRegion())) {
           ErrorUtil.addError(dfe, REGION_REQUIRED_IF_AUTH, AiProviderAuthSchema.AWS);
           log.warn("Region is required for {}", REGION_REQUIRED_IF_AUTH);
        } else if (!_oamSinkArnRetrieverService.getUnmodifiableRegionsSet().contains(input.getRegion().toLowerCase())) {
            ErrorUtil.addError(dfe, UNSUPPORTED_REGION, input.getRegion());
            log.warn(UNSUPPORTED_REGION.name());
        }
    }

    private void createDraftOrFullyProvisionedProviderAccount(
            AiAgentProviderAccountCreateInput input,
            DataFetchingEnvironment dfe, CompletableFuture<AiAgentProviderAccount> completion) {
        String idpAccountId = _aiAgentProviderAccountCommonServiceUtil.getIdpAccountId(dfe);
        log.info(START_PROVISIONING_NEW_CUSTOMER_WITH_ASSUME_ROLE);
        printProvisioningStep1Log(IS_SUCCESS, input, idpAccountId);
        com.boomi.aiagentregistry.entity.AiAgentProviderAccount providerAccount = PROVIDER_MAPPER
                .toAiAgentProviderAccountEntity(input);
        String externalProviderAccountId = input.getExternalAccountId();
        providerAccount.setExternalProviderAccountId(externalProviderAccountId);
        String guid = GuidUtil.createAIAgentProviderGuid();
        providerAccount.setGuid(guid);
        providerAccount.setIdpAccountId(idpAccountId);
        String secretName = _awsAssumeRoleAuthorizationParserStrategy
                .getSecretName(providerAccount.getExternalProviderAccountId());
        providerAccount.setCredentialsKey(secretName);
        _auditUtil.setCreatedAudit(providerAccount, dfe);
        _auditUtil.setUpdatedAudit(providerAccount, dfe);

        Optional<com.boomi.aiagentregistry.entity.AiAgentProviderAccount> previouslyConnectExternalAccount =
                findExternalAccountThatIsConnected(externalProviderAccountId);
        if (previouslyConnectExternalAccount.isPresent()) {
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount previouslyConnectedProviderAccount =
                       previouslyConnectExternalAccount.get();
            log.info(EXTERNAL_ACCOUNT_WAS_PREVIOUSLY_FULLY_PROVISIONED, externalProviderAccountId,
                    previouslyConnectedProviderAccount.getGuid(),
                    previouslyConnectedProviderAccount.getCredentialsKey());
            providerAccount.setProviderAccountStatus(AiAgentProviderAccountStatus.CONNECTED);
            // the External ID secret already exists fot this AWS account and assumes roles should have been created
            // in the customer's AWS account
            // Hence, do the assume roles steps

            // Copy the credentials key in case the logic to get the secret key name changes after the
            // previous account was created
            providerAccount.setCredentialsKey(previouslyConnectedProviderAccount.getCredentialsKey());
            fullyProvisionProviderAccount(providerAccount, dfe, completion, ProvisioningStep.CREATING_DRAFT);
        }
        else {
            Optional<com.boomi.aiagentregistry.entity.AiAgentProviderAccount>
                    previouslyPartiallyProvisionedProviderAccount =
                    _accountRepo.findFirstByExternalProviderAccountIdAndAuthSchemaAndIsDeletedFalse(
                            externalProviderAccountId, AiProviderAuthSchema.AWS_ASSUME_ROLE);
            if (previouslyPartiallyProvisionedProviderAccount.isEmpty()) {
                boolean isSecretCreated = createExternalIdSecret(input, dfe, completion, idpAccountId, secretName);
                if (!isSecretCreated) {
                    return;
                }
            } else {
                // Copy the credentials key in case the logic to get the secret key name changes after the
                // previous account was created
                String secret = previouslyPartiallyProvisionedProviderAccount.get().getCredentialsKey();
                providerAccount.setCredentialsKey(secret);
                log.info(SKIPPING_CREATING_SECRET_FOR, secret, externalProviderAccountId);
            }
            providerAccount.setProviderAccountStatus(AiAgentProviderAccountStatus.PROVISIONING_STEP1_COMPLETE);
            _accountRepo.saveAndFlush(providerAccount);
            completion.complete(mapProviderAccount(providerAccount));
        }
    }

    private static void printProvisioningStep1Log(boolean isSuccess,
            AiAgentProviderAccountCreateInput input,
            String idpAccountId) {
        String message = String.format(STEP_1_LOG_PATTERN,
            input.getExternalAccountId(),
            input.getProviderAccountName(),
            idpAccountId);

        if (isSuccess) {
            log.info(message);
        } else {
            log.error(message);
        }
    }

    private static void printProvisioningStepLog(boolean isSuccess,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount aiAgentProviderAccount,
            String idpAccountId, ProvisioningStep step) {
        String message;
        if (step == ProvisioningStep.CONNECTING) {
            message = String.format(STEP2_LOG_PATTERN, aiAgentProviderAccount.getGuid(),
                    aiAgentProviderAccount.getExternalProviderAccountId(),
                    aiAgentProviderAccount.getProviderAccountName(), idpAccountId);
        }
        else if (step == ProvisioningStep.RECHECK_ASSUME_ROLE) {
                    message = String.format(RECHECK_ASSUME_ROLE_LOG, aiAgentProviderAccount.getGuid(),
                            aiAgentProviderAccount.getExternalProviderAccountId(),
                            aiAgentProviderAccount.getProviderAccountName(), idpAccountId);
                }
        else {
            message = String.format(STEP_1_LOG_PATTERN,
                      aiAgentProviderAccount.getExternalProviderAccountId(),
                      aiAgentProviderAccount.getProviderAccountName(),
                      idpAccountId);
        }

        if (isSuccess) {
            log.info(message);
        } else {
            log.error(message);
        }
    }

    private static void printGetOnboardingTemplateData(boolean isError,
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount providerAccount) {
        String message = String.format("Get onboarding template: External account ID %s, provider account name %s,"
                        + " provider GUID %s, Idp account ID %s",
            providerAccount.getExternalProviderAccountId(),
            providerAccount.getProviderAccountName(),
            providerAccount.getGuid(),
            providerAccount.getIdpAccountId());

        if (isError) {
            log.error(message);
        } else {
            log.info(message);
        }
   }

    // suppress log JsonProcessingException message because it should not log secrets
    @SuppressWarnings("java:S1166")
    // The external ID will be made available to the customer when the Cloudformation query is executed
    // The external ID (similar to MFA code) is used by ACT when assuming role in the customer's aws account
    /**
     * @see #fullyProvisionProviderAccount(com.boomi.aiagentregistry.entity.AiAgentProviderAccount,
     * DataFetchingEnvironment, CompletableFuture, ProvisioningStep) (boolean,
     * AiAgentProviderAccountCreateInput, String)
     */
    private boolean createExternalIdSecret(AiAgentProviderAccountCreateInput input,
            DataFetchingEnvironment dfe, CompletableFuture<AiAgentProviderAccount> completion,
            String idpAccountId, String secretName) {
        AwsCredentials awsCredentials = new AwsCredentials();
        awsCredentials.setExternalId(GuidUtil.createGuid());
        awsCredentials.setAwsRegion(input.getRegion());
        awsCredentials.setAwsAccountId(input.getExternalAccountId());
        String secret;
        try {
          secret = _objectMapper.writeValueAsString(awsCredentials);
        }
        catch (JsonProcessingException e) {
             printProvisioningStep1Log(!IS_SUCCESS, input, idpAccountId);
             log.error("Step1: JsonProcessingException in creating temp aws credentials string");
             ErrorUtil.addError(dfe, SSM_UPDATE_FAILED);
             completion.complete(null);
            return false;
         }

        SecretsManagerResponse createSecret = _secretsManagerService.createSecret(secretName, secret);
        if (createSecret == null) {
            printProvisioningStep1Log(!IS_SUCCESS, input, idpAccountId);
            log.error("Step1: Failed to write temp aws creds to SSM");
            ErrorUtil.addError(dfe, SSM_UPDATE_FAILED);
            completion.complete(null);
            return false;
        }

        log.info(STEP_1_SSM_CREDENTIALS_CREATED);
        return true;
    }

    private CompletableFuture<String> getExternalId(
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount dbProviderAccount,
            AiProviderAuthSchema authSchema,
            String providerAccountGuid,
            DataFetchingEnvironment dfe) {

        String secretName = dbProviderAccount.getCredentialsKey();

        return _secretsManagerService.getSecret(secretName)
            .single()
            .flatMap(credentialsJson -> {
                if (StringUtils.isBlank(credentialsJson)) {
                    log.warn("No credentialsJson found for provider account {}", providerAccountGuid);
                    ErrorUtil.addError(dfe, INVALID_CONNECTION_DETAILS);
                    return Mono.empty();
                }

                return _authorizationParsingService.createAuthorization(credentialsJson, authSchema)
                    .cast(AwsCredentials.class)
                    .map(AwsCredentials::getExternalId);
            })
            .doOnError(error -> {
                log.warn("Error processing credentials for provider account {}: ", providerAccountGuid, error);
                ErrorUtil.addError(dfe, EXTERNAL_ID_NOT_FOUND);
            })
            .onErrorResume(e -> Mono.empty())
            .publishOn(Schedulers.boundedElastic())
            .toFuture();
    }

    private static @NotNull Set<AiAgentProviderAccountStatus> getConnectedAccountStatuses() {
        Set<AiAgentProviderAccountStatus> excludedStatuses = EnumSet.of(
            AiAgentProviderAccountStatus.PROVISIONING_STEP1_COMPLETE
        );
        return Arrays.
             stream(AiAgentProviderAccountStatus.values())
            .filter(status -> !excludedStatuses.contains(status))
            .collect(Collectors.toUnmodifiableSet());
    }

     private CompletableFuture<com.boomi.aiagentregistry.entity.AiAgentProviderAccount> recheckAssumeRole(
            com.boomi.aiagentregistry.entity.AiAgentProviderAccount providerAccount,
            DataFetchingEnvironment dfe) {

         if (providerAccount.getAuthSchema() != AiProviderAuthSchema.AWS_ASSUME_ROLE) {
             return CompletableFuture.completedFuture(providerAccount);
         }
         String idpAccountId = _aiAgentProviderAccountCommonServiceUtil.getIdpAccountId(dfe);
         if (providerAccount.getProviderAccountStatus() != AiAgentProviderAccountStatus.DISCONNECTED) {
             log.info(SKIP_ASSUME_ROLE_RECHECK, providerAccount.getProviderAccountStatus());
             printProvisioningStepLog(IS_SUCCESS, providerAccount, idpAccountId,
             ProvisioningStep.RECHECK_ASSUME_ROLE);
             return CompletableFuture.completedFuture(providerAccount);
         }

        CompletableFuture<com.boomi.aiagentregistry.entity.AiAgentProviderAccount> completion =
                new CompletableFuture<>();
        _secretsManagerService.getSecret(providerAccount.getCredentialsKey())
                .single()
                .doOnSuccess(credentials -> {
                    if (StringUtils.isBlank(credentials)) {
                        log.warn("Edit account - No credentials found for account {}. Is Step1 completed?",
                                providerAccount.getGuid());
                        printProvisioningStepLog(!IS_SUCCESS, providerAccount, idpAccountId,
                                ProvisioningStep.RECHECK_ASSUME_ROLE);
                        ErrorUtil.addError(dfe, AUTHENTICATED_ACCOUNT_ID_MISSING);
                        completion.complete(null);
                        return;
                    }
                    validateConnectionAndSaveTempAccessToken(providerAccount, dfe, credentials,
                            idpAccountId, ProvisioningStep.RECHECK_ASSUME_ROLE)
                        .thenCompose(validatedCredentials -> {
                            if (validatedCredentials != null && !ErrorUtil.hasErrors(dfe)) {
                                providerAccount.setProviderAccountStatus(AiAgentProviderAccountStatus.CONNECTED);
                                return CompletableFuture.completedFuture(providerAccount);
                            } else {
                                return CompletableFuture.completedFuture(null);
                            }
                        })
                        .thenAccept(completion::complete)
                        .exceptionally(throwable -> {
                            printProvisioningStepLog(!IS_SUCCESS, providerAccount, idpAccountId,
                                    ProvisioningStep.RECHECK_ASSUME_ROLE);
                            log.error("Edit account - Error during healthcheck ", throwable);
                            ErrorUtil.addError(dfe, INVALID_CONNECTION_DETAILS);
                            completion.complete(null);
                            return null;
                        });
                })
                .doOnError(error -> {
                    log.warn("Edit account - Outer Error processing credentials for account {}  ",
                            providerAccount.getGuid(), error);
                    printProvisioningStepLog(!IS_SUCCESS, providerAccount, idpAccountId,
                            ProvisioningStep.RECHECK_ASSUME_ROLE);
                    ErrorUtil.addError(dfe, INVALID_CONNECTION_DETAILS);
                    completion.complete(null);
                })
                .subscribe();

        return completion;
    }

    private record ProvisioningResult(
        boolean success,
        com.boomi.aiagentregistry.entity.AiAgentProviderAccount aiAgentProviderAccount
    ) {}

}
