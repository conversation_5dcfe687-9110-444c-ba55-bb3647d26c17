// Copyright (c) 2025 Boomi, Inc.
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.graphql.server.schema.types.AiAgentLlmsQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentLlmsQueryResponse;

import org.springframework.stereotype.Service;

import java.util.concurrent.CompletionStage;

@Slf4j
@Service
@RequiredArgsConstructor
public class AiAgentLlmListingServiceImpl implements IAiAgentLlmListingService{

    @Override
    public CompletionStage<AiAgentLlmsQueryResponse> getAiAgentLlms(AiAgentLlmsQueryInput input,
            DataFetchingEnvironment dfe) {
        return null;
    }
}