// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.sqs;

import com.boomi.aiagentregistry.exception.ScheduleException;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;

@Slf4j
@Service
public class AwsSqsServiceImpl implements AwsSqsService {
    @Value("${sync.queue.region}")
    private String _queueRegion;

    private  SqsClient  _sqsClient;

    @PostConstruct
    private void init() {
        _sqsClient = SqsClient.builder()
                .region(Region.of(_queueRegion))
                .build();
    }

    @Override
    public void sendMessage(SendMessageRequest messageRequest) {
        try {
            log.info("Sending account to sync queue");
            _sqsClient.sendMessage(messageRequest);
            log.info("Finished sending account to sync  queue");

        } catch (Exception e) {
            log.error("Error sending message to sync queue", e);
            throw new ScheduleException("Failed to send messages to sync queue", e);
        }
    }


}
