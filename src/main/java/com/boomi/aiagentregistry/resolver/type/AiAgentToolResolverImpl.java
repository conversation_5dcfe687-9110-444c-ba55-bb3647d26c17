// Copyright (c) 2024 Boom<PERSON>, LP

package com.boomi.aiagentregistry.resolver.type;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.aiagentregistry.service.AiAgentToolService;
import com.boomi.graphql.server.schema.resolvers.AiAgentToolResolver;
import com.boomi.graphql.server.schema.types.AiAgentTool;
import com.boomi.graphql.server.schema.types.AiAgentToolResource;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletionStage;

/**
 * <AUTHOR>
 */
@Component
public class AiAgentToolResolverImpl implements AiAgentToolResolver {

    private final AiAgentToolService _aiAgentToolService;

    public AiAgentToolResolverImpl(AiAgentToolService aiAgentToolService) {
        _aiAgentToolService = aiAgentToolService;
    }

    @Override
    public CompletionStage<List<AiAgentToolResource>> resources(
            AiAgentTool aiAgentTool, DataFetchingEnvironment dfe) {
        return _aiAgentToolService.getAiAgentToolResources(aiAgentTool, dfe);
    }

}
