// Copyright (c) 2024 Boomi, LP.
package com.boomi.aiagentregistry.resolver.type;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.aiagentregistry.entity.AiAgentTagAssociation;
import com.boomi.aiagentregistry.repo.AiAgentRepository;
import com.boomi.aiagentregistry.repo.AiAgentTagAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentTagRepository;
import com.boomi.graphql.server.schema.resolvers.AiAgentResolver;
import com.boomi.graphql.server.schema.types.AiAgent;
import com.boomi.graphql.server.schema.types.AiAgentTag;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

import static com.boomi.aiagentregistry.util.ValidationUtil.isInputFieldPresent;
import static com.boomi.graphql.server.schema.types.AiAgent.PROP_TAGS;

@Component
public class AiAgentResolverImpl implements AiAgentResolver {

    private final AiAgentRepository _aiAgentRepository;

    private final AiAgentTagRepository _aiAgentTagRepository;

    private final AiAgentTagAssociationRepository _aiAgentTagAssociationRepository;

    public AiAgentResolverImpl(AiAgentRepository aiAgentRepository,
            AiAgentTagRepository aiAgentTagRepository,
            AiAgentTagAssociationRepository aiAgentTagAssociationRepository) {
        _aiAgentRepository = aiAgentRepository;
        _aiAgentTagRepository = aiAgentTagRepository;
        _aiAgentTagAssociationRepository = aiAgentTagAssociationRepository;
    }

    @Override
    @Transactional
    public CompletionStage<List<AiAgentTag>> tags(AiAgent aiAgent, DataFetchingEnvironment dfe) {

        com.boomi.aiagentregistry.entity.AiAgent aiAgentFromDB =
                _aiAgentRepository.findByGuid(aiAgent.getId()).orElse(null);
        if(aiAgentFromDB == null){
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

        List<AiAgentTagAssociation> aiAgentTagAssociations =
                _aiAgentTagAssociationRepository.findByRelatedEntityUid(aiAgentFromDB.getUid());
        if(aiAgentTagAssociations == null || aiAgentTagAssociations.isEmpty()){
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

        List<Integer> tagIds = aiAgentTagAssociations.stream().map(AiAgentTagAssociation::getTagUid).toList();

        List<com.boomi.aiagentregistry.entity.AiAgentTag> aiAgentTags = _aiAgentTagRepository.findAllById(
                tagIds);

        if (isInputFieldPresent(dfe, PROP_TAGS)) {
            List<AiAgentTag> graphqlAiAgentTags = aiAgentTags.stream()
                    .map(AiAgentResolverImpl::getAiAgentTag)
                    .toList();

            return CompletableFuture.completedFuture(graphqlAiAgentTags);
        }

        return CompletableFuture.completedFuture(Collections.emptyList());
    }

    private static AiAgentTag getAiAgentTag(com.boomi.aiagentregistry.entity.AiAgentTag aiAgentTag) {
        AiAgentTag graphqlAiAgentTag = new AiAgentTag();
        graphqlAiAgentTag.withId(aiAgentTag.getGuid());
        graphqlAiAgentTag.withKey(aiAgentTag.getKey());
        return graphqlAiAgentTag;
    }
}
