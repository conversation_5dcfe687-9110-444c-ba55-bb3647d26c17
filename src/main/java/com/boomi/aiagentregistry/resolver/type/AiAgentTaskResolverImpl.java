// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.resolver.type;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.aiagentregistry.service.AiAgentTaskToolService;
import com.boomi.graphql.server.schema.resolvers.AiAgentTaskResolver;
import com.boomi.graphql.server.schema.types.AiAgentTask;
import com.boomi.graphql.server.schema.types.AiAgentTool;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletionStage;

/**
 * <AUTHOR> <PERSON>pute
 */
@Component
public class AiAgentTaskResolverImpl implements AiAgentTaskResolver {

    private AiAgentTaskToolService _aiAgentTaskToolService;

    public AiAgentTaskResolverImpl(AiAgentTaskToolService aiAgentTaskToolService) {
        _aiAgentTaskToolService = aiAgentTaskToolService;
    }

    @Override
    public CompletionStage<List<String>> instructions(AiAgentTask aiAgentTask, DataFetchingEnvironment dfe) {
        return _aiAgentTaskToolService.getInstructions(aiAgentTask, dfe);
    }

    @Override
    public CompletionStage<List<AiAgentTool>> tools(AiAgentTask aiAgentTask, DataFetchingEnvironment dfe) {
        return _aiAgentTaskToolService.getAiAgentTaskTools(aiAgentTask, dfe);
    }
}
