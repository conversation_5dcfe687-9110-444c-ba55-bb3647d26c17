// Copyright (c) 2024 Boom<PERSON>, LP.
package com.boomi.aiagentregistry.resolver.type;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.aiagentregistry.service.AiAgentVersionService;
import com.boomi.graphql.server.schema.resolvers.AiAgentVersionResolver;
import com.boomi.graphql.server.schema.types.AiAgentAlias;
import com.boomi.graphql.server.schema.types.AiAgentGuardrail;
import com.boomi.graphql.server.schema.types.AiAgentLlm;
import com.boomi.graphql.server.schema.types.AiAgentTag;
import com.boomi.graphql.server.schema.types.AiAgentTask;
import com.boomi.graphql.server.schema.types.AiAgentTool;
import com.boomi.graphql.server.schema.types.AiAgentVersion;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncData;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

@Component
public class AiAgentVersionResolverImpl implements AiAgentVersionResolver {

    private final AiAgentVersionService _aiAgentVersionService;

    public AiAgentVersionResolverImpl(AiAgentVersionService aiAgentVersionService) {
        _aiAgentVersionService = aiAgentVersionService;
    }

    @Override
    public CompletionStage<List<String>> instructions(AiAgentVersion aiAgentVersion, DataFetchingEnvironment dfe) {
        return _aiAgentVersionService.getVersionInstructions(aiAgentVersion, dfe);
    }

    @Override
    @Transactional(readOnly = true)
    public CompletionStage<List<AiAgentAlias>> agentAliases(AiAgentVersion aiAgentVersion,
            DataFetchingEnvironment dfe) {
        if (aiAgentVersion.getAgentAliases() != null && !aiAgentVersion.getAgentAliases().isEmpty()) {
            return CompletableFuture.completedFuture(aiAgentVersion.getAgentAliases());
        }
        return _aiAgentVersionService.getAiAgentAliases(aiAgentVersion, dfe);
    }

    @Transactional
    @Override
    public CompletionStage<List<AiAgentGuardrail>> agentGuardrails(AiAgentVersion aiAgentVersion,
            DataFetchingEnvironment dfe) {
        return _aiAgentVersionService.getAiAgentGuardrails(aiAgentVersion, dfe);
    }

    @Override
    public CompletionStage<List<AiAgentTag>> tags(AiAgentVersion aiAgentVersion, DataFetchingEnvironment dfe) {
        return _aiAgentVersionService.getAiAgentTags(aiAgentVersion, dfe);
    }

    @Override
    public CompletionStage<List<AiAgentLlm>> llms(AiAgentVersion aiAgentVersion, DataFetchingEnvironment dfe) {
        return _aiAgentVersionService.getAiAgentLlms(aiAgentVersion, dfe);
    }

    @Override
    public CompletionStage<AiRegistryEntitySyncData> syncData(AiAgentVersion aiAgentVersion,
            DataFetchingEnvironment dfe) {
        return _aiAgentVersionService.syncData(aiAgentVersion, dfe);
    }

    @Override
    @Transactional
    public CompletionStage<List<AiAgentTool>> agentTools(AiAgentVersion aiAgentVersion, DataFetchingEnvironment dfe) {
        return _aiAgentVersionService.getAiAgentTools(aiAgentVersion, dfe);
    }

    @Override
    @Transactional
    public CompletionStage<List<AiAgentTask>> agentTasks(AiAgentVersion aiAgentVersion, DataFetchingEnvironment dfe) {
        return _aiAgentVersionService.getAiAgentTasks(aiAgentVersion, dfe);
    }
}
