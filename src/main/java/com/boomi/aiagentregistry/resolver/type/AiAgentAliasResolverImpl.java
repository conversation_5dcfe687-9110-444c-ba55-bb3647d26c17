// Copyright (c) 2020 Boomi, Inc.
package com.boomi.aiagentregistry.resolver.type;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.aiagentregistry.service.AiAgentAliasService;
import com.boomi.graphql.server.schema.resolvers.AiAgentAliasResolver;
import com.boomi.graphql.server.schema.types.AiAgentAlias;
import com.boomi.graphql.server.schema.types.AiAgentTag;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncData;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

@Component
public class AiAgentAliasResolverImpl implements AiAgentAliasResolver {

    private final AiAgentAliasService _aiAgentAliasService;

    public AiAgentAliasResolverImpl(AiAgentAliasService aiAgentAliasService) {
        _aiAgentAliasService = aiAgentAliasService;
    }

    @Override
    public CompletionStage<List<AiAgentTag>> tags(AiAgentAlias aiAgentAlias, DataFetchingEnvironment dfe) {
        if (aiAgentAlias != null) {
            final List<AiAgentTag> tagsForAlias = _aiAgentAliasService.getTagsForAlias(aiAgentAlias, dfe);
            return CompletableFuture.completedFuture(tagsForAlias);
        }
        return null;
    }

    @Override
    public CompletionStage<AiRegistryEntitySyncData> syncData(AiAgentAlias aiAgentAlias,
            DataFetchingEnvironment dfe) {
        if (aiAgentAlias != null) {
                    return _aiAgentAliasService.getLatestSyncData(aiAgentAlias, dfe);
        }
        return null;
    }
}
