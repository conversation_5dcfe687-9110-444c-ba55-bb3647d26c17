// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.resolver.mutation;

import graphql.schema.DataFetchingEnvironment;
import jakarta.validation.constraints.NotNull;
import com.boomi.graphql.server.schema.resolvers.AiAgentGuardrailMutationResolver;
import com.boomi.graphql.server.schema.types.AiAgentGuardrail;
import com.boomi.graphql.server.schema.types.AiAgentGuardrailCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentGuardrailUpdateInput;
import com.boomi.graphql.server.schema.types.AiEntityMutationReferenceIdInput;
import com.boomi.graphql.server.schema.types.AiRegistryEntityTypeInput;
import com.boomi.graphql.server.schema.types.GuardrailWithoutIdInput;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletionStage;

@Component
public class AiAgentGuardrailMutationResolverImpl implements AiAgentGuardrailMutationResolver {

    @Override
    public CompletionStage<AiAgentGuardrail> aiAgentGuardrailCreate(AiAgentGuardrailCreateInput input,
                                                                    DataFetchingEnvironment dfe) {
        return null;
    }

    @Override
    public CompletionStage<AiAgentGuardrail> aiAgentGuardrailUpdate( AiAgentGuardrailUpdateInput input,
                                                                     DataFetchingEnvironment dfe) {
        return null;
    }

    @Override
    public CompletionStage<String> aiAgentGuardrailDelete(
            @NotNull AiEntityMutationReferenceIdInput aiEntityMutationReferenceIdInput,
            DataFetchingEnvironment dataFetchingEnvironment) {
        return null;
    }

    @Override
    public CompletionStage<String> attachGuardrailToAiEntity(
            @NotNull AiEntityMutationReferenceIdInput aiEntityMutationReferenceIdInput,
            @NotNull AiEntityMutationReferenceIdInput aiEntityMutationReferenceIdInput1,
            @NotNull AiRegistryEntityTypeInput aiAgentEntityTypeInput,
            DataFetchingEnvironment dataFetchingEnvironment) {
        return null;
    }

    @Override
    public CompletionStage<String> detachGuardrailFromAiEntity(
            @NotNull AiEntityMutationReferenceIdInput aiEntityMutationReferenceIdInput,
            @NotNull AiEntityMutationReferenceIdInput aiEntityMutationReferenceIdInput1,
            @NotNull AiRegistryEntityTypeInput aiAgentEntityTypeInput,
            DataFetchingEnvironment dataFetchingEnvironment) {
        return null;
    }

    @Override
    public CompletionStage<String> addGuardrailsToAiEntity(
            @NotNull AiEntityMutationReferenceIdInput aiEntityMutationReferenceIdInput,
            @NotNull AiRegistryEntityTypeInput aiAgentEntityTypeInput,
            @NotNull List<GuardrailWithoutIdInput> list,
            DataFetchingEnvironment dataFetchingEnvironment) {
        return null;
    }
}
