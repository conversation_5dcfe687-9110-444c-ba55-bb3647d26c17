// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.resolver.mutation;

import graphql.schema.DataFetchingEnvironment;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.service.AiAgentVersionService;
import com.boomi.aiagentregistry.service.IAiAgentService;
import com.boomi.graphql.server.schema.resolvers.AiAgentMutationResolver;
import com.boomi.graphql.server.schema.types.AiAgent;
import com.boomi.graphql.server.schema.types.AiAgentCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentUpdateInput;
import com.boomi.graphql.server.schema.types.AiAgentVersion;
import com.boomi.graphql.server.schema.types.AiAgentVersionEnableInput;
import com.boomi.graphql.server.schema.types.AiAgentVersionTrustLevelAddInput;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.CompletionStage;

@Slf4j
@Component
public class AiAgentMutationResolverImpl implements AiAgentMutationResolver {

    private final IAiAgentService _aiAgentService;
    private final AiAgentVersionService _aiAgentVersionService;

    public AiAgentMutationResolverImpl(IAiAgentService aiAgentService, AiAgentVersionService aiAgentVersionService) {
        _aiAgentService = aiAgentService;
        _aiAgentVersionService = aiAgentVersionService;
    }

    @Transactional
    @Override public CompletionStage<AiAgent> aiAgentCreate(
            @NotNull AiAgentCreateInput input, DataFetchingEnvironment dfe){
        return _aiAgentService.createAIAgent(input, dfe);
    }

    @Override
    public CompletionStage<AiAgent> aiAgentUpdate(
            @NotNull AiAgentUpdateInput input, DataFetchingEnvironment dfe){
        return _aiAgentService.updateAIAgent(input, dfe);
    }

    @Override
    public CompletionStage<AiAgentVersion> aiAgentVersionTrustLevelAdd(
            @NotNull AiAgentVersionTrustLevelAddInput input, DataFetchingEnvironment dfe) {
        return _aiAgentVersionService.aiAgentVersionTrustLevelAdd(input, dfe);
    }

    @Override
    public CompletionStage<AiAgentVersion> aiAgentVersionEnable(@NotNull AiAgentVersionEnableInput input,
            DataFetchingEnvironment dfe) {
        return _aiAgentVersionService.aiAgentVersionEnable(input, dfe);
    }

    @Override
    public CompletionStage<Boolean> aiAgentDelete(@NotNull String id, DataFetchingEnvironment dfe) {
        return _aiAgentService.deleteAIAgent(id, dfe);
    }
}
