// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.resolver.mutation;

import graphql.schema.DataFetchingEnvironment;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.aop.IdpAccountFilter;
import com.boomi.aiagentregistry.service.AiAgentProviderAccountService;
import com.boomi.aiagentregistry.util.AiAgentProviderAccountCommonServiceUtil;
import com.boomi.graphql.server.schema.resolvers.AiAgentProviderAccountMutationResolver;
import com.boomi.graphql.server.schema.types.AiAgentCloudProviderAccountConfirmInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccount;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountUpdateInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus;
import com.boomi.graphql.server.servlet.ErrorUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_ACCOUNT_NOT_FOUND;
import static com.boomi.graphql.server.servlet.ErrorUtil.GeneralErrorCode.SYSTEM_ERROR;

@Component
@Slf4j
public class AiAgentProviderAccountMutationResolverImpl implements AiAgentProviderAccountMutationResolver {

    @Autowired
    private Map<AiAgentProviderType, AiAgentProviderAccountService> _getBeanMappedByAiAgentProviderType;

    @Autowired
    private AiAgentProviderAccountCommonServiceUtil _aiAgentProviderAccountCommonServiceUtil;

    @Override
    @Transactional
    public CompletionStage<AiAgentProviderAccount> aiAgentProviderAccountCreate(
            @NotNull AiAgentProviderAccountCreateInput input, DataFetchingEnvironment dfe) {
        log.info("Inside AiAgentProviderAccountMutationResolverImpl -> aiAgentProviderAccountCreate");
        CompletableFuture<AiAgentProviderAccount> completion =
                new CompletableFuture<>();

        if (!_aiAgentProviderAccountCommonServiceUtil.isValidProviderType(input.getProviderType(), dfe)) {
            completion.complete(null);
            return completion;
        }
        // it will decide which implementation need to pic
        AiAgentProviderAccountService accountService = _getBeanMappedByAiAgentProviderType.get(
                input.getProviderType());
        doLogOfServiceType(accountService.getClass().getName());

        return accountService.createAiAgentProviderAccount(input, dfe);
    }

    @Transactional
    @Override
    @IdpAccountFilter
    public CompletionStage<AiAgentProviderAccount> aiAgentProviderAccountUpdate(
            @NotNull AiAgentProviderAccountUpdateInput input, DataFetchingEnvironment dfe) {
        CompletableFuture<AiAgentProviderAccount> completion =
                new CompletableFuture<>();
        try {

            final com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount =
                    _aiAgentProviderAccountCommonServiceUtil.getExistingProviderAccountForId(input.getId());

            if (existingProviderAccount == null) {
                ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.ACCOUNT_NOT_FOUND, input.getId());
                log.warn(ERROR_ACCOUNT_NOT_FOUND, input.getId());
                completion.complete(null);
                return completion;
            }

            AiAgentProviderAccountService accountService =
                    _getBeanMappedByAiAgentProviderType.get(existingProviderAccount.getProviderType());

            doLogOfServiceType(accountService.getClass().getName());

            log.info("Updating the provider details for the provider {} with GUID {}.",
                    existingProviderAccount.getProviderType().name(), existingProviderAccount.getGuid());

            return accountService.updateAiAgentProviderAccount(input, existingProviderAccount, dfe);
        } catch (Exception e) {
            ErrorUtil.addError(dfe, SYSTEM_ERROR);
            log.error("Error while Updating the Providers details for Guid {}", input.getId(), e);
            completion.complete(null);
            return completion;
        }
    }

    @Override
    public CompletionStage<AiRegistryEntitySyncStatus> aiAgentProviderAccountSync(String id,
            DataFetchingEnvironment dfe) {

        // to get default service implementation
        AiAgentProviderAccountService accountService =
                _getBeanMappedByAiAgentProviderType.get(null);
        doLogOfServiceType(accountService.getClass().getName());

        return accountService.syncAiAgentProviderAccount(id, dfe);
    }

    @Transactional
    @IdpAccountFilter
    @Override
    public CompletionStage<AiAgentProviderAccount> aiAgentCloudProviderAccountConfirm(
            @NotNull AiAgentCloudProviderAccountConfirmInput input,
            DataFetchingEnvironment dfe) {
        CompletableFuture<AiAgentProviderAccount> completion =
                        new CompletableFuture<>();
        try {

            final com.boomi.aiagentregistry.entity.AiAgentProviderAccount existingProviderAccount =
                    _aiAgentProviderAccountCommonServiceUtil.getExistingProviderAccountForId(input.getId());

            if (existingProviderAccount == null) {
                ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.ACCOUNT_NOT_FOUND, input.getId());
                log.warn(ERROR_ACCOUNT_NOT_FOUND, input.getId());
                completion.complete(null);
                return completion;
            }

            AiAgentProviderAccountService accountService =
                    _getBeanMappedByAiAgentProviderType.get(existingProviderAccount.getProviderType());

            doLogOfServiceType(accountService.getClass().getName());

            log.info("Processing infrastructure confirmation request for provider {} with GUID {}.",
                    existingProviderAccount.getProviderType().name(), existingProviderAccount.getGuid());

            return accountService.aiAgentCloudProviderAccountConfirm(input, existingProviderAccount, dfe);
        } catch (Exception e) {
            ErrorUtil.addError(dfe, SYSTEM_ERROR);
            log.error("Error while Updating the Providers details for Guid {}", input.getId(), e);
            completion.complete(null);
            return completion;
        }
    }

    @Transactional
    @Override
    public CompletionStage<Boolean> aiAgentProviderAccountDelete(@NotNull String id, DataFetchingEnvironment dfe) {

        AiAgentProviderAccountService accountService =
                _getBeanMappedByAiAgentProviderType.get(null);

        doLogOfServiceType(accountService.getClass().getName());
        return accountService.deleteProviderAccount(id, dfe, _getBeanMappedByAiAgentProviderType);
    }

    private void doLogOfServiceType(String serviceClassName) {
        log.info("AiAgentProviderAccountService type {}", serviceClassName);
    }


}
