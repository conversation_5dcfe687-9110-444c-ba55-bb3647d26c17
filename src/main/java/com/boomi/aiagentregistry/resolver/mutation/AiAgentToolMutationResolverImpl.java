// Copyright (c) 2024 Boom<PERSON>, LP

package com.boomi.aiagentregistry.resolver.mutation;

import graphql.schema.DataFetchingEnvironment;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.service.AiAgentToolService;
import com.boomi.graphql.server.schema.resolvers.AiAgentToolMutationResolver;
import com.boomi.graphql.server.schema.types.AiAgentTool;
import com.boomi.graphql.server.schema.types.AiAgentToolCreateInput;
import com.boomi.graphql.server.schema.types.AiAgentToolUpdateInput;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.CompletionStage;

@Component
@Slf4j
@RequiredArgsConstructor
public class AiAgentToolMutationResolverImpl implements AiAgentToolMutationResolver {

    private final AiAgentToolService _aiAgentToolService;

    @Override
    @Transactional
    public CompletionStage<AiAgentTool> aiAgentToolCreate(@NotNull AiAgentToolCreateInput input,
            DataFetchingEnvironment dfe) {

        return _aiAgentToolService.createAIAgentTool(input, dfe);
    }

    @Override
    public CompletionStage<AiAgentTool> aiAgentToolUpdate(@NotNull AiAgentToolUpdateInput input,
            DataFetchingEnvironment dfe) {
        return _aiAgentToolService.updateAIAgentTool(input, dfe);
    }
}
