// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.resolver.mutation;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.aiagentregistry.service.IAiAgentTagService;
import com.boomi.graphql.server.schema.resolvers.AiAgentTagAddMutationResolver;
import com.boomi.graphql.server.schema.types.AiAgentTagMutationInput;
import com.boomi.graphql.server.schema.types.AiAgentTagMutationOutput;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.CompletionStage;

@Component
public class AiAgentTagAddMutationResolverImpl implements AiAgentTagAddMutationResolver {

    IAiAgentTagService _aiAgentTagService;

    public AiAgentTagAddMutationResolverImpl(IAiAgentTagService aiAgentTagService) {
        _aiAgentTagService = aiAgentTagService;
    }

    @Override
    @Transactional
    public CompletionStage<AiAgentTagMutationOutput> aiAgentAddTags(AiAgentTagMutationInput input,
            DataFetchingEnvironment dfe) {
        return _aiAgentTagService.aiAgentAddTags(input,dfe);
    }
}
