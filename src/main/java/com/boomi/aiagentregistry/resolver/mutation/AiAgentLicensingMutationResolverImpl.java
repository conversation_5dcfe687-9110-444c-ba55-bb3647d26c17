// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.resolver.mutation;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.aiagentregistry.service.IAiAgentLicensingService;
import com.boomi.graphql.server.schema.resolvers.AiAgentLicensingMutationResolver;
import com.boomi.graphql.server.schema.types.AiAgentRegistryLicense;
import com.boomi.graphql.server.schema.types.AiAgentRegistryLicenseIdpAccountAssociation;
import com.boomi.graphql.server.schema.types.AiRegistryLicenseLimitInput;
import com.boomi.graphql.server.schema.types.IdpAccountTierLimitInput;

import org.springframework.stereotype.Component;

import java.util.concurrent.CompletionStage;

@Component
public class AiAgentLicensingMutationResolverImpl implements AiAgentLicensingMutationResolver {

    IAiAgentLicensingService _iAiAgentLicensingService;

    public AiAgentLicensingMutationResolverImpl(IAiAgentLicensingService iAiAgentLicensingService) {
        _iAiAgentLicensingService = iAiAgentLicensingService;
    }

    @Override
    public CompletionStage<AiAgentRegistryLicense> aiRegistryLicenseLimitUpdate(
            AiRegistryLicenseLimitInput input, DataFetchingEnvironment dfe) {
        return _iAiAgentLicensingService.updateAiRegistryLicenseLimit(input,dfe);
    }

    @Override
    public CompletionStage<AiAgentRegistryLicenseIdpAccountAssociation> idpAccountTierLimitUpdate(
            IdpAccountTierLimitInput input, DataFetchingEnvironment dfe) {
        return _iAiAgentLicensingService.updateIdpAccountTierLimit(input,dfe);
    }
}
