// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.resolver.query;

import graphql.schema.DataFetchingEnvironment;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.service.AiAgentProviderAccountService;
import com.boomi.graphql.server.schema.resolvers.AiAgentProviderAccountQueryResolver;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccount;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountsQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountsQueryResponse;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.concurrent.CompletionStage;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class AiAgentProviderAccountQueryResolverImpl implements AiAgentProviderAccountQueryResolver {

    @Autowired
    private Map<AiAgentProviderType, AiAgentProviderAccountService> _getBeanMappedByAiAgentProviderType;

    @Override
    public CompletionStage<AiAgentProviderAccountsQueryResponse> aiAgentProviderAccounts(
            AiAgentProviderAccountsQueryInput aiAgentProviderAccountsQueryInput,
            DataFetchingEnvironment dataFetchingEnvironment) {

        // to get default service implementation
        AiAgentProviderAccountService accountService =
                _getBeanMappedByAiAgentProviderType.get(null);

        doLogOfServiceType(accountService.getClass().getName());

        return accountService.getAiAgentProviders(aiAgentProviderAccountsQueryInput, dataFetchingEnvironment);
    }

    @Transactional
    @Override
    public CompletionStage<AiAgentProviderAccount> aiAgentProviderAccount(@NotNull String id,
            DataFetchingEnvironment dfe) {
        // to get default service implementation
        AiAgentProviderAccountService accountService =
                _getBeanMappedByAiAgentProviderType.get(null);

        doLogOfServiceType(accountService.getClass().getName());

        return accountService.getAiAgentProvider(id, dfe);
    }

    private void doLogOfServiceType(String serviceClassName) {
        log.info("AiAgentProviderAccountService type {}", serviceClassName);
    }
}
