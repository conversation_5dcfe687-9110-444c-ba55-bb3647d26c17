// Copyright (c) 2024 Boom<PERSON>, LP.
package com.boomi.aiagentregistry.resolver.query;

import graphql.schema.DataFetchingEnvironment;
import jakarta.validation.constraints.NotNull;
import com.boomi.aiagentregistry.service.IAiAgentService;
import com.boomi.graphql.server.schema.resolvers.AiAgentQueryResolver;
import com.boomi.graphql.server.schema.types.AiAgent;
import com.boomi.graphql.server.schema.types.AiAgentsQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentsQueryResponse;

import org.springframework.stereotype.Component;

import java.util.concurrent.CompletionStage;

@Component
public class AiAgentQueryResolverImpl implements AiAgentQueryResolver {

    private final IAiAgentService _aiAgentService;

    public AiAgentQueryResolverImpl(IAiAgentService aiAgentService) {
        _aiAgentService = aiAgentService;
    }

    @Override
    public CompletionStage<AiAgentsQueryResponse> aiAgents(AiAgentsQueryInput input, DataFetchingEnvironment dfe) {
        return _aiAgentService.getAiAgents(input, dfe);
    }

    @Override
    public CompletionStage<AiAgent> aiAgentByVersionId(@NotNull String aiAgentVersionId,
            DataFetchingEnvironment dfe) {
        return _aiAgentService.getAiAgentByVersionId(aiAgentVersionId, dfe);
    }

    @Override
    public CompletionStage<AiAgent> aiAgentByAliasId(@NotNull String aiAgentAliasId,
            DataFetchingEnvironment dfe) {
        return _aiAgentService.getAiAgentByAliasId(aiAgentAliasId, dfe);
    }
}
