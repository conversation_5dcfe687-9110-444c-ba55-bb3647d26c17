// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.resolver.query;

import graphql.schema.DataFetchingEnvironment;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.boomi.aiagentregistry.service.metrics.AiAgentRegistryMetricsService;
import com.boomi.graphql.server.schema.resolvers.AiAgentRegistryMetricsQueryResolver;
import com.boomi.graphql.server.schema.types.AiAgentRegistryAgentMetric;
import com.boomi.graphql.server.schema.types.AiAgentRegistryAggregateMetrics;
import com.boomi.graphql.server.schema.types.AiAgentRegistryGraphMetrics;
import com.boomi.graphql.server.schema.types.AiAgentRegistryMetricsInput;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletionStage;

@Component
@RequiredArgsConstructor
public class AiAgentRegistryGraphMetricsQueryResolverImpl implements AiAgentRegistryMetricsQueryResolver {
    private final AiAgentRegistryMetricsService _aiAgentRegistryMetricsService;

    @Override
    public CompletionStage<List<AiAgentRegistryGraphMetrics>> aiAgentRegistryGraphMetrics(
            @NotNull AiAgentRegistryMetricsInput input, DataFetchingEnvironment dfe) {
            return _aiAgentRegistryMetricsService.getGraphMetrics(input, dfe);
    }

    @Override
    public CompletionStage<AiAgentRegistryAggregateMetrics> aiAgentRegistryAggregateMetrics(
            @NotNull AiAgentRegistryMetricsInput input, DataFetchingEnvironment dfe) {
        return _aiAgentRegistryMetricsService.getAggregateMetrics(input, dfe);
    }

    @Override
    public CompletionStage<List<AiAgentRegistryAgentMetric>> aiAgentRegistryInvocationsMetrics(
            @NotNull AiAgentRegistryMetricsInput input, DataFetchingEnvironment dfe) {
        return _aiAgentRegistryMetricsService.getInvocationsMetrics(input, dfe);
    }
}
