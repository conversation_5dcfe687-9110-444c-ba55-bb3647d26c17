// Copyright (c) 2025 Boomi, Inc.
package com.boomi.aiagentregistry.resolver.query;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.aiagentregistry.service.AiAgentVersionService;
import com.boomi.graphql.server.schema.resolvers.AiAgentTrustLevelQueryResolver;
import com.boomi.graphql.server.schema.types.AiAgentTrustLevelQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentTrustLevelQueryResponse;

import org.springframework.stereotype.Component;

import java.util.concurrent.CompletionStage;

@Component
public class AiAgentTrustLevelQueryResolverImpl implements AiAgentTrustLevelQueryResolver {

    private final AiAgentVersionService _aiAgentVersionService;

    public AiAgentTrustLevelQueryResolverImpl(AiAgentVersionService aiAgentVersionService) {
        this._aiAgentVersionService = aiAgentVersionService;
    }

    @Override
    public CompletionStage<AiAgentTrustLevelQueryResponse> aiAgentTrustLevel(AiAgentTrustLevelQueryInput input,
            DataFetchingEnvironment dfe) {
        return _aiAgentVersionService.getAiAgentTrustLevelMetrics(input, dfe);
    }
}