// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.resolver.query;

import com.boomi.aiagentregistry.service.AiAgentProviderServiceImpl;
import com.boomi.graphql.server.schema.resolvers.AiAgentProviderQueryResolver;
import com.boomi.graphql.server.schema.types.AiAgentProvidersQueryResponse;
import graphql.schema.DataFetchingEnvironment;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletionStage;

@Component
public class AiAgentProvidersQueryResolverImpl implements AiAgentProviderQueryResolver {

    private final AiAgentProviderServiceImpl _aiAgentProviderServiceImpl;

    public AiAgentProvidersQueryResolverImpl(AiAgentProviderServiceImpl aiAgentProviderServiceImpl) {
        _aiAgentProviderServiceImpl = aiAgentProviderServiceImpl;
    }

    @Override
    public CompletionStage<AiAgentProvidersQueryResponse> aiAgentProviders(
            DataFetchingEnvironment dfe) {

        return _aiAgentProviderServiceImpl.aiAgentProviders(dfe);
    }
}
