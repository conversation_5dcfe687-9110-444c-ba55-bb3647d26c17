// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.resolver.query;

import graphql.schema.DataFetchingEnvironment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.constant.AWSBedrockEnabledRegionConstant;
import com.boomi.aiagentregistry.service.FeatureManager;
import com.boomi.aiagentregistry.service.metrics.OamSinkArnRetrieverService;
import com.boomi.graphql.server.schema.resolvers.AiAgentRegistryRegionQueryResolver;
import com.boomi.graphql.server.schema.types.AWSRegion;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

@Component
@Slf4j
@RequiredArgsConstructor
public class AiAgentRegistryRegionQueryResolverImpl implements AiAgentRegistryRegionQueryResolver {
    private final FeatureManager _featureManager;
    private final OamSinkArnRetrieverService _oamSinkArnRetrieverService;

    @Override
    public CompletionStage<List<AWSRegion>> aiAgentRegions(DataFetchingEnvironment dfe) {
        if (_featureManager.isDoProvisioningWithAssumeRole()) {
            return CompletableFuture.completedFuture(_oamSinkArnRetrieverService.getAwsRegionUnmodifiableList());
        }
        // Get all regions
        Map<String, String> allRegions = AWSBedrockEnabledRegionConstant.getAllRegions();
        return CompletableFuture.supplyAsync(() -> allRegions.entrySet()
                .stream()
                .map(entry -> new AWSRegion(entry.getKey(), entry.getValue()))
                .toList());
    }
}
