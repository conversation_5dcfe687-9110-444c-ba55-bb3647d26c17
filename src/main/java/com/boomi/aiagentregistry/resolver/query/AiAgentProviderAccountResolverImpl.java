// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.resolver.query;

import graphql.schema.DataFetchingEnvironment;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.service.AiAgentProviderAccountService;
import com.boomi.graphql.server.api.ResolverCallback;
import com.boomi.graphql.server.schema.resolvers.AiAgentProviderAccountResolver;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccount;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountOnboardingTemplate;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class AiAgentProviderAccountResolverImpl implements AiAgentProviderAccountResolver {

    private final Map<AiAgentProviderType, AiAgentProviderAccountService> _getBeanMappedByAiAgentProviderType;

    // WARNING - Do not use @Transactional on this  method otherwise the bulk resolver is not registered
    @Override
    public void bulkOnboardingTemplate(Collection<@NotNull AiAgentProviderAccount> input,
            ResolverCallback<@NotNull AiAgentProviderAccount, AiAgentProviderAccountOnboardingTemplate> callback,
            DataFetchingEnvironment dfe) {

        log.info("bulkOnboardingTemplate: input: {}", input);
        if (CollectionUtils.isEmpty(input)) {
            callback.complete();
            return;
        }

        List<CompletableFuture<Void>> futures = new ArrayList<>();

        Map<String, AiAgentProviderAccount> idToProviderAccountMap = input.stream()
            .collect(Collectors.toMap(
                AiAgentProviderAccount::getId, account -> account
            ));

        Map<AiAgentProviderType, List<AiAgentProviderAccount>> providerTypeToProviderAccounstMap = input.stream()
            .collect(Collectors.groupingBy(AiAgentProviderAccount::getProviderType, Collectors.toList()
            ));

        providerTypeToProviderAccounstMap.forEach((providerType, accounts) -> {
            log.debug("Provider Type: {}", providerType);
            AiAgentProviderAccountService accountService = _getBeanMappedByAiAgentProviderType.get(providerType);

            List<CompletableFuture<AiAgentProviderAccount>> onboardingTemplateFutures =
                    accountService.populateOnboardingTemplate(accounts, dfe);

            onboardingTemplateFutures.forEach(resolvedProviderAccountFutures -> {
                CompletableFuture<Void> future = resolvedProviderAccountFutures
                        .thenAccept(resolvedProviderAccount -> {
                            String providerAccountId = resolvedProviderAccount.getId();
                            callback.set(idToProviderAccountMap.get(providerAccountId),
                                    resolvedProviderAccount.getOnboardingTemplate());
                        });
                futures.add(future);
            });
        });

        CompletableFuture.allOf(futures.toArray(CompletableFuture[]::new))
            .thenRun(callback::complete);
    }
}
