// Copyright (c) 2025 Boomi, LP.
package com.boomi.aiagentregistry.resolver.query;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.aiagentregistry.service.IAiAgentListingService;
import com.boomi.graphql.server.schema.resolvers.AiAgentListingQueryResolver;
import com.boomi.graphql.server.schema.types.AiAgentListingAllFiltersResponse;
import com.boomi.graphql.server.schema.types.AiAgentListingFilterQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryResponse;

import org.springframework.stereotype.Component;

import java.util.concurrent.CompletionStage;

@Component
public class AiAgentListingQueryResolverImpl implements AiAgentListingQueryResolver {

    private final IAiAgentListingService _aiAgentListingService;

    public AiAgentListingQueryResolverImpl(IAiAgentListingService aiAgentListingService) {
        _aiAgentListingService = aiAgentListingService;
    }

    @Override
    public CompletionStage<AiAgentsListingQueryResponse> aiAgentListings(AiAgentsListingQueryInput input,
            DataFetchingEnvironment dfe) {
        return _aiAgentListingService.getAiAgentListings(input, dfe);
    }

    @Override
    public CompletionStage<AiAgentListingAllFiltersResponse> aiAgentListingFilterValues(
            AiAgentListingFilterQueryInput input,DataFetchingEnvironment dfe) {
        return _aiAgentListingService.allAiAgentListingFilters(input,dfe);
    }
}
