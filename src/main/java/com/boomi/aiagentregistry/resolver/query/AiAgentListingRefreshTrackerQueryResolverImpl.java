// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.resolver.query;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.aiagentregistry.service.IAiAgentRefreshTrackerService;
import com.boomi.graphql.server.schema.resolvers.AiAgentListingRefreshTrackerQueryResolver;
import com.boomi.graphql.server.schema.types.AiAgentListingRefreshTracker;

import org.springframework.stereotype.Component;

import java.util.concurrent.CompletionStage;

@Component
public class AiAgentListingRefreshTrackerQueryResolverImpl
        implements AiAgentListingRefreshTrackerQueryResolver {

    private IAiAgentRefreshTrackerService _iAiAgentRefreshTrackerService;

    public AiAgentListingRefreshTrackerQueryResolverImpl(
            IAiAgentRefreshTrackerService iAiAgentRefreshTrackerService) {
        _iAiAgentRefreshTrackerService = iAiAgentRefreshTrackerService;
    }

    @Override
    public CompletionStage<AiAgentListingRefreshTracker> aiAgentListingRefreshTracker(
            DataFetchingEnvironment dataFetchingEnvironment) {
        return _iAiAgentRefreshTrackerService.getRefreshTracker(dataFetchingEnvironment);
    }
}
