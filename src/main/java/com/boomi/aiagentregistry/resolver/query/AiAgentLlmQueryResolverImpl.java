// Copyright (c) 2025 Boomi, Inc.
package com.boomi.aiagentregistry.resolver.query;

import graphql.schema.DataFetchingEnvironment;
import lombok.RequiredArgsConstructor;
import com.boomi.aiagentregistry.service.AiAgentLlmListingService;
import com.boomi.graphql.server.schema.resolvers.AiAgentLlmQueryResolver;
import com.boomi.graphql.server.schema.types.AiAgentLlmsQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentLlmsQueryResponse;

import org.springframework.stereotype.Component;

import java.util.concurrent.CompletionStage;

@Component
@RequiredArgsConstructor
public class AiAgentLlmQueryResolverImpl implements AiAgentLlmQueryResolver {
    private final AiAgentLlmListingService _aiAgentLlmListingService;

    @Override
    public CompletionStage<AiAgentLlmsQueryResponse> aiAgentLlms(AiAgentLlmsQueryInput input,
            DataFetchingEnvironment dfe) {
        return _aiAgentLlmListingService.getAiAgentLlms(input, dfe);
    }
}