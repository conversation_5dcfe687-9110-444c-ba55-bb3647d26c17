// Copyright (c) 2024 Boomi, LP.
package com.boomi.aiagentregistry.resolver.query;

import graphql.schema.DataFetchingEnvironment;
import com.boomi.aiagentregistry.service.IAiAgentTagService;
import com.boomi.graphql.server.schema.resolvers.AiAgentTagQueryResolver;
import com.boomi.graphql.server.schema.types.AiAgentTagQueryResponse;

import org.springframework.stereotype.Component;

import java.util.concurrent.CompletionStage;

@Component
public class AiAgentTagQueryResolverImpl implements AiAgentTagQueryResolver {

    IAiAgentTagService _iAiAgentTagService;

    public AiAgentTagQueryResolverImpl(IAiAgentTagService iAiAgentTagService) {
        _iAiAgentTagService = iAiAgentTagService;
    }

    @Override
    public CompletionStage<AiAgentTagQueryResponse> aiAgentTags(DataFetchingEnvironment dfe) {
        return _iAiAgentTagService.getAiAgentTags(dfe);
    }
}
