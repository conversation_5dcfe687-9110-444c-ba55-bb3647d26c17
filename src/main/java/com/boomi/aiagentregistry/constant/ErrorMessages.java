// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.constant;

/**
 * <AUTHOR>
 */

public final class ErrorMessages {

    private ErrorMessages() {
    }

    public static final String UNSUPPORTED_AUTH_SCHEMA = "Unsupported authentication schema: %s";
    public static final String UNSUPPORTED_PROVIDER = "Unsupported provider type: %s";
    public static final String INVALID_CREDENTIALS_TYPE = "Invalid credentials object";


    public static final String INVALID_CREDENTIALS_KEYS = "Invalid credential keys";
    public static final String FAILED_TO_FETCH_JWT = "Error while fetching jwt token";
    public static final String ERROR_LOADING_ADDITIONAL_PROPERTIES = "Error loading additional properties: ";
    public static final String ERROR_CREATING_AUTHORIZATION = "Error creating authorization: ";
    public static final String ERROR_DURING_AGENT_SYNC_JOB = "Error during agent sync job {}";

    public static String format(String message, Object... args) {
        return String.format(message, args);
    }

    public static final String ERROR_DELETE_ALIAS = "Failed to delete alias";
    public static final String FAILED_TO_CREATE_ALIAS_FOR_AGENT = "Failed to create alias for agent {}";
    public static final String ERROR_UPDATE_ALIAS = "Failed to update alias";
    public static final String ERROR_FETCH_ALIAS_DETAILS = "Error fetching agent alias details";
    public static final String ERROR_LIST_ALIASES = "Failed to list aliases for agent";
    public static final String ERROR_DELETE_VERSION = "Failed to delete version";
    public static final String ERROR_DELETE_VERSION_WITH_ID = "Failed to delete version {}";
    public static final String ERROR_CREATE_VERSION = "Failed to create version";
    public static final String ERROR_UPDATE_VERSION = "Failed to update version {}";
    public static final String ERROR_FETCH_VERSION_DETAILS = "Error fetching agent version details {}";
    public static final String ERROR_LIST_VERSIONS = "Failed to list versions for agent";
    public static final String FAILED_TO_LIST_AGENTS = "Failed to list agents";
    public static final String FAILED_TO_CREATE_BEDROCK_CLIENT = "Failed to create Bedrock Client for provider "
            + "account {}";
    public static final String FAILED_TO_CREATE_AUTHORIZATION = "Failed to create authorization for client for "
            + "provider account {}";
    public static final String FAILED_TO_DELETE_AGENT = "Failed to delete agent {}";
    public static final String FAILED_TO_CREATE_AGENT = "Failed to create agent";
    public static final String ERROR_FETCHING_AGENT_DETAILS = "Error fetching agent  details: {}";
    public static final String ERROR_VALIDATING_CONNECTION = "Error validating connection";
    public static final String UNEXPECTED_ERROR_DURING_CONNECTION_VALIDATION =
            "Unexpected error during connection validation";
    public static final String ERROR_CREATING_AWS_CREDENTIALS_OR_CLIENT = "Error creating AWS credentials or client";
    public static final String FAILED_TO_CREATE_VERSION_FOR_AGENT = "Failed to create version for agent {}";
    public static final String ERROR_DURING_CLEANUP_OF_UNPROCESSED_ENTITIES =
            "Error during cleanup of unprocessed entities";
    public static final String ERROR_DURING_SYNC_COMPLETION = "Error during sync completion";
    public static final String ERROR_PROCESSING_AGENT = "Error processing agent {}";
    public static final String ERROR_PROCESSING_VERSION = "Error processing version {} of agent {}";
    public static final String FAILED_TO_UPDATED_ALIAS = "Failed to updated alias {}";
    public static final String VERSION_NOT_FOUND_FOR_ALIAS = "Version not found for alias: %s";
    public static final String AGENT_OR_ALIAS_SUMMARY_IS_NULL = "Agent or alias summary is null";
    public static final String NO_ROUTING_CONFIGURATION_FOUND_FOR_ALIAS =
            "No routing configuration found for alias: {}";
    public static final String TARGET_VERSION_IS_EMPTY_FOR_ALIAS = "Target version is empty for alias: {}";
    public static final String ERROR_REGISTRY_ACCOUNT_ID_EMPTY =
            "The provided AI Agent registry account is empty. The AI Agent registry account ID cannot be null"
                    + ". Please ensure that you provide a valid account ID.";
    public static final String ERROR_ACCOUNT_NAME_EMPTY = "The supplied registry account name is either empty or null.";
    public static final String ERROR_REGION_EMPTY = "The specified region is either empty or null.";
    public static final String ERROR_INVALID_METADATA_JSON = "The supplied metadata JSON is either empty or invalid.";
    public static final String ERROR_EMPTY_CREDENTIALS_INPUT = "Provided credentials are either empty";
    public static final String ERROR_INVALID_CREDENTIALS_INPUT = "Provided credentials are improperly formatted";
    public static final String ERROR_SSM_UPDATE_FAILED = "The SSM update has failed.";
    public static final String ERROR_INVALID_AUTH_SCHEMA =
            "The provided authentication schema is either empty or not of type AiProviderAuthSchema.";
    public static final String ERROR_INVALID_CREDENTIALS_KEY
            = "The provided credentials are invalid, preventing successful authentication with the agent provider.";
    public static final String ERROR_INVALID_REGISTRY_ACCOUNT_STATUS =
            "The registry account status provided is invalid.";
    public static final String ERROR_ACCOUNT_NOT_FOUND =
            "The AI agent registry account corresponding to the provided ID {} could not be located.";
    public static final String ERROR_DELETING_UNPROCESSED_ENTITIES_FOR_AGENT =
            "Error deleting unprocessed entities for agent {}:";
    public static final String ERROR_FETCHING_VERSIONS =
            "Error while trying to fetch versions for agent {}:";
    public static final String ERROR_FETCHING_AGENTS = "Error while trying to fetch agents:";
    public static final String ERROR_FETCHING_ALIASES =
            "Error while trying to fetch aliases for agent {}:";
    public static final String ERROR_WHILE_RETRIEVING_AI_AGENT_PROVIDERS = "Error while retrieving AI Agent providers";
    public static final String FETCHING_AI_AGENT_PROVIDERS_FOR_ACCOUNT = "Fetching AI Agent providers for account: {}";
    public static final String PROVIDER_ACCOUNT_ID_INPUT_ERROR =
            "The provided provider account ID either null or empty";
    public static final String ACCOUNT_EXISTS_FOR_PROVIDER_ACCOUNT_ID_AND_TYPE_ERROR =
            "An AI agent registry account already exists for "
                    + "the specified provider account ID {} and provider type {}.";
    public static final String ERROR_FETCHING_FROM_MATERIALISED_VIEW = "Error accessing materialized view, falling "
            + "back to main table";
    public static final String ERROR_FETCHING_AI_AGENT_TAGS = "Error while retrieving AI Agent Tag Details.";
    public static final String TAG_SYNC_ERROR = "Error while saving tags.";
    public static final String ERROR_LIST_ACTION_GROUPS = "Failed to list action groups for versio";
    public static final String ERROR_FETCHING_ACTION_GROUP =
            "Error while trying to fetch action groups for version {}:";
    public static final String ERROR_FETCHING_ACTION_GROUP_DETAILS = "Error fetching action group details";
    public static final String ERROR_PROCESSING_ACTION_GROUP = "Error processing action group {}";
    public static final String ERROR_PROCESSING_VERSION_CHILD_ENTITIES =
            "Error processing version {} child entities of agent{}";
    public static final String ERROR_DELETING_UNPROCESSED_CHILDREN =
            "Error deleting unprocessed children for version {}";

    public static final String ERROR_FETCHING_AGENT_FROM_REF_ID_FOR_TAGS=
            "Agent not found for reference entity id {}.";
    public  static final String ERROR_KEY_GENERATION_PARAMETERS_FOR_TAGS=
            "Tag key generation input parameters cannot be null";
    public static final String ERROR_PROCESSING_TAG_FOR_REF_ENTITY=
            "Error processing tag sync for entity {}: ";

    public static final String ERROR_AGENT_VERSION_NOT_FOUND = "Agent version not found for id {}";
    public static final String ERROR_AGENT_TASK_NOT_FOUND = "Agent Task not found for id {}";
    public static final String ERROR_ALIAS_NOT_FOUND_FOR_VERSION = "Alias not found for agent version with id {}";
    public static final String INFO_ALIAS_FOUND_FOR_VERSION = "Agent version has {} aliases";
    public static final String ERROR_INVALID_ALIAS_ID = "Invalid or empty agent alias id";
    public static final String ERROR_ALIAS_NOT_FOUND = "Alias not found for id {}";
    public static final String INFO_NO_TAGS_ASSOCIATED_WITH_ALIAS = "There is no tags associated with Alias with id {}";
    public static final String INFO_NO_SYNC_DATA_FOR_ALIAS = "There is no Sync data for Alias with id {}";
    public static final String INFO_NO_SYNC_DATA_FOR_VERSION = "There is no Sync data for Version with id {}";

    public static final String INFO_TAGS_FOUND_FOR_ALIAS = "Found {} tags associated with Alias with id {}";
    public static final String TOOL_CREATION_FAILED = "Tool creation failed due to concurrent modification";

    public static final String INFO_UPDATING_SECRET = "Updating Secrets Manager";
    public static final String INFO_UPDATED_SECRET = "Updated Secrets Manager";
    public static final String ERROR_UPDATING_SECRET = "Exception while updating secret, Error: {}";

    public static final String FAILED_TO_GET_TOOL = "Error in getting tool with id {} and type {}";
    public static final String FAILED_TO_CREATE_TOOL = "Failed to create tool with external Id {}";
    public static final String INVALID_TRUST_LEVEL_ERROR =
            "The provided trust level is either null or not of type AiAgentRegistryTrustLevel.";
    public static final String FAILED_TO_ADD_TRUST_LEVEL_ERROR =
            "Failed to add trust level for the provided agent version {}, Error {}";
    public static final String ERROR_AI_AGENT_BY_VERSION = "Error while fetching AI Agent by version ID {}, Error: {}";
    public static final String ERROR_AI_AGENT_BY_ALIAS = "Error while fetching AI Agent by alias ID {}, Error: {}";
    public static final String ERROR_UPDATING_GUARDRAIL = "Error updating guardrail {}";
    public static final String ERROR_PROCESSING_GUARDRAIL = "Error processing guardrail {}.";
    public static final String ERROR_DELETING_GUARD_RAIL = "Error while deleting guardrails for version {}";
    public static final String ERROR_CREATING_GUARDRAIL = "Error creating guardrail {}";

    public static final String ERROR_PROCESSING_ACCOUNT = "Error while processing account {}";

    public static final String ERROR_PROCESSING_TASK = "Error while processing task {} for version Id {}";
    public static final String ERROR_PROCESSING_TASKS = "Error processing tasks for version Id {}";
    public static final String FAILED_TO_CREATE_TASK_FOR_VERSION = "Failed to create task for version {}";
    public static final String ERROR_PROCESSING_TASK_CHILD_ENTITIES =
            "Error processing task {} child entities of agent version {}";

    public static final String ERROR_PROCESSING_TOOL = "Error while processing tool for task Id {} of agent version {}";
    public static final String ERROR_DELETING_TOOL = "Error while deleting tool for task Id {} of agent version {}";
    public static final String ERROR_CREATING_TOOL = "Error creating tool {}";

    public static final String ERROR_MAKING_REQUEST_TO_GARDEN = "Error making request to {}";

    public static final String ACCOUNT_NAME_TOO_LONG_ERROR =
            "The provided provider account name `{}` exceeds the maximum limit of 200 characters. Please shorten it to "
                    + "comply with this requirement.";

    public static final String ERROR_INSTALLING_GARDEN_AGENT =
            "Error installing Boomi agent with id: {}";
    public static final String ERROR_UNINSTALLING_GARDEN_AGENT =
            "Error uninstalling Boomi agent with id: {}";
    public static final String ERROR_ENABLING_BOOMI_AGENT =
            "Error enabling Boomi agent with external id: {}";
    public static final String ERROR_DISABLING_BOOMI_AGENT =
            "Error disabling Boomi agent with external id: {}";
    public static final String ERROR_ENABLING_AGENT_VERSION =
            "Error enabling agent version with id: {}";
    public static final String ERROR_DISABLING_AGENT_VERSION =
            "Error disabling agent version with id: {}";

    public static final String ERROR_UNABLE_TO_ACQUIRE_LOCK="Unable to acquire lock.";

    public static final String ERROR_THREAD_INTERRUPTION_DURING_SYNC ="Thread was interrupted during tag sync";

    public static final String EXCEPTION_DATA_INTEGRITY_VIOLATION="Data Integrity violation issue due"
            + " to multiple users trying to update same tags concurrently "
            + "for Idp Account Id : {} and message is : {}";
    public static final String EXCEPTION_PESSIMISTIC_LOCKING_FAILURE="Lock acquisition failed for account: {} and "
            + "message is: {}";
    public static final String EXCEPTION_QUERY_TIMEOUT="Query timeout for account: {} and message is: {}";
    public static final String JSON_ERROR = "Json error";
    public static final String GARDEN_ACCOUNT_HAS_NO_ACCESS_ERROR =
            "The account with ID %s does not have permission to access the Boomi Agent Garden";

    public static final String ERROR_DELETING_SECRET = "Error deleting secret {}: {}";
    public static final String SUCCESSFUL_SECRET_DELETION = "Secret successfully deleted: {}";
    public static final String SECRET_DOES_NOT_EXIST = "Secret {} does not exist or is not accessible and message is "
            + "{}";
    public static final String ERROR_DELETE_REPLICATION_SECRET = "Could not remove replication for secret {}, "
            + "proceeding with deletion and message is {} ";

    public static final String EMPTY_EXTERNAL_ACCOUNT_ID = "External provider account ID is empty or null";
    public static final String INVALID_PROVIDER_TYPE_ERROR =
            "The provided provider type is either null or not of type AiAgentProviderType.";
    public static final String PROVIDER_ACCOUNT_DESCRIPTION_TOO_LONG_ERROR =
            "The provided provider account description `{}` exceeds the maximum limit of 1000 characters. Please "
                    + "shorten it to comply with this requirement.";
    public static final String ERROR_EMPTY_SECRET_NAME = "Secret name cannot be null or empty";
    public static final String ERROR_SECRET_NOT_EXISTS = "Secret {} does not exist: {}";
    public static final String ERROR_INVALID_REQUEST_FOR_SECRET_CHECK = "Invalid request while checking secret {}: {}";
    public static final String ERROR_WHILE_CHECKING_SECRET = "AWS Secrets Manager error while checking secret {}: {}";
    public static final String UNEXPECTED_ERROR_WHILE_CHECKING_SECRET = "Unexpected error while checking secret {}: {}";
    public static final String ERROR_FAILED_TO_DELETE_PROVIDER_ACCOUNT =
            "Unable to delete the specified agent provider account: {}. Please check the provider account ID and try"
                    + " again.";
    public static final String ERROR_OCCURRED_WHILE_CHECKING_SECRET = "Unexpected error while checking secret";
    public static final String ERROR_ON_ADDING_PROVIDER_DETAILS_FOR_AWS_BEDROCK =
            "Error while Adding the Providers details for AWS Bedrock Agent Provider";
    public static final String ERROR_ON_UPDATING_PROVIDER_DETAILS_FOR_AWS_BEDROCK =
            "Error while Updating the Providers details for AWS Bedrock Agent Provider. Guid {}";
    public static final String ERROR_ON_ADDING_PROVIDER_DETAILS_FOR_BOOMI = "Error while Adding the Providers "
            + "details for Boomi Provider.";
    public static final String ERROR_ON_UPDATING_PROVIDER_DETAILS_FOR_BOOMI =
            "Error while Updating the Providers details for Boomi. Guid {}";
    public static final String ERROR_ON_ADDING_PROVIDER_DETAILS_FOR_CUSTOM =
            "Error while Adding the Providers details for Custom Provider.";
    public static final String ERROR_ON_ADDING_PROVIDER_ACCOUNT = "Error while Adding the Providers details. Error: {}";
    public static final String DUPLICATE_PROVIDER_ACCOUNT_ERROR =
            "Duplicate provider account detected: {}";
    public static final String SYNC_FAILED_AFTER_CREATING_PROVIDER =
            "Sync failed after the provider account {} is created";
    public static final String IMMUTABLE_FIELD_VALIDATION_ERROR = "Error validating immutable fields in update for {}";
    public static final String PROVIDER_ACCOUNT_NOT_FOUND_ERROR = "Provider account not found with the supplied ID {}";
    public static final String INVALID_TAG_INPUT_ERROR =
            "The provided tags input is either null or empty, or one of the tag key is also null or empty.";
    public static final String INVALID_LLM_INPUT_ERROR =
            "The provided LLM input is either null or empty, or one of the LLM names is also null or empty.";
    public static final String AI_AGENT_CREATE_INPUT_NULL_ERROR =
            "The input provided for AI agent creation is either null or empty. Please provide valid input parameters.";
    public static final String INVALID_AI_AGENT_NAME_ERROR = "The provided AI agent name either null or empty";
    public static final String AI_AGENT_NAME_ALREADY_EXIST_ERROR =
            "AI agent name `{}` already exists. Please use a unique name.";
    public static final String ADD_LLM_TO_AGENT_ERROR =
            "Error while adding llms into current agent version. Error {}";
    public static final String ADD_TAG_TO_AGENT_ERROR = "Error while adding tags into current agent version. Error {}";
    public static final String INVALID_INPUT = "The provided input does not meet the required validation criteria. "
            + "Please ensure all inputs are correct and properly formatted.";
    public static final String VERSION_ID_REQUIRED_ERROR =
            "Ai agent version id is required, Please provide a valid version id for the Agent.";
    public static final String AI_AGENT_NOT_FOUND_ERROR = "AI Agent not found for id {}";
    public static final String ERROR_ON_AI_AGENT_DELETE =
            "Unexpected error while deleting the AI Agent with id {}. Error {}";
    public static final String NON_CUSTOM_PROVIDER_AGENT_ERROR =
            "Only custom provider agents can be deleted. deletion is not allowed for any other provider type.";
}
