// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.constant;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */

public final class ApplicationConstant {
    private ApplicationConstant() {
    }
    public static final String AUTH_JWT_GENERATE_PATH = "/auth/jwt/generate/";
    public static final int MAX_RECORD_CONNECTION_TEST = 1;
    public static final String AGENT_VERSION_STATUS_DRAFT = "DRAFT";
    public static final int DEFAULT_PAGE_SIZE = 10;
    public static final int DEFAULT_PAGE_INDEX = 0;
    public static final int ZERO = 0;
    public static final String DELIMITER = "/";
    public static final String CORRELATION_PROPERTY_NAME = "correlation";

    public static final String IDP_ACCOUNT_FILTER_NAME = "idpAccountFilter";
    public static final String IDP_ACCOUNT_FILTER_PARAM = "idpAccountId";
    public static final String IDP_ACCOUNT_FILTER_CONDITION = "idp_account_id = :idpAccountId";

    public static final String EXCLUDE_PROVIDER_ACCOUNT_STATUS_FILTER_NAME = "excludeProviderAccountStatusesFilter";
    public static final String EXCLUDE_PROVIDER_ACCOUNT_STATUS_FILTER_PARAM = "statusesToExclude";
    public static final String EXCLUDE_PROVIDER_ACCOUNT_STATUS_FILTER_CONDITION =
            "provider_account_status NOT IN (:statusesToExclude)";

    public static final String NOT_IMPLEMENTED = "Not implemented";

    public static final String BOOMI_ACCOUNT_ID_PARAM = "{ACCOUNT_ID}";
    public static final String REGION_PARAM = "{REGION}";
    public static final String AGENT_ID_PARAM = "{AGENT_ID}";
    public static final String AGENT_VERSION_PARAM = "{AGENT_VERSION}";
    public static final String AGENT_ALIAS_ID_PARAM = "{AGENT_ALIAS_ID}";

    public static final String IS_DELETED_FILTER_NAME = "isDeletedFilter";
    public static final String IS_DELETED_FILTER_PARAM = "isDeleted";
    public static final String IS_DELETED_FILTER_CONDITION = "is_deleted = :isDeleted";
    public static final Integer AI_AGENT_PROVIDER_NAME_LENGTH = 200;
    public static final Integer AGENT_PROVIDER_DESCRIPTION_LENGTH = 1000;

    public static final String SECRET_EXISTS = "secret exists";
    public static final String SECRET_NOT_EXISTS = "secret not exists";
    public static final String EXCEPTION_OCCURRED = "Exception occurred";

    public static final int SECRET_NOT_FOUND = -1;
    public static final int SECRET_FOUND = 1;

    public static final String COMMA = ",";
    public static final String DOUBLE_QUOTED_STRING = "\"%s\"";
    public static final String ESCAPED_DOUBLE_QUOTE = "\"";
    public static final String SECRET_KEY_PATH_AWS = "aws/";
    public static final String BACKSLASH = "/";
    public static final Pattern AWS_ACCOUNT_ID_PATTERN = Pattern.compile("^\\d{12}$");
    public static final int HOURS_12 = 60 * 60 * 12;
    public static final String CLOUDFORMATION_S3_KEY_PREFIX = "cloudformation/";
    public static final String HYPHEN = "-";
    public static final String CF_PARAM_ACT_OAM_CUSTOMER_ROLE_NAME = "%ACT_OAM_CUSTOMER_ROLE_NAME%";
    public static final String BOOMI_CONTACT = "BoomiContact";
    public static final String REGISTRY_CUSTOMER = "Customer";
    public static final String ENVIRONMENT = "Environment";
    public static final String SERVICE = "Service";
    public static final String STACK_TYPE = "StackType";
    public static final String STACK_NAME = "StackName";
    public static final String ROLE = "Role";
}
