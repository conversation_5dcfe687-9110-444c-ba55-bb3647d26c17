// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.constant;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;

public class AWSBedrockEnabledRegionConstant {

    private AWSBedrockEnabledRegionConstant() {
    }

    private static final Map<String, String> AWS_BEDROCK_REGIONS;

    static {
        Map<String, String> regions = new LinkedHashMap<>();

        // North America
        regions.put("us-east-1", "US East (N. Virginia)");
        regions.put("us-east-2", "US East (Ohio)");
        regions.put("us-west-2", "US West (Oregon)");
        regions.put("us-gov-east-1", "US-East (AWS GovCloud)");
        regions.put("us-gov-west-1", "US-West (AWS GovCloud)");
        regions.put("ap-northeast-1", "Tokyo (Asia Pacific)");
        regions.put("ap-northeast-2", "Seoul (Asia Pacific)");
        regions.put("ap-south-1", "Mumbai (Asia Pacific)");
        regions.put("ap-southeast-1", "Singapore (Asia Pacific)");
        regions.put("ap-southeast-2", "Sydney (Asia Pacific)");
        regions.put("ca-central-1", "Canada (Central)");
        regions.put("eu-central-1", "Frankfurt (Europe)");
        regions.put("eu-central-2", "Zurich (Europe)");
        regions.put("eu-west-1", "Ireland (Europe)");
        regions.put("eu-west-2", "London (Europe)");
        regions.put("eu-west-3", "Paris (Europe)");
        regions.put("sa-east-1", "São Paulo (South America)");

        AWS_BEDROCK_REGIONS = Collections.unmodifiableMap(regions);
    }

    /**
     * Get all AWS regions as an unmodifiable map
     * @return Map with region code as key and region label as value
     */
    public static Map<String, String> getAllRegions() {
        return AWS_BEDROCK_REGIONS;
    }

    /**
     * Get region label for a given region code
     * @param regionCode AWS region code
     * @return Region label or null if region code doesn't exist
     */
    public static String getRegionLabel(String regionCode) {
        return AWS_BEDROCK_REGIONS.get(regionCode);
    }

    /**
     * Check if a region code is valid
     * @param regionCode AWS region code to validate
     * @return true if region code exists, false otherwise
     */
    public static boolean isValidRegion(String regionCode) {
        return AWS_BEDROCK_REGIONS.containsKey(regionCode);
    }
}
