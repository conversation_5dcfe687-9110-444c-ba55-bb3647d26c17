// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry;

import com.boomi.services.common.config.DisableDefaultAndJspServlets;
import com.boomi.services.common.config.MonitorMeterConfig;
import com.boomi.services.common.config.NewRelicMetricsConfig;
import com.boomi.services.common.ssm.ParameterStoreConfig;
import com.boomi.services.common.util.SpelEvaluationBeanPostProcessor;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;
import org.springframework.retry.annotation.EnableRetry;

import com.boomi.services.common.webflux.config.CorrelationFilterConfig;
import com.boomi.services.common.webflux.config.ErrorPageWebfluxConfig;
import com.boomi.services.common.webflux.config.MonitorWebfluxConfig;
import com.boomi.services.common.webflux.graphql.GraphQLWebfluxConfig;
import com.boomi.services.common.jobs.DbSchedulerConfig;

@EnableConfigurationProperties
@EnableRetry
@SpringBootApplication
@Import({ GraphQLWebfluxConfig.class, MonitorWebfluxConfig.class, MonitorMeterConfig.class,DbSchedulerConfig.class,
        DisableDefaultAndJspServlets.class, ErrorPageWebfluxConfig.class, NewRelicMetricsConfig.class,
        CorrelationFilterConfig.class, ParameterStoreConfig.class, SpelEvaluationBeanPostProcessor.class })
public class Application {
    public static void main(final String[] args) {
        SpringApplication.run(Application.class, args);
    }
}

