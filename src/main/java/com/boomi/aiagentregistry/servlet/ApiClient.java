// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.servlet;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;
import com.boomi.aiagentregistry.exception.CustomNonRetryableException;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.time.Duration;
import java.util.Set;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ApiClient {
    private final WebClient.Builder _webClientBuilder;
    private final int _maxRetryAttempts;
    private final long _backoffMillis;
    private final long _maxBackoffSeconds;
    private final double _jitter;

    private static final Set<HttpStatus> RETRYABLE_STATUSES = Set.of(
            // 408
            HttpStatus.REQUEST_TIMEOUT,
            // 429
            HttpStatus.TOO_MANY_REQUESTS,
            // 500
            HttpStatus.INTERNAL_SERVER_ERROR,
            // 502
            HttpStatus.BAD_GATEWAY,
            // 503
            HttpStatus.SERVICE_UNAVAILABLE,
            // 504
            HttpStatus.GATEWAY_TIMEOUT
    );

    public ApiClient(WebClient.Builder webClientBuilder,
            @Value("${spring.application.spring-retry.maxAttempts:3}") int maxRetryAttempts,
            @Value("${spring.application.spring-retry.backoffMillis:1000}") long backoffMillis,
            @Value("${spring.application.spring-retry.maxBackoffSeconds:8}") long maxBackoffSeconds,
            @Value("${spring.application.spring-retry.jitter:1.0}") double jitter) {
        _webClientBuilder = webClientBuilder;
        _maxRetryAttempts = maxRetryAttempts;
        _backoffMillis = backoffMillis;
        _maxBackoffSeconds = maxBackoffSeconds;
        _jitter = jitter;
    }

    public <T> Flux<T> get(RequestBuilder requestBuilder, Class<T> responseType) {
        return sendWithRetry(() ->
                _webClientBuilder.build()
                        .get()
                        .uri(requestBuilder.buildUrl())
                        .headers(httpHeaders -> requestBuilder.getHeaders().forEach(httpHeaders::add))
                        .retrieve()
                        .bodyToFlux(responseType)
        );
    }

    public <T> Flux<T> post(RequestBuilder requestBuilder, Class<T> responseType) {
        return sendWithRetry(() ->
                _webClientBuilder.build()
                        .post()
                        .uri(requestBuilder.buildUrl())
                        .headers(httpHeaders -> requestBuilder.getHeaders().forEach(httpHeaders::add))
                        .bodyValue(requestBuilder.getBody())
                        .retrieve()
                        .bodyToFlux(responseType)
        );
    }

    <T> Flux<T> sendWithRetry(Supplier<Flux<T>> requestSupplier) {
        return Mono.defer(() -> {
                    Flux<T> flux = requestSupplier.get();
                    if (flux == null) {
                        return Mono.error(new NullPointerException("Request supplier returned null Flux"));
                    }
                    return flux.collectList();
                })
                .retryWhen(
                        // Initial backoff starting at 1 second default. Then increase exponentially.
                        Retry.backoff(_maxRetryAttempts, Duration.ofMillis(_backoffMillis))
                                // Increase back off time exponentially only until default 8 seconds max
                                .maxBackoff(Duration.ofSeconds(_maxBackoffSeconds))
                                // Full jitter for randomness to avoid collisions
                                .jitter(_jitter)
                                .filter(throwable -> {
                                    if (throwable instanceof WebClientResponseException) {
                                        HttpStatus status =
                                                (HttpStatus) ((WebClientResponseException) throwable).getStatusCode();
                                        boolean retry = RETRYABLE_STATUSES.contains(status);
                                        if (retry) {
                                            log.info("Retry for error ({} {}): {}", status.value(),
                                                    status.getReasonPhrase(), throwable.getMessage(), throwable);
                                        }
                                        return retry;
                                    }
                                    return false;
                                })
                                .onRetryExhaustedThrow((spec, signal) -> signal.failure())
                )
                .onErrorMap(WebClientResponseException.class, ex -> {
                    HttpStatus status = (HttpStatus) ex.getStatusCode();
                    if (RETRYABLE_STATUSES.contains(status)) {
                        log.info("Retryable error during API call ({} {}): {}",
                                status.value(), status.getReasonPhrase(), ex.getMessage(), ex);
                        return ex;
                    } else {
                        log.error("Non-retryable client error during API call: {}", status, ex);
                        return new CustomNonRetryableException("Client error: " + status, ex);
                    }
                })
                .flatMapMany(Flux::fromIterable);
    }
}

