// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.servlet;

import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class ResponseHandler {

    public <T> Flux<T> handle(Flux<T> response) {
        log.debug("Handling response: {}", response);
        return response
                .onErrorResume(this::handleError);
    }

    private <T> Flux<T> handleError(Throwable error) {
        log.error("Error occurred: {}", error.getMessage(), error);
        return Flux.error(error);
    }
}

