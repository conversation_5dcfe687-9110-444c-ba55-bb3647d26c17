// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.servlet;

import org.springframework.stereotype.Service;

import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 */
@Service
public class ApiService {
    private final ApiClient _apiClient;
    private final ResponseHandler _responseHandler;

    public ApiService(ApiClient apiClient, ResponseHandler responseHandler) {
        _apiClient = apiClient;
        _responseHandler = responseHandler;
    }

    public <T> Flux<T> executeGet(RequestBuilder requestBuilder, Class<T> responseType) {

        return _responseHandler.handle(_apiClient.get(requestBuilder, responseType));
    }

    public <T> Flux<T> executePost(RequestBuilder requestBuilder, Class<T> responseType) {
        return _responseHandler.handle(_apiClient.post(requestBuilder, responseType));
    }
}

