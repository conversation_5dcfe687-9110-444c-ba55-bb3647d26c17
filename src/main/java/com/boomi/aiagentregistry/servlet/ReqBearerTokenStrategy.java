// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.servlet;

/**
 * <AUTHOR>
 */
public class ReqBearerTokenStrategy implements ReqAuthStrategy {
    private final String _token;

    public ReqBearerTokenStrategy(String token) {
        _token = token;
    }

    @Override
    public void apply(RequestBuilder requestBuilder) {
        requestBuilder.addHeader("Authorization", "Bearer " + _token);
    }
}
