// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.servlet;

import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.internal.http.loader.DefaultSdkAsyncHttpClientBuilder;
import software.amazon.awssdk.core.retry.RetryMode;
import software.amazon.awssdk.http.SdkHttpConfigurationOption;
import software.amazon.awssdk.http.async.SdkAsyncHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.bedrock.BedrockAsyncClient;
import software.amazon.awssdk.services.bedrockagent.BedrockAgentAsyncClient;
import software.amazon.awssdk.services.sts.StsAsyncClient;
import software.amazon.awssdk.utils.AttributeMap;
import com.boomi.aiagentregistry.service.auth.AwsCredentials;

import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class AwsClient {
    private static final int MAX_CONCURRENT_REQUEST = 10;
    private static final int CONNECTION_TIMEOUT = 5;
    private static final int CONNECTION_MAX_IDLE_TIMEOUT = 12;

    public SdkAsyncHttpClient createAsyncHttpClient() {
        return new DefaultSdkAsyncHttpClientBuilder()
                .buildWithDefaults(AttributeMap.builder()
                        .put(SdkHttpConfigurationOption.MAX_CONNECTIONS, MAX_CONCURRENT_REQUEST)
                        .put(SdkHttpConfigurationOption.CONNECTION_TIMEOUT, Duration.ofSeconds(CONNECTION_TIMEOUT))
                        .put(SdkHttpConfigurationOption.CONNECTION_MAX_IDLE_TIMEOUT, Duration.ofSeconds(
                                CONNECTION_MAX_IDLE_TIMEOUT))
                        .put(SdkHttpConfigurationOption.TCP_KEEPALIVE, Boolean.TRUE)
                        .build());
    }

    public BedrockAgentAsyncClient createBedrockAgentAsyncClient(AwsCredentials credentials) {
        AwsBasicCredentials basicCredentials = createBasicCredentials(credentials);
        StaticCredentialsProvider awsCredentialsProvider = StaticCredentialsProvider.create(basicCredentials);
        return createBedrockAgentAsyncClient(awsCredentialsProvider, credentials.getAwsRegion());
    }

    public BedrockAgentAsyncClient createBedrockAgentAsyncClient(AwsCredentialsProvider awsCredentialsProvider,
            String region) {
        return BedrockAgentAsyncClient
                .builder()
                .httpClient(createAsyncHttpClient())
                .overrideConfiguration(o -> o.retryStrategy(RetryMode.defaultRetryMode()))
                .credentialsProvider(awsCredentialsProvider)
                .region(Region.of(region))
                .build();
    }

    public BedrockAsyncClient createBedrockAsyncClient(AwsCredentials awsCredentials) {
        AwsBasicCredentials basicCredentials = createBasicCredentials(awsCredentials);

        StaticCredentialsProvider awsCredentialsProvider = StaticCredentialsProvider.create(basicCredentials);
        return createBedrockAsyncClient(awsCredentialsProvider, awsCredentials.getAwsRegion());
    }

    public BedrockAsyncClient createBedrockAsyncClient(AwsCredentialsProvider awsCredentialsProvider, String region) {
        return BedrockAsyncClient.builder()
                                 .httpClient(createAsyncHttpClient())
                                 .overrideConfiguration(o -> o.retryStrategy(RetryMode.defaultRetryMode()))
                                 .credentialsProvider(awsCredentialsProvider)
                                 .region(Region.of(region))
                                 .build();
    }

    private AwsBasicCredentials createBasicCredentials(AwsCredentials credentials) {
        return AwsBasicCredentials.create(
                credentials.getAwsAccessKeyId(),
                credentials.getAwsSecretAccessKey());
    }

    public StsAsyncClient createStsAsyncClient(AwsCredentials credentials) {
        AwsBasicCredentials basicCredentials = AwsBasicCredentials.create(credentials.getAwsAccessKeyId(),
                credentials.getAwsSecretAccessKey());
        return StsAsyncClient.builder().credentialsProvider(StaticCredentialsProvider.create(basicCredentials)).region(
                Region.of(credentials.getAwsRegion())).build();
    }
}
