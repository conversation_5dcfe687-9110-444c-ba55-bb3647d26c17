// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.servlet;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR>
 */
public class ReqBasicAuthStrategy implements ReqAuthStrategy {
    private final String _username;
    private final String _password;

    public ReqBasicAuthStrategy(String username, String password) {
        _username = username;
        _password = password;
    }

    @Override
    public void apply(RequestBuilder requestBuilder) {
        String encodedCredentials = Base64.getEncoder().encodeToString(
                (_username + ":" + _password).getBytes(StandardCharsets.UTF_8)
        );
        requestBuilder.addHeader("Authorization", "Basic " + encodedCredentials);
    }
}
