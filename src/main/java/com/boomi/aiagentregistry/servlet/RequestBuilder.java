// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.servlet;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.Accessors;

import org.springframework.web.util.UriComponentsBuilder;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Builder
@Accessors(prefix = "_")
@Getter
@AllArgsConstructor
public class RequestBuilder {

    private String _baseUrl;
    private String _path;
    private Map<String, String> _queryParams;
    private Map<String, String> _headers;
    private Object _body;
    private ReqAuthStrategy _authStrategy;

    public RequestBuilder() {
        _queryParams = new HashMap<>();
        _headers = new HashMap<>();
    }


    public void addHeader(String key, String value) {
        if(_headers == null) {
            _headers = new HashMap<>();
        }
        _headers.put(key, value);
    }

    public String buildUrl() {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(_baseUrl).path(_path);
        if(_queryParams != null){
            _queryParams.forEach(builder::queryParam);
        }
        return builder.toUriString();
    }

    public Map<String, String> getHeaders() {
        if (_authStrategy != null) {
            _authStrategy.apply(this);
        }
        return _headers;
    }

}
