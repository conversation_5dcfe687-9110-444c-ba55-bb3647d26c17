// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.servlet;

import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeStrategies;

import java.time.Duration;

/**
 * <AUTHOR>
 */
@Configuration
public class WebClientConfig {
    private static final int MEMORY_SIZE = 16 * 1024 * 1024;
    private static final int MAX_CONNECTIONS = 600;
    private static final int MAX_IDLE_TIMEOUT = 20;
    private static final int MAX_LIFE_TIME = 60;
    private static final int PENDING_ACQUIRE_TIMEOUT = 60;
    private static final int EVICT_IN_BACKGROUND = 120;
    private static final String FIXED = "fixed";

    private final ConnectionProvider _provider = ConnectionProvider.builder(FIXED)
            .maxConnections(MAX_CONNECTIONS)
            .maxIdleTime(Duration.ofSeconds(MAX_IDLE_TIMEOUT))
            .maxLifeTime(Duration.ofSeconds(MAX_LIFE_TIME))
            .pendingAcquireTimeout(Duration.ofSeconds(PENDING_ACQUIRE_TIMEOUT))
            .evictInBackground(Duration.ofSeconds(EVICT_IN_BACKGROUND)).build();

    @Bean(name = "WebClientBuilder")
    public org.springframework.web.reactive.function.client.WebClient.Builder webClientBuilder() {
        return org.springframework.web.reactive.function.client.WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(HttpClient.create(_provider)))
                .exchangeStrategies(
                        ExchangeStrategies.builder()
                                .codecs(configurer -> configurer
                                        .defaultCodecs()
                                        .maxInMemorySize(MEMORY_SIZE))
                                .build())
                .clone();
    }

}
