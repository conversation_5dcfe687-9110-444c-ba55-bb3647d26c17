# AI Agent Registry Service

## Local Development Setup

### JWT Configuration with <PERSON>ye Sandbox

To configure JWT authentication using <PERSON>ye sandbox instead of local port 8081, follow these steps:

#### Application Configuration

In `application.run.xml`, update the following properties:

```properties
-Dboomi.services.jwks.url=https://popeye1.sandbox.boomi.com/auth/.well-known/jwks.json
-Dboomi.services.jwks.issuer=https://popeye1.sandbox.boomi.com
```

# Altair Environment Settings

## Configure your Altair environment with the following values:

{
"host": "https://popeye1.sandbox.boomi.com",
"accountId": "aicontroltowerengineering-DQV622",
"username": "your_popeye_username",
"password": "your_popeye_password"
}

## AWS Configuration

### AWS Credentials Setup

Before running the application, you need to configure your AWS credentials.

#### Prerequisites

- Access to boomi-cj-sandbox AWS account
- IAM user permissions

#### Steps to Generate AWS Credentials

1. Log in to AWS Console (boomi-cj-sandbox account)
2. Navigate to IAM Service:
    - Select "IAM Users" from the services menu
    - Go to "Users" section
3. Set up your IAM User:
    - Locate your username
    - If no user exists, create a new one
    - Copy permissions from the user account: Ashish
4. Generate Access Keys:
    - Select your IAM user
    - Navigate to "Security credentials" tab
    - Click "Create access key"
    - Save both Access Key ID and Secret Access Key to the aws credentials file under default profile ([default])

#### Configure AWS Credentials File

1. Locate the AWS credentials file:
   ```bash
   # Linux/macOS
   ~/.aws/credentials

## Logging Configuration

### Console Logs Setup

To enable console logging for local development:

1. Navigate to `Application.run.xml`
2. Locate the "Active profile" section
3. Set the value to:
   ````
   Active profile: dev
````