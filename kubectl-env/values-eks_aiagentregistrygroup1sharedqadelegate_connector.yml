config:
  ai_agent_registry_boomi_contact: "Customer Journey - AI Registry"
  ai_agent_registry_environment: "qa"
  ai_agent_registry_env_ssm: "QA"
  ai_agent_registry_stackName: "ai-agent-registry"
  ai_agent_registry_image_uri: "871876053574.dkr.ecr.us-east-1.amazonaws.com/boomi/ai-agent-registry:latest"
  ai_agent_registry_aws_region: "us-east-1"
  ai_agent_registry_aws_secondary_region: "us-west-2"
  ai_agent_registry_spring_datasource_url: "******************************************************************************************************************************"
  ai_agent_registry_spring_datasource_username: "postgres"
  ai_agent_registry_spring_datasource_default_url: "*************************************************************************************************************"
  ai_agent_registry_NEW_RELIC_APP_NAME: "ai-agent-registry-qa"
  ai_agent_registry_newrelic_secret_name: "newrelic_secrets"
  ai_agent_registry_newrelic_api_key: "d5a7b4a5841da6b257f0ceeea15b73d9FFFFNRAL"
  ai_agent_registry_newrelic_enabled: true
  ai_agent_registry_newrelic_forward_enabled: true
  ai_agent_registry_jwks_url: "https://qa.boomi.com/auth/.well-known/jwks.json"
  ai_agent_registry_jwks_issuer: "https://qa.boomi.com"
  ai_agent_registry_platformBaseUrl: "https://qa.boomi.com"
  ai_agent_registry_apiPlatformUrl: "https://qa.boomi.com"
  ai_agent_registry_monitor_user: "admin"
  ai_agent_registry_private_ipv4_addresses: "***********, ***********, ***********, ***********"
  ai_agent_registry_acm: "arn:aws:acm:us-east-1:************:certificate/0178bfc1-0ed8-4e92-874c-79a3ee08af22"
  ai_agent_registry_externalLink_agent_aws_bedrock: "https://{REGION}.console.aws.amazon.com/bedrock/home?region={REGION}#/agents/{AGENT_ID}"
  ai_agent_registry_externalLink_agent_boomi: "https://qa.boomi.com/BoomiAI.html#ai;accountId={ACCOUNT_ID}"
  ai_agent_registry_externalLink_version_aws_bedrock: "https://{REGION}.console.aws.amazon.com/bedrock/home?region={REGION}#/agents/{AGENT_ID}/versions/{AGENT_VERSION}"
  ai_agent_registry_externalLink_version_boomi: "https://qa.boomi.com/BoomiAI.html#ai;accountId={ACCOUNT_ID}"
  ai_agent_registry_externalLink_alias_aws_bedrock: "https://{REGION}.console.aws.amazon.com/bedrock/home?region={REGION}#/agents/{AGENT_ID}/alias/{AGENT_ALIAS_ID}"
  ai_agent_registry_garden_apiUrl: "https://ai-agent-garden.datalake-pipeline.boomi.com"
  ai_agent_registry_garden_jwtUrl: "https://qa.boomi.com"
  boomi_services_timestream_roleArn: "arn:aws:iam::************:role/SharedTimeStreamRole-Metering-QAPlat"
  boomi_services_timestream_database_name: "Metering-QAPlat"
  ai_agent_registry_monitoring_account_id: "************"
  ai_agent_registry_use_BedrockAssumeRole: true
  s3_bucket: boomi-ai-agent-registry-app-data
  ai_agent_service_account_role: ai-registry-QA-ai-agent-registry-sa-role-us-east-1
  ai_agent_registry_allow_one_external_account_with_one_idp_one_auth: true
  ai_agent_registry_aws_metrics_stream_in_all_regions: true
  ai_agent_registry_use_real_oam_client: true
  boomi_services_aiagentregistry_sync_queue_url: "https://sqs.us-east-1.amazonaws.com/************/ai-agent-registry-qa-manual-sqs-queue"
  boomi_services_aiagentregistry_sync_queue_region: "us-east-1"
  ai_agent_registry_mail_enabled: true
  ai_agent_registry_mail_dl: "<EMAIL>"
  ai_agent_registry_scheduler_cron_expression: "0 0 1 * * *"
  ai_agent_registry_mail_host: "email-smtp.us-east-1.amazonaws.com"
  ai_agent_registry_mail_port: 587
  ai_agent_registry_service_enable_hpa: false
  ai_agent_sync_auto_service_account_role: ai-agent-registry-qa-auto-sync-role-us-east-1
  ai_agent_sync_manual_service_account_role: ai-agent-registry-qa-manual-sync-role-us-east-1
