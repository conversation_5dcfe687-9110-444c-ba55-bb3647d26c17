<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="DBInit Runner.run.xml" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ALTERNATIVE_JRE_PATH" value="corretto-17" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="DEBUG_MODE" value="true" />
    <module name="ai-agent-registry-service" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.boomi.services.liquibase.LiquibaseRunner" />
    <option name="VM_PARAMETERS" value="-Dboomi.services.aiagentregistry.db.default.url=***************************************** -Dboomi.services.aiagentregistry.db.username=admin -Dboomi.services.aiagentregistry.db.password=pw -Dspring.profiles.active=dev,db-init" />
    <extension name="software.aws.toolkits.jetbrains.core.execution.JavaAwsConnectionExtension">
      <option name="credential" />
      <option name="region" />
      <option name="useCurrentConnection" value="false" />
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>