<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Application" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
    <option name="ACTIVE_PROFILES" value="local" />
    <module name="ai-agent-registry-service" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.boomi.aiagentregistry.Application" />
    <option name="VM_PARAMETERS" value="-Dboomi.services.jwks.url=https://popeye1.sandbox.boomi.com/auth/.well-known/jwks.json -Dboomi.services.jwks.issuer=https://popeye1.sandbox.boomi.com -Dnewrelic.enabled=false -Dnewrelic.api-key=xyz -Dnewrelic.stack=local -Dboomi.services.monitor.user=xyz -Dboomi.services.monitor.pass=xyz -Dcom.boomi.aiagentregistry.aws.secondary.region=us-west-2 -Dboomi.services.aiagentregistry.db.url=********************************************************** -Dboomi.services.aiagentregistry.db.username=admin -Dboomi.services.aiagentregistry.db.password=pw -Dapi.boomi.platform.url=http://localhost:8081 -Dcom.boomi.aiagentregistry.aws.region=us-east-1 -Daws.secretsmanager.local.enabled=false -Daws.s3.local.enabled=true -Dboomi.services.aiagentregistry.local.auth=true -Dboomi.services.timestream.aws.local.access=true -Dboomi.services.timestream.region=us-east-1 -Dboomi.services.timestream.database_name=Metering-Shared -Dboomi.services.timestream.aws.profile.for.local=boomi-services-pipeline-admin -Dboomi.services.aiagentregistry.garden.jwtUrl=https://biaiagentp.datalake-sandbox.boomi.com -Dboomi.services.aiagentregistry.garden.apiUrl=https://ai-agent-garden.datalake-sandbox.boomi.com -Dboomi.services.aiagentregistry.use.bedrock.assume.role=true -Dboomi.services.aiagentregistry.customer.aws.role.assume.session_duration_seconds=3600 -Dboomi.services.aiagentregistry.monitoring.account.id=************ -Dboomi.services.aiagentregistry.s3.bucket=boomi-ai-agent-registry-app-data-sandbox-us-east-1 -Dboomi.services.aiagentregistry.aws.metrics.stream.in.all.regions=false -Dcom.boomi.aiagentregistry.environment=local -Dboomi.services.aiagentregistry.customer.aws.role.prefix=Boomi-ACT-bedrock-customer-role -Dboomi.services.aiagentregistry.customer.aws.role.policy.prefix=Boomi-ACT-bedrock-access-policy -Dboomi.services.aiagentregistry.customer.aws.bedrock.role.prefix=Boomi-ACT-bedrock-customer-role -Dboomi.services.aiagentregistry.customer.aws.bedrock.role.policy.prefix=Boomi-ACT-bedrock-access-policy -Dboomi.services.aiagentregistry.customer.aws.oam.role.prefix=Boomi-ACT-oam-customer-role -Dboomi.services.aiagentregistry.customer.aws.oam.role.policy.prefix=Boomi-ACT-oam-access-policy -Dboomi.services.aiagentregistry.service.account.role=ai-agent-registry-sandbox-auto-sync-role-us-east-1 -Dboomi.services.aiagentregistry.allow.one.external.account.with.one.idp.and.one.auth.type=true -Dboomi.services.aiagentregistry.use.real.oam.client=false -Dboomi.services.aiagentregistry.service.assume.role.session.name=BoomiActSession -Dboomi.services.aiagentregistry.sync.queue.url=http://host.docker.internal:4566/************/ai-agent-registry-sandbox-account-sync-queue -Dboomi.services.aiagentregistry.sync.queue.region=us-east-1 -Dboomi.services.aiagentregistry.sync.queue.isLocal=true -Dboomi.services.aiagentregistry.write.non.summary.logs=true -Dboomi.services.aiagentregistry.mail.enabled=false -Dboomi.services.aiagentregistry.mail.dl=<EMAIL> -Dboomi.services.aiagentregistry.scheduler.cron.expression=&quot;0 0 1 * * *&quot; -Dboomi.services.aiagentregistry.mail.host=localhost.boomi.com -Dboomi.services.aiagentregistry.mail.username=check-with-team -Dboomi.services.aiagentregistry.mail.password=check-with-team -Dboomi.services.aiagentregistry.mail.port=587 -Dcom.boomi.aiagentregistry.stackType=&quot;ai-agent-registry&quot; -Dcom.boomi.aiagentregistry.stackName=&quot;ai-agent-registry&quot; -Dcom.boomi.aiagentregistry.boomiContact=&quot;Customer Journey - AI Registry&quot; -Dcom.boomi.aiagentregistry.customer=&quot;Boomi&quot; -Dcom.boomi.aiagentregistry.serviceName=&quot;ai-agent-registry&quot; -Dcom.boomi.aiagentregistry.secretManagerRole=&quot;SecretStore&quot; -Dboomi.services.aiagentsync.service.auto.account.role=ai-agent-registry-sandbox-auto-sync-role-us-east-1 -Dboomi.services.aiagentsync.service.manual.account.role=ai-agent-registry-sandbox-manual-sync-role-us-east-1" />
    <extension name="software.aws.toolkits.jetbrains.core.execution.JavaAwsConnectionExtension">
      <option name="credential" />
      <option name="region" />
      <option name="useCurrentConnection" value="false" />
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>